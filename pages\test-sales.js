import { useState } from 'react';
import Layout from '../components/Layout';
import ProductCustomizer from '../components/sales/ProductCustomizer';
import PaymentManager from '../components/sales/PaymentManager';
import InvoicePrintView from '../components/sales/InvoicePrintView';

export default function TestSalesPage() {
  const [showCustomizer, setShowCustomizer] = useState(false);
  const [showPaymentManager, setShowPaymentManager] = useState(false);
  const [showInvoicePreview, setShowInvoicePreview] = useState(false);

  // Sample customizable product
  const sampleProduct = {
    id: '6',
    code: 'CUSTOM001',
    name: 'Custom Gaming PC',
    nameAr: 'جهاز كمبيوتر ألعاب مخصص',
    basePrice: 800.00,
    isCustomizable: true,
    customizationOptions: [
      {
        id: 'cpu',
        name: 'المعالج',
        nameEn: 'Processor',
        required: true,
        options: [
          { id: 'cpu1', name: 'Intel i5-12400F', price: 200, componentId: '3' },
          { id: 'cpu2', name: 'Intel i7-12700F', price: 350, componentId: '3' },
          { id: 'cpu3', name: 'AMD Ryzen 5 5600X', price: 250, componentId: '3' }
        ]
      },
      {
        id: 'ram',
        name: 'الذاكرة',
        nameEn: 'Memory',
        required: true,
        options: [
          { id: 'ram1', name: '16GB DDR4', price: 80, componentId: '4' },
          { id: 'ram2', name: '32GB DDR4', price: 160, componentId: '4' },
          { id: 'ram3', name: '64GB DDR4', price: 320, componentId: '4' }
        ]
      },
      {
        id: 'gpu',
        name: 'كارت الشاشة',
        nameEn: 'Graphics Card',
        required: false,
        options: [
          { id: 'gpu1', name: 'RTX 3060', price: 400, componentId: '2' },
          { id: 'gpu2', name: 'RTX 3070', price: 600, componentId: '2' },
          { id: 'gpu3', name: 'RTX 4080', price: 1200, componentId: '2' }
        ]
      }
    ]
  };

  // Sample invoice
  const sampleInvoice = {
    id: '1',
    invoiceNumber: 'INV-2024-001',
    customerId: '1',
    customerName: 'شركة ABC للتكنولوجيا',
    status: 'PARTIALLY_PAID',
    createdAt: new Date().toISOString(),
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    items: [
      {
        id: '1',
        productName: 'جهاز كمبيوتر ألعاب مخصص',
        quantity: 1,
        unitPrice: 1450.00,
        discount: 5,
        total: 1377.50,
        isCustomized: true,
        customizationDetails: [
          { optionName: 'المعالج', selectedName: 'Intel i7-12700F' },
          { optionName: 'الذاكرة', selectedName: '32GB DDR4' },
          { optionName: 'كارت الشاشة', selectedName: 'RTX 3070' }
        ]
      },
      {
        id: '2',
        productName: 'خدمة التركيب والإعداد',
        quantity: 1,
        unitPrice: 100.00,
        discount: 0,
        total: 100.00
      }
    ],
    subtotal: 1477.50,
    taxAmount: 206.85,
    total: 1684.35,
    paidAmount: 800.00,
    remainingAmount: 884.35,
    payments: [
      {
        id: '1',
        method: 'CASH',
        amount: 500.00,
        paidAt: new Date().toISOString(),
        reference: ''
      },
      {
        id: '2',
        method: 'INSTAPAY',
        amount: 300.00,
        paidAt: new Date().toISOString(),
        reference: 'IP123456789'
      }
    ],
    notes: 'فاتورة تجريبية لاختبار النظام'
  };

  const sampleCustomer = {
    id: '1',
    name: 'شركة ABC للتكنولوجيا',
    email: '<EMAIL>',
    phone: '+201234567890',
    address: 'شارع التحرير، القاهرة، مصر'
  };

  const handleCustomizedProduct = (customizedProduct) => {
    console.log('Customized Product:', customizedProduct);
    alert(`تم تخصيص المنتج بنجاح! السعر النهائي: $${customizedProduct.finalPrice.toFixed(2)}`);
  };

  const handlePaymentAdd = (payment) => {
    console.log('New Payment:', payment);
    alert(`تم إضافة دفعة بقيمة $${payment.amount} بطريقة ${payment.method}`);
  };

  return (
    <Layout>
      <div className="space-y-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            اختبار مكونات نظام المبيعات
          </h1>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Product Customizer Test */}
            <div className="border rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-3">تخصيص المنتجات</h3>
              <p className="text-gray-600 mb-4">
                اختبار نظام تخصيص المنتجات مع خيارات متعددة
              </p>
              <button
                onClick={() => setShowCustomizer(true)}
                className="btn-primary w-full"
              >
                فتح مخصص المنتجات
              </button>
            </div>

            {/* Payment Manager Test */}
            <div className="border rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-3">إدارة المدفوعات</h3>
              <p className="text-gray-600 mb-4">
                اختبار نظام الدفع المتقدم مع طرق دفع متعددة
              </p>
              <button
                onClick={() => setShowPaymentManager(true)}
                className="btn-primary w-full"
              >
                فتح إدارة المدفوعات
              </button>
            </div>

            {/* Invoice Preview Test */}
            <div className="border rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-3">معاينة الفاتورة</h3>
              <p className="text-gray-600 mb-4">
                اختبار تصميم الفاتورة المحسن للطباعة
              </p>
              <button
                onClick={() => setShowInvoicePreview(true)}
                className="btn-primary w-full"
              >
                معاينة الفاتورة
              </button>
            </div>
          </div>
        </div>

        {/* Sample Data Display */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">البيانات التجريبية</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-2">منتج قابل للتخصيص:</h3>
              <div className="bg-gray-50 p-3 rounded text-sm">
                <p><strong>الاسم:</strong> {sampleProduct.nameAr}</p>
                <p><strong>السعر الأساسي:</strong> ${sampleProduct.basePrice}</p>
                <p><strong>خيارات التخصيص:</strong> {sampleProduct.customizationOptions.length}</p>
              </div>
            </div>
            
            <div>
              <h3 className="font-medium mb-2">فاتورة تجريبية:</h3>
              <div className="bg-gray-50 p-3 rounded text-sm">
                <p><strong>رقم الفاتورة:</strong> {sampleInvoice.invoiceNumber}</p>
                <p><strong>الإجمالي:</strong> ${sampleInvoice.total.toFixed(2)}</p>
                <p><strong>المدفوع:</strong> ${sampleInvoice.paidAmount.toFixed(2)}</p>
                <p><strong>المتبقي:</strong> ${sampleInvoice.remainingAmount.toFixed(2)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Invoice Preview */}
        {showInvoicePreview && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-4 border-b">
                <h3 className="text-lg font-semibold">معاينة الفاتورة</h3>
                <button
                  onClick={() => setShowInvoicePreview(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              <div className="p-4">
                <InvoicePrintView
                  invoice={sampleInvoice}
                  customer={sampleCustomer}
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <ProductCustomizer
        isOpen={showCustomizer}
        onClose={() => setShowCustomizer(false)}
        product={sampleProduct}
        onSave={handleCustomizedProduct}
      />

      <PaymentManager
        isOpen={showPaymentManager}
        onClose={() => setShowPaymentManager(false)}
        totalAmount={sampleInvoice.total}
        paidAmount={sampleInvoice.paidAmount}
        onPaymentAdd={handlePaymentAdd}
        existingPayments={sampleInvoice.payments}
      />
    </Layout>
  );
}
