const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Get all branches
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { includeInactive = false } = req.query;

    const where = includeInactive === 'true' ? {} : { isActive: true };

    const branches = await prisma.branch.findMany({
      where,
      include: {
        manager: {
          select: { id: true, firstName: true, lastName: true }
        },
        _count: {
          select: {
            users: true,
            salesOrders: true,
            inventory: true
          }
        },
        cashBox: {
          select: {
            currentBalance: true,
            lastTransferDate: true
          }
        }
      },
      orderBy: [
        { isMainBranch: 'desc' },
        { name: 'asc' }
      ]
    });

    res.json({ branches });

  } catch (error) {
    console.error('Get branches error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get single branch with detailed info
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const branch = await prisma.branch.findUnique({
      where: { id: req.params.id },
      include: {
        company: true,
        manager: {
          select: { id: true, firstName: true, lastName: true, email: true }
        },
        users: {
          select: { id: true, firstName: true, lastName: true, role: true, isActive: true }
        },
        cashBox: {
          include: {
            transactions: {
              take: 10,
              orderBy: { createdAt: 'desc' },
              include: {
                user: {
                  select: { firstName: true, lastName: true }
                }
              }
            }
          }
        },
        inventory: {
          include: {
            product: {
              select: { id: true, name: true, nameAr: true, code: true, unitPrice: true }
            }
          },
          where: {
            quantity: { gt: 0 }
          }
        }
      }
    });

    if (!branch) {
      return res.status(404).json({ error: 'Branch not found' });
    }

    res.json({ branch });

  } catch (error) {
    console.error('Get branch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create new branch
router.post('/', authenticateToken, authorizeRoles('ADMIN'), async (req, res) => {
  try {
    const {
      companyId,
      code,
      name,
      nameAr,
      address,
      addressAr,
      phone,
      email,
      managerId,
      isMainBranch = false,
      openingBalance = 0
    } = req.body;

    // Validation
    if (!companyId || !code || !name || !nameAr || !address || !phone) {
      return res.status(400).json({ error: 'Required fields are missing' });
    }

    // Check if code already exists
    const existingBranch = await prisma.branch.findUnique({
      where: { code }
    });

    if (existingBranch) {
      return res.status(400).json({ error: 'Branch code already exists' });
    }

    // If setting as main branch, unset other main branches
    if (isMainBranch) {
      await prisma.branch.updateMany({
        where: { companyId, isMainBranch: true },
        data: { isMainBranch: false }
      });
    }

    // Create branch with cash box in transaction
    const result = await prisma.$transaction(async (tx) => {
      const branch = await tx.branch.create({
        data: {
          companyId,
          code,
          name,
          nameAr,
          address,
          addressAr,
          phone,
          email,
          managerId,
          isMainBranch
        },
        include: {
          manager: {
            select: { id: true, firstName: true, lastName: true }
          }
        }
      });

      // Create cash box for the branch
      await tx.cashBox.create({
        data: {
          branchId: branch.id,
          currentBalance: parseFloat(openingBalance),
          openingBalance: parseFloat(openingBalance)
        }
      });

      // If opening balance > 0, create initial transaction
      if (parseFloat(openingBalance) > 0) {
        const cashBox = await tx.cashBox.findUnique({
          where: { branchId: branch.id }
        });

        await tx.cashTransaction.create({
          data: {
            cashBoxId: cashBox.id,
            type: 'INCOME',
            amount: parseFloat(openingBalance),
            description: 'Opening balance',
            descriptionAr: 'الرصيد الافتتاحي',
            userId: req.user.id
          }
        });
      }

      return branch;
    });

    res.status(201).json({
      branch: result,
      message: 'Branch created successfully'
    });

  } catch (error) {
    console.error('Create branch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update branch
router.put('/:id', authenticateToken, authorizeRoles('ADMIN'), async (req, res) => {
  try {
    const {
      name,
      nameAr,
      address,
      addressAr,
      phone,
      email,
      managerId,
      isMainBranch,
      isActive
    } = req.body;

    // Check if branch exists
    const existingBranch = await prisma.branch.findUnique({
      where: { id: req.params.id }
    });

    if (!existingBranch) {
      return res.status(404).json({ error: 'Branch not found' });
    }

    // If setting as main branch, unset other main branches
    if (isMainBranch && !existingBranch.isMainBranch) {
      await prisma.branch.updateMany({
        where: { 
          companyId: existingBranch.companyId, 
          isMainBranch: true,
          id: { not: req.params.id }
        },
        data: { isMainBranch: false }
      });
    }

    const branch = await prisma.branch.update({
      where: { id: req.params.id },
      data: {
        ...(name && { name }),
        ...(nameAr && { nameAr }),
        ...(address && { address }),
        ...(addressAr && { addressAr }),
        ...(phone && { phone }),
        ...(email !== undefined && { email }),
        ...(managerId !== undefined && { managerId }),
        ...(isMainBranch !== undefined && { isMainBranch }),
        ...(isActive !== undefined && { isActive })
      },
      include: {
        manager: {
          select: { id: true, firstName: true, lastName: true }
        }
      }
    });

    res.json({
      branch,
      message: 'Branch updated successfully'
    });

  } catch (error) {
    console.error('Update branch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get branch inventory
router.get('/:id/inventory', authenticateToken, async (req, res) => {
  try {
    const { lowStock = false } = req.query;

    const where = {
      branchId: req.params.id,
      ...(lowStock === 'true' ? {
        quantity: { lte: prisma.branchInventory.fields.minStock }
      } : {})
    };

    const inventory = await prisma.branchInventory.findMany({
      where,
      include: {
        product: {
          select: {
            id: true,
            code: true,
            name: true,
            nameAr: true,
            unitPrice: true,
            costPrice: true,
            unit: true,
            unitAr: true
          }
        }
      },
      orderBy: [
        { quantity: 'asc' },
        { product: { name: 'asc' } }
      ]
    });

    res.json({ inventory });

  } catch (error) {
    console.error('Get branch inventory error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Transfer inventory between branches
router.post('/:id/transfer', authenticateToken, authorizeRoles('ADMIN', 'MANAGER'), async (req, res) => {
  try {
    const fromBranchId = req.params.id;
    const { toBranchId, items, notes } = req.body;

    // Validation
    if (!toBranchId || !items || items.length === 0) {
      return res.status(400).json({ error: 'Required fields are missing' });
    }

    // Check if branches exist
    const [fromBranch, toBranch] = await Promise.all([
      prisma.branch.findUnique({ where: { id: fromBranchId } }),
      prisma.branch.findUnique({ where: { id: toBranchId } })
    ]);

    if (!fromBranch || !toBranch) {
      return res.status(400).json({ error: 'Branch not found' });
    }

    // Generate transfer number
    const transferCount = await prisma.branchTransfer.count();
    const transferNumber = `BT-${String(transferCount + 1).padStart(6, '0')}`;

    // Validate inventory availability
    for (const item of items) {
      const inventory = await prisma.branchInventory.findUnique({
        where: {
          branchId_productId: {
            branchId: fromBranchId,
            productId: item.productId
          }
        },
        include: { product: true }
      });

      if (!inventory || inventory.quantity < item.quantity) {
        return res.status(400).json({
          error: `Insufficient inventory for ${inventory?.product?.name || 'product'}. Available: ${inventory?.quantity || 0}`
        });
      }
    }

    // Create transfer
    const transfer = await prisma.$transaction(async (tx) => {
      const transfer = await tx.branchTransfer.create({
        data: {
          transferNumber,
          fromBranchId,
          toBranchId,
          requestedBy: req.user.id,
          approvedBy: req.user.id, // Auto-approve for admin/manager
          status: 'APPROVED',
          transferDate: new Date(),
          notes,
          items: {
            create: items.map(item => ({
              productId: item.productId,
              quantity: item.quantity,
              notes: item.notes
            }))
          }
        },
        include: {
          fromBranch: true,
          toBranch: true,
          items: {
            include: { product: true }
          }
        }
      });

      // Update inventories
      for (const item of items) {
        // Decrease from source branch
        await tx.branchInventory.update({
          where: {
            branchId_productId: {
              branchId: fromBranchId,
              productId: item.productId
            }
          },
          data: {
            quantity: { decrement: item.quantity }
          }
        });

        // Increase in destination branch (create if doesn't exist)
        await tx.branchInventory.upsert({
          where: {
            branchId_productId: {
              branchId: toBranchId,
              productId: item.productId
            }
          },
          update: {
            quantity: { increment: item.quantity }
          },
          create: {
            branchId: toBranchId,
            productId: item.productId,
            quantity: item.quantity
          }
        });
      }

      return transfer;
    });

    res.status(201).json({
      transfer,
      message: 'Inventory transfer completed successfully'
    });

  } catch (error) {
    console.error('Branch transfer error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
