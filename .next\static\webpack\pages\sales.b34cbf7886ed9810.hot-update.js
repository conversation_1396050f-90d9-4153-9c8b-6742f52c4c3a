"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "./components/sales/InvoiceModal.js":
/*!******************************************!*\
  !*** ./components/sales/InvoiceModal.js ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InvoiceModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CogIcon,CreditCardIcon,PlusIcon,PrinterIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=CogIcon,CreditCardIcon,PlusIcon,PrinterIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-to-print */ \"./node_modules/react-to-print/lib/index.js\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_to_print__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _ProductCustomizer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ProductCustomizer */ \"./components/sales/ProductCustomizer.js\");\n/* harmony import */ var _PaymentManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./PaymentManager */ \"./components/sales/PaymentManager.js\");\n/* harmony import */ var _InvoicePrintView__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./InvoicePrintView */ \"./components/sales/InvoicePrintView.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction InvoiceModal(param) {\n    let { isOpen, onClose, onSave, invoice = null, fromSalesOrder = null } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const printRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Modal states\n    const [showCustomizer, setShowCustomizer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPaymentManager, setShowPaymentManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: \"\",\n        dueDate: \"\",\n        notes: \"\",\n        items: [],\n        payments: [],\n        // إضافة خصم عام\n        generalDiscount: 0,\n        generalDiscountType: \"PERCENTAGE\" // PERCENTAGE or AMOUNT\n    });\n    const [showPaymentModal, setShowPaymentModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentData, setPaymentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        method: \"CASH\",\n        amount: 0,\n        reference: \"\",\n        installmentPlan: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            loadCustomers();\n            loadProducts();\n            if (invoice) {\n                setFormData({\n                    customerId: invoice.customerId || \"\",\n                    dueDate: invoice.dueDate ? invoice.dueDate.split(\"T\")[0] : \"\",\n                    notes: invoice.notes || \"\",\n                    items: invoice.items || [],\n                    payments: invoice.payments || []\n                });\n            } else if (fromSalesOrder) {\n                // Convert sales order to invoice\n                const dueDate = new Date();\n                dueDate.setDate(dueDate.getDate() + 30); // 30 days payment terms\n                setFormData({\n                    customerId: fromSalesOrder.customerId || \"\",\n                    dueDate: dueDate.toISOString().split(\"T\")[0],\n                    notes: \"تم التحويل من أمر البيع: \".concat(fromSalesOrder.orderNumber),\n                    items: fromSalesOrder.items || [],\n                    payments: []\n                });\n            } else {\n                // New invoice\n                const dueDate = new Date();\n                dueDate.setDate(dueDate.getDate() + 30);\n                setFormData((prev)=>({\n                        ...prev,\n                        dueDate: dueDate.toISOString().split(\"T\")[0]\n                    }));\n            }\n        }\n    }, [\n        isOpen,\n        invoice,\n        fromSalesOrder\n    ]);\n    const loadCustomers = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/customers\"));\n            setCustomers(response.data.customers || []);\n        } catch (error) {\n            console.error(\"Error loading customers:\", error);\n        }\n    };\n    const loadProducts = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/products\"));\n            setProducts(response.data.products || []);\n        } catch (error) {\n            console.error(\"Error loading products:\", error);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const addItem = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: [\n                    ...prev.items,\n                    {\n                        productId: \"\",\n                        productName: \"\",\n                        quantity: 1,\n                        unitPrice: 0,\n                        discount: 0,\n                        taxRate: 14,\n                        hasTax: true,\n                        total: 0,\n                        isCustomized: false,\n                        customizations: null,\n                        customizationDetails: []\n                    }\n                ]\n            }));\n    };\n    const removeItem = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: prev.items.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateItem = (index, field, value)=>{\n        setFormData((prev)=>{\n            const newItems = [\n                ...prev.items\n            ];\n            newItems[index] = {\n                ...newItems[index],\n                [field]: value\n            };\n            if (field === \"productId\") {\n                const product = products.find((p)=>p.id === value);\n                if (product) {\n                    newItems[index].unitPrice = parseFloat(product.unitPrice);\n                    newItems[index].productName = product.nameAr || product.name;\n                    // Check if product is customizable\n                    if (product.isCustomizable) {\n                        setSelectedProduct(product);\n                        setCurrentItemIndex(index);\n                        setShowCustomizer(true);\n                        return prev; // Don't update yet, wait for customization\n                    }\n                }\n            }\n            // Recalculate totals for any change\n            if ([\n                \"quantity\",\n                \"unitPrice\",\n                \"discount\",\n                \"taxRate\",\n                \"hasTax\"\n            ].includes(field)) {\n                const item = newItems[index];\n                const quantity = parseFloat(item.quantity) || 0;\n                const unitPrice = parseFloat(item.unitPrice) || 0;\n                const discountPercent = parseFloat(item.discount) || 0;\n                const taxRate = parseFloat(item.taxRate) || 0;\n                // Calculate subtotal\n                const subtotal = quantity * unitPrice;\n                // Apply discount\n                const discountAmount = subtotal * (discountPercent / 100);\n                const afterDiscount = subtotal - discountAmount;\n                // Apply tax if enabled\n                const taxAmount = item.hasTax ? afterDiscount * (taxRate / 100) : 0;\n                const total = afterDiscount + taxAmount;\n                newItems[index].total = total;\n                newItems[index].subtotal = subtotal;\n                newItems[index].discountAmount = discountAmount;\n                newItems[index].taxAmount = taxAmount;\n            }\n            return {\n                ...prev,\n                items: newItems\n            };\n        });\n    };\n    // Handle customized product\n    const handleCustomizedProduct = (customizedProduct)=>{\n        setFormData((prev)=>{\n            const newItems = [\n                ...prev.items\n            ];\n            newItems[currentItemIndex] = {\n                ...newItems[currentItemIndex],\n                productId: customizedProduct.id,\n                productName: customizedProduct.nameAr || customizedProduct.name,\n                unitPrice: customizedProduct.finalPrice,\n                customizations: customizedProduct.customizations,\n                customizationDetails: customizedProduct.customizationDetails,\n                isCustomized: true\n            };\n            // Recalculate total\n            const item = newItems[currentItemIndex];\n            const subtotal = (parseFloat(item.quantity) || 0) * (parseFloat(item.unitPrice) || 0);\n            const discountAmount = subtotal * ((parseFloat(item.discount) || 0) / 100);\n            newItems[currentItemIndex].total = subtotal - discountAmount;\n            return {\n                ...prev,\n                items: newItems\n            };\n        });\n    };\n    // Print functionality\n    const handlePrint = (0,react_to_print__WEBPACK_IMPORTED_MODULE_4__.useReactToPrint)({\n        content: ()=>printRef.current,\n        documentTitle: \"فاتورة-\".concat(formData.invoiceNumber || \"جديدة\")\n    });\n    const calculateTotals = ()=>{\n        // حساب المجاميع من العناصر\n        const itemsSubtotal = formData.items.reduce((sum, item)=>sum + (parseFloat(item.subtotal) || 0), 0);\n        const itemsDiscountAmount = formData.items.reduce((sum, item)=>sum + (parseFloat(item.discountAmount) || 0), 0);\n        const itemsTaxAmount = formData.items.reduce((sum, item)=>sum + (parseFloat(item.taxAmount) || 0), 0);\n        const itemsTotal = formData.items.reduce((sum, item)=>sum + (parseFloat(item.total) || 0), 0);\n        // حساب الخصم العام\n        let generalDiscountAmount = 0;\n        if (formData.generalDiscount > 0) {\n            if (formData.generalDiscountType === \"PERCENTAGE\") {\n                generalDiscountAmount = itemsTotal * (parseFloat(formData.generalDiscount) / 100);\n            } else {\n                generalDiscountAmount = parseFloat(formData.generalDiscount);\n            }\n        }\n        // الإجمالي النهائي بعد الخصم العام\n        const finalTotal = itemsTotal - generalDiscountAmount;\n        // المدفوعات\n        const paidAmount = formData.payments.reduce((sum, payment)=>sum + (parseFloat(payment.amount) || 0), 0);\n        const remainingAmount1 = finalTotal - paidAmount;\n        return {\n            itemsSubtotal,\n            itemsDiscountAmount,\n            itemsTaxAmount,\n            itemsTotal,\n            generalDiscountAmount,\n            finalTotal,\n            paidAmount,\n            remainingAmount: remainingAmount1\n        };\n    };\n    const addPayment = ()=>{\n        const { finalTotal, paidAmount } = calculateTotals();\n        const maxAmount = finalTotal - paidAmount;\n        if (maxAmount <= 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"تم دفع المبلغ بالكامل\");\n            return;\n        }\n        setShowPaymentManager(true);\n    };\n    const handlePaymentAdd = (payment)=>{\n        setFormData((prev)=>({\n                ...prev,\n                payments: [\n                    ...prev.payments,\n                    {\n                        ...payment,\n                        id: Date.now().toString()\n                    }\n                ]\n            }));\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"تم إضافة الدفعة بنجاح\");\n    };\n    const savePayment = ()=>{\n        if (!paymentData.amount || paymentData.amount <= 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى إدخال مبلغ صحيح\");\n            return;\n        }\n        const { finalTotal, paidAmount } = calculateTotals();\n        if (paidAmount + parseFloat(paymentData.amount) > finalTotal) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"المبلغ المدفوع أكبر من المبلغ المطلوب\");\n            return;\n        }\n        setFormData((prev)=>({\n                ...prev,\n                payments: [\n                    ...prev.payments,\n                    {\n                        ...paymentData,\n                        id: Date.now().toString(),\n                        paidAt: new Date().toISOString()\n                    }\n                ]\n            }));\n        setShowPaymentModal(false);\n        setPaymentData({\n            method: \"CASH\",\n            amount: 0,\n            reference: \"\",\n            installmentPlan: \"\"\n        });\n    };\n    const removePayment = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                payments: prev.payments.filter((_, i)=>i !== index)\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.customerId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى اختيار العميل\");\n            return;\n        }\n        if (formData.items.length === 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى إضافة عنصر واحد على الأقل\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const { itemsSubtotal, itemsDiscountAmount, itemsTaxAmount, itemsTotal, generalDiscountAmount, finalTotal, paidAmount, remainingAmount: remainingAmount1 } = calculateTotals();\n            const invoiceData = {\n                ...formData,\n                itemsSubtotal,\n                itemsDiscountAmount,\n                itemsTaxAmount,\n                itemsTotal,\n                generalDiscountAmount,\n                finalTotal,\n                paidAmount,\n                remainingAmount: remainingAmount1,\n                status: paidAmount >= finalTotal ? \"PAID\" : paidAmount > 0 ? \"PARTIALLY_PAID\" : \"PENDING\",\n                salesOrderId: (fromSalesOrder === null || fromSalesOrder === void 0 ? void 0 : fromSalesOrder.id) || null\n            };\n            const response = invoice ? await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].put(\"\".concat(\"http://localhost:3070\", \"/api/invoices/\").concat(invoice.id), invoiceData) : await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].post(\"\".concat(\"http://localhost:3070\", \"/api/invoices\"), invoiceData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.data.message || (invoice ? \"تم تحديث الفاتورة\" : \"تم إنشاء الفاتورة\"));\n            onSave(response.data.invoice);\n            onClose();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"حدث خطأ\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: [\n                                        invoice ? \"تعديل الفاتورة\" : \"فاتورة جديدة\",\n                                        fromSalesOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-green-600 block\",\n                                            children: [\n                                                \"تحويل من أمر البيع: \",\n                                                fromSalesOrder.orderNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.XMarkIcon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"p-6 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"form-label\",\n                                                    children: \"العميل *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    name: \"customerId\",\n                                                    value: formData.customerId,\n                                                    onChange: handleChange,\n                                                    className: \"form-input\",\n                                                    required: true,\n                                                    disabled: fromSalesOrder,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"اختر العميل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: customer.id,\n                                                                children: customer.name\n                                                            }, customer.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"form-label\",\n                                                    children: \"تاريخ الاستحقاق\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"date\",\n                                                    name: \"dueDate\",\n                                                    value: formData.dueDate,\n                                                    onChange: handleChange,\n                                                    className: \"form-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"العناصر\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: addItem,\n                                                    className: \"btn-primary flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.PlusIcon, {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"إضافة عنصر\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 422,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: formData.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-12 gap-2 items-end p-4 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"المنتج\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: item.productId,\n                                                                            onChange: (e)=>updateItem(index, \"productId\", e.target.value),\n                                                                            className: \"form-input flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"\",\n                                                                                    children: \"اختر المنتج\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                                    lineNumber: 445,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: product.id,\n                                                                                        children: [\n                                                                                            product.nameAr || product.name,\n                                                                                            product.isCustomizable && \" (قابل للتخصيص)\"\n                                                                                        ]\n                                                                                    }, product.id, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                                        lineNumber: 447,\n                                                                                        columnNumber: 29\n                                                                                    }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 440,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        item.isCustomized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>{\n                                                                                const product = products.find((p)=>p.id === item.productId);\n                                                                                if (product) {\n                                                                                    setSelectedProduct(product);\n                                                                                    setCurrentItemIndex(index);\n                                                                                    setShowCustomizer(true);\n                                                                                }\n                                                                            },\n                                                                            className: \"mr-2 px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200\",\n                                                                            title: \"تعديل التخصيص\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.CogIcon, {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                                lineNumber: 467,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                item.customizationDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 text-xs text-gray-600 bg-blue-50 p-2 rounded\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"التخصيصات:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 473,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        item.customizationDetails.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    \"• \",\n                                                                                    detail.optionName,\n                                                                                    \": \",\n                                                                                    detail.selectedName\n                                                                                ]\n                                                                            }, idx, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                                lineNumber: 475,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"الكمية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: item.quantity,\n                                                                    onChange: (e)=>updateItem(index, \"quantity\", e.target.value),\n                                                                    className: \"form-input\",\n                                                                    min: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"السعر\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: item.unitPrice,\n                                                                    onChange: (e)=>updateItem(index, \"unitPrice\", e.target.value),\n                                                                    className: \"form-input\",\n                                                                    step: \"0.01\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"خصم %\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: item.discount,\n                                                                    onChange: (e)=>updateItem(index, \"discount\", e.target.value),\n                                                                    className: \"form-input\",\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"ضريبة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: item.hasTax,\n                                                                            onChange: (e)=>updateItem(index, \"hasTax\", e.target.checked),\n                                                                            className: \"rounded\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 518,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            value: item.taxRate,\n                                                                            onChange: (e)=>updateItem(index, \"taxRate\", e.target.value),\n                                                                            className: \"form-input w-16\",\n                                                                            min: \"0\",\n                                                                            max: \"100\",\n                                                                            disabled: !item.hasTax\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 524,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs\",\n                                                                            children: \"%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"الإجمالي\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900 py-2\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        (item.total || 0).toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>removeItem(index),\n                                                                className: \"text-red-600 hover:text-red-800\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.TrashIcon, {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 434,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"المدفوعات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: addPayment,\n                                                    className: \"btn-secondary flex items-center\",\n                                                    disabled: calculateTotals().remainingAmount <= 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.CreditCardIcon, {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"إضافة دفعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, this),\n                                        formData.payments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: formData.payments.map((payment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: payment.method\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"$\",\n                                                                        parseFloat(payment.amount).toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 579,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                payment.reference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"المرجع: \",\n                                                                        payment.reference\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>removePayment(index),\n                                                            className: \"text-red-600 hover:text-red-800\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.TrashIcon, {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 584,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5 text-red-400\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 600,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-red-800\",\n                                                        children: \"تنبيه خصم المخزون\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-sm text-red-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"عند حفظ الفاتورة، سيتم خصم الكميات من المخزون نهائياً وتسجيل المبيعات. هذا الإجراء لا يمكن التراجع عنه.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 605,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                        lineNumber: 599,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 598,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-3\",\n                                            children: \"خصم عام\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 621,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"نوع الخصم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: formData.generalDiscountType,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        generalDiscountType: e.target.value\n                                                                    })),\n                                                            className: \"form-input\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"PERCENTAGE\",\n                                                                    children: \"نسبة مئوية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 630,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"AMOUNT\",\n                                                                    children: \"مبلغ ثابت\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 631,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 625,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: [\n                                                                \"قيمة الخصم \",\n                                                                formData.generalDiscountType === \"PERCENTAGE\" ? \"(%)\" : \"($)\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 635,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: formData.generalDiscount,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        generalDiscount: e.target.value\n                                                                    })),\n                                                            className: \"form-input\",\n                                                            min: \"0\",\n                                                            step: \"0.01\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"مبلغ الخصم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-semibold text-red-600 py-2\",\n                                                            children: [\n                                                                \"$\",\n                                                                calculateTotals().generalDiscountAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 622,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 620,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"المجموع الفرعي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateTotals().itemsSubtotal.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 659,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-red-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"خصم العناصر:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"-$\",\n                                                            calculateTotals().itemsDiscountAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 663,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-blue-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"الضرائب:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateTotals().itemsTaxAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 667,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between font-medium border-t pt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"إجمالي العناصر:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateTotals().itemsTotal.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 671,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-red-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"الخصم العام:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"-$\",\n                                                            calculateTotals().generalDiscountAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 675,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between font-bold text-lg border-t pt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"الإجمالي النهائي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateTotals().finalTotal.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 679,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-green-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"المدفوع:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateTotals().paidAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 683,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-red-600 font-bold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"المتبقي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateTotals().remainingAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 687,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                        lineNumber: 658,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 657,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"ملاحظات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 696,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            name: \"notes\",\n                                            value: formData.notes,\n                                            onChange: handleChange,\n                                            className: \"form-input\",\n                                            rows: \"3\",\n                                            placeholder: \"ملاحظات إضافية...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 697,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 695,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: invoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: handlePrint,\n                                                className: \"btn-secondary flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.PrinterIcon, {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"طباعة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 711,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: onClose,\n                                                    className: \"btn-secondary\",\n                                                    children: \"إلغاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: loading,\n                                                    className: \"btn-primary\",\n                                                    children: loading ? \"جاري الحفظ...\" : invoice ? \"تحديث\" : \"إنشاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 722,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 708,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                lineNumber: 367,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InvoicePrintView__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    ref: printRef,\n                    invoice: {\n                        ...formData,\n                        invoiceNumber: (invoice === null || invoice === void 0 ? void 0 : invoice.invoiceNumber) || \"جديدة\",\n                        ...calculateTotals()\n                    },\n                    customer: customers.find((c)=>c.id === formData.customerId)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                    lineNumber: 745,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                lineNumber: 744,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCustomizer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showCustomizer,\n                onClose: ()=>setShowCustomizer(false),\n                product: selectedProduct,\n                onSave: handleCustomizedProduct\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                lineNumber: 757,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PaymentManager__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: showPaymentManager,\n                onClose: ()=>setShowPaymentManager(false),\n                totalAmount: calculateTotals().finalTotal,\n                paidAmount: calculateTotals().paidAmount,\n                onPaymentAdd: handlePaymentAdd,\n                existingPayments: formData.payments\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                lineNumber: 765,\n                columnNumber: 7\n            }, this),\n             false && /*#__PURE__*/ 0\n        ]\n    }, void 0, true);\n}\n_s(InvoiceModal, \"DGiUdf9m2Q22iXvk0MtiEmsClKs=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        react_to_print__WEBPACK_IMPORTED_MODULE_4__.useReactToPrint\n    ];\n});\n_c = InvoiceModal;\nvar _c;\n$RefreshReg$(_c, \"InvoiceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/sales/InvoiceModal.js\n"));

/***/ })

});