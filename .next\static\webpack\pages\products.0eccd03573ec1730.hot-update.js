"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/products",{

/***/ "./pages/products.js":
/*!***************************!*\
  !*** ./pages/products.js ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: function() { return /* binding */ __N_SSG; },\n/* harmony export */   \"default\": function() { return /* binding */ Products; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/LoadingSpinner */ \"./components/LoadingSpinner.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Products() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showViewModal, setShowViewModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch products\n    const { data: productsData, isLoading, error, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        \"products\",\n        currentPage,\n        searchTerm,\n        selectedCategory,\n        selectedStatus\n    ], async ()=>{\n        const params = new URLSearchParams({\n            page: currentPage.toString(),\n            limit: \"10\",\n            search: searchTerm,\n            category: selectedCategory,\n            status: selectedStatus\n        });\n        const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(\"http://localhost:3001\", \"/api/products?\").concat(params));\n        return response.data;\n    }, {\n        keepPreviousData: true\n    });\n    const products = (productsData === null || productsData === void 0 ? void 0 : productsData.products) || [];\n    const pagination = (productsData === null || productsData === void 0 ? void 0 : productsData.pagination) || {};\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        setCurrentPage(1);\n        refetch();\n    };\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n    };\n    // Delete product mutation\n    const deleteMutation = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)(async (productId)=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"][\"delete\"](\"\".concat(\"http://localhost:3001\", \"/api/products/\").concat(productId));\n        return response.data;\n    }, {\n        onSuccess: ()=>{\n            queryClient.invalidateQueries([\n                \"products\"\n            ]);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(t(\"products.deleteSuccess\") || \"Product deleted successfully\");\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"Failed to delete product\");\n        }\n    });\n    // Handle delete product\n    const handleDelete = async (product)=>{\n        if (window.confirm(t(\"products.confirmDelete\") || \"Are you sure you want to delete \".concat(product.name, \"?\"))) {\n            deleteMutation.mutate(product.id);\n        }\n    };\n    // Handle view product\n    const handleView = (product)=>{\n        setSelectedProduct(product);\n        setShowViewModal(true);\n    };\n    // Handle edit product\n    const handleEdit = (product)=>{\n        setSelectedProduct(product);\n        setShowEditModal(true);\n    };\n    // Handle create product\n    const handleCreate = ()=>{\n        setSelectedProduct(null);\n        setShowCreateModal(true);\n    };\n    if (isLoading && !products.length) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                lineNumber: 106,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"mt-2 text-sm font-medium text-gray-900\",\n                    children: t(\"common.error\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: \"Failed to load products\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>refetch(),\n                    className: \"mt-4 btn-primary\",\n                    children: \"Try Again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n            lineNumber: 113,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: t(\"products.title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-600\",\n                                children: \"Manage your product catalog\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"btn-primary\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.PlusIcon, {\n                                className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this),\n                            t(\"products.addProduct\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-4 rounded-lg shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col sm:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.MagnifyingGlassIcon, {\n                                        className: \"absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t(\"common.search\"),\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"form-input pl-10 rtl:pl-3 rtl:pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedCategory,\n                            onChange: (e)=>setSelectedCategory(e.target.value),\n                            className: \"form-input\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"all\",\n                                    children: \"All Categories\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"electronics\",\n                                    children: \"Electronics\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"computers\",\n                                    children: \"Computers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"mobile\",\n                                    children: \"Mobile Devices\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedStatus,\n                            onChange: (e)=>setSelectedStatus(e.target.value),\n                            className: \"form-input\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"all\",\n                                    children: \"All Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"active\",\n                                    children: t(\"products.active\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"inactive\",\n                                    children: t(\"products.inactive\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary\",\n                            children: t(\"common.search\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"table\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"products.productCode\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"products.productName\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"products.category\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"products.unitPrice\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"products.currentStock\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"products.status\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"common.actions\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: products.map((product)=>{\n                                        var _product_category, _product_category1;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"font-medium\",\n                                                    children: product.code\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: product.nameAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: (_product_category1 = product.category) === null || _product_category1 === void 0 ? void 0 : _product_category1.nameAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: [\n                                                        \"$\",\n                                                        product.unitPrice.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(product.currentStock <= product.minStock ? \"bg-red-100 text-red-800\" : product.currentStock <= product.minStock * 2 ? \"bg-yellow-100 text-yellow-800\" : \"bg-green-100 text-green-800\"),\n                                                        children: [\n                                                            product.currentStock,\n                                                            \" \",\n                                                            product.unit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"badge \".concat(product.isActive ? \"badge-success\" : \"badge-secondary\"),\n                                                        children: product.isActive ? t(\"products.active\") : t(\"products.inactive\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 text-gray-400 hover:text-blue-600\",\n                                                                title: t(\"common.view\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.EyeIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 text-gray-400 hover:text-green-600\",\n                                                                title: t(\"common.edit\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.PencilIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 text-gray-400 hover:text-red-600\",\n                                                                title: t(\"common.delete\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.TrashIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, product.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this),\n                    pagination.pages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-700\",\n                                    children: [\n                                        \"Showing \",\n                                        (pagination.page - 1) * pagination.limit + 1,\n                                        \" to\",\n                                        \" \",\n                                        Math.min(pagination.page * pagination.limit, pagination.total),\n                                        \" of\",\n                                        \" \",\n                                        pagination.total,\n                                        \" results\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handlePageChange(pagination.page - 1),\n                                            disabled: pagination.page <= 1,\n                                            className: \"btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Previous\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        Array.from({\n                                            length: Math.min(5, pagination.pages)\n                                        }, (_, i)=>{\n                                            const page = i + Math.max(1, pagination.page - 2);\n                                            if (page > pagination.pages) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePageChange(page),\n                                                className: \"btn-sm \".concat(page === pagination.page ? \"btn-primary\" : \"btn-secondary\"),\n                                                children: page\n                                            }, page, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handlePageChange(pagination.page + 1),\n                                            disabled: pagination.page >= pagination.pages,\n                                            className: \"btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Next\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this),\n                    products.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"mt-2 text-sm font-medium text-gray-900\",\n                                children: \"No products found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-500\",\n                                children: \"Get started by creating your first product.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.PlusIcon, {\n                                            className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, this),\n                                        t(\"products.addProduct\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n_s(Products, \"FI+UvlqzrDOuwYaDp5bu17nRwRM=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation\n    ];\n});\n_c = Products;\nvar _c;\n$RefreshReg$(_c, \"Products\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/products.js\n"));

/***/ })

});