@echo off
echo 🚀 Starting Business Management System...
echo.

echo 📡 Starting Backend Server on port 3070...
start "Backend Server" cmd /k "PORT=3070 node server/simple-server.js"

echo ⏳ Waiting 3 seconds for backend to start...
timeout /t 3 /nobreak >nul

echo 🌐 Starting Frontend Server on port 3071...
start "Frontend Server" cmd /k "npm run dev -- -p 3071"

echo.
echo ✅ Both servers are starting...
echo 📱 Frontend: http://localhost:3071
echo 🔧 Backend: http://localhost:3070
echo.
echo 💡 Close the command windows to stop the servers
pause
