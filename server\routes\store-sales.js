const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Generate unique numbers
const generateInvoiceNumber = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const timestamp = Date.now().toString().slice(-6);
  return `INV-${year}${month}-${timestamp}`;
};

const generateOrderNumber = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const timestamp = Date.now().toString().slice(-6);
  return `ORD-${year}${month}-${timestamp}`;
};

const generateQuoteNumber = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const timestamp = Date.now().toString().slice(-6);
  return `QUO-${year}${month}-${timestamp}`;
};

// Direct Sale - Complete transaction immediately
router.post('/direct-sale', authenticateToken, async (req, res) => {
  try {
    const {
      customerId,
      items,
      payments,
      notes,
      generalDiscount = 0,
      generalDiscountType = 'PERCENTAGE'
    } = req.body;

    const userId = req.user.id;
    const branchId = req.user.branchId;

    // Validate required fields
    if (!items || items.length === 0) {
      return res.status(400).json({ error: 'يجب إضافة منتجات للبيع' });
    }

    if (!payments || payments.length === 0) {
      return res.status(400).json({ error: 'يجب إضافة طريقة دفع' });
    }

    // Calculate totals
    let subtotal = 0;
    let totalTax = 0;
    let totalDiscount = 0;

    items.forEach(item => {
      const itemSubtotal = parseFloat(item.quantity) * parseFloat(item.unitPrice);
      const itemDiscount = itemSubtotal * (parseFloat(item.discount || 0) / 100);
      const afterDiscount = itemSubtotal - itemDiscount;
      const itemTax = item.hasTax ? (afterDiscount * (parseFloat(item.taxRate || 0) / 100)) : 0;
      
      subtotal += itemSubtotal;
      totalDiscount += itemDiscount;
      totalTax += itemTax;
    });

    // Apply general discount
    let generalDiscountAmount = 0;
    if (generalDiscount > 0) {
      if (generalDiscountType === 'PERCENTAGE') {
        generalDiscountAmount = (subtotal - totalDiscount) * (parseFloat(generalDiscount) / 100);
      } else {
        generalDiscountAmount = parseFloat(generalDiscount);
      }
    }

    const finalTotal = subtotal - totalDiscount - generalDiscountAmount + totalTax;
    const totalPaid = payments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);

    // For direct sales, must be fully paid
    if (Math.abs(finalTotal - totalPaid) > 0.01) {
      return res.status(400).json({ error: 'يجب دفع المبلغ كاملاً للبيع المباشر' });
    }

    // Start transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create invoice
      const invoice = await tx.invoice.create({
        data: {
          invoiceNumber: generateInvoiceNumber(),
          customerId: customerId || null,
          userId,
          branchId,
          invoiceDate: new Date(),
          status: 'COMPLETED',
          subtotal,
          taxAmount: totalTax,
          discount: totalDiscount + generalDiscountAmount,
          total: finalTotal,
          paidAmount: totalPaid,
          remainingAmount: 0,
          paymentStatus: 'PAID',
          notes: notes || ''
        }
      });

      // Create invoice items
      for (const item of items) {
        await tx.invoiceItem.create({
          data: {
            invoiceId: invoice.id,
            productId: item.productId,
            quantity: parseFloat(item.quantity),
            unitPrice: parseFloat(item.unitPrice),
            discount: parseFloat(item.discount || 0),
            taxRate: parseFloat(item.taxRate || 0),
            hasTax: item.hasTax || false,
            total: parseFloat(item.total),
            customizations: item.customization ? JSON.stringify(item.customization) : null
          }
        });

        // Update inventory
        const product = await tx.product.findUnique({
          where: { id: item.productId }
        });

        if (product && product.hasInventory) {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              currentStock: {
                decrement: parseInt(item.quantity)
              }
            }
          });

          // Update branch inventory
          const branchInventory = await tx.branchInventory.findFirst({
            where: {
              productId: item.productId,
              branchId
            }
          });

          if (branchInventory) {
            await tx.branchInventory.update({
              where: { id: branchInventory.id },
              data: {
                quantity: {
                  decrement: parseInt(item.quantity)
                }
              }
            });
          }
        }
      }

      // Create payments
      for (const payment of payments) {
        await tx.invoicePayment.create({
          data: {
            invoiceId: invoice.id,
            method: payment.method,
            amount: parseFloat(payment.amount),
            reference: payment.reference || null,
            installmentPlan: payment.installmentPlan || null,
            notes: payment.notes || null,
            paymentDate: new Date()
          }
        });

        // Update cash box for cash payments
        if (payment.method === 'CASH') {
          const cashBox = await tx.cashBox.findFirst({
            where: { branchId }
          });

          if (cashBox) {
            await tx.cashBox.update({
              where: { id: cashBox.id },
              data: {
                currentBalance: {
                  increment: parseFloat(payment.amount)
                }
              }
            });
          }
        }
      }

      return invoice;
    });

    res.json({
      success: true,
      invoice: result,
      message: 'تم إتمام البيع بنجاح'
    });

  } catch (error) {
    console.error('Direct sale error:', error);
    res.status(500).json({ error: 'خطأ في إتمام البيع' });
  }
});

// Custom Order - For products that need assembly/customization
router.post('/custom-order', authenticateToken, async (req, res) => {
  try {
    const {
      customerId,
      items,
      payments = [],
      notes,
      expectedDate,
      generalDiscount = 0,
      generalDiscountType = 'PERCENTAGE'
    } = req.body;

    const userId = req.user.id;
    const branchId = req.user.branchId;

    // Validate required fields
    if (!customerId) {
      return res.status(400).json({ error: 'العميل مطلوب للطلبات المخصصة' });
    }

    if (!items || items.length === 0) {
      return res.status(400).json({ error: 'يجب إضافة منتجات للطلب' });
    }

    // Calculate totals
    let subtotal = 0;
    let totalTax = 0;
    let totalDiscount = 0;

    items.forEach(item => {
      const itemSubtotal = parseFloat(item.quantity) * parseFloat(item.unitPrice);
      const itemDiscount = itemSubtotal * (parseFloat(item.discount || 0) / 100);
      const afterDiscount = itemSubtotal - itemDiscount;
      const itemTax = item.hasTax ? (afterDiscount * (parseFloat(item.taxRate || 0) / 100)) : 0;
      
      subtotal += itemSubtotal;
      totalDiscount += itemDiscount;
      totalTax += itemTax;
    });

    // Apply general discount
    let generalDiscountAmount = 0;
    if (generalDiscount > 0) {
      if (generalDiscountType === 'PERCENTAGE') {
        generalDiscountAmount = (subtotal - totalDiscount) * (parseFloat(generalDiscount) / 100);
      } else {
        generalDiscountAmount = parseFloat(generalDiscount);
      }
    }

    const finalTotal = subtotal - totalDiscount - generalDiscountAmount + totalTax;
    const totalPaid = payments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
    const remainingAmount = finalTotal - totalPaid;

    // Start transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create sales order
      const salesOrder = await tx.salesOrder.create({
        data: {
          orderNumber: generateOrderNumber(),
          customerId,
          userId,
          branchId,
          orderDate: new Date(),
          dueDate: expectedDate ? new Date(expectedDate) : null,
          status: 'CONFIRMED',
          subtotal,
          taxAmount: totalTax,
          discount: totalDiscount + generalDiscountAmount,
          total: finalTotal,
          paidAmount: totalPaid,
          remainingAmount,
          paymentStatus: remainingAmount > 0.01 ? 'PARTIAL' : 'PAID',
          notes: notes || ''
        }
      });

      // Create order items
      for (const item of items) {
        await tx.salesOrderItem.create({
          data: {
            salesOrderId: salesOrder.id,
            productId: item.productId,
            quantity: parseFloat(item.quantity),
            unitPrice: parseFloat(item.unitPrice),
            discount: parseFloat(item.discount || 0),
            taxRate: parseFloat(item.taxRate || 0),
            hasTax: item.hasTax || false,
            total: parseFloat(item.total),
            customizations: item.customization ? JSON.stringify(item.customization) : null
          }
        });

        // Reserve inventory for customizable products
        const product = await tx.product.findUnique({
          where: { id: item.productId }
        });

        if (product && product.hasInventory && product.productType !== 'CUSTOMIZABLE') {
          // Reserve stock for regular products
          await tx.product.update({
            where: { id: item.productId },
            data: {
              currentStock: {
                decrement: parseInt(item.quantity)
              }
            }
          });
        }
      }

      // Create payments if any
      for (const payment of payments) {
        await tx.payment.create({
          data: {
            salesOrderId: salesOrder.id,
            method: payment.method,
            amount: parseFloat(payment.amount),
            reference: payment.reference || null,
            installmentPlan: payment.installmentPlan || null,
            notes: payment.notes || null,
            paymentDate: new Date()
          }
        });

        // Update cash box for cash payments
        if (payment.method === 'CASH') {
          const cashBox = await tx.cashBox.findFirst({
            where: { branchId }
          });

          if (cashBox) {
            await tx.cashBox.update({
              where: { id: cashBox.id },
              data: {
                currentBalance: {
                  increment: parseFloat(payment.amount)
                }
              }
            });
          }
        }
      }

      return salesOrder;
    });

    res.json({
      success: true,
      order: result,
      message: 'تم إنشاء الطلب المخصص بنجاح'
    });

  } catch (error) {
    console.error('Custom order error:', error);
    res.status(500).json({ error: 'خطأ في إنشاء الطلب المخصص' });
  }
});

// Quick Quote - For price inquiries
router.post('/quick-quote', authenticateToken, async (req, res) => {
  try {
    const {
      customerId,
      items,
      notes,
      validUntil,
      generalDiscount = 0,
      generalDiscountType = 'PERCENTAGE'
    } = req.body;

    const userId = req.user.id;
    const branchId = req.user.branchId;

    // Validate required fields
    if (!items || items.length === 0) {
      return res.status(400).json({ error: 'يجب إضافة منتجات لعرض السعر' });
    }

    // Calculate totals
    let subtotal = 0;
    let totalTax = 0;
    let totalDiscount = 0;

    items.forEach(item => {
      const itemSubtotal = parseFloat(item.quantity) * parseFloat(item.unitPrice);
      const itemDiscount = itemSubtotal * (parseFloat(item.discount || 0) / 100);
      const afterDiscount = itemSubtotal - itemDiscount;
      const itemTax = item.hasTax ? (afterDiscount * (parseFloat(item.taxRate || 0) / 100)) : 0;
      
      subtotal += itemSubtotal;
      totalDiscount += itemDiscount;
      totalTax += itemTax;
    });

    // Apply general discount
    let generalDiscountAmount = 0;
    if (generalDiscount > 0) {
      if (generalDiscountType === 'PERCENTAGE') {
        generalDiscountAmount = (subtotal - totalDiscount) * (parseFloat(generalDiscount) / 100);
      } else {
        generalDiscountAmount = parseFloat(generalDiscount);
      }
    }

    const finalTotal = subtotal - totalDiscount - generalDiscountAmount + totalTax;

    // Create quote
    const quote = await prisma.quote.create({
      data: {
        quoteNumber: generateQuoteNumber(),
        customerId: customerId || null,
        userId,
        branchId,
        quoteDate: new Date(),
        validUntil: validUntil ? new Date(validUntil) : null,
        status: 'DRAFT',
        subtotal,
        taxAmount: totalTax,
        discount: totalDiscount + generalDiscountAmount,
        total: finalTotal,
        notes: notes || ''
      }
    });

    // Create quote items
    for (const item of items) {
      await prisma.quoteItem.create({
        data: {
          quoteId: quote.id,
          productId: item.productId,
          quantity: parseFloat(item.quantity),
          unitPrice: parseFloat(item.unitPrice),
          discount: parseFloat(item.discount || 0),
          taxRate: parseFloat(item.taxRate || 0),
          hasTax: item.hasTax || false,
          total: parseFloat(item.total),
          customizations: item.customization ? JSON.stringify(item.customization) : null
        }
      });
    }

    res.json({
      success: true,
      quote,
      message: 'تم إنشاء عرض السعر بنجاح'
    });

  } catch (error) {
    console.error('Quick quote error:', error);
    res.status(500).json({ error: 'خطأ في إنشاء عرض السعر' });
  }
});

// Get daily sales summary
router.get('/daily-summary', authenticateToken, async (req, res) => {
  try {
    const branchId = req.user.branchId;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const [invoices, orders, quotes, cashBox] = await Promise.all([
      // Today's invoices
      prisma.invoice.findMany({
        where: {
          branchId,
          invoiceDate: {
            gte: today,
            lt: tomorrow
          }
        },
        include: {
          customer: true,
          payments: true
        }
      }),
      
      // Today's orders
      prisma.salesOrder.findMany({
        where: {
          branchId,
          orderDate: {
            gte: today,
            lt: tomorrow
          }
        },
        include: {
          customer: true
        }
      }),
      
      // Today's quotes
      prisma.quote.findMany({
        where: {
          branchId,
          quoteDate: {
            gte: today,
            lt: tomorrow
          }
        },
        include: {
          customer: true
        }
      }),
      
      // Cash box
      prisma.cashBox.findFirst({
        where: { branchId }
      })
    ]);

    const summary = {
      invoices: {
        count: invoices.length,
        total: invoices.reduce((sum, inv) => sum + parseFloat(inv.total), 0),
        paid: invoices.reduce((sum, inv) => sum + parseFloat(inv.paidAmount), 0)
      },
      orders: {
        count: orders.length,
        total: orders.reduce((sum, ord) => sum + parseFloat(ord.total), 0),
        paid: orders.reduce((sum, ord) => sum + parseFloat(ord.paidAmount), 0)
      },
      quotes: {
        count: quotes.length,
        total: quotes.reduce((sum, quo) => sum + parseFloat(quo.total), 0)
      },
      cashBox: {
        balance: cashBox ? parseFloat(cashBox.currentBalance) : 0
      }
    };

    res.json(summary);

  } catch (error) {
    console.error('Daily summary error:', error);
    res.status(500).json({ error: 'خطأ في جلب ملخص اليوم' });
  }
});

module.exports = router;
