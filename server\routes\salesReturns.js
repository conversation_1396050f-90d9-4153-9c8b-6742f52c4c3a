const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');

const DATA_DIR = path.join(__dirname, '../data');
const SALES_RETURNS_FILE = path.join(DATA_DIR, 'salesReturns.json');
const INVOICES_FILE = path.join(DATA_DIR, 'invoices.json');
const INVENTORY_FILE = path.join(DATA_DIR, 'inventory.json');

// Helper functions
const readSalesReturns = async () => {
  try {
    const data = await fs.readFile(SALES_RETURNS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
};

const writeSalesReturns = async (salesReturns) => {
  await fs.writeFile(SALES_RETURNS_FILE, JSON.stringify(salesReturns, null, 2));
};

const readInvoices = async () => {
  try {
    const data = await fs.readFile(INVOICES_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
};

const readInventory = async () => {
  try {
    const data = await fs.readFile(INVENTORY_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
};

const writeInventory = async (inventory) => {
  await fs.writeFile(INVENTORY_FILE, JSON.stringify(inventory, null, 2));
};

const generateReturnNumber = (salesReturns) => {
  const year = new Date().getFullYear();
  const month = String(new Date().getMonth() + 1).padStart(2, '0');
  const existingReturns = salesReturns.filter(r => 
    r.returnNumber && r.returnNumber.startsWith(`RET-${year}${month}`)
  );
  const nextNumber = existingReturns.length + 1;
  return `RET-${year}${month}-${String(nextNumber).padStart(4, '0')}`;
};

// GET /api/sales-returns - Get all sales returns
router.get('/', async (req, res) => {
  try {
    const salesReturns = await readSalesReturns();
    res.json({ salesReturns });
  } catch (error) {
    console.error('Error fetching sales returns:', error);
    res.status(500).json({ error: 'خطأ في جلب المرتجعات' });
  }
});

// GET /api/sales-returns/:id - Get specific sales return
router.get('/:id', async (req, res) => {
  try {
    const salesReturns = await readSalesReturns();
    const salesReturn = salesReturns.find(r => r.id === req.params.id);
    
    if (!salesReturn) {
      return res.status(404).json({ error: 'المرتجع غير موجود' });
    }
    
    res.json({ salesReturn });
  } catch (error) {
    console.error('Error fetching sales return:', error);
    res.status(500).json({ error: 'خطأ في جلب المرتجع' });
  }
});

// POST /api/sales-returns - Create new sales return
router.post('/', async (req, res) => {
  try {
    const salesReturns = await readSalesReturns();
    const invoices = await readInvoices();
    
    // Validate invoice exists
    const invoice = invoices.find(inv => inv.id === req.body.invoiceId);
    if (!invoice) {
      return res.status(400).json({ error: 'الفاتورة غير موجودة' });
    }
    
    // Generate return number
    const returnNumber = generateReturnNumber(salesReturns);
    
    const newSalesReturn = {
      id: Date.now().toString(),
      returnNumber,
      customerId: req.body.customerId,
      invoiceId: req.body.invoiceId,
      invoiceNumber: req.body.invoiceNumber,
      customerName: req.body.customerName,
      returnDate: req.body.returnDate,
      reason: req.body.reason,
      notes: req.body.notes || '',
      items: req.body.items || [],
      refundMethod: req.body.refundMethod || 'CASH',
      refundAmount: parseFloat(req.body.refundAmount) || 0,
      status: 'PENDING',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    salesReturns.push(newSalesReturn);
    await writeSalesReturns(salesReturns);
    
    // Update inventory if return is approved
    if (req.body.status === 'APPROVED') {
      await updateInventoryForReturn(newSalesReturn.items, 'ADD');
    }
    
    res.status(201).json({ 
      message: 'تم إنشاء المرتجع بنجاح',
      salesReturn: newSalesReturn 
    });
  } catch (error) {
    console.error('Error creating sales return:', error);
    res.status(500).json({ error: 'خطأ في إنشاء المرتجع' });
  }
});

// PUT /api/sales-returns/:id - Update sales return
router.put('/:id', async (req, res) => {
  try {
    const salesReturns = await readSalesReturns();
    const index = salesReturns.findIndex(r => r.id === req.params.id);
    
    if (index === -1) {
      return res.status(404).json({ error: 'المرتجع غير موجود' });
    }
    
    const oldReturn = salesReturns[index];
    const updatedReturn = {
      ...oldReturn,
      ...req.body,
      updatedAt: new Date().toISOString()
    };
    
    // Handle inventory updates based on status change
    if (oldReturn.status !== updatedReturn.status) {
      if (updatedReturn.status === 'APPROVED' && oldReturn.status !== 'APPROVED') {
        // Add items back to inventory
        await updateInventoryForReturn(updatedReturn.items, 'ADD');
      } else if (oldReturn.status === 'APPROVED' && updatedReturn.status !== 'APPROVED') {
        // Remove items from inventory
        await updateInventoryForReturn(updatedReturn.items, 'SUBTRACT');
      }
    }
    
    salesReturns[index] = updatedReturn;
    await writeSalesReturns(salesReturns);
    
    res.json({ 
      message: 'تم تحديث المرتجع بنجاح',
      salesReturn: updatedReturn 
    });
  } catch (error) {
    console.error('Error updating sales return:', error);
    res.status(500).json({ error: 'خطأ في تحديث المرتجع' });
  }
});

// DELETE /api/sales-returns/:id - Delete sales return
router.delete('/:id', async (req, res) => {
  try {
    const salesReturns = await readSalesReturns();
    const index = salesReturns.findIndex(r => r.id === req.params.id);
    
    if (index === -1) {
      return res.status(404).json({ error: 'المرتجع غير موجود' });
    }
    
    const deletedReturn = salesReturns[index];
    
    // If return was approved, subtract items from inventory
    if (deletedReturn.status === 'APPROVED') {
      await updateInventoryForReturn(deletedReturn.items, 'SUBTRACT');
    }
    
    salesReturns.splice(index, 1);
    await writeSalesReturns(salesReturns);
    
    res.json({ message: 'تم حذف المرتجع بنجاح' });
  } catch (error) {
    console.error('Error deleting sales return:', error);
    res.status(500).json({ error: 'خطأ في حذف المرتجع' });
  }
});

// POST /api/sales-returns/:id/approve - Approve sales return
router.post('/:id/approve', async (req, res) => {
  try {
    const salesReturns = await readSalesReturns();
    const index = salesReturns.findIndex(r => r.id === req.params.id);
    
    if (index === -1) {
      return res.status(404).json({ error: 'المرتجع غير موجود' });
    }
    
    const salesReturn = salesReturns[index];
    
    if (salesReturn.status === 'APPROVED') {
      return res.status(400).json({ error: 'المرتجع موافق عليه بالفعل' });
    }
    
    // Update inventory
    await updateInventoryForReturn(salesReturn.items, 'ADD');
    
    // Update return status
    salesReturns[index] = {
      ...salesReturn,
      status: 'APPROVED',
      approvedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    await writeSalesReturns(salesReturns);
    
    res.json({ 
      message: 'تم الموافقة على المرتجع وإضافة العناصر للمخزون',
      salesReturn: salesReturns[index]
    });
  } catch (error) {
    console.error('Error approving sales return:', error);
    res.status(500).json({ error: 'خطأ في الموافقة على المرتجع' });
  }
});

// Helper function to update inventory
const updateInventoryForReturn = async (returnItems, operation) => {
  try {
    const inventory = await readInventory();
    
    for (const item of returnItems) {
      const inventoryItem = inventory.find(inv => inv.id === item.productId);
      if (inventoryItem) {
        const quantity = parseFloat(item.returnQuantity) || 0;
        
        if (operation === 'ADD') {
          inventoryItem.quantity = (parseFloat(inventoryItem.quantity) || 0) + quantity;
        } else if (operation === 'SUBTRACT') {
          inventoryItem.quantity = Math.max(0, (parseFloat(inventoryItem.quantity) || 0) - quantity);
        }
        
        inventoryItem.updatedAt = new Date().toISOString();
      }
    }
    
    await writeInventory(inventory);
  } catch (error) {
    console.error('Error updating inventory for return:', error);
    throw error;
  }
};

module.exports = router;
