"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "./pages/sales/index.js":
/*!******************************!*\
  !*** ./pages/sales/index.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SalesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _components_sales_QuoteModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/sales/QuoteModal */ \"./components/sales/QuoteModal.js\");\n/* harmony import */ var _components_sales_SalesOrderModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/sales/SalesOrderModal */ \"./components/sales/SalesOrderModal.js\");\n/* harmony import */ var _components_sales_InvoiceModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../components/sales/InvoiceModal */ \"./components/sales/InvoiceModal.js\");\n/* harmony import */ var _components_sales_SalesReturnModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../components/sales/SalesReturnModal */ \"./components/sales/SalesReturnModal.js\");\n/* harmony import */ var _components_sales_ProductCustomizer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../components/sales/ProductCustomizer */ \"./components/sales/ProductCustomizer.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUturnLeftIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,ReceiptPercentIcon,ShoppingCartIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightIcon,ArrowUturnLeftIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,ReceiptPercentIcon,ShoppingCartIcon,XCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SalesPage() {\n    _s();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"quotes\");\n    // Modal states\n    const [quoteModal, setQuoteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        quote: null\n    });\n    const [salesOrderModal, setSalesOrderModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        salesOrder: null,\n        fromQuote: null\n    });\n    const [invoiceModal, setInvoiceModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        invoice: null,\n        fromSalesOrder: null\n    });\n    const [returnModal, setReturnModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        salesReturn: null\n    });\n    // Data states\n    const [quotes, setQuotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [salesOrders, setSalesOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [salesReturns, setSalesReturns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // التحقق من تسجيل الدخول\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && !user) {\n            router.push(\"/login\");\n        }\n    }, [\n        user,\n        isLoading,\n        router\n    ]);\n    // عرض شاشة التحميل أثناء التحقق من المصادقة\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 57,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this);\n    }\n    // إذا لم يكن المستخدم مسجل دخول، لا تعرض شيء (سيتم التوجيه)\n    if (!user) {\n        return null;\n    }\n    // Load data when tab changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            loadData();\n        }\n    }, [\n        activeTab,\n        user\n    ]);\n    const loadData = async ()=>{\n        setLoading(true);\n        try {\n            if (activeTab === \"quotes\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/quotes\"));\n                setQuotes(response.data.quotes || []);\n            } else if (activeTab === \"orders\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/sales-orders\"));\n                setSalesOrders(response.data.salesOrders || []);\n            } else if (activeTab === \"invoices\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/invoices\"));\n                setInvoices(response.data.invoices || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"خطأ في تحميل البيانات\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Modal handlers\n    const handleQuoteCreate = ()=>{\n        setQuoteModal({\n            isOpen: true,\n            quote: null\n        });\n    };\n    const handleQuoteEdit = (quote)=>{\n        setQuoteModal({\n            isOpen: true,\n            quote\n        });\n    };\n    const handleQuoteConvert = (quote)=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder: null,\n            fromQuote: quote\n        });\n    };\n    const handleSalesOrderCreate = ()=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder: null,\n            fromQuote: null\n        });\n    };\n    const handleSalesOrderEdit = (salesOrder)=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder,\n            fromQuote: null\n        });\n    };\n    const handleSalesOrderConvert = (salesOrder)=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice: null,\n            fromSalesOrder: salesOrder\n        });\n    };\n    const handleInvoiceCreate = ()=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice: null,\n            fromSalesOrder: null\n        });\n    };\n    const handleInvoiceEdit = (invoice)=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice,\n            fromSalesOrder: null\n        });\n    };\n    // Save handlers\n    const handleQuoteSave = (quote)=>{\n        loadData();\n    };\n    const handleSalesOrderSave = (salesOrder)=>{\n        loadData();\n    };\n    const handleInvoiceSave = (invoice)=>{\n        loadData();\n    };\n    const tabs = [\n        {\n            id: \"quotes\",\n            name: \"عروض الأسعار\",\n            nameEn: \"Quotes\",\n            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.DocumentTextIcon,\n            color: \"blue\",\n            description: \"لا تؤثر على المخزون - صالحة لمدة محددة\"\n        },\n        {\n            id: \"orders\",\n            name: \"أوامر البيع\",\n            nameEn: \"Sales Orders\",\n            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ShoppingCartIcon,\n            color: \"orange\",\n            description: \"تحجز من المخزون - لها مدة صلاحية\"\n        },\n        {\n            id: \"invoices\",\n            name: \"الفواتير\",\n            nameEn: \"Invoices\",\n            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ReceiptPercentIcon,\n            color: \"green\",\n            description: \"تخصم من المخزون نهائياً - تحسب في المبيعات\"\n        },\n        {\n            id: \"returns\",\n            name: \"مرتجع المبيعات\",\n            nameEn: \"Sales Returns\",\n            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowUturnLeftIcon,\n            color: \"red\",\n            description: \"إرجاع المنتجات وإضافة للمخزون\"\n        }\n    ];\n    const getStatusColor = (status)=>{\n        const colors = {\n            \"DRAFT\": \"gray\",\n            \"PENDING\": \"yellow\",\n            \"APPROVED\": \"green\",\n            \"REJECTED\": \"red\",\n            \"EXPIRED\": \"red\",\n            \"CONFIRMED\": \"blue\",\n            \"SHIPPED\": \"purple\",\n            \"DELIVERED\": \"green\",\n            \"CANCELLED\": \"red\",\n            \"PAID\": \"green\",\n            \"PARTIALLY_PAID\": \"yellow\",\n            \"OVERDUE\": \"red\"\n        };\n        return colors[status] || \"gray\";\n    };\n    const getStatusText = (status)=>{\n        const statusTexts = {\n            \"DRAFT\": \"مسودة\",\n            \"PENDING\": \"في الانتظار\",\n            \"APPROVED\": \"موافق عليه\",\n            \"REJECTED\": \"مرفوض\",\n            \"EXPIRED\": \"منتهي الصلاحية\",\n            \"CONFIRMED\": \"مؤكد\",\n            \"SHIPPED\": \"تم الشحن\",\n            \"DELIVERED\": \"تم التسليم\",\n            \"CANCELLED\": \"ملغي\",\n            \"PAID\": \"مدفوع\",\n            \"PARTIALLY_PAID\": \"مدفوع جزئياً\",\n            \"OVERDUE\": \"متأخر\"\n        };\n        return statusTexts[status] || status;\n    };\n    const renderQuotes = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"عروض الأسعار\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 218,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleQuoteCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 223,\n                                    columnNumber: 11\n                                }, this),\n                                \"عرض سعر جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 219,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 217,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم العرض\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"صالح حتى\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: quotes.map((quote)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: quote.quoteNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: quote.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(quote.status), \"-100 text-\").concat(getStatusColor(quote.status), \"-800\"),\n                                                    children: getStatusText(quote.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    quote.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: quote.validUntil\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuoteEdit(quote),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    quote.status === \"APPROVED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuoteConvert(quote),\n                                                        className: \"text-green-600 hover:text-green-900 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                                className: \"h-5 w-5 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"تحويل لأمر\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, quote.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 228,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 216,\n            columnNumber: 5\n        }, this);\n    const renderOrders = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"أوامر البيع\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 300,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSalesOrderCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 305,\n                                    columnNumber: 11\n                                }, this),\n                                \"أمر بيع جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 301,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 299,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم الأمر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: salesOrders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: order.orderNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: order.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(order.status), \"-100 text-\").concat(getStatusColor(order.status), \"-800\"),\n                                                    children: getStatusText(order.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    order.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: order.reservedStock ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ClockIcon, {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"محجوز\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                                    children: \"غير محجوز\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSalesOrderEdit(order),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    order.status === \"CONFIRMED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSalesOrderConvert(order),\n                                                        className: \"text-green-600 hover:text-green-900 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                                className: \"h-5 w-5 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"تحويل لفاتورة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, order.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 310,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 298,\n            columnNumber: 5\n        }, this);\n    const renderInvoices = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"الفواتير\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 391,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleInvoiceCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 396,\n                                    columnNumber: 11\n                                }, this),\n                                \"فاتورة جديدة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 392,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 390,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم الفاتورة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 411,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ الإجمالي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 414,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المدفوع\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: invoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: invoice.invoiceNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: invoice.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(invoice.status), \"-100 text-\").concat(getStatusColor(invoice.status), \"-800\"),\n                                                    children: getStatusText(invoice.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    invoice.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    invoice.paidAmount.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleInvoiceEdit(invoice),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-green-600 hover:text-green-900\",\n                                                        children: \"طباعة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, invoice.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 401,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 389,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: \"إدارة المبيعات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"نظام المبيعات بثلاث مراحل: عروض الأسعار → أوامر البيع → الفواتير\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 472,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-8 mb-6\",\n                                children: tabs.map((tab, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center p-4 rounded-lg border-2 \".concat(activeTab === tab.id ? \"border-\".concat(tab.color, \"-500 bg-\").concat(tab.color, \"-50\") : \"border-gray-200 bg-gray-50\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                        className: \"h-8 w-8 mb-2 \".concat(activeTab === tab.id ? \"text-\".concat(tab.color, \"-600\") : \"text-gray-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium \".concat(activeTab === tab.id ? \"text-\".concat(tab.color, \"-900\") : \"text-gray-600\"),\n                                                        children: tab.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 text-center mt-1\",\n                                                        children: tab.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, this),\n                                            index < tabs.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                className: \"h-6 w-6 text-gray-400 mx-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 498,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 477,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8 px-6\",\n                                    children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-\".concat(tab.color, \"-500 text-\").concat(tab.color, \"-600\") : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                    className: \"h-5 w-5 inline-block ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 19\n                                                }, this),\n                                                tab.name\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 510,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 507,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    activeTab === \"quotes\" && renderQuotes(),\n                                    activeTab === \"orders\" && renderOrders(),\n                                    activeTab === \"invoices\" && renderInvoices()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 526,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 506,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 466,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_QuoteModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: quoteModal.isOpen,\n                onClose: ()=>setQuoteModal({\n                        isOpen: false,\n                        quote: null\n                    }),\n                onSave: handleQuoteSave,\n                quote: quoteModal.quote\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 535,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_SalesOrderModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: salesOrderModal.isOpen,\n                onClose: ()=>setSalesOrderModal({\n                        isOpen: false,\n                        salesOrder: null,\n                        fromQuote: null\n                    }),\n                onSave: handleSalesOrderSave,\n                salesOrder: salesOrderModal.salesOrder,\n                fromQuote: salesOrderModal.fromQuote\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 542,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_InvoiceModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: invoiceModal.isOpen,\n                onClose: ()=>setInvoiceModal({\n                        isOpen: false,\n                        invoice: null,\n                        fromSalesOrder: null\n                    }),\n                onSave: handleInvoiceSave,\n                invoice: invoiceModal.invoice,\n                fromSalesOrder: invoiceModal.fromSalesOrder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 550,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n        lineNumber: 465,\n        columnNumber: 5\n    }, this);\n}\n_s(SalesPage, \"RRWhhBSYkrjK1fRa3K47H2b2dW4=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = SalesPage;\nvar _c;\n$RefreshReg$(_c, \"SalesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/sales/index.js\n"));

/***/ })

});