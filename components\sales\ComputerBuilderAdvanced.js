import { useState, useEffect } from 'react';
import { XMarkIcon, ComputerDesktopIcon, DevicePhoneMobileIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export default function ComputerBuilderAdvanced({ isOpen, onClose, onSave, buildType = 'DESKTOP' }) {
  const [selectedComponents, setSelectedComponents] = useState({
    motherboard: null,
    cpu: null,
    ram: [],
    storage: [],
    gpu: null,
    psu: null,
    case: null,
    cooling: null
  });

  const [totalPrice, setTotalPrice] = useState(0);
  const [compatibility, setCompatibility] = useState({
    valid: true,
    warnings: [],
    errors: []
  });

  // Component categories for desktop computers
  const desktopComponents = {
    motherboard: {
      name: 'اللوحة الأم',
      required: true,
      options: [
        { id: 'mb1', name: 'ASUS PRIME B450M-A', socket: 'AM4', ramType: 'DDR4', maxRam: 64, price: 85, stock: 5 },
        { id: 'mb2', name: 'MSI B550M PRO-B', socket: 'AM4', ramType: 'DDR4', maxRam: 128, price: 120, stock: 3 },
        { id: 'mb3', name: 'ASUS ROG STRIX B550-F', socket: 'AM4', ramType: 'DDR4', maxRam: 128, price: 180, stock: 2 },
        { id: 'mb4', name: 'MSI Z690-A PRO', socket: 'LGA1700', ramType: 'DDR4', maxRam: 128, price: 200, stock: 4 },
        { id: 'mb5', name: 'ASUS PRIME Z690-P', socket: 'LGA1700', ramType: 'DDR5', maxRam: 128, price: 250, stock: 2 }
      ]
    },
    cpu: {
      name: 'المعالج',
      required: true,
      options: [
        { id: 'cpu1', name: 'AMD Ryzen 5 5600', socket: 'AM4', cores: 6, price: 150, stock: 8 },
        { id: 'cpu2', name: 'AMD Ryzen 7 5700X', socket: 'AM4', cores: 8, price: 200, stock: 5 },
        { id: 'cpu3', name: 'AMD Ryzen 9 5900X', socket: 'AM4', cores: 12, price: 350, stock: 2 },
        { id: 'cpu4', name: 'Intel Core i5-12400F', socket: 'LGA1700', cores: 6, price: 180, stock: 6 },
        { id: 'cpu5', name: 'Intel Core i7-12700F', socket: 'LGA1700', cores: 12, price: 280, stock: 4 },
        { id: 'cpu6', name: 'Intel Core i9-12900F', socket: 'LGA1700', cores: 16, price: 450, stock: 1 }
      ]
    },
    ram: {
      name: 'الذاكرة العشوائية',
      required: true,
      allowMultiple: true,
      maxSlots: 4,
      options: [
        { id: 'ram1', name: 'Corsair 8GB DDR4-3200', type: 'DDR4', size: 8, speed: 3200, price: 35, stock: 15 },
        { id: 'ram2', name: 'Corsair 16GB DDR4-3200', type: 'DDR4', size: 16, speed: 3200, price: 65, stock: 12 },
        { id: 'ram3', name: 'G.Skill 32GB DDR4-3600', type: 'DDR4', size: 32, speed: 3600, price: 120, stock: 8 },
        { id: 'ram4', name: 'Corsair 16GB DDR5-5600', type: 'DDR5', size: 16, speed: 5600, price: 90, stock: 6 },
        { id: 'ram5', name: 'G.Skill 32GB DDR5-6000', type: 'DDR5', size: 32, speed: 6000, price: 180, stock: 4 }
      ]
    },
    storage: {
      name: 'وحدات التخزين',
      required: true,
      allowMultiple: true,
      maxSlots: 6,
      options: [
        { id: 'ssd1', name: 'Samsung 980 250GB NVMe', type: 'NVMe', size: 250, price: 45, stock: 20 },
        { id: 'ssd2', name: 'Samsung 980 500GB NVMe', type: 'NVMe', size: 500, price: 75, stock: 15 },
        { id: 'ssd3', name: 'Samsung 980 PRO 1TB NVMe', type: 'NVMe', size: 1000, price: 150, stock: 10 },
        { id: 'ssd4', name: 'Kingston 500GB SATA SSD', type: 'SATA_SSD', size: 500, price: 55, stock: 12 },
        { id: 'ssd5', name: 'Crucial 1TB SATA SSD', type: 'SATA_SSD', size: 1000, price: 95, stock: 8 },
        { id: 'hdd1', name: 'Seagate 1TB HDD', type: 'HDD', size: 1000, price: 45, stock: 25 },
        { id: 'hdd2', name: 'WD Blue 2TB HDD', type: 'HDD', size: 2000, price: 65, stock: 18 }
      ]
    },
    gpu: {
      name: 'كارت الشاشة',
      required: false,
      options: [
        { id: 'gpu1', name: 'NVIDIA GTX 1650', memory: 4, price: 180, stock: 8 },
        { id: 'gpu2', name: 'NVIDIA RTX 3060', memory: 12, price: 350, stock: 5 },
        { id: 'gpu3', name: 'NVIDIA RTX 3070', memory: 8, price: 550, stock: 3 },
        { id: 'gpu4', name: 'NVIDIA RTX 4060', memory: 8, price: 400, stock: 4 },
        { id: 'gpu5', name: 'NVIDIA RTX 4070', memory: 12, price: 650, stock: 2 },
        { id: 'gpu6', name: 'AMD RX 6600', memory: 8, price: 280, stock: 6 }
      ]
    },
    psu: {
      name: 'مزود الطاقة',
      required: true,
      options: [
        { id: 'psu1', name: 'Corsair CV450 450W', wattage: 450, price: 55, stock: 12 },
        { id: 'psu2', name: 'Corsair CV650 650W', wattage: 650, price: 85, stock: 8 },
        { id: 'psu3', name: 'Corsair RM750 750W Gold', wattage: 750, price: 120, stock: 5 },
        { id: 'psu4', name: 'Corsair RM850 850W Gold', wattage: 850, price: 150, stock: 3 }
      ]
    },
    case: {
      name: 'صندوق الجهاز',
      required: true,
      options: [
        { id: 'case1', name: 'Cooler Master MasterBox Q300L', size: 'Mini-ITX', price: 45, stock: 10 },
        { id: 'case2', name: 'Corsair 4000D Mid-Tower', size: 'ATX', price: 95, stock: 6 },
        { id: 'case3', name: 'NZXT H510 Mid-Tower', size: 'ATX', price: 85, stock: 8 },
        { id: 'case4', name: 'Fractal Design Define 7', size: 'ATX', price: 150, stock: 3 }
      ]
    },
    cooling: {
      name: 'نظام التبريد',
      required: false,
      options: [
        { id: 'cool1', name: 'AMD Stock Cooler', type: 'Air', price: 0, stock: 999 },
        { id: 'cool2', name: 'Intel Stock Cooler', type: 'Air', price: 0, stock: 999 },
        { id: 'cool3', name: 'Cooler Master Hyper 212', type: 'Air', price: 35, stock: 15 },
        { id: 'cool4', name: 'Noctua NH-D15', type: 'Air', price: 95, stock: 5 },
        { id: 'cool5', name: 'Corsair H100i AIO', type: 'Liquid', price: 120, stock: 4 }
      ]
    }
  };

  // Laptop upgrade options (more limited)
  const laptopComponents = {
    ram: {
      name: 'ترقية الذاكرة',
      required: false,
      allowMultiple: true,
      maxSlots: 2,
      options: [
        { id: 'lram1', name: 'Corsair 8GB DDR4-3200 SO-DIMM', type: 'DDR4', size: 8, price: 40, stock: 20 },
        { id: 'lram2', name: 'Corsair 16GB DDR4-3200 SO-DIMM', type: 'DDR4', size: 16, price: 75, stock: 15 },
        { id: 'lram3', name: 'Corsair 32GB DDR4-3200 SO-DIMM', type: 'DDR4', size: 32, price: 140, stock: 8 },
        { id: 'lram4', name: 'Corsair 16GB DDR5-4800 SO-DIMM', type: 'DDR5', size: 16, price: 95, stock: 10 }
      ]
    },
    storage: {
      name: 'ترقية التخزين',
      required: false,
      allowMultiple: true,
      maxSlots: 2,
      options: [
        { id: 'lssd1', name: 'Samsung 980 250GB M.2', type: 'M.2', size: 250, price: 50, stock: 25 },
        { id: 'lssd2', name: 'Samsung 980 500GB M.2', type: 'M.2', size: 500, price: 80, stock: 20 },
        { id: 'lssd3', name: 'Samsung 980 PRO 1TB M.2', type: 'M.2', size: 1000, price: 160, stock: 12 },
        { id: 'lssd4', name: 'Crucial 500GB SATA SSD 2.5"', type: 'SATA', size: 500, price: 60, stock: 15 }
      ]
    }
  };

  const components = buildType === 'DESKTOP' ? desktopComponents : laptopComponents;

  useEffect(() => {
    calculateTotalPrice();
    checkCompatibility();
  }, [selectedComponents]);

  const calculateTotalPrice = () => {
    let total = 0;
    
    Object.entries(selectedComponents).forEach(([category, selection]) => {
      if (Array.isArray(selection)) {
        selection.forEach(item => {
          const component = components[category]?.options.find(opt => opt.id === item.id);
          if (component) {
            total += component.price * item.quantity;
          }
        });
      } else if (selection) {
        const component = components[category]?.options.find(opt => opt.id === selection);
        if (component) {
          total += component.price;
        }
      }
    });
    
    setTotalPrice(total);
  };

  const checkCompatibility = () => {
    const warnings = [];
    const errors = [];

    if (buildType === 'DESKTOP') {
      // Check CPU and Motherboard compatibility
      const motherboard = components.motherboard?.options.find(mb => mb.id === selectedComponents.motherboard);
      const cpu = components.cpu?.options.find(c => c.id === selectedComponents.cpu);
      
      if (motherboard && cpu && motherboard.socket !== cpu.socket) {
        errors.push(`المعالج ${cpu.name} غير متوافق مع اللوحة الأم ${motherboard.name}`);
      }

      // Check RAM compatibility
      if (motherboard && selectedComponents.ram.length > 0) {
        const totalRamSize = selectedComponents.ram.reduce((sum, ram) => {
          const ramComponent = components.ram?.options.find(r => r.id === ram.id);
          return sum + (ramComponent ? ramComponent.size * ram.quantity : 0);
        }, 0);

        if (totalRamSize > motherboard.maxRam) {
          errors.push(`إجمالي الذاكرة ${totalRamSize}GB يتجاوز الحد الأقصى للوحة الأم ${motherboard.maxRam}GB`);
        }

        // Check RAM type compatibility
        selectedComponents.ram.forEach(ram => {
          const ramComponent = components.ram?.options.find(r => r.id === ram.id);
          if (ramComponent && motherboard.ramType !== ramComponent.type) {
            errors.push(`نوع الذاكرة ${ramComponent.type} غير متوافق مع اللوحة الأم ${motherboard.ramType}`);
          }
        });
      }

      // Check power requirements
      const psu = components.psu?.options.find(p => p.id === selectedComponents.psu);
      const gpu = components.gpu?.options.find(g => g.id === selectedComponents.gpu);
      
      if (psu && gpu) {
        const estimatedPower = 150 + (gpu ? 250 : 50); // Basic estimation
        if (psu.wattage < estimatedPower) {
          warnings.push(`مزود الطاقة قد لا يكون كافياً. يُنصح بـ ${estimatedPower}W على الأقل`);
        }
      }
    }

    setCompatibility({
      valid: errors.length === 0,
      warnings,
      errors
    });
  };

  const handleComponentSelect = (category, componentId) => {
    if (components[category]?.allowMultiple) {
      // Handle multiple selection (RAM, Storage)
      const existing = selectedComponents[category].find(item => item.id === componentId);
      if (existing) {
        // Increase quantity
        setSelectedComponents(prev => ({
          ...prev,
          [category]: prev[category].map(item =>
            item.id === componentId 
              ? { ...item, quantity: item.quantity + 1 }
              : item
          )
        }));
      } else {
        // Add new component
        setSelectedComponents(prev => ({
          ...prev,
          [category]: [...prev[category], { id: componentId, quantity: 1 }]
        }));
      }
    } else {
      // Handle single selection
      setSelectedComponents(prev => ({
        ...prev,
        [category]: componentId
      }));
    }
  };

  const handleComponentRemove = (category, componentId) => {
    if (components[category]?.allowMultiple) {
      setSelectedComponents(prev => ({
        ...prev,
        [category]: prev[category].filter(item => item.id !== componentId)
      }));
    } else {
      setSelectedComponents(prev => ({
        ...prev,
        [category]: null
      }));
    }
  };

  const handleQuantityChange = (category, componentId, newQuantity) => {
    if (newQuantity <= 0) {
      handleComponentRemove(category, componentId);
      return;
    }

    setSelectedComponents(prev => ({
      ...prev,
      [category]: prev[category].map(item =>
        item.id === componentId 
          ? { ...item, quantity: newQuantity }
          : item
      )
    }));
  };

  const handleSave = () => {
    // Check if all required components are selected
    const missingRequired = Object.entries(components).filter(([category, config]) => {
      if (!config.required) return false;
      
      if (config.allowMultiple) {
        return selectedComponents[category].length === 0;
      } else {
        return !selectedComponents[category];
      }
    });

    if (missingRequired.length > 0) {
      toast.error(`يجب اختيار: ${missingRequired.map(([cat, config]) => config.name).join(', ')}`);
      return;
    }

    if (!compatibility.valid) {
      toast.error('يوجد مشاكل في التوافق يجب حلها أولاً');
      return;
    }

    // Prepare build details
    const buildDetails = {
      buildType,
      components: selectedComponents,
      totalPrice,
      compatibility,
      summary: generateBuildSummary()
    };

    onSave(buildDetails);
  };

  const generateBuildSummary = () => {
    const summary = [];
    
    Object.entries(selectedComponents).forEach(([category, selection]) => {
      const categoryConfig = components[category];
      if (!categoryConfig) return;

      if (Array.isArray(selection)) {
        selection.forEach(item => {
          const component = categoryConfig.options.find(opt => opt.id === item.id);
          if (component) {
            summary.push({
              category: categoryConfig.name,
              name: component.name,
              quantity: item.quantity,
              price: component.price,
              total: component.price * item.quantity
            });
          }
        });
      } else if (selection) {
        const component = categoryConfig.options.find(opt => opt.id === selection);
        if (component) {
          summary.push({
            category: categoryConfig.name,
            name: component.name,
            quantity: 1,
            price: component.price,
            total: component.price
          });
        }
      }
    });

    return summary;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            {buildType === 'DESKTOP' ? (
              <ComputerDesktopIcon className="h-6 w-6 ml-2 text-blue-600" />
            ) : (
              <DevicePhoneMobileIcon className="h-6 w-6 ml-2 text-green-600" />
            )}
            {buildType === 'DESKTOP' ? 'تجميع جهاز كمبيوتر' : 'ترقية لابتوب'}
          </h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            {/* Components Selection */}
            <div className="lg:col-span-2 space-y-6">
              {Object.entries(components).map(([category, config]) => (
                <div key={category} className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    {config.name}
                    {config.required && <span className="text-red-500 mr-1">*</span>}
                    {config.allowMultiple && (
                      <span className="text-sm text-gray-500 mr-2">
                        (يمكن اختيار أكثر من واحد)
                      </span>
                    )}
                  </h3>

                  {/* Selected Components */}
                  {config.allowMultiple && selectedComponents[category].length > 0 && (
                    <div className="mb-4 space-y-2">
                      <h4 className="text-sm font-medium text-gray-700">المختار:</h4>
                      {selectedComponents[category].map(item => {
                        const component = config.options.find(opt => opt.id === item.id);
                        return (
                          <div key={item.id} className="flex items-center justify-between bg-white p-2 rounded border">
                            <span className="text-sm">{component?.name}</span>
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => handleQuantityChange(category, item.id, item.quantity - 1)}
                                className="w-6 h-6 bg-gray-200 rounded text-xs"
                              >
                                -
                              </button>
                              <span className="text-sm w-8 text-center">{item.quantity}</span>
                              <button
                                onClick={() => handleQuantityChange(category, item.id, item.quantity + 1)}
                                className="w-6 h-6 bg-gray-200 rounded text-xs"
                              >
                                +
                              </button>
                              <span className="text-sm text-green-600 w-16 text-right">
                                ${(component?.price * item.quantity).toFixed(2)}
                              </span>
                              <button
                                onClick={() => handleComponentRemove(category, item.id)}
                                className="text-red-500 hover:text-red-700"
                              >
                                <XMarkIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}

                  {/* Available Components */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {config.options.map(component => {
                      const isSelected = config.allowMultiple 
                        ? selectedComponents[category].some(item => item.id === component.id)
                        : selectedComponents[category] === component.id;

                      return (
                        <button
                          key={component.id}
                          onClick={() => handleComponentSelect(category, component.id)}
                          disabled={component.stock === 0}
                          className={`p-3 border rounded-lg text-right transition-colors ${
                            isSelected
                              ? 'border-blue-500 bg-blue-50'
                              : component.stock === 0
                              ? 'border-gray-200 bg-gray-100 text-gray-400'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          <div className="font-medium text-sm">{component.name}</div>
                          <div className="text-xs text-gray-600 mt-1">
                            ${component.price.toFixed(2)} - المخزون: {component.stock}
                          </div>
                          {component.socket && (
                            <div className="text-xs text-blue-600">Socket: {component.socket}</div>
                          )}
                          {component.type && (
                            <div className="text-xs text-green-600">Type: {component.type}</div>
                          )}
                        </button>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>

            {/* Build Summary */}
            <div className="space-y-6">
              {/* Compatibility Check */}
              {!compatibility.valid && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-medium text-red-900 mb-2">مشاكل التوافق:</h4>
                  {compatibility.errors.map((error, index) => (
                    <div key={index} className="text-sm text-red-700">• {error}</div>
                  ))}
                </div>
              )}

              {compatibility.warnings.length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h4 className="font-medium text-yellow-900 mb-2">تحذيرات:</h4>
                  {compatibility.warnings.map((warning, index) => (
                    <div key={index} className="text-sm text-yellow-700">• {warning}</div>
                  ))}
                </div>
              )}

              {/* Price Summary */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-900 mb-2">ملخص السعر:</h4>
                <div className="space-y-1 text-sm">
                  {generateBuildSummary().map((item, index) => (
                    <div key={index} className="flex justify-between">
                      <span>{item.name} {item.quantity > 1 && `(${item.quantity})`}</span>
                      <span>${item.total.toFixed(2)}</span>
                    </div>
                  ))}
                  <div className="border-t pt-2 font-bold flex justify-between">
                    <span>الإجمالي:</span>
                    <span>${totalPrice.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="space-y-3">
                <button
                  onClick={handleSave}
                  disabled={!compatibility.valid}
                  className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  حفظ التجميعة
                </button>
                <button
                  onClick={onClose}
                  className="w-full btn-secondary"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
