import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useQuery } from 'react-query';
import axios from 'axios';
import {
  BuildingOfficeIcon,
  UsersIcon,
  CogIcon,
  ShieldCheckIcon,
  LanguageIcon,
  CurrencyDollarIcon,
  ClockIcon,
  DocumentArrowDownIcon,
} from '@heroicons/react/24/outline';

export default function Settings() {
  const { t } = useTranslation('common');
  const [activeTab, setActiveTab] = useState('company');

  const settingsTabs = [
    {
      id: 'company',
      name: t('settings.companyInfo'),
      nameAr: 'معلومات الشركة',
      icon: BuildingOfficeIcon,
      description: 'Company details and business information'
    },
    {
      id: 'users',
      name: t('settings.userManagement'),
      nameAr: 'إدارة المستخدمين',
      icon: UsersIcon,
      description: 'Manage user accounts and permissions'
    },
    {
      id: 'system',
      name: t('settings.systemSettings'),
      nameAr: 'إعدادات النظام',
      icon: CogIcon,
      description: 'System configuration and preferences'
    },
    {
      id: 'security',
      name: 'Security',
      nameAr: 'الأمان',
      icon: ShieldCheckIcon,
      description: 'Security settings and access control'
    },
    {
      id: 'localization',
      name: 'Localization',
      nameAr: 'التوطين',
      icon: LanguageIcon,
      description: 'Language, currency, and regional settings'
    },
    {
      id: 'backup',
      name: t('settings.backupRestore'),
      nameAr: 'النسخ الاحتياطي',
      icon: DocumentArrowDownIcon,
      description: 'Backup and restore system data'
    }
  ];

  // Fetch company info
  const { data: companyData, isLoading: companyLoading } = useQuery('company', async () => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/company`);
    return response.data;
  });

  // Fetch users
  const { data: usersData, isLoading: usersLoading } = useQuery('users', async () => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/users`);
    return response.data;
  });

  const renderCompanySettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">{t('settings.companySettings')}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('settings.companyName')}
            </label>
            <input
              type="text"
              defaultValue={companyData?.company?.name || ''}
              className="form-input"
              placeholder="Enter company name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('settings.companyNameAr')}
            </label>
            <input
              type="text"
              defaultValue={companyData?.company?.nameAr || ''}
              className="form-input"
              placeholder="أدخل اسم الشركة"
              dir="rtl"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Address (English)
            </label>
            <textarea
              defaultValue={companyData?.company?.address || ''}
              className="form-input"
              rows="3"
              placeholder="Enter company address"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Address (Arabic)
            </label>
            <textarea
              defaultValue={companyData?.company?.addressAr || ''}
              className="form-input"
              rows="3"
              placeholder="أدخل عنوان الشركة"
              dir="rtl"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone
            </label>
            <input
              type="tel"
              defaultValue={companyData?.company?.phone || ''}
              className="form-input"
              placeholder="+1234567890"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email
            </label>
            <input
              type="email"
              defaultValue={companyData?.company?.email || ''}
              className="form-input"
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Website
            </label>
            <input
              type="url"
              defaultValue={companyData?.company?.website || ''}
              className="form-input"
              placeholder="https://company.com"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Logo
            </label>
            <input
              type="file"
              accept="image/*"
              className="form-input"
            />
          </div>
        </div>
      </div>
      <div className="flex justify-end">
        <button className="btn-primary">
          {t('settings.save')}
        </button>
      </div>
    </div>
  );

  const renderUserManagement = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">{t('settings.userManagement')}</h3>
        <button className="btn-primary">
          Add New User
        </button>
      </div>
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <table className="table">
          <thead>
            <tr>
              <th>User</th>
              <th>Role</th>
              <th>Branch</th>
              <th>Status</th>
              <th>Last Login</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {usersData?.users?.map((user) => (
              <tr key={user.id}>
                <td>
                  <div>
                    <div className="font-medium text-gray-900">
                      {user.firstName} {user.lastName}
                    </div>
                    <div className="text-sm text-gray-500">{user.email}</div>
                  </div>
                </td>
                <td>
                  <span className="badge badge-secondary">{user.role}</span>
                </td>
                <td>{user.branch?.name || 'Not assigned'}</td>
                <td>
                  <span className={`badge ${user.isActive ? 'badge-success' : 'badge-secondary'}`}>
                    {user.isActive ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td>
                  {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
                </td>
                <td>
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <button className="btn-secondary btn-sm">Edit</button>
                    <button className="btn-danger btn-sm">Delete</button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderSystemSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">System Configuration</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <LanguageIcon className="h-4 w-4 inline mr-1 rtl:mr-0 rtl:ml-1" />
              Default Language
            </label>
            <select className="form-input">
              <option value="ar">العربية (Arabic)</option>
              <option value="en">English</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <CurrencyDollarIcon className="h-4 w-4 inline mr-1 rtl:mr-0 rtl:ml-1" />
              Default Currency
            </label>
            <select className="form-input">
              <option value="USD">USD - US Dollar</option>
              <option value="EUR">EUR - Euro</option>
              <option value="EGP">EGP - Egyptian Pound</option>
              <option value="SAR">SAR - Saudi Riyal</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <ClockIcon className="h-4 w-4 inline mr-1 rtl:mr-0 rtl:ml-1" />
              Timezone
            </label>
            <select className="form-input">
              <option value="UTC">UTC</option>
              <option value="Africa/Cairo">Cairo</option>
              <option value="Asia/Riyadh">Riyadh</option>
              <option value="America/New_York">New York</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date Format
            </label>
            <select className="form-input">
              <option value="DD/MM/YYYY">DD/MM/YYYY</option>
              <option value="MM/DD/YYYY">MM/DD/YYYY</option>
              <option value="YYYY-MM-DD">YYYY-MM-DD</option>
            </select>
          </div>
        </div>
      </div>
      <div className="flex justify-end">
        <button className="btn-primary">
          Save Settings
        </button>
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Security Settings</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">Two-Factor Authentication</h4>
              <p className="text-sm text-gray-500">Add an extra layer of security to user accounts</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
            </label>
          </div>
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">Session Timeout</h4>
              <p className="text-sm text-gray-500">Automatically log out inactive users</p>
            </div>
            <select className="form-input w-32">
              <option value="30">30 minutes</option>
              <option value="60">1 hour</option>
              <option value="120">2 hours</option>
              <option value="480">8 hours</option>
            </select>
          </div>
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">Password Policy</h4>
              <p className="text-sm text-gray-500">Enforce strong password requirements</p>
            </div>
            <button className="btn-secondary">Configure</button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderBackupSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Backup & Restore</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-6 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Create Backup</h4>
            <p className="text-sm text-blue-700 mb-4">
              Create a complete backup of your system data
            </p>
            <button className="btn-primary">
              <DocumentArrowDownIcon className="h-4 w-4 mr-2 rtl:mr-0 rtl:ml-2" />
              Create Backup
            </button>
          </div>
          <div className="p-6 bg-green-50 rounded-lg">
            <h4 className="font-medium text-green-900 mb-2">Restore Data</h4>
            <p className="text-sm text-green-700 mb-4">
              Restore system data from a backup file
            </p>
            <input type="file" accept=".sql,.zip" className="form-input mb-2" />
            <button className="btn-secondary">
              Restore Backup
            </button>
          </div>
        </div>
      </div>
      <div>
        <h4 className="font-medium text-gray-900 mb-4">Automatic Backups</h4>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h5 className="font-medium text-gray-900">Enable Automatic Backups</h5>
              <p className="text-sm text-gray-500">Automatically create backups on schedule</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
            </label>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Backup Frequency
              </label>
              <select className="form-input">
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Retention Period
              </label>
              <select className="form-input">
                <option value="7">7 days</option>
                <option value="30">30 days</option>
                <option value="90">90 days</option>
                <option value="365">1 year</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'company':
        return renderCompanySettings();
      case 'users':
        return renderUserManagement();
      case 'system':
        return renderSystemSettings();
      case 'security':
        return renderSecuritySettings();
      case 'backup':
        return renderBackupSettings();
      default:
        return <div>Select a settings category</div>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">{t('settings.title')}</h1>
        <p className="mt-1 text-sm text-gray-600">
          إدارة إعدادات النظام والتكوين
        </p>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Settings Navigation */}
        <div className="lg:w-1/4">
          <nav className="space-y-1">
            {settingsTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-primary-100 text-primary-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center">
                  <tab.icon className="h-5 w-5 mr-3 rtl:mr-0 rtl:ml-3" />
                  <div>
                    <div className="font-medium">{tab.name}</div>
                    <div className="text-xs text-gray-500">{tab.description}</div>
                  </div>
                </div>
              </button>
            ))}
          </nav>
        </div>

        {/* Settings Content */}
        <div className="lg:w-3/4">
          <div className="bg-white shadow rounded-lg p-6">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  };
}
