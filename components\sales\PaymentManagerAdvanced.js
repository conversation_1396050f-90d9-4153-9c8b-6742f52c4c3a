import { useState, useEffect } from 'react';
import { XMarkIcon, CreditCardIcon, BanknotesIcon, DevicePhoneMobileIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export default function PaymentManagerAdvanced({ 
  isOpen, 
  onClose, 
  totalAmount, 
  customer,
  onPaymentComplete,
  saleType = 'DIRECT' // DIRECT, CUSTOM_ORDER, QUOTE
}) {
  const [payments, setPayments] = useState([]);
  const [currentPayment, setCurrentPayment] = useState({
    method: 'CASH',
    amount: 0,
    reference: '',
    installmentPlan: '',
    notes: ''
  });

  const [showInstallmentOptions, setShowInstallmentOptions] = useState(false);
  const [installmentDetails, setInstallmentDetails] = useState({
    numberOfInstallments: 3,
    firstPayment: 0,
    monthlyAmount: 0,
    startDate: ''
  });

  const paymentMethods = [
    { value: 'CASH', label: 'نقدي', icon: BanknotesIcon, color: 'green' },
    { value: 'VISA', label: 'فيزا', icon: CreditCardIcon, color: 'blue' },
    { value: 'INSTAPAY', label: 'إنستاباي', icon: DevicePhoneMobileIcon, color: 'purple' },
    { value: 'VODAFONE_CASH', label: 'فودافون كاش', icon: DevicePhoneMobileIcon, color: 'red' },
    { value: 'INSTALLMENT', label: 'تقسيط', icon: CreditCardIcon, color: 'orange' },
    { value: 'CREDIT', label: 'آجل', icon: CreditCardIcon, color: 'gray' }
  ];

  useEffect(() => {
    if (isOpen) {
      // Initialize with full amount for direct sales
      if (saleType === 'DIRECT') {
        setCurrentPayment(prev => ({ ...prev, amount: totalAmount }));
      }
      // Reset for new payment session
      setPayments([]);
    }
  }, [isOpen, totalAmount, saleType]);

  const addPayment = () => {
    if (currentPayment.amount <= 0) {
      toast.error('يجب إدخال مبلغ صحيح');
      return;
    }

    const remainingAmount = totalAmount - getTotalPaid();
    if (currentPayment.amount > remainingAmount) {
      toast.error('المبلغ أكبر من المبلغ المتبقي');
      return;
    }

    if (currentPayment.method === 'INSTALLMENT') {
      if (!installmentDetails.numberOfInstallments || installmentDetails.numberOfInstallments < 2) {
        toast.error('يجب تحديد عدد الأقساط (2 على الأقل)');
        return;
      }
    }

    const newPayment = {
      id: Date.now(),
      ...currentPayment,
      installmentDetails: currentPayment.method === 'INSTALLMENT' ? installmentDetails : null,
      timestamp: new Date().toISOString()
    };

    setPayments([...payments, newPayment]);
    
    // Reset current payment
    setCurrentPayment({
      method: 'CASH',
      amount: Math.max(0, remainingAmount - currentPayment.amount),
      reference: '',
      installmentPlan: '',
      notes: ''
    });

    toast.success('تم إضافة الدفعة');
  };

  const removePayment = (paymentId) => {
    setPayments(payments.filter(p => p.id !== paymentId));
    toast.success('تم حذف الدفعة');
  };

  const getTotalPaid = () => {
    return payments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
  };

  const getRemainingAmount = () => {
    return totalAmount - getTotalPaid();
  };

  const calculateInstallments = () => {
    const { numberOfInstallments, firstPayment } = installmentDetails;
    const remainingAfterFirst = currentPayment.amount - firstPayment;
    const monthlyAmount = remainingAfterFirst / (numberOfInstallments - 1);
    
    setInstallmentDetails(prev => ({
      ...prev,
      monthlyAmount: monthlyAmount.toFixed(2)
    }));
  };

  const handleComplete = () => {
    const totalPaid = getTotalPaid();
    const remaining = getRemainingAmount();

    if (saleType === 'DIRECT' && remaining > 0.01) {
      toast.error('يجب دفع المبلغ كاملاً للبيع المباشر');
      return;
    }

    if (payments.length === 0) {
      toast.error('يجب إضافة دفعة واحدة على الأقل');
      return;
    }

    const paymentData = {
      payments,
      totalPaid,
      remainingAmount: remaining,
      paymentStatus: remaining > 0.01 ? 'PARTIAL' : 'PAID',
      saleType
    };

    onPaymentComplete(paymentData);
  };

  if (!isOpen) return null;

  const remainingAmount = getRemainingAmount();
  const totalPaid = getTotalPaid();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">إدارة الدفع</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            {/* Payment Form */}
            <div className="space-y-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-medium text-blue-900 mb-2">ملخص المبالغ</h3>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>إجمالي الفاتورة:</span>
                    <span className="font-bold">${totalAmount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>المدفوع:</span>
                    <span className="font-bold text-green-600">${totalPaid.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between border-t pt-1">
                    <span>المتبقي:</span>
                    <span className={`font-bold ${remainingAmount > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      ${remainingAmount.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>

              {customer && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium text-gray-900 mb-2">معلومات العميل</h3>
                  <div className="text-sm">
                    <div>{customer.nameAr || customer.name}</div>
                    <div className="text-gray-600">{customer.phone}</div>
                    <div className="text-gray-600">الرصيد: ${parseFloat(customer.balance || 0).toFixed(2)}</div>
                  </div>
                </div>
              )}

              {/* Payment Method Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">طريقة الدفع</label>
                <div className="grid grid-cols-2 gap-2">
                  {paymentMethods.map(method => {
                    const Icon = method.icon;
                    return (
                      <button
                        key={method.value}
                        onClick={() => {
                          setCurrentPayment(prev => ({ ...prev, method: method.value }));
                          setShowInstallmentOptions(method.value === 'INSTALLMENT');
                        }}
                        className={`p-3 border rounded-lg text-sm font-medium transition-colors ${
                          currentPayment.method === method.value
                            ? `border-${method.color}-500 bg-${method.color}-50 text-${method.color}-700`
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        <Icon className="h-5 w-5 mx-auto mb-1" />
                        {method.label}
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Payment Amount */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المبلغ</label>
                <input
                  type="number"
                  value={currentPayment.amount}
                  onChange={(e) => setCurrentPayment(prev => ({ ...prev, amount: parseFloat(e.target.value) || 0 }))}
                  className="form-input"
                  step="0.01"
                  min="0"
                  max={remainingAmount}
                />
                <div className="mt-1 flex space-x-2">
                  <button
                    onClick={() => setCurrentPayment(prev => ({ ...prev, amount: remainingAmount }))}
                    className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded"
                  >
                    المبلغ كاملاً
                  </button>
                  <button
                    onClick={() => setCurrentPayment(prev => ({ ...prev, amount: remainingAmount / 2 }))}
                    className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
                  >
                    نصف المبلغ
                  </button>
                </div>
              </div>

              {/* Reference Number */}
              {['VISA', 'INSTAPAY', 'VODAFONE_CASH'].includes(currentPayment.method) && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">رقم المرجع</label>
                  <input
                    type="text"
                    value={currentPayment.reference}
                    onChange={(e) => setCurrentPayment(prev => ({ ...prev, reference: e.target.value }))}
                    className="form-input"
                    placeholder="رقم العملية أو المرجع"
                  />
                </div>
              )}

              {/* Installment Details */}
              {showInstallmentOptions && (
                <div className="bg-orange-50 p-4 rounded-lg space-y-4">
                  <h4 className="font-medium text-orange-900">تفاصيل التقسيط</h4>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">عدد الأقساط</label>
                      <select
                        value={installmentDetails.numberOfInstallments}
                        onChange={(e) => setInstallmentDetails(prev => ({ 
                          ...prev, 
                          numberOfInstallments: parseInt(e.target.value) 
                        }))}
                        className="form-input"
                      >
                        <option value={2}>قسطين</option>
                        <option value={3}>3 أقساط</option>
                        <option value={4}>4 أقساط</option>
                        <option value={6}>6 أقساط</option>
                        <option value={12}>12 قسط</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">الدفعة الأولى</label>
                      <input
                        type="number"
                        value={installmentDetails.firstPayment}
                        onChange={(e) => setInstallmentDetails(prev => ({ 
                          ...prev, 
                          firstPayment: parseFloat(e.target.value) || 0 
                        }))}
                        className="form-input"
                        step="0.01"
                        min="0"
                        max={currentPayment.amount}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ بداية الأقساط</label>
                    <input
                      type="date"
                      value={installmentDetails.startDate}
                      onChange={(e) => setInstallmentDetails(prev => ({ 
                        ...prev, 
                        startDate: e.target.value 
                      }))}
                      className="form-input"
                    />
                  </div>
                  
                  <button
                    onClick={calculateInstallments}
                    className="btn-secondary text-sm"
                  >
                    حساب الأقساط
                  </button>
                  
                  {installmentDetails.monthlyAmount > 0 && (
                    <div className="bg-white p-3 rounded border">
                      <div className="text-sm">
                        <div>الدفعة الأولى: ${installmentDetails.firstPayment}</div>
                        <div>القسط الشهري: ${installmentDetails.monthlyAmount}</div>
                        <div>عدد الأقساط: {installmentDetails.numberOfInstallments - 1}</div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                <textarea
                  value={currentPayment.notes}
                  onChange={(e) => setCurrentPayment(prev => ({ ...prev, notes: e.target.value }))}
                  className="form-input"
                  rows="2"
                  placeholder="ملاحظات إضافية..."
                />
              </div>

              <button
                onClick={addPayment}
                className="w-full btn-primary"
                disabled={currentPayment.amount <= 0}
              >
                إضافة الدفعة
              </button>
            </div>

            {/* Payments List */}
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900">الدفعات المضافة</h3>
              
              {payments.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  <CreditCardIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                  <p>لم يتم إضافة دفعات بعد</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {payments.map(payment => {
                    const method = paymentMethods.find(m => m.value === payment.method);
                    const Icon = method?.icon || CreditCardIcon;
                    
                    return (
                      <div key={payment.id} className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex justify-between items-start">
                          <div className="flex items-center space-x-3">
                            <Icon className="h-5 w-5 text-gray-600" />
                            <div>
                              <div className="font-medium">{method?.label}</div>
                              <div className="text-sm text-gray-600">
                                ${parseFloat(payment.amount).toFixed(2)}
                              </div>
                              {payment.reference && (
                                <div className="text-xs text-gray-500">
                                  مرجع: {payment.reference}
                                </div>
                              )}
                              {payment.installmentDetails && (
                                <div className="text-xs text-orange-600">
                                  {payment.installmentDetails.numberOfInstallments} أقساط
                                </div>
                              )}
                            </div>
                          </div>
                          <button
                            onClick={() => removePayment(payment.id)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <XMarkIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 mt-6 pt-6 border-t">
            <button onClick={onClose} className="btn-secondary">
              إلغاء
            </button>
            <button
              onClick={handleComplete}
              className="btn-primary"
              disabled={payments.length === 0}
            >
              {saleType === 'DIRECT' ? 'إتمام البيع' : 'حفظ الدفعات'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
