"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "__barrel_optimize__?names=CogIcon,PlusIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!********************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=CogIcon,PlusIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \********************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CogIcon: function() { return /* reexport safe */ _CogIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   PlusIcon: function() { return /* reexport safe */ _PlusIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   TrashIcon: function() { return /* reexport safe */ _TrashIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   XMarkIcon: function() { return /* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _CogIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CogIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _PlusIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./PlusIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _TrashIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TrashIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Db2dJY29uLFBsdXNJY29uLFRyYXNoSWNvbixYTWFya0ljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUNpRDtBQUNFO0FBQ0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/MzljOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29nSWNvbiB9IGZyb20gXCIuL0NvZ0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQbHVzSWNvbiB9IGZyb20gXCIuL1BsdXNJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVHJhc2hJY29uIH0gZnJvbSBcIi4vVHJhc2hJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgWE1hcmtJY29uIH0gZnJvbSBcIi4vWE1hcmtJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=CogIcon,PlusIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "./components/sales/ProductCustomizerAdvanced.js":
/*!*******************************************************!*\
  !*** ./components/sales/ProductCustomizerAdvanced.js ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductCustomizerAdvanced; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_MinusIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=MinusIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=MinusIcon,PlusIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\nfunction ProductCustomizerAdvanced(param) {\n    let { isOpen, onClose, product, onSave } = param;\n    var _product_customizationOptions;\n    _s();\n    const [customizations, setCustomizations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [totalPrice, setTotalPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen && product) {\n            var _product_customizationOptions;\n            // Initialize with default selections\n            const defaultCustomizations = {};\n            (_product_customizationOptions = product.customizationOptions) === null || _product_customizationOptions === void 0 ? void 0 : _product_customizationOptions.forEach((option)=>{\n                if (option.required && option.options.length > 0) {\n                    if (option.allowMultiple) {\n                        defaultCustomizations[option.id] = [\n                            {\n                                optionId: option.options[0].id,\n                                quantity: 1\n                            }\n                        ];\n                    } else {\n                        defaultCustomizations[option.id] = option.options[0].id;\n                    }\n                }\n            });\n            setCustomizations(defaultCustomizations);\n        }\n    }, [\n        isOpen,\n        product\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        calculateTotalPrice();\n    }, [\n        customizations,\n        product\n    ]);\n    const calculateTotalPrice = ()=>{\n        if (!product) return;\n        let total = parseFloat(product.basePrice) || 0;\n        Object.entries(customizations).forEach((param)=>{\n            let [optionId, selection] = param;\n            var _product_customizationOptions;\n            const option = (_product_customizationOptions = product.customizationOptions) === null || _product_customizationOptions === void 0 ? void 0 : _product_customizationOptions.find((opt)=>opt.id === optionId);\n            if (option) {\n                if (option.allowMultiple && Array.isArray(selection)) {\n                    selection.forEach((item)=>{\n                        const selected = option.options.find((opt)=>opt.id === item.optionId);\n                        if (selected) {\n                            total += (parseFloat(selected.price) || 0) * (parseInt(item.quantity) || 1);\n                        }\n                    });\n                } else {\n                    const selected = option.options.find((opt)=>opt.id === selection);\n                    if (selected) {\n                        total += parseFloat(selected.price) || 0;\n                    }\n                }\n            }\n        });\n        setTotalPrice(total);\n    };\n    // Handle single selection options\n    const handleSingleOptionChange = (optionId, selectedId)=>{\n        setCustomizations((prev)=>({\n                ...prev,\n                [optionId]: selectedId\n            }));\n    };\n    // Handle multiple selection options\n    const handleMultipleOptionChange = (optionId, index, field, value)=>{\n        setCustomizations((prev)=>{\n            const newCustomizations = {\n                ...prev\n            };\n            if (!newCustomizations[optionId]) {\n                newCustomizations[optionId] = [];\n            }\n            const newArray = [\n                ...newCustomizations[optionId]\n            ];\n            if (field === \"optionId\") {\n                newArray[index] = {\n                    ...newArray[index],\n                    optionId: value\n                };\n            } else if (field === \"quantity\") {\n                newArray[index] = {\n                    ...newArray[index],\n                    quantity: parseInt(value) || 1\n                };\n            }\n            newCustomizations[optionId] = newArray;\n            return newCustomizations;\n        });\n    };\n    // Add new item to multiple selection\n    const addMultipleItem = (optionId)=>{\n        var _product_customizationOptions;\n        const option = (_product_customizationOptions = product.customizationOptions) === null || _product_customizationOptions === void 0 ? void 0 : _product_customizationOptions.find((opt)=>opt.id === optionId);\n        if (!option) return;\n        setCustomizations((prev)=>{\n            const current = prev[optionId] || [];\n            if (current.length >= (option.maxQuantity || 10)) return prev;\n            return {\n                ...prev,\n                [optionId]: [\n                    ...current,\n                    {\n                        optionId: option.options[0].id,\n                        quantity: 1\n                    }\n                ]\n            };\n        });\n    };\n    // Remove item from multiple selection\n    const removeMultipleItem = (optionId, index)=>{\n        setCustomizations((prev)=>{\n            const newArray = [\n                ...prev[optionId] || []\n            ];\n            newArray.splice(index, 1);\n            return {\n                ...prev,\n                [optionId]: newArray\n            };\n        });\n    };\n    const handleSave = ()=>{\n        const customizationDetails = [];\n        Object.entries(customizations).forEach((param)=>{\n            let [optionId, selection] = param;\n            var _product_customizationOptions;\n            const option = (_product_customizationOptions = product.customizationOptions) === null || _product_customizationOptions === void 0 ? void 0 : _product_customizationOptions.find((opt)=>opt.id === optionId);\n            if (!option) return;\n            if (option.allowMultiple && Array.isArray(selection)) {\n                selection.forEach((item)=>{\n                    const selectedOption = option.options.find((opt)=>opt.id === item.optionId);\n                    if (selectedOption) {\n                        customizationDetails.push({\n                            optionId,\n                            optionName: option.name,\n                            selectedId: item.optionId,\n                            selectedName: selectedOption.nameAr || selectedOption.name,\n                            price: selectedOption.price,\n                            quantity: item.quantity,\n                            componentId: selectedOption.componentId\n                        });\n                    }\n                });\n            } else {\n                const selectedOption = option.options.find((opt)=>opt.id === selection);\n                if (selectedOption) {\n                    customizationDetails.push({\n                        optionId,\n                        optionName: option.name,\n                        selectedId: selection,\n                        selectedName: selectedOption.nameAr || selectedOption.name,\n                        price: selectedOption.price,\n                        quantity: 1,\n                        componentId: selectedOption.componentId\n                    });\n                }\n            }\n        });\n        onSave({\n            customizations,\n            customizationDetails,\n            totalPrice\n        });\n        onClose();\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: [\n                                \"تخصيص المنتج: \",\n                                (product === null || product === void 0 ? void 0 : product.nameAr) || (product === null || product === void 0 ? void 0 : product.name)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MinusIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.XMarkIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-blue-900 mb-2\",\n                                    children: \"السعر الأساسي\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: [\n                                        \"$\",\n                                        parseFloat((product === null || product === void 0 ? void 0 : product.basePrice) || 0).toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        product === null || product === void 0 ? void 0 : (_product_customizationOptions = product.customizationOptions) === null || _product_customizationOptions === void 0 ? void 0 : _product_customizationOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: [\n                                                    option.name,\n                                                    option.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500 mr-1\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 39\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            option.allowMultiple && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>addMultipleItem(option.id),\n                                                className: \"btn-secondary flex items-center text-sm\",\n                                                disabled: (customizations[option.id] || []).length >= (option.maxQuantity || 10),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MinusIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.PlusIcon, {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"إضافة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this),\n                                    option.allowMultiple ? // Multiple selection with quantities\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: (customizations[option.id] || []).map((item, index)=>{\n                                            var _option_options_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-12 gap-4 items-center p-3 bg-white rounded border\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: item.optionId,\n                                                            onChange: (e)=>handleMultipleOptionChange(option.id, index, \"optionId\", e.target.value),\n                                                            className: \"form-input\",\n                                                            children: option.options.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: opt.id,\n                                                                    children: [\n                                                                        opt.nameAr || opt.name,\n                                                                        \" - $\",\n                                                                        opt.price\n                                                                    ]\n                                                                }, opt.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleMultipleOptionChange(option.id, index, \"quantity\", Math.max(1, item.quantity - 1)),\n                                                                    className: \"p-1 text-gray-500 hover:text-gray-700\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MinusIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.MinusIcon, {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                                        lineNumber: 231,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: item.quantity,\n                                                                    onChange: (e)=>handleMultipleOptionChange(option.id, index, \"quantity\", e.target.value),\n                                                                    className: \"form-input mx-2 text-center w-16\",\n                                                                    min: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleMultipleOptionChange(option.id, index, \"quantity\", item.quantity + 1),\n                                                                    className: \"p-1 text-gray-500 hover:text-gray-700\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MinusIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.PlusIcon, {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: [\n                                                                \"$\",\n                                                                ((((_option_options_find = option.options.find((opt)=>opt.id === item.optionId)) === null || _option_options_find === void 0 ? void 0 : _option_options_find.price) || 0) * item.quantity).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"col-span-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeMultipleItem(option.id, index),\n                                                            className: \"text-red-600 hover:text-red-800\",\n                                                            disabled: (customizations[option.id] || []).length <= 1 && option.required,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MinusIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.TrashIcon, {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                lineNumber: 210,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this) : // Single selection\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                        children: option.options.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center p-3 border rounded-lg cursor-pointer transition-colors \".concat(customizations[option.id] === opt.id ? \"border-blue-500 bg-blue-50\" : \"border-gray-300 hover:border-gray-400\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"radio\",\n                                                        name: option.id,\n                                                        value: opt.id,\n                                                        checked: customizations[option.id] === opt.id,\n                                                        onChange: (e)=>handleSingleOptionChange(option.id, e.target.value),\n                                                        className: \"sr-only\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: opt.nameAr || opt.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    \"+$\",\n                                                                    opt.price.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, opt.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                                lineNumber: 271,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, option.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 p-4 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-green-900\",\n                                        children: \"السعر الإجمالي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: [\n                                            \"$\",\n                                            totalPrice.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"btn-secondary\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSave,\n                                    className: \"btn-primary\",\n                                    children: \"حفظ التخصيص\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizerAdvanced.js\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCustomizerAdvanced, \"3ulmrnrWCBIy8GjIK5AXI3C37j8=\");\n_c = ProductCustomizerAdvanced;\nvar _c;\n$RefreshReg$(_c, \"ProductCustomizerAdvanced\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/sales/ProductCustomizerAdvanced.js\n"));

/***/ }),

/***/ "./components/sales/QuoteModal.js":
/*!****************************************!*\
  !*** ./components/sales/QuoteModal.js ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ QuoteModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CogIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=CogIcon,PlusIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _ProductCustomizerAdvanced__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ProductCustomizerAdvanced */ \"./components/sales/ProductCustomizerAdvanced.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction QuoteModal(param) {\n    let { isOpen, onClose, onSave, quote = null } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: \"\",\n        validUntil: \"\",\n        notes: \"\",\n        items: []\n    });\n    // Load customers and products\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            loadCustomers();\n            loadProducts();\n            if (quote) {\n                setFormData({\n                    customerId: quote.customerId || \"\",\n                    validUntil: quote.validUntil ? quote.validUntil.split(\"T\")[0] : \"\",\n                    notes: quote.notes || \"\",\n                    items: quote.items || []\n                });\n            } else {\n                // Set default valid until date (7 days from now)\n                const validUntil = new Date();\n                validUntil.setDate(validUntil.getDate() + 7);\n                setFormData((prev)=>({\n                        ...prev,\n                        validUntil: validUntil.toISOString().split(\"T\")[0]\n                    }));\n            }\n        }\n    }, [\n        isOpen,\n        quote\n    ]);\n    const loadCustomers = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/customers\"));\n            setCustomers(response.data.customers || []);\n        } catch (error) {\n            console.error(\"Error loading customers:\", error);\n        }\n    };\n    const loadProducts = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/products\"));\n            setProducts(response.data.products || []);\n        } catch (error) {\n            console.error(\"Error loading products:\", error);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const addItem = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: [\n                    ...prev.items,\n                    {\n                        productId: \"\",\n                        productName: \"\",\n                        quantity: 1,\n                        unitPrice: 0,\n                        discount: 0,\n                        taxRate: 14,\n                        hasTax: true,\n                        total: 0,\n                        isCustomized: false,\n                        customizations: null,\n                        customizationDetails: []\n                    }\n                ]\n            }));\n    };\n    const removeItem = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: prev.items.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateItem = (index, field, value)=>{\n        setFormData((prev)=>{\n            const newItems = [\n                ...prev.items\n            ];\n            newItems[index] = {\n                ...newItems[index],\n                [field]: value\n            };\n            // Auto-calculate total\n            if (field === \"productId\") {\n                const product = products.find((p)=>p.id === value);\n                if (product) {\n                    newItems[index].unitPrice = parseFloat(product.unitPrice);\n                }\n            }\n            if (field === \"quantity\" || field === \"unitPrice\" || field === \"discount\") {\n                const item = newItems[index];\n                const subtotal = (parseFloat(item.quantity) || 0) * (parseFloat(item.unitPrice) || 0);\n                const discountAmount = subtotal * ((parseFloat(item.discount) || 0) / 100);\n                newItems[index].total = subtotal - discountAmount;\n            }\n            return {\n                ...prev,\n                items: newItems\n            };\n        });\n    };\n    const calculateTotals = ()=>{\n        const subtotal = formData.items.reduce((sum, item)=>sum + (parseFloat(item.total) || 0), 0);\n        const taxAmount = subtotal * 0.14; // 14% tax\n        const total = subtotal + taxAmount;\n        return {\n            subtotal,\n            taxAmount,\n            total\n        };\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.customerId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى اختيار العميل\");\n            return;\n        }\n        if (formData.items.length === 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى إضافة عنصر واحد على الأقل\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const { subtotal, taxAmount, total } = calculateTotals();\n            const quoteData = {\n                ...formData,\n                subtotal,\n                taxAmount,\n                total,\n                status: \"DRAFT\"\n            };\n            const response = quote ? await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].put(\"\".concat(\"http://localhost:3070\", \"/api/quotes/\").concat(quote.id), quoteData) : await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"\".concat(\"http://localhost:3070\", \"/api/quotes\"), quoteData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.data.message || (quote ? \"تم تحديث عرض السعر\" : \"تم إنشاء عرض السعر\"));\n            onSave(response.data.quote);\n            onClose();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"حدث خطأ\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    const { subtotal, taxAmount, total } = calculateTotals();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: quote ? \"تعديل عرض السعر\" : \"عرض سعر جديد\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.XMarkIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"العميل *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            name: \"customerId\",\n                                            value: formData.customerId,\n                                            onChange: handleChange,\n                                            className: \"form-input\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"اختر العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: customer.id,\n                                                        children: customer.name\n                                                    }, customer.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"صالح حتى *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            name: \"validUntil\",\n                                            value: formData.validUntil,\n                                            onChange: handleChange,\n                                            className: \"form-input\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"العناصر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addItem,\n                                            className: \"btn-primary flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PlusIcon, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"إضافة عنصر\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: formData.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-12 gap-4 items-end p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"المنتج\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: item.productId,\n                                                            onChange: (e)=>updateItem(index, \"productId\", e.target.value),\n                                                            className: \"form-input\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر المنتج\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: product.id,\n                                                                        children: product.name\n                                                                    }, product.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"الكمية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.quantity,\n                                                            onChange: (e)=>updateItem(index, \"quantity\", e.target.value),\n                                                            className: \"form-input\",\n                                                            min: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"السعر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.unitPrice,\n                                                            onChange: (e)=>updateItem(index, \"unitPrice\", e.target.value),\n                                                            className: \"form-input\",\n                                                            step: \"0.01\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"خصم %\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.discount,\n                                                            onChange: (e)=>updateItem(index, \"discount\", e.target.value),\n                                                            className: \"form-input\",\n                                                            min: \"0\",\n                                                            max: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"الإجمالي\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900 py-2\",\n                                                            children: [\n                                                                \"$\",\n                                                                (item.total || 0).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>removeItem(index),\n                                                        className: \"text-red-600 hover:text-red-800\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.TrashIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"المجموع الفرعي:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    subtotal.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"الضريبة (14%):\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    taxAmount.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between font-bold text-lg border-t pt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"الإجمالي:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"form-label\",\n                                    children: \"ملاحظات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    name: \"notes\",\n                                    value: formData.notes,\n                                    onChange: handleChange,\n                                    className: \"form-input\",\n                                    rows: \"3\",\n                                    placeholder: \"ملاحظات إضافية...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"btn-secondary\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"btn-primary\",\n                                    children: loading ? \"جاري الحفظ...\" : quote ? \"تحديث\" : \"إنشاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n        lineNumber: 175,\n        columnNumber: 5\n    }, this);\n}\n_s(QuoteModal, \"SuIipWdoMEzVJgo0Mdy9Wkj1NxA=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = QuoteModal;\nvar _c;\n$RefreshReg$(_c, \"QuoteModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/sales/QuoteModal.js\n"));

/***/ })

});