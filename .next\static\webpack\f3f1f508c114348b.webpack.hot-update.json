{"c": ["webpack"], "r": ["pages/purchases", "pages/accounting"], "m": ["./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js", "./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Cpurchases.js&page=%2Fpurchases!", "./pages/purchases.js", "__barrel_optimize__?names=CurrencyDollarIcon,EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,ShoppingBagIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./components/AccountingModals.js", "./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js", "./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js", "./node_modules/@heroicons/react/24/outline/esm/BanknotesIcon.js", "./node_modules/@heroicons/react/24/outline/esm/BuildingLibraryIcon.js", "./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js", "./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Caccounting.js&page=%2Faccounting!", "./pages/accounting.js", "__barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ArrowsRightLeftIcon,BanknotesIcon,BuildingLibraryIcon,CreditCardIcon,CurrencyDollarIcon,MagnifyingGlassIcon,PlusIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=ArrowsRightLeftIcon,CurrencyDollarIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js"]}