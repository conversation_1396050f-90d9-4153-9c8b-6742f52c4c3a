"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "./components/sales/QuoteModal.js":
/*!****************************************!*\
  !*** ./components/sales/QuoteModal.js ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ QuoteModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CogIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=CogIcon,PlusIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _ProductCustomizerAdvanced__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ProductCustomizerAdvanced */ \"./components/sales/ProductCustomizerAdvanced.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction QuoteModal(param) {\n    let { isOpen, onClose, onSave, quote = null } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: \"\",\n        validUntil: \"\",\n        notes: \"\",\n        items: []\n    });\n    // Customization states\n    const [showCustomizer, setShowCustomizer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load customers and products\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            loadCustomers();\n            loadProducts();\n            if (quote) {\n                setFormData({\n                    customerId: quote.customerId || \"\",\n                    validUntil: quote.validUntil ? quote.validUntil.split(\"T\")[0] : \"\",\n                    notes: quote.notes || \"\",\n                    items: quote.items || []\n                });\n            } else {\n                // Set default valid until date (7 days from now)\n                const validUntil = new Date();\n                validUntil.setDate(validUntil.getDate() + 7);\n                setFormData((prev)=>({\n                        ...prev,\n                        validUntil: validUntil.toISOString().split(\"T\")[0]\n                    }));\n            }\n        }\n    }, [\n        isOpen,\n        quote\n    ]);\n    const loadCustomers = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/customers\"));\n            setCustomers(response.data.customers || []);\n        } catch (error) {\n            console.error(\"Error loading customers:\", error);\n        }\n    };\n    const loadProducts = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/products\"));\n            setProducts(response.data.products || []);\n        } catch (error) {\n            console.error(\"Error loading products:\", error);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const addItem = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: [\n                    ...prev.items,\n                    {\n                        productId: \"\",\n                        productName: \"\",\n                        quantity: 1,\n                        unitPrice: 0,\n                        discount: 0,\n                        taxRate: 14,\n                        hasTax: true,\n                        total: 0,\n                        isCustomized: false,\n                        customizations: null,\n                        customizationDetails: []\n                    }\n                ]\n            }));\n    };\n    const removeItem = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: prev.items.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateItem = (index, field, value)=>{\n        setFormData((prev)=>{\n            const newItems = [\n                ...prev.items\n            ];\n            newItems[index] = {\n                ...newItems[index],\n                [field]: value\n            };\n            // Auto-calculate total\n            if (field === \"productId\") {\n                const product = products.find((p)=>p.id === value);\n                if (product) {\n                    newItems[index].unitPrice = parseFloat(product.unitPrice);\n                }\n            }\n            if (field === \"quantity\" || field === \"unitPrice\" || field === \"discount\") {\n                const item = newItems[index];\n                const subtotal = (parseFloat(item.quantity) || 0) * (parseFloat(item.unitPrice) || 0);\n                const discountAmount = subtotal * ((parseFloat(item.discount) || 0) / 100);\n                newItems[index].total = subtotal - discountAmount;\n            }\n            return {\n                ...prev,\n                items: newItems\n            };\n        });\n    };\n    const calculateTotals = ()=>{\n        const subtotal = formData.items.reduce((sum, item)=>sum + (parseFloat(item.total) || 0), 0);\n        const taxAmount = subtotal * 0.14; // 14% tax\n        const total = subtotal + taxAmount;\n        return {\n            subtotal,\n            taxAmount,\n            total\n        };\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.customerId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى اختيار العميل\");\n            return;\n        }\n        if (formData.items.length === 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى إضافة عنصر واحد على الأقل\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const { subtotal, taxAmount, total } = calculateTotals();\n            const quoteData = {\n                ...formData,\n                subtotal,\n                taxAmount,\n                total,\n                status: \"DRAFT\"\n            };\n            const response = quote ? await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].put(\"\".concat(\"http://localhost:3070\", \"/api/quotes/\").concat(quote.id), quoteData) : await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"\".concat(\"http://localhost:3070\", \"/api/quotes\"), quoteData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.data.message || (quote ? \"تم تحديث عرض السعر\" : \"تم إنشاء عرض السعر\"));\n            onSave(response.data.quote);\n            onClose();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"حدث خطأ\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    const { subtotal, taxAmount, total } = calculateTotals();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: quote ? \"تعديل عرض السعر\" : \"عرض سعر جديد\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.XMarkIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"العميل *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            name: \"customerId\",\n                                            value: formData.customerId,\n                                            onChange: handleChange,\n                                            className: \"form-input\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"اختر العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: customer.id,\n                                                        children: customer.name\n                                                    }, customer.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"صالح حتى *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            name: \"validUntil\",\n                                            value: formData.validUntil,\n                                            onChange: handleChange,\n                                            className: \"form-input\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"العناصر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addItem,\n                                            className: \"btn-primary flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PlusIcon, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"إضافة عنصر\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: formData.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-12 gap-4 items-end p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"المنتج\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: item.productId,\n                                                            onChange: (e)=>updateItem(index, \"productId\", e.target.value),\n                                                            className: \"form-input\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر المنتج\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: product.id,\n                                                                        children: product.name\n                                                                    }, product.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"الكمية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.quantity,\n                                                            onChange: (e)=>updateItem(index, \"quantity\", e.target.value),\n                                                            className: \"form-input\",\n                                                            min: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"السعر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.unitPrice,\n                                                            onChange: (e)=>updateItem(index, \"unitPrice\", e.target.value),\n                                                            className: \"form-input\",\n                                                            step: \"0.01\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"خصم %\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.discount,\n                                                            onChange: (e)=>updateItem(index, \"discount\", e.target.value),\n                                                            className: \"form-input\",\n                                                            min: \"0\",\n                                                            max: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"الإجمالي\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900 py-2\",\n                                                            children: [\n                                                                \"$\",\n                                                                (item.total || 0).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>removeItem(index),\n                                                        className: \"text-red-600 hover:text-red-800\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.TrashIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"المجموع الفرعي:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    subtotal.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"الضريبة (14%):\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    taxAmount.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between font-bold text-lg border-t pt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"الإجمالي:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"form-label\",\n                                    children: \"ملاحظات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    name: \"notes\",\n                                    value: formData.notes,\n                                    onChange: handleChange,\n                                    className: \"form-input\",\n                                    rows: \"3\",\n                                    placeholder: \"ملاحظات إضافية...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"btn-secondary\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"btn-primary\",\n                                    children: loading ? \"جاري الحفظ...\" : quote ? \"تحديث\" : \"إنشاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s(QuoteModal, \"rwn8c/8tmO85V7gLP0UzTJ2M44c=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = QuoteModal;\nvar _c;\n$RefreshReg$(_c, \"QuoteModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/sales/QuoteModal.js\n"));

/***/ })

});