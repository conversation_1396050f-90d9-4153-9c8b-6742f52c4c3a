"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/reports",{

/***/ "./pages/reports.js":
/*!**************************!*\
  !*** ./pages/reports.js ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: function() { return /* binding */ __N_SSG; },\n/* harmony export */   \"default\": function() { return /* binding */ Reports; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,CubeIcon,CurrencyDollarIcon,DocumentTextIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,CubeIcon,CurrencyDollarIcon,DocumentTextIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/LoadingSpinner */ \"./components/LoadingSpinner.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Reports() {\n    var _reportTypes_find, _branches_find;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [selectedReport, setSelectedReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"sales\");\n    const [dateFrom, setDateFrom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [dateTo, setDateTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedBranch, setSelectedBranch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    // Fetch branches for filter\n    const { data: branchesData } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)(\"branches\", async ()=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"\".concat(\"http://localhost:3001\", \"/api/branches\"));\n        return response.data;\n    });\n    // Fetch report data\n    const { data: reportData, isLoading, error, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        \"reports\",\n        selectedReport,\n        dateFrom,\n        dateTo,\n        selectedBranch\n    ], async ()=>{\n        if (!dateFrom || !dateTo) return null;\n        const params = new URLSearchParams({\n            dateFrom,\n            dateTo,\n            branchId: selectedBranch\n        });\n        const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"\".concat(\"http://localhost:3001\", \"/api/reports/\").concat(selectedReport, \"?\").concat(params));\n        return response.data;\n    }, {\n        enabled: !!(dateFrom && dateTo),\n        keepPreviousData: true\n    });\n    const branches = (branchesData === null || branchesData === void 0 ? void 0 : branchesData.branches) || [];\n    const reportTypes = [\n        {\n            id: \"sales\",\n            name: t(\"reports.salesReport\"),\n            nameAr: \"تقرير المبيعات\",\n            icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ShoppingCartIcon,\n            color: \"bg-blue-500\",\n            description: \"Sales performance and revenue analysis\"\n        },\n        {\n            id: \"purchases\",\n            name: t(\"reports.purchaseReport\"),\n            nameAr: \"تقرير المشتريات\",\n            icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ShoppingBagIcon,\n            color: \"bg-green-500\",\n            description: \"Purchase orders and supplier analysis\"\n        },\n        {\n            id: \"inventory\",\n            name: t(\"reports.inventoryReport\"),\n            nameAr: \"تقرير المخزون\",\n            icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CubeIcon,\n            color: \"bg-yellow-500\",\n            description: \"Stock levels and inventory valuation\"\n        },\n        {\n            id: \"maintenance\",\n            name: t(\"reports.maintenanceReport\"),\n            nameAr: \"تقرير الصيانة\",\n            icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.WrenchScrewdriverIcon,\n            color: \"bg-purple-500\",\n            description: \"Maintenance services and performance\"\n        },\n        {\n            id: \"financial\",\n            name: t(\"reports.financialReport\"),\n            nameAr: \"التقرير المالي\",\n            icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CurrencyDollarIcon,\n            color: \"bg-indigo-500\",\n            description: \"Cash flow and financial analysis\"\n        },\n        {\n            id: \"customers\",\n            name: t(\"reports.customerReport\"),\n            nameAr: \"تقرير العملاء\",\n            icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.UsersIcon,\n            color: \"bg-pink-500\",\n            description: \"Customer behavior and revenue analysis\"\n        }\n    ];\n    const handleGenerateReport = ()=>{\n        if (dateFrom && dateTo) {\n            refetch();\n        }\n    };\n    const handleExportReport = (format)=>{\n        // TODO: Implement export functionality\n        console.log(\"Exporting \".concat(selectedReport, \" report as \").concat(format));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: t(\"reports.title\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-600\",\n                            children: \"إنشاء تقارير أعمال شاملة وتحليلات\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Select Report Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: reportTypes.map((report)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative rounded-lg border-2 cursor-pointer transition-all \".concat(selectedReport === report.id ? \"border-primary-500 bg-primary-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                onClick: ()=>setSelectedReport(report.id),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 p-2 rounded-lg \".concat(report.color),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(report.icon, {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4 rtl:ml-0 rtl:mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: report.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: report.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            }, report.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"Report Filters\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CalendarIcon, {\n                                                className: \"h-4 w-4 inline mr-1 rtl:mr-0 rtl:ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, this),\n                                            t(\"reports.from\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        value: dateFrom,\n                                        onChange: (e)=>setDateFrom(e.target.value),\n                                        className: \"form-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CalendarIcon, {\n                                                className: \"h-4 w-4 inline mr-1 rtl:mr-0 rtl:ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, this),\n                                            t(\"reports.to\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        value: dateTo,\n                                        onChange: (e)=>setDateTo(e.target.value),\n                                        className: \"form-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Branch\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedBranch,\n                                        onChange: (e)=>setSelectedBranch(e.target.value),\n                                        className: \"form-input\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"All Branches\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this),\n                                            branches.map((branch)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: branch.id,\n                                                    children: branch.name\n                                                }, branch.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGenerateReport,\n                                    disabled: !dateFrom || !dateTo,\n                                    className: \"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ChartBarIcon, {\n                                            className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        t(\"reports.generate\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-12 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: \"large\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Generating report...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-12 rounded-lg shadow text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.DocumentTextIcon, {\n                            className: \"h-12 w-12 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium\",\n                            children: \"Error generating report\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm\",\n                            children: \"Please try again or contact support.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                    lineNumber: 228,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                lineNumber: 227,\n                columnNumber: 9\n            }, this),\n            reportData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: (_reportTypes_find = reportTypes.find((r)=>r.id === selectedReport)) === null || _reportTypes_find === void 0 ? void 0 : _reportTypes_find.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: [\n                                                dateFrom,\n                                                \" to \",\n                                                dateTo,\n                                                selectedBranch !== \"all\" && \" • \".concat((_branches_find = branches.find((b)=>b.id === selectedBranch)) === null || _branches_find === void 0 ? void 0 : _branches_find.name)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleExportReport(\"pdf\"),\n                                            className: \"btn-secondary btn-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ArrowDownTrayIcon, {\n                                                    className: \"h-4 w-4 mr-1 rtl:mr-0 rtl:ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"PDF\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleExportReport(\"excel\"),\n                                            className: \"btn-secondary btn-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ArrowDownTrayIcon, {\n                                                    className: \"h-4 w-4 mr-1 rtl:mr-0 rtl:ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Excel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            reportData.metrics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                                children: Object.entries(reportData.metrics).map((param)=>{\n                                    let [key, value] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-sm font-medium text-gray-500 capitalize\",\n                                                children: key.replace(/([A-Z])/g, \" $1\").trim()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 276,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-1 text-2xl font-semibold text-gray-900\",\n                                                children: typeof value === \"number\" && key.toLowerCase().includes(\"revenue\") || key.toLowerCase().includes(\"cost\") || key.toLowerCase().includes(\"total\") && key.toLowerCase().includes(\"value\") ? \"$\".concat(value.toFixed(2)) : typeof value === \"number\" && key.toLowerCase().includes(\"rate\") ? \"\".concat(value.toFixed(1), \"%\") : value\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, key, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 275,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, this),\n                            reportData.orders && reportData.orders.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"Order Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"Customer/Supplier\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"Total\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: reportData.orders.slice(0, 10).map((order)=>{\n                                                    var _order_customer, _order_supplier;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"font-medium\",\n                                                                children: order.orderNumber\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: new Date(order.orderDate || order.receivedDate).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: ((_order_customer = order.customer) === null || _order_customer === void 0 ? void 0 : _order_customer.name) || ((_order_supplier = order.supplier) === null || _order_supplier === void 0 ? void 0 : _order_supplier.name)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    (order.total || order.actualCost || 0).toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"badge badge-secondary\",\n                                                                    children: order.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, order.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 304,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this),\n                                    reportData.orders.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center text-sm text-gray-500\",\n                                        children: [\n                                            \"Showing 10 of \",\n                                            reportData.orders.length,\n                                            \" records\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 319,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 293,\n                                columnNumber: 15\n                            }, this),\n                            reportData.inventory && reportData.inventory.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"table\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"Product\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"Branch\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"Current Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 331,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                            lineNumber: 330,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: reportData.inventory.slice(0, 10).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            children: item.product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            children: item.branch.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            children: [\n                                                                item.quantity,\n                                                                \" \",\n                                                                item.product.unit\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            children: [\n                                                                \"$\",\n                                                                (item.quantity * item.product.costPrice).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"badge \".concat(item.quantity === 0 ? \"badge-danger\" : item.quantity <= item.minStock ? \"badge-warning\" : \"badge-success\"),\n                                                                children: item.quantity === 0 ? \"Out of Stock\" : item.quantity <= item.minStock ? \"Low Stock\" : \"In Stock\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                            lineNumber: 339,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                    lineNumber: 329,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 328,\n                                columnNumber: 15\n                            }, this),\n                            (!reportData.orders || reportData.orders.length === 0) && (!reportData.inventory || reportData.inventory.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.DocumentTextIcon, {\n                                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 366,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"mt-2 text-sm font-medium text-gray-900\",\n                                        children: \"No data found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 367,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-gray-500\",\n                                        children: \"No data available for the selected date range and filters.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 368,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 365,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this),\n            !dateFrom || !dateTo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-12 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ChartBarIcon, {\n                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"mt-2 text-sm font-medium text-gray-900\",\n                        children: \"Select date range\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-gray-500\",\n                        children: \"Choose a date range and report type to generate your report.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                lineNumber: 379,\n                columnNumber: 9\n            }, this) : null\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s(Reports, \"fsvpDMX+lTo0Z5rfn2mnd8KE7IY=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery\n    ];\n});\n_c = Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9yZXBvcnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBaUM7QUFDYTtBQUVQO0FBQ2I7QUFZVztBQUNxQjs7QUFFM0MsU0FBU2U7UUErTkxDLG1CQUlrQ0M7O0lBbE9uRCxNQUFNLEVBQUVDLENBQUMsRUFBRSxHQUFHakIsNERBQWNBLENBQUM7SUFDN0IsTUFBTSxDQUFDa0IsZ0JBQWdCQyxrQkFBa0IsR0FBR3BCLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ3FCLFVBQVVDLFlBQVksR0FBR3RCLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ3VCLFFBQVFDLFVBQVUsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU0sQ0FBQ3lCLGdCQUFnQkMsa0JBQWtCLEdBQUcxQiwrQ0FBUUEsQ0FBQztJQUVyRCw0QkFBNEI7SUFDNUIsTUFBTSxFQUFFMkIsTUFBTUMsWUFBWSxFQUFFLEdBQUcxQixxREFBUUEsQ0FBQyxZQUFZO1FBQ2xELE1BQU0yQixXQUFXLE1BQU0xQixpREFBUyxDQUFDLEdBQW1DLE9BQWhDNEIsdUJBQStCLEVBQUM7UUFDcEUsT0FBT0YsU0FBU0YsSUFBSTtJQUN0QjtJQUVBLG9CQUFvQjtJQUNwQixNQUFNLEVBQUVBLE1BQU1PLFVBQVUsRUFBRUMsU0FBUyxFQUFFQyxLQUFLLEVBQUVDLE9BQU8sRUFBRSxHQUFHbkMscURBQVFBLENBQzlEO1FBQUM7UUFBV2lCO1FBQWdCRTtRQUFVRTtRQUFRRTtLQUFlLEVBQzdEO1FBQ0UsSUFBSSxDQUFDSixZQUFZLENBQUNFLFFBQVEsT0FBTztRQUVqQyxNQUFNZSxTQUFTLElBQUlDLGdCQUFnQjtZQUNqQ2xCO1lBQ0FFO1lBQ0FpQixVQUFVZjtRQUNaO1FBRUEsTUFBTUksV0FBVyxNQUFNMUIsaURBQVMsQ0FBQyxHQUFrRGdCLE9BQS9DWSx1QkFBK0IsRUFBQyxpQkFBaUNPLE9BQWxCbkIsZ0JBQWUsS0FBVSxPQUFQbUI7UUFDckcsT0FBT1QsU0FBU0YsSUFBSTtJQUN0QixHQUNBO1FBQ0VjLFNBQVMsQ0FBQyxDQUFFcEIsQ0FBQUEsWUFBWUUsTUFBSztRQUM3Qm1CLGtCQUFrQjtJQUNwQjtJQUdGLE1BQU16QixXQUFXVyxDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWNYLFFBQVEsS0FBSSxFQUFFO0lBRTdDLE1BQU1ELGNBQWM7UUFDbEI7WUFDRTJCLElBQUk7WUFDSkMsTUFBTTFCLEVBQUU7WUFDUjJCLFFBQVE7WUFDUkMsTUFBTXZDLHlQQUFnQkE7WUFDdEJ3QyxPQUFPO1lBQ1BDLGFBQWE7UUFDZjtRQUNBO1lBQ0VMLElBQUk7WUFDSkMsTUFBTTFCLEVBQUU7WUFDUjJCLFFBQVE7WUFDUkMsTUFBTXRDLHdQQUFlQTtZQUNyQnVDLE9BQU87WUFDUEMsYUFBYTtRQUNmO1FBQ0E7WUFDRUwsSUFBSTtZQUNKQyxNQUFNMUIsRUFBRTtZQUNSMkIsUUFBUTtZQUNSQyxNQUFNbkMsaVBBQVFBO1lBQ2RvQyxPQUFPO1lBQ1BDLGFBQWE7UUFDZjtRQUNBO1lBQ0VMLElBQUk7WUFDSkMsTUFBTTFCLEVBQUU7WUFDUjJCLFFBQVE7WUFDUkMsTUFBTXJDLDhQQUFxQkE7WUFDM0JzQyxPQUFPO1lBQ1BDLGFBQWE7UUFDZjtRQUNBO1lBQ0VMLElBQUk7WUFDSkMsTUFBTTFCLEVBQUU7WUFDUjJCLFFBQVE7WUFDUkMsTUFBTXhDLDJQQUFrQkE7WUFDeEJ5QyxPQUFPO1lBQ1BDLGFBQWE7UUFDZjtRQUNBO1lBQ0VMLElBQUk7WUFDSkMsTUFBTTFCLEVBQUU7WUFDUjJCLFFBQVE7WUFDUkMsTUFBTXBDLGtQQUFTQTtZQUNmcUMsT0FBTztZQUNQQyxhQUFhO1FBQ2Y7S0FDRDtJQUVELE1BQU1DLHVCQUF1QjtRQUMzQixJQUFJNUIsWUFBWUUsUUFBUTtZQUN0QmM7UUFDRjtJQUNGO0lBRUEsTUFBTWEscUJBQXFCLENBQUNDO1FBQzFCLHVDQUF1QztRQUN2Q0MsUUFBUUMsR0FBRyxDQUFDLGFBQXlDRixPQUE1QmhDLGdCQUFlLGVBQW9CLE9BQVBnQztJQUN2RDtJQUVBLHFCQUNFLDhEQUFDRztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEOztzQ0FDQyw4REFBQ0U7NEJBQUdELFdBQVU7c0NBQW9DckMsRUFBRTs7Ozs7O3NDQUNwRCw4REFBQ3VDOzRCQUFFRixXQUFVO3NDQUE2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTzlDLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNHO3dCQUFHSCxXQUFVO2tDQUF5Qzs7Ozs7O2tDQUN2RCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1p2QyxZQUFZMkMsR0FBRyxDQUFDLENBQUNDLHVCQUNoQiw4REFBQ047Z0NBRUNDLFdBQVcsOERBSVYsT0FIQ3BDLG1CQUFtQnlDLE9BQU9qQixFQUFFLEdBQ3hCLHFDQUNBO2dDQUVOa0IsU0FBUyxJQUFNekMsa0JBQWtCd0MsT0FBT2pCLEVBQUU7MENBRTFDLDRFQUFDVztvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVyxnQ0FBNkMsT0FBYkssT0FBT2IsS0FBSzswREFDMUQsNEVBQUNhLE9BQU9kLElBQUk7b0RBQUNTLFdBQVU7Ozs7Ozs7Ozs7OzBEQUV6Qiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDTzt3REFBR1AsV0FBVTtrRUFBcUNLLE9BQU9oQixJQUFJOzs7Ozs7a0VBQzlELDhEQUFDYTt3REFBRUYsV0FBVTtrRUFBeUJLLE9BQU9aLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQWZ6RFksT0FBT2pCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBeUJ0Qiw4REFBQ1c7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRzt3QkFBR0gsV0FBVTtrQ0FBeUM7Ozs7OztrQ0FDdkQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7O2tEQUNDLDhEQUFDUzt3Q0FBTVIsV0FBVTs7MERBQ2YsOERBQUMzQyxxUEFBWUE7Z0RBQUMyQyxXQUFVOzs7Ozs7NENBQ3ZCckMsRUFBRTs7Ozs7OztrREFFTCw4REFBQzhDO3dDQUNDQyxNQUFLO3dDQUNMQyxPQUFPN0M7d0NBQ1A4QyxVQUFVLENBQUNDLElBQU05QyxZQUFZOEMsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO3dDQUMzQ1gsV0FBVTs7Ozs7Ozs7Ozs7OzBDQUdkLDhEQUFDRDs7a0RBQ0MsOERBQUNTO3dDQUFNUixXQUFVOzswREFDZiw4REFBQzNDLHFQQUFZQTtnREFBQzJDLFdBQVU7Ozs7Ozs0Q0FDdkJyQyxFQUFFOzs7Ozs7O2tEQUVMLDhEQUFDOEM7d0NBQ0NDLE1BQUs7d0NBQ0xDLE9BQU8zQzt3Q0FDUDRDLFVBQVUsQ0FBQ0MsSUFBTTVDLFVBQVU0QyxFQUFFQyxNQUFNLENBQUNILEtBQUs7d0NBQ3pDWCxXQUFVOzs7Ozs7Ozs7Ozs7MENBR2QsOERBQUNEOztrREFDQyw4REFBQ1M7d0NBQU1SLFdBQVU7a0RBQStDOzs7Ozs7a0RBR2hFLDhEQUFDZTt3Q0FDQ0osT0FBT3pDO3dDQUNQMEMsVUFBVSxDQUFDQyxJQUFNMUMsa0JBQWtCMEMsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO3dDQUNqRFgsV0FBVTs7MERBRVYsOERBQUNnQjtnREFBT0wsT0FBTTswREFBTTs7Ozs7OzRDQUNuQmpELFNBQVMwQyxHQUFHLENBQUMsQ0FBQ2EsdUJBQ2IsOERBQUNEO29EQUF1QkwsT0FBT00sT0FBTzdCLEVBQUU7OERBQ3JDNkIsT0FBTzVCLElBQUk7bURBREQ0QixPQUFPN0IsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTTVCLDhEQUFDVztnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ2tCO29DQUNDWixTQUFTWjtvQ0FDVHlCLFVBQVUsQ0FBQ3JELFlBQVksQ0FBQ0U7b0NBQ3hCZ0MsV0FBVTs7c0RBRVYsOERBQUNuRCxxUEFBWUE7NENBQUNtRCxXQUFVOzs7Ozs7d0NBQ3ZCckMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBT1ZpQiwyQkFDQyw4REFBQ21CO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ3pDLGtFQUFjQTt3QkFBQzZELE1BQUs7Ozs7OztrQ0FDckIsOERBQUNsQjt3QkFBRUYsV0FBVTtrQ0FBcUI7Ozs7Ozs7Ozs7OztZQUlyQ25CLHVCQUNDLDhEQUFDa0I7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ2xELHlQQUFnQkE7NEJBQUNrRCxXQUFVOzs7Ozs7c0NBQzVCLDhEQUFDTzs0QkFBR1AsV0FBVTtzQ0FBc0I7Ozs7OztzQ0FDcEMsOERBQUNFOzRCQUFFRixXQUFVO3NDQUFlOzs7Ozs7Ozs7Ozs7Ozs7OztZQUtqQ3JCLDRCQUNDLDhEQUFDb0I7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDs7c0RBQ0MsOERBQUNJOzRDQUFHSCxXQUFVO3VEQUNYdkMsb0JBQUFBLFlBQVk0RCxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVsQyxFQUFFLEtBQUt4Qiw2QkFBL0JILHdDQUFBQSxrQkFBZ0Q0QixJQUFJOzs7Ozs7c0RBRXZELDhEQUFDYTs0Q0FBRUYsV0FBVTs7Z0RBQ1ZsQztnREFBUztnREFBS0U7Z0RBQ2RFLG1CQUFtQixTQUFTLE1BQXdELFFBQWxEUixpQkFBQUEsU0FBUzJELElBQUksQ0FBQ0UsQ0FBQUEsSUFBS0EsRUFBRW5DLEVBQUUsS0FBS2xCLDZCQUE1QlIscUNBQUFBLGVBQTZDMkIsSUFBSTs7Ozs7Ozs7Ozs7Ozs4Q0FHeEYsOERBQUNVO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ2tCOzRDQUNDWixTQUFTLElBQU1YLG1CQUFtQjs0Q0FDbENLLFdBQVU7OzhEQUVWLDhEQUFDMUMsMFBBQWlCQTtvREFBQzBDLFdBQVU7Ozs7OztnREFBbUM7Ozs7Ozs7c0RBR2xFLDhEQUFDa0I7NENBQ0NaLFNBQVMsSUFBTVgsbUJBQW1COzRDQUNsQ0ssV0FBVTs7OERBRVYsOERBQUMxQywwUEFBaUJBO29EQUFDMEMsV0FBVTs7Ozs7O2dEQUFtQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVF4RSw4REFBQ0Q7d0JBQUlDLFdBQVU7OzRCQUVackIsV0FBVzZDLE9BQU8sa0JBQ2pCLDhEQUFDekI7Z0NBQUlDLFdBQVU7MENBQ1p5QixPQUFPQyxPQUFPLENBQUMvQyxXQUFXNkMsT0FBTyxFQUFFcEIsR0FBRyxDQUFDO3dDQUFDLENBQUN1QixLQUFLaEIsTUFBTTt5REFDbkQsOERBQUNaO3dDQUFjQyxXQUFVOzswREFDdkIsOERBQUM0QjtnREFBRzVCLFdBQVU7MERBQ1gyQixJQUFJRSxPQUFPLENBQUMsWUFBWSxPQUFPQyxJQUFJOzs7Ozs7MERBRXRDLDhEQUFDQztnREFBRy9CLFdBQVU7MERBQ1gsT0FBT1csVUFBVSxZQUFZZ0IsSUFBSUssV0FBVyxHQUFHQyxRQUFRLENBQUMsY0FBY04sSUFBSUssV0FBVyxHQUFHQyxRQUFRLENBQUMsV0FBV04sSUFBSUssV0FBVyxHQUFHQyxRQUFRLENBQUMsWUFBWU4sSUFBSUssV0FBVyxHQUFHQyxRQUFRLENBQUMsV0FDM0ssSUFBcUIsT0FBakJ0QixNQUFNdUIsT0FBTyxDQUFDLE1BQ2xCLE9BQU92QixVQUFVLFlBQVlnQixJQUFJSyxXQUFXLEdBQUdDLFFBQVEsQ0FBQyxVQUN4RCxHQUFvQixPQUFqQnRCLE1BQU11QixPQUFPLENBQUMsSUFBRyxPQUNwQnZCOzs7Ozs7O3VDQVRFZ0I7Ozs7Ozs7Ozs7OzRCQWlCZmhELFdBQVd3RCxNQUFNLElBQUl4RCxXQUFXd0QsTUFBTSxDQUFDQyxNQUFNLEdBQUcsbUJBQy9DLDhEQUFDckM7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDcUM7d0NBQU1yQyxXQUFVOzswREFDZiw4REFBQ3NDOzBEQUNDLDRFQUFDQzs7c0VBQ0MsOERBQUNDO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7c0VBQ0osOERBQUNBO3NFQUFHOzs7Ozs7Ozs7Ozs7Ozs7OzswREFHUiw4REFBQ0M7MERBQ0U5RCxXQUFXd0QsTUFBTSxDQUFDTyxLQUFLLENBQUMsR0FBRyxJQUFJdEMsR0FBRyxDQUFDLENBQUN1Qzt3REFJNUJBLGlCQUF3QkE7eUVBSC9CLDhEQUFDSjs7MEVBQ0MsOERBQUNLO2dFQUFHNUMsV0FBVTswRUFBZTJDLE1BQU1FLFdBQVc7Ozs7OzswRUFDOUMsOERBQUNEOzBFQUFJLElBQUlFLEtBQUtILE1BQU1JLFNBQVMsSUFBSUosTUFBTUssWUFBWSxFQUFFQyxrQkFBa0I7Ozs7OzswRUFDdkUsOERBQUNMOzBFQUFJRCxFQUFBQSxrQkFBQUEsTUFBTU8sUUFBUSxjQUFkUCxzQ0FBQUEsZ0JBQWdCdEQsSUFBSSxPQUFJc0Qsa0JBQUFBLE1BQU1RLFFBQVEsY0FBZFIsc0NBQUFBLGdCQUFnQnRELElBQUk7Ozs7OzswRUFDakQsOERBQUN1RDs7b0VBQUc7b0VBQUdELENBQUFBLE1BQU1TLEtBQUssSUFBSVQsTUFBTVUsVUFBVSxJQUFJLEdBQUduQixPQUFPLENBQUM7Ozs7Ozs7MEVBQ3JELDhEQUFDVTswRUFDQyw0RUFBQ1U7b0VBQUt0RCxXQUFVOzhFQUF5QjJDLE1BQU1ZLE1BQU07Ozs7Ozs7Ozs7Ozt1REFOaERaLE1BQU12RCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OztvQ0FZdEJULFdBQVd3RCxNQUFNLENBQUNDLE1BQU0sR0FBRyxvQkFDMUIsOERBQUNyQzt3Q0FBSUMsV0FBVTs7NENBQXlDOzRDQUN2Q3JCLFdBQVd3RCxNQUFNLENBQUNDLE1BQU07NENBQUM7Ozs7Ozs7Ozs7Ozs7NEJBTy9DekQsV0FBVzZFLFNBQVMsSUFBSTdFLFdBQVc2RSxTQUFTLENBQUNwQixNQUFNLEdBQUcsbUJBQ3JELDhEQUFDckM7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNxQztvQ0FBTXJDLFdBQVU7O3NEQUNmLDhEQUFDc0M7c0RBQ0MsNEVBQUNDOztrRUFDQyw4REFBQ0M7a0VBQUc7Ozs7OztrRUFDSiw4REFBQ0E7a0VBQUc7Ozs7OztrRUFDSiw4REFBQ0E7a0VBQUc7Ozs7OztrRUFDSiw4REFBQ0E7a0VBQUc7Ozs7OztrRUFDSiw4REFBQ0E7a0VBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUdSLDhEQUFDQztzREFDRTlELFdBQVc2RSxTQUFTLENBQUNkLEtBQUssQ0FBQyxHQUFHLElBQUl0QyxHQUFHLENBQUMsQ0FBQ3FELE1BQU1DLHNCQUM1Qyw4REFBQ25COztzRUFDQyw4REFBQ0s7c0VBQUlhLEtBQUtFLE9BQU8sQ0FBQ3RFLElBQUk7Ozs7OztzRUFDdEIsOERBQUN1RDtzRUFBSWEsS0FBS3hDLE1BQU0sQ0FBQzVCLElBQUk7Ozs7OztzRUFDckIsOERBQUN1RDs7Z0VBQUlhLEtBQUtHLFFBQVE7Z0VBQUM7Z0VBQUVILEtBQUtFLE9BQU8sQ0FBQ0UsSUFBSTs7Ozs7OztzRUFDdEMsOERBQUNqQjs7Z0VBQUc7Z0VBQUdhLENBQUFBLEtBQUtHLFFBQVEsR0FBR0gsS0FBS0UsT0FBTyxDQUFDRyxTQUFTLEVBQUU1QixPQUFPLENBQUM7Ozs7Ozs7c0VBQ3ZELDhEQUFDVTtzRUFDQyw0RUFBQ1U7Z0VBQUt0RCxXQUFXLFNBR2hCLE9BRkN5RCxLQUFLRyxRQUFRLEtBQUssSUFBSSxpQkFDdEJILEtBQUtHLFFBQVEsSUFBSUgsS0FBS00sUUFBUSxHQUFHLGtCQUFrQjswRUFFbEROLEtBQUtHLFFBQVEsS0FBSyxJQUFJLGlCQUN0QkgsS0FBS0csUUFBUSxJQUFJSCxLQUFLTSxRQUFRLEdBQUcsY0FBYzs7Ozs7Ozs7Ozs7O21EQVg3Q0w7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFzQmpCLEVBQUMvRSxXQUFXd0QsTUFBTSxJQUFJeEQsV0FBV3dELE1BQU0sQ0FBQ0MsTUFBTSxLQUFLLE1BQ25ELEVBQUN6RCxXQUFXNkUsU0FBUyxJQUFJN0UsV0FBVzZFLFNBQVMsQ0FBQ3BCLE1BQU0sS0FBSyxvQkFDekQsOERBQUNyQztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNsRCx5UEFBZ0JBO3dDQUFDa0QsV0FBVTs7Ozs7O2tEQUM1Qiw4REFBQ087d0NBQUdQLFdBQVU7a0RBQXlDOzs7Ozs7a0RBQ3ZELDhEQUFDRTt3Q0FBRUYsV0FBVTtrREFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVVuRCxDQUFDbEMsWUFBWSxDQUFDRSx1QkFDYiw4REFBQytCO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ25ELHFQQUFZQTt3QkFBQ21ELFdBQVU7Ozs7OztrQ0FDeEIsOERBQUNPO3dCQUFHUCxXQUFVO2tDQUF5Qzs7Ozs7O2tDQUN2RCw4REFBQ0U7d0JBQUVGLFdBQVU7a0NBQTZCOzs7Ozs7Ozs7Ozt1QkFJMUM7Ozs7Ozs7QUFHVjtHQWpYd0J4Qzs7UUFDUmQsd0RBQWNBO1FBT0dDLGlEQUFRQTtRQU1pQkEsaURBQVFBOzs7S0FkMUNhIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3BhZ2VzL3JlcG9ydHMuanM/Y2UwMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnbmV4dC1pMThuZXh0JztcbmltcG9ydCB7IHNlcnZlclNpZGVUcmFuc2xhdGlvbnMgfSBmcm9tICduZXh0LWkxOG5leHQvc2VydmVyU2lkZVRyYW5zbGF0aW9ucyc7XG5pbXBvcnQgeyB1c2VRdWVyeSB9IGZyb20gJ3JlYWN0LXF1ZXJ5JztcbmltcG9ydCBheGlvcyBmcm9tICdheGlvcyc7XG5pbXBvcnQge1xuICBDaGFydEJhckljb24sXG4gIERvY3VtZW50VGV4dEljb24sXG4gIEN1cnJlbmN5RG9sbGFySWNvbixcbiAgU2hvcHBpbmdDYXJ0SWNvbixcbiAgU2hvcHBpbmdCYWdJY29uLFxuICBXcmVuY2hTY3Jld2RyaXZlckljb24sXG4gIFVzZXJzSWNvbixcbiAgQ3ViZUljb24sXG4gIENhbGVuZGFySWNvbixcbiAgQXJyb3dEb3duVHJheUljb24sXG59IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5pbXBvcnQgTG9hZGluZ1NwaW5uZXIgZnJvbSAnLi4vY29tcG9uZW50cy9Mb2FkaW5nU3Bpbm5lcic7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJlcG9ydHMoKSB7XG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oJ2NvbW1vbicpO1xuICBjb25zdCBbc2VsZWN0ZWRSZXBvcnQsIHNldFNlbGVjdGVkUmVwb3J0XSA9IHVzZVN0YXRlKCdzYWxlcycpO1xuICBjb25zdCBbZGF0ZUZyb20sIHNldERhdGVGcm9tXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2RhdGVUbywgc2V0RGF0ZVRvXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3NlbGVjdGVkQnJhbmNoLCBzZXRTZWxlY3RlZEJyYW5jaF0gPSB1c2VTdGF0ZSgnYWxsJyk7XG5cbiAgLy8gRmV0Y2ggYnJhbmNoZXMgZm9yIGZpbHRlclxuICBjb25zdCB7IGRhdGE6IGJyYW5jaGVzRGF0YSB9ID0gdXNlUXVlcnkoJ2JyYW5jaGVzJywgYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkx9L2FwaS9icmFuY2hlc2ApO1xuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICB9KTtcblxuICAvLyBGZXRjaCByZXBvcnQgZGF0YVxuICBjb25zdCB7IGRhdGE6IHJlcG9ydERhdGEsIGlzTG9hZGluZywgZXJyb3IsIHJlZmV0Y2ggfSA9IHVzZVF1ZXJ5KFxuICAgIFsncmVwb3J0cycsIHNlbGVjdGVkUmVwb3J0LCBkYXRlRnJvbSwgZGF0ZVRvLCBzZWxlY3RlZEJyYW5jaF0sXG4gICAgYXN5bmMgKCkgPT4ge1xuICAgICAgaWYgKCFkYXRlRnJvbSB8fCAhZGF0ZVRvKSByZXR1cm4gbnVsbDtcbiAgICAgIFxuICAgICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh7XG4gICAgICAgIGRhdGVGcm9tLFxuICAgICAgICBkYXRlVG8sXG4gICAgICAgIGJyYW5jaElkOiBzZWxlY3RlZEJyYW5jaCxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMfS9hcGkvcmVwb3J0cy8ke3NlbGVjdGVkUmVwb3J0fT8ke3BhcmFtc31gKTtcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xuICAgIH0sXG4gICAge1xuICAgICAgZW5hYmxlZDogISEoZGF0ZUZyb20gJiYgZGF0ZVRvKSxcbiAgICAgIGtlZXBQcmV2aW91c0RhdGE6IHRydWUsXG4gICAgfVxuICApO1xuXG4gIGNvbnN0IGJyYW5jaGVzID0gYnJhbmNoZXNEYXRhPy5icmFuY2hlcyB8fCBbXTtcblxuICBjb25zdCByZXBvcnRUeXBlcyA9IFtcbiAgICB7XG4gICAgICBpZDogJ3NhbGVzJyxcbiAgICAgIG5hbWU6IHQoJ3JlcG9ydHMuc2FsZXNSZXBvcnQnKSxcbiAgICAgIG5hbWVBcjogJ9iq2YLYsdmK2LEg2KfZhNmF2KjZiti52KfYqicsXG4gICAgICBpY29uOiBTaG9wcGluZ0NhcnRJY29uLFxuICAgICAgY29sb3I6ICdiZy1ibHVlLTUwMCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ1NhbGVzIHBlcmZvcm1hbmNlIGFuZCByZXZlbnVlIGFuYWx5c2lzJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdwdXJjaGFzZXMnLFxuICAgICAgbmFtZTogdCgncmVwb3J0cy5wdXJjaGFzZVJlcG9ydCcpLFxuICAgICAgbmFtZUFyOiAn2KrZgtix2YrYsSDYp9mE2YXYtNiq2LHZitin2KonLFxuICAgICAgaWNvbjogU2hvcHBpbmdCYWdJY29uLFxuICAgICAgY29sb3I6ICdiZy1ncmVlbi01MDAnLFxuICAgICAgZGVzY3JpcHRpb246ICdQdXJjaGFzZSBvcmRlcnMgYW5kIHN1cHBsaWVyIGFuYWx5c2lzJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdpbnZlbnRvcnknLFxuICAgICAgbmFtZTogdCgncmVwb3J0cy5pbnZlbnRvcnlSZXBvcnQnKSxcbiAgICAgIG5hbWVBcjogJ9iq2YLYsdmK2LEg2KfZhNmF2K7YstmI2YYnLFxuICAgICAgaWNvbjogQ3ViZUljb24sXG4gICAgICBjb2xvcjogJ2JnLXllbGxvdy01MDAnLFxuICAgICAgZGVzY3JpcHRpb246ICdTdG9jayBsZXZlbHMgYW5kIGludmVudG9yeSB2YWx1YXRpb24nXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ21haW50ZW5hbmNlJyxcbiAgICAgIG5hbWU6IHQoJ3JlcG9ydHMubWFpbnRlbmFuY2VSZXBvcnQnKSxcbiAgICAgIG5hbWVBcjogJ9iq2YLYsdmK2LEg2KfZhNi12YrYp9mG2KknLFxuICAgICAgaWNvbjogV3JlbmNoU2NyZXdkcml2ZXJJY29uLFxuICAgICAgY29sb3I6ICdiZy1wdXJwbGUtNTAwJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnTWFpbnRlbmFuY2Ugc2VydmljZXMgYW5kIHBlcmZvcm1hbmNlJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdmaW5hbmNpYWwnLFxuICAgICAgbmFtZTogdCgncmVwb3J0cy5maW5hbmNpYWxSZXBvcnQnKSxcbiAgICAgIG5hbWVBcjogJ9in2YTYqtmC2LHZitixINin2YTZhdin2YTZiicsXG4gICAgICBpY29uOiBDdXJyZW5jeURvbGxhckljb24sXG4gICAgICBjb2xvcjogJ2JnLWluZGlnby01MDAnLFxuICAgICAgZGVzY3JpcHRpb246ICdDYXNoIGZsb3cgYW5kIGZpbmFuY2lhbCBhbmFseXNpcydcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnY3VzdG9tZXJzJyxcbiAgICAgIG5hbWU6IHQoJ3JlcG9ydHMuY3VzdG9tZXJSZXBvcnQnKSxcbiAgICAgIG5hbWVBcjogJ9iq2YLYsdmK2LEg2KfZhNi52YXZhNin2KEnLFxuICAgICAgaWNvbjogVXNlcnNJY29uLFxuICAgICAgY29sb3I6ICdiZy1waW5rLTUwMCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ0N1c3RvbWVyIGJlaGF2aW9yIGFuZCByZXZlbnVlIGFuYWx5c2lzJ1xuICAgIH1cbiAgXTtcblxuICBjb25zdCBoYW5kbGVHZW5lcmF0ZVJlcG9ydCA9ICgpID0+IHtcbiAgICBpZiAoZGF0ZUZyb20gJiYgZGF0ZVRvKSB7XG4gICAgICByZWZldGNoKCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUV4cG9ydFJlcG9ydCA9IChmb3JtYXQpID0+IHtcbiAgICAvLyBUT0RPOiBJbXBsZW1lbnQgZXhwb3J0IGZ1bmN0aW9uYWxpdHlcbiAgICBjb25zb2xlLmxvZyhgRXhwb3J0aW5nICR7c2VsZWN0ZWRSZXBvcnR9IHJlcG9ydCBhcyAke2Zvcm1hdH1gKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7LyogUGFnZSBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPnt0KCdyZXBvcnRzLnRpdGxlJyl9PC9oMT5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAg2KXZhti02KfYoSDYqtmC2KfYsdmK2LEg2KPYudmF2KfZhCDYtNin2YXZhNipINmI2KrYrdmE2YrZhNin2KpcbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBSZXBvcnQgVHlwZSBTZWxlY3Rpb24gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHAtNiByb3VuZGVkLWxnIHNoYWRvd1wiPlxuICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTRcIj5TZWxlY3QgUmVwb3J0IFR5cGU8L2gyPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICB7cmVwb3J0VHlwZXMubWFwKChyZXBvcnQpID0+IChcbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAga2V5PXtyZXBvcnQuaWR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YHJlbGF0aXZlIHJvdW5kZWQtbGcgYm9yZGVyLTIgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1hbGwgJHtcbiAgICAgICAgICAgICAgICBzZWxlY3RlZFJlcG9ydCA9PT0gcmVwb3J0LmlkXG4gICAgICAgICAgICAgICAgICA/ICdib3JkZXItcHJpbWFyeS01MDAgYmctcHJpbWFyeS01MCdcbiAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTIwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZFJlcG9ydChyZXBvcnQuaWQpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleC1zaHJpbmstMCBwLTIgcm91bmRlZC1sZyAke3JlcG9ydC5jb2xvcn1gfT5cbiAgICAgICAgICAgICAgICAgICAgPHJlcG9ydC5pY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtNCBydGw6bWwtMCBydGw6bXItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e3JlcG9ydC5uYW1lfTwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPntyZXBvcnQuZGVzY3JpcHRpb259PC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBSZXBvcnQgRmlsdGVycyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcC02IHJvdW5kZWQtbGcgc2hhZG93XCI+XG4gICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItNFwiPlJlcG9ydCBGaWx0ZXJzPC9oMj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC00XCI+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICA8Q2FsZW5kYXJJY29uIGNsYXNzTmFtZT1cImgtNCB3LTQgaW5saW5lIG1yLTEgcnRsOm1yLTAgcnRsOm1sLTFcIiAvPlxuICAgICAgICAgICAgICB7dCgncmVwb3J0cy5mcm9tJyl9XG4gICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcbiAgICAgICAgICAgICAgdmFsdWU9e2RhdGVGcm9tfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldERhdGVGcm9tKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dFwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgPENhbGVuZGFySWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IGlubGluZSBtci0xIHJ0bDptci0wIHJ0bDptbC0xXCIgLz5cbiAgICAgICAgICAgICAge3QoJ3JlcG9ydHMudG8nKX1cbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxuICAgICAgICAgICAgICB2YWx1ZT17ZGF0ZVRvfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldERhdGVUbyhlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvcm0taW5wdXRcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgIEJyYW5jaFxuICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkQnJhbmNofVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlbGVjdGVkQnJhbmNoKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhbGxcIj5BbGwgQnJhbmNoZXM8L29wdGlvbj5cbiAgICAgICAgICAgICAge2JyYW5jaGVzLm1hcCgoYnJhbmNoKSA9PiAoXG4gICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2JyYW5jaC5pZH0gdmFsdWU9e2JyYW5jaC5pZH0+XG4gICAgICAgICAgICAgICAgICB7YnJhbmNoLm5hbWV9XG4gICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWVuZFwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVHZW5lcmF0ZVJlcG9ydH1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFkYXRlRnJvbSB8fCAhZGF0ZVRvfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tcHJpbWFyeSB3LWZ1bGwgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8Q2hhcnRCYXJJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMiBydGw6bXItMCBydGw6bWwtMlwiIC8+XG4gICAgICAgICAgICAgIHt0KCdyZXBvcnRzLmdlbmVyYXRlJyl9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFJlcG9ydCBSZXN1bHRzICovfVxuICAgICAge2lzTG9hZGluZyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcC0xMiByb3VuZGVkLWxnIHNoYWRvdyB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxMb2FkaW5nU3Bpbm5lciBzaXplPVwibGFyZ2VcIiAvPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTQgdGV4dC1ncmF5LTYwMFwiPkdlbmVyYXRpbmcgcmVwb3J0Li4uPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcC0xMiByb3VuZGVkLWxnIHNoYWRvdyB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwXCI+XG4gICAgICAgICAgICA8RG9jdW1lbnRUZXh0SWNvbiBjbGFzc05hbWU9XCJoLTEyIHctMTIgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtXCI+RXJyb3IgZ2VuZXJhdGluZyByZXBvcnQ8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMiB0ZXh0LXNtXCI+UGxlYXNlIHRyeSBhZ2FpbiBvciBjb250YWN0IHN1cHBvcnQuPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHtyZXBvcnREYXRhICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvd1wiPlxuICAgICAgICAgIHsvKiBSZXBvcnQgSGVhZGVyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBweS00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICB7cmVwb3J0VHlwZXMuZmluZChyID0+IHIuaWQgPT09IHNlbGVjdGVkUmVwb3J0KT8ubmFtZX1cbiAgICAgICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAge2RhdGVGcm9tfSB0byB7ZGF0ZVRvfVxuICAgICAgICAgICAgICAgICAge3NlbGVjdGVkQnJhbmNoICE9PSAnYWxsJyAmJiBgIOKAoiAke2JyYW5jaGVzLmZpbmQoYiA9PiBiLmlkID09PSBzZWxlY3RlZEJyYW5jaCk/Lm5hbWV9YH1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yIHJ0bDpzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVFeHBvcnRSZXBvcnQoJ3BkZicpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXNlY29uZGFyeSBidG4tc21cIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxBcnJvd0Rvd25UcmF5SWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTEgcnRsOm1yLTAgcnRsOm1sLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgUERGXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRXhwb3J0UmVwb3J0KCdleGNlbCcpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXNlY29uZGFyeSBidG4tc21cIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxBcnJvd0Rvd25UcmF5SWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTEgcnRsOm1yLTAgcnRsOm1sLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgRXhjZWxcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBSZXBvcnQgQ29udGVudCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgey8qIFN1bW1hcnkgTWV0cmljcyAqL31cbiAgICAgICAgICAgIHtyZXBvcnREYXRhLm1ldHJpY3MgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTYgbWItOFwiPlxuICAgICAgICAgICAgICAgIHtPYmplY3QuZW50cmllcyhyZXBvcnREYXRhLm1ldHJpY3MpLm1hcCgoW2tleSwgdmFsdWVdKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGtleT17a2V5fSBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHAtNCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkdCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgY2FwaXRhbGl6ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtrZXkucmVwbGFjZSgvKFtBLVpdKS9nLCAnICQxJykudHJpbSgpfVxuICAgICAgICAgICAgICAgICAgICA8L2R0PlxuICAgICAgICAgICAgICAgICAgICA8ZGQgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LTJ4bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7dHlwZW9mIHZhbHVlID09PSAnbnVtYmVyJyAmJiBrZXkudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygncmV2ZW51ZScpIHx8IGtleS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCdjb3N0JykgfHwga2V5LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ3RvdGFsJykgJiYga2V5LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ3ZhbHVlJylcbiAgICAgICAgICAgICAgICAgICAgICAgID8gYCQke3ZhbHVlLnRvRml4ZWQoMil9YFxuICAgICAgICAgICAgICAgICAgICAgICAgOiB0eXBlb2YgdmFsdWUgPT09ICdudW1iZXInICYmIGtleS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCdyYXRlJylcbiAgICAgICAgICAgICAgICAgICAgICAgID8gYCR7dmFsdWUudG9GaXhlZCgxKX0lYFxuICAgICAgICAgICAgICAgICAgICAgICAgOiB2YWx1ZX1cbiAgICAgICAgICAgICAgICAgICAgPC9kZD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBEYXRhIFRhYmxlICovfVxuICAgICAgICAgICAge3JlcG9ydERhdGEub3JkZXJzICYmIHJlcG9ydERhdGEub3JkZXJzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgIDx0YWJsZSBjbGFzc05hbWU9XCJ0YWJsZVwiPlxuICAgICAgICAgICAgICAgICAgPHRoZWFkPlxuICAgICAgICAgICAgICAgICAgICA8dHI+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoPk9yZGVyIE51bWJlcjwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoPkRhdGU8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aD5DdXN0b21lci9TdXBwbGllcjwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoPlRvdGFsPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGg+U3RhdHVzPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICAgICAgICA8dGJvZHk+XG4gICAgICAgICAgICAgICAgICAgIHtyZXBvcnREYXRhLm9yZGVycy5zbGljZSgwLCAxMCkubWFwKChvcmRlcikgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDx0ciBrZXk9e29yZGVyLmlkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntvcmRlci5vcmRlck51bWJlcn08L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkPntuZXcgRGF0ZShvcmRlci5vcmRlckRhdGUgfHwgb3JkZXIucmVjZWl2ZWREYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoKX08L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkPntvcmRlci5jdXN0b21lcj8ubmFtZSB8fCBvcmRlci5zdXBwbGllcj8ubmFtZX08L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkPiR7KG9yZGVyLnRvdGFsIHx8IG9yZGVyLmFjdHVhbENvc3QgfHwgMCkudG9GaXhlZCgyKX08L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiYWRnZSBiYWRnZS1zZWNvbmRhcnlcIj57b3JkZXIuc3RhdHVzfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICAgICAgICAgIDwvdGFibGU+XG4gICAgICAgICAgICAgICAge3JlcG9ydERhdGEub3JkZXJzLmxlbmd0aCA+IDEwICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWNlbnRlciB0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgU2hvd2luZyAxMCBvZiB7cmVwb3J0RGF0YS5vcmRlcnMubGVuZ3RofSByZWNvcmRzXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBJbnZlbnRvcnkgRGF0YSAqL31cbiAgICAgICAgICAgIHtyZXBvcnREYXRhLmludmVudG9yeSAmJiByZXBvcnREYXRhLmludmVudG9yeS5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwidGFibGVcIj5cbiAgICAgICAgICAgICAgICAgIDx0aGVhZD5cbiAgICAgICAgICAgICAgICAgICAgPHRyPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aD5Qcm9kdWN0PC90aD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGg+QnJhbmNoPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGg+Q3VycmVudCBTdG9jazwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoPlZhbHVlPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGg+U3RhdHVzPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICAgICAgICA8dGJvZHk+XG4gICAgICAgICAgICAgICAgICAgIHtyZXBvcnREYXRhLmludmVudG9yeS5zbGljZSgwLCAxMCkubWFwKChpdGVtLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDx0ciBrZXk9e2luZGV4fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZD57aXRlbS5wcm9kdWN0Lm5hbWV9PC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZD57aXRlbS5icmFuY2gubmFtZX08L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkPntpdGVtLnF1YW50aXR5fSB7aXRlbS5wcm9kdWN0LnVuaXR9PC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZD4keyhpdGVtLnF1YW50aXR5ICogaXRlbS5wcm9kdWN0LmNvc3RQcmljZSkudG9GaXhlZCgyKX08L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BiYWRnZSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0ucXVhbnRpdHkgPT09IDAgPyAnYmFkZ2UtZGFuZ2VyJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS5xdWFudGl0eSA8PSBpdGVtLm1pblN0b2NrID8gJ2JhZGdlLXdhcm5pbmcnIDogJ2JhZGdlLXN1Y2Nlc3MnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5xdWFudGl0eSA9PT0gMCA/ICdPdXQgb2YgU3RvY2snIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS5xdWFudGl0eSA8PSBpdGVtLm1pblN0b2NrID8gJ0xvdyBTdG9jaycgOiAnSW4gU3RvY2snfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC90Ym9keT5cbiAgICAgICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBObyBEYXRhIE1lc3NhZ2UgKi99XG4gICAgICAgICAgICB7KCFyZXBvcnREYXRhLm9yZGVycyB8fCByZXBvcnREYXRhLm9yZGVycy5sZW5ndGggPT09IDApICYmIFxuICAgICAgICAgICAgICghcmVwb3J0RGF0YS5pbnZlbnRvcnkgfHwgcmVwb3J0RGF0YS5pbnZlbnRvcnkubGVuZ3RoID09PSAwKSAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICAgICAgICA8RG9jdW1lbnRUZXh0SWNvbiBjbGFzc05hbWU9XCJteC1hdXRvIGgtMTIgdy0xMiB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwibXQtMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5ObyBkYXRhIGZvdW5kPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgTm8gZGF0YSBhdmFpbGFibGUgZm9yIHRoZSBzZWxlY3RlZCBkYXRlIHJhbmdlIGFuZCBmaWx0ZXJzLlxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBFbXB0eSBTdGF0ZSAqL31cbiAgICAgIHshZGF0ZUZyb20gfHwgIWRhdGVUbyA/IChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBwLTEyIHJvdW5kZWQtbGcgc2hhZG93IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPENoYXJ0QmFySWNvbiBjbGFzc05hbWU9XCJteC1hdXRvIGgtMTIgdy0xMiB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwibXQtMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5TZWxlY3QgZGF0ZSByYW5nZTwvaDM+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgIENob29zZSBhIGRhdGUgcmFuZ2UgYW5kIHJlcG9ydCB0eXBlIHRvIGdlbmVyYXRlIHlvdXIgcmVwb3J0LlxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICApIDogbnVsbH1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFN0YXRpY1Byb3BzKHsgbG9jYWxlIH0pIHtcbiAgcmV0dXJuIHtcbiAgICBwcm9wczoge1xuICAgICAgLi4uKGF3YWl0IHNlcnZlclNpZGVUcmFuc2xhdGlvbnMobG9jYWxlLCBbJ2NvbW1vbiddKSksXG4gICAgfSxcbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZVRyYW5zbGF0aW9uIiwidXNlUXVlcnkiLCJheGlvcyIsIkNoYXJ0QmFySWNvbiIsIkRvY3VtZW50VGV4dEljb24iLCJDdXJyZW5jeURvbGxhckljb24iLCJTaG9wcGluZ0NhcnRJY29uIiwiU2hvcHBpbmdCYWdJY29uIiwiV3JlbmNoU2NyZXdkcml2ZXJJY29uIiwiVXNlcnNJY29uIiwiQ3ViZUljb24iLCJDYWxlbmRhckljb24iLCJBcnJvd0Rvd25UcmF5SWNvbiIsIkxvYWRpbmdTcGlubmVyIiwiUmVwb3J0cyIsInJlcG9ydFR5cGVzIiwiYnJhbmNoZXMiLCJ0Iiwic2VsZWN0ZWRSZXBvcnQiLCJzZXRTZWxlY3RlZFJlcG9ydCIsImRhdGVGcm9tIiwic2V0RGF0ZUZyb20iLCJkYXRlVG8iLCJzZXREYXRlVG8iLCJzZWxlY3RlZEJyYW5jaCIsInNldFNlbGVjdGVkQnJhbmNoIiwiZGF0YSIsImJyYW5jaGVzRGF0YSIsInJlc3BvbnNlIiwiZ2V0IiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJyZXBvcnREYXRhIiwiaXNMb2FkaW5nIiwiZXJyb3IiLCJyZWZldGNoIiwicGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwiYnJhbmNoSWQiLCJlbmFibGVkIiwia2VlcFByZXZpb3VzRGF0YSIsImlkIiwibmFtZSIsIm5hbWVBciIsImljb24iLCJjb2xvciIsImRlc2NyaXB0aW9uIiwiaGFuZGxlR2VuZXJhdGVSZXBvcnQiLCJoYW5kbGVFeHBvcnRSZXBvcnQiLCJmb3JtYXQiLCJjb25zb2xlIiwibG9nIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiaDIiLCJtYXAiLCJyZXBvcnQiLCJvbkNsaWNrIiwiaDMiLCJsYWJlbCIsImlucHV0IiwidHlwZSIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0Iiwic2VsZWN0Iiwib3B0aW9uIiwiYnJhbmNoIiwiYnV0dG9uIiwiZGlzYWJsZWQiLCJzaXplIiwiZmluZCIsInIiLCJiIiwibWV0cmljcyIsIk9iamVjdCIsImVudHJpZXMiLCJrZXkiLCJkdCIsInJlcGxhY2UiLCJ0cmltIiwiZGQiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwidG9GaXhlZCIsIm9yZGVycyIsImxlbmd0aCIsInRhYmxlIiwidGhlYWQiLCJ0ciIsInRoIiwidGJvZHkiLCJzbGljZSIsIm9yZGVyIiwidGQiLCJvcmRlck51bWJlciIsIkRhdGUiLCJvcmRlckRhdGUiLCJyZWNlaXZlZERhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJjdXN0b21lciIsInN1cHBsaWVyIiwidG90YWwiLCJhY3R1YWxDb3N0Iiwic3BhbiIsInN0YXR1cyIsImludmVudG9yeSIsIml0ZW0iLCJpbmRleCIsInByb2R1Y3QiLCJxdWFudGl0eSIsInVuaXQiLCJjb3N0UHJpY2UiLCJtaW5TdG9jayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/reports.js\n"));

/***/ })

});