"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/customers",{

/***/ "./pages/customers.js":
/*!****************************!*\
  !*** ./pages/customers.js ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: function() { return /* binding */ __N_SSG; },\n/* harmony export */   \"default\": function() { return /* binding */ Customers; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon,TruckIcon,UserIcon,UsersIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon,TruckIcon,UserIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/LoadingSpinner */ \"./components/LoadingSpinner.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Customers() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showViewModal, setShowViewModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCustomer, setSelectedCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch customers\n    const { data: customersData, isLoading, error, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        \"customers\",\n        currentPage,\n        searchTerm,\n        selectedType,\n        selectedStatus\n    ], async ()=>{\n        const params = new URLSearchParams({\n            page: currentPage.toString(),\n            limit: \"10\",\n            search: searchTerm,\n            type: selectedType,\n            status: selectedStatus\n        });\n        const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(\"http://localhost:3001\", \"/api/customers?\").concat(params));\n        return response.data;\n    }, {\n        keepPreviousData: true\n    });\n    const customers = (customersData === null || customersData === void 0 ? void 0 : customersData.customers) || [];\n    const pagination = (customersData === null || customersData === void 0 ? void 0 : customersData.pagination) || {};\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        setCurrentPage(1);\n        refetch();\n    };\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n    };\n    // Delete customer mutation\n    const deleteMutation = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)(async (customerId)=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"][\"delete\"](\"\".concat(\"http://localhost:3001\", \"/api/customers/\").concat(customerId));\n        return response.data;\n    }, {\n        onSuccess: ()=>{\n            queryClient.invalidateQueries([\n                \"customers\"\n            ]);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(t(\"customers.deleteSuccess\") || \"Customer deleted successfully\");\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"Failed to delete customer\");\n        }\n    });\n    // Handle delete customer\n    const handleDelete = async (customer)=>{\n        if (window.confirm(t(\"customers.confirmDelete\") || \"Are you sure you want to delete \".concat(customer.name, \"?\"))) {\n            deleteMutation.mutate(customer.id);\n        }\n    };\n    // Handle view customer\n    const handleView = (customer)=>{\n        setSelectedCustomer(customer);\n        setShowViewModal(true);\n    };\n    // Handle edit customer\n    const handleEdit = (customer)=>{\n        setSelectedCustomer(customer);\n        setShowEditModal(true);\n    };\n    // Handle create customer\n    const handleCreate = ()=>{\n        setSelectedCustomer(null);\n        setShowCreateModal(true);\n    };\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case \"CUSTOMER\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.UserIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 109,\n                    columnNumber: 16\n                }, this);\n            case \"SUPPLIER\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.TruckIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 111,\n                    columnNumber: 16\n                }, this);\n            case \"BOTH\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.UsersIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 113,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.UserIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 115,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getTypeColor = (type)=>{\n        switch(type){\n            case \"CUSTOMER\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"SUPPLIER\":\n                return \"bg-green-100 text-green-800\";\n            case \"BOTH\":\n                return \"bg-purple-100 text-purple-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    if (isLoading && !customers.length) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"mt-2 text-sm font-medium text-gray-900\",\n                    children: t(\"common.error\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: \"Failed to load customers\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>refetch(),\n                    className: \"mt-4 btn-primary\",\n                    children: \"Try Again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: t(\"customers.title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-600\",\n                                children: \"Manage your customers and suppliers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCreate,\n                        className: \"btn-primary\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.PlusIcon, {\n                                className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            t(\"customers.addCustomer\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-4 rounded-lg shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col sm:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.MagnifyingGlassIcon, {\n                                        className: \"absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t(\"common.search\"),\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"form-input pl-10 rtl:pl-3 rtl:pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedType,\n                            onChange: (e)=>setSelectedType(e.target.value),\n                            className: \"form-input\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"all\",\n                                    children: \"All Types\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"customer\",\n                                    children: t(\"customers.customer\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"supplier\",\n                                    children: t(\"customers.supplier\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"both\",\n                                    children: t(\"customers.both\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedStatus,\n                            onChange: (e)=>setSelectedStatus(e.target.value),\n                            className: \"form-input\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"all\",\n                                    children: \"All Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"active\",\n                                    children: \"Active\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"inactive\",\n                                    children: \"Inactive\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary\",\n                            children: t(\"common.search\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"table\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"customers.customerCode\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"customers.customerName\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"customers.customerType\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"customers.phone\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"customers.balance\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"common.actions\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"font-medium\",\n                                                    children: customer.code\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: customer.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: customer.nameAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getTypeColor(customer.type)),\n                                                        children: [\n                                                            getTypeIcon(customer.type),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-1 rtl:ml-0 rtl:mr-1\",\n                                                                children: t(\"customers.\".concat(customer.type.toLowerCase()))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: customer.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium \".concat((customer.balance || 0) >= 0 ? \"text-green-600\" : \"text-red-600\"),\n                                                        children: [\n                                                            \"$\",\n                                                            (parseFloat(customer.balance) || 0).toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"badge \".concat(customer.isActive ? \"badge-success\" : \"badge-secondary\"),\n                                                        children: customer.isActive ? \"Active\" : \"Inactive\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleView(customer),\n                                                                className: \"p-1 text-gray-400 hover:text-blue-600 transition-colors\",\n                                                                title: t(\"common.view\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.EyeIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleEdit(customer),\n                                                                className: \"p-1 text-gray-400 hover:text-green-600 transition-colors\",\n                                                                title: t(\"common.edit\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.PencilIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDelete(customer),\n                                                                className: \"p-1 text-gray-400 hover:text-red-600 transition-colors\",\n                                                                title: t(\"common.delete\"),\n                                                                disabled: deleteMutation.isLoading,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.TrashIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, customer.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this),\n                    pagination.pages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-700\",\n                                    children: [\n                                        \"Showing \",\n                                        (pagination.page - 1) * pagination.limit + 1,\n                                        \" to\",\n                                        \" \",\n                                        Math.min(pagination.page * pagination.limit, pagination.total),\n                                        \" of\",\n                                        \" \",\n                                        pagination.total,\n                                        \" results\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handlePageChange(pagination.page - 1),\n                                            disabled: pagination.page <= 1,\n                                            className: \"btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Previous\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this),\n                                        Array.from({\n                                            length: Math.min(5, pagination.pages)\n                                        }, (_, i)=>{\n                                            const page = i + Math.max(1, pagination.page - 2);\n                                            if (page > pagination.pages) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePageChange(page),\n                                                className: \"btn-sm \".concat(page === pagination.page ? \"btn-primary\" : \"btn-secondary\"),\n                                                children: page\n                                            }, page, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 308,\n                                                columnNumber: 21\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handlePageChange(pagination.page + 1),\n                                            disabled: pagination.page >= pagination.pages,\n                                            className: \"btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Next\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                            lineNumber: 319,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                        lineNumber: 289,\n                        columnNumber: 11\n                    }, this),\n                    customers.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.UsersIcon, {\n                                className: \"mx-auto h-12 w-12 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"mt-2 text-sm font-medium text-gray-900\",\n                                children: \"No customers found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-500\",\n                                children: \"Get started by creating your first customer.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.PlusIcon, {\n                                            className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this),\n                                        t(\"customers.addCustomer\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                        lineNumber: 333,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_s(Customers, \"naqQlQ1bFfTUjfEp20SmSRNn9OQ=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation\n    ];\n});\n_c = Customers;\nvar _c;\n$RefreshReg$(_c, \"Customers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/customers.js\n"));

/***/ })

});