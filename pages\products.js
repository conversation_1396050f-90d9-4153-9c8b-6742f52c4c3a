import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import axios from 'axios';
import toast from 'react-hot-toast';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/LoadingSpinner';
import { ViewProductModal, ProductFormModal } from '../components/ProductModals';

export default function Products() {
  const { t } = useTranslation('common');
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);

  // Fetch products
  const { data: productsData, isLoading, error, refetch } = useQuery(
    ['products', currentPage, searchTerm, selectedCategory, selectedStatus],
    async () => {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        search: searchTerm,
        category: selectedCategory,
        status: selectedStatus,
      });

      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/products?${params}`);
      return response.data;
    },
    {
      keepPreviousData: true,
    }
  );

  const products = productsData?.products || [];
  const pagination = productsData?.pagination || {};

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    refetch();
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Delete product mutation
  const deleteMutation = useMutation(
    async (productId) => {
      const response = await axios.delete(`${process.env.NEXT_PUBLIC_API_URL}/api/products/${productId}`);
      return response.data;
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['products']);
        toast.success(t('products.deleteSuccess') || 'Product deleted successfully');
      },
      onError: (error) => {
        toast.error(error.response?.data?.error || 'Failed to delete product');
      }
    }
  );

  // Handle delete product
  const handleDelete = async (product) => {
    if (window.confirm(t('products.confirmDelete') || `Are you sure you want to delete ${product.name}?`)) {
      deleteMutation.mutate(product.id);
    }
  };

  // Handle view product
  const handleView = (product) => {
    setSelectedProduct(product);
    setShowViewModal(true);
  };

  // Handle edit product
  const handleEdit = (product) => {
    setSelectedProduct(product);
    setShowEditModal(true);
  };

  // Handle create product
  const handleCreate = () => {
    setSelectedProduct(null);
    setShowCreateModal(true);
  };

  if (isLoading && !products.length) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h3 className="mt-2 text-sm font-medium text-gray-900">{t('common.error')}</h3>
        <p className="mt-1 text-sm text-gray-500">Failed to load products</p>
        <button
          onClick={() => refetch()}
          className="mt-4 btn-primary"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{t('products.title')}</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage your product catalog
          </p>
        </div>
        <button onClick={handleCreate} className="btn-primary">
          <PlusIcon className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2" />
          {t('products.addProduct')}
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder={t('common.search')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input pl-10 rtl:pl-3 rtl:pr-10"
              />
            </div>
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="form-input"
          >
            <option value="all">All Categories</option>
            <option value="electronics">Electronics</option>
            <option value="computers">Computers</option>
            <option value="mobile">Mobile Devices</option>
          </select>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="form-input"
          >
            <option value="all">All Status</option>
            <option value="active">{t('products.active')}</option>
            <option value="inactive">{t('products.inactive')}</option>
          </select>
          <button type="submit" className="btn-primary">
            {t('common.search')}
          </button>
        </form>
      </div>

      {/* Products Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="table">
            <thead>
              <tr>
                <th>{t('products.productCode')}</th>
                <th>{t('products.productName')}</th>
                <th>{t('products.category')}</th>
                <th>{t('products.unitPrice')}</th>
                <th>{t('products.currentStock')}</th>
                <th>{t('products.status')}</th>
                <th>{t('common.actions')}</th>
              </tr>
            </thead>
            <tbody>
              {products.map((product) => (
                <tr key={product.id}>
                  <td className="font-medium">{product.code}</td>
                  <td>
                    <div>
                      <div className="font-medium text-gray-900">{product.name}</div>
                      <div className="text-sm text-gray-500">{product.nameAr}</div>
                    </div>
                  </td>
                  <td>
                    <div>
                      <div className="text-sm text-gray-900">{product.category?.name}</div>
                      <div className="text-xs text-gray-500">{product.category?.nameAr}</div>
                    </div>
                  </td>
                  <td>${(parseFloat(product.unitPrice) || 0).toFixed(2)}</td>
                  <td>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      product.currentStock <= product.minStock
                        ? 'bg-red-100 text-red-800'
                        : product.currentStock <= product.minStock * 2
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {product.currentStock} {product.unit}
                    </span>
                  </td>
                  <td>
                    <span className={`badge ${product.isActive ? 'badge-success' : 'badge-secondary'}`}>
                      {product.isActive ? t('products.active') : t('products.inactive')}
                    </span>
                  </td>
                  <td>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <button
                        onClick={() => handleView(product)}
                        className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        title={t('common.view')}
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleEdit(product)}
                        className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                        title={t('common.edit')}
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(product)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title={t('common.delete')}
                        disabled={deleteMutation.isLoading}
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="px-6 py-3 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                {pagination.total} results
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                  const page = i + Math.max(1, pagination.page - 2);
                  if (page > pagination.pages) return null;
                  return (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`btn-sm ${
                        page === pagination.page ? 'btn-primary' : 'btn-secondary'
                      }`}
                    >
                      {page}
                    </button>
                  );
                })}
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.pages}
                  className="btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {products.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating your first product.
            </p>
            <div className="mt-6">
              <button onClick={handleCreate} className="btn-primary">
                <PlusIcon className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2" />
                {t('products.addProduct')}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <ViewProductModal
        product={selectedProduct}
        isOpen={showViewModal}
        onClose={() => setShowViewModal(false)}
      />

      <ProductFormModal
        product={selectedProduct}
        isOpen={showCreateModal || showEditModal}
        onClose={() => {
          setShowCreateModal(false);
          setShowEditModal(false);
          setSelectedProduct(null);
        }}
        onSuccess={() => {
          setShowCreateModal(false);
          setShowEditModal(false);
          setSelectedProduct(null);
        }}
      />
    </div>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  };
}
