# Database
DATABASE_URL="postgresql://username:password@localhost:5432/business_management_db"

# JWT Secret
JWT_SECRET="your-super-secret-jwt-key-here"

# Server Configuration
PORT=3001
NEXT_PUBLIC_API_URL="http://localhost:3001"
NEXT_PUBLIC_SOCKET_URL="http://localhost:3001"

# Company Settings
COMPANY_NAME="Business Management System"
COMPANY_NAME_AR="نظام إدارة الأعمال"
DEFAULT_CURRENCY="USD"
DEFAULT_LANGUAGE="ar"

# Email Configuration (Optional)
SMTP_HOST=""
SMTP_PORT=""
SMTP_USER=""
SMTP_PASS=""

# WhatsApp API (Optional for maintenance notifications)
WHATSAPP_API_URL=""
WHATSAPP_API_TOKEN=""

# File Upload
MAX_FILE_SIZE="5MB"
UPLOAD_PATH="./uploads"

# Security
BCRYPT_ROUNDS=12
SESSION_TIMEOUT=3600

# Development
NODE_ENV="development"
