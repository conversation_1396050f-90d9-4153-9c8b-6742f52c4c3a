const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Business Management System...\n');

// تشغيل الخادم الخلفي على المنفذ 3070
console.log('📡 Starting Backend Server on port 3070...');
const backendProcess = spawn('node', ['server/simple-server.js'], {
  env: { ...process.env, PORT: '3070' },
  stdio: 'pipe',
  shell: true
});

backendProcess.stdout.on('data', (data) => {
  console.log(`[BACKEND] ${data.toString().trim()}`);
});

backendProcess.stderr.on('data', (data) => {
  console.error(`[BACKEND ERROR] ${data.toString().trim()}`);
});

// انتظار ثانيتين ثم تشغيل الخادم الأمامي
setTimeout(() => {
  console.log('\n🌐 Starting Frontend Server on port 3071...');
  
  const frontendProcess = spawn('npm', ['run', 'dev', '--', '-p', '3071'], {
    stdio: 'pipe',
    shell: true
  });

  frontendProcess.stdout.on('data', (data) => {
    console.log(`[FRONTEND] ${data.toString().trim()}`);
  });

  frontendProcess.stderr.on('data', (data) => {
    console.error(`[FRONTEND ERROR] ${data.toString().trim()}`);
  });

  frontendProcess.on('close', (code) => {
    console.log(`\n❌ Frontend server exited with code ${code}`);
    process.exit(code);
  });

}, 2000);

backendProcess.on('close', (code) => {
  console.log(`\n❌ Backend server exited with code ${code}`);
  process.exit(code);
});

// التعامل مع إيقاف البرنامج
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down servers...');
  backendProcess.kill();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down servers...');
  backendProcess.kill();
  process.exit(0);
});

console.log('\n✅ Both servers are starting...');
console.log('📱 Frontend: http://localhost:3071');
console.log('🔧 Backend: http://localhost:3070');
console.log('💡 Press Ctrl+C to stop both servers\n');
