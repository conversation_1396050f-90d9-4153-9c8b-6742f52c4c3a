const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Get all products with pagination and search
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '', 
      category = '', 
      status = 'all',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Build where clause
    const where = {
      AND: [
        search ? {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { nameAr: { contains: search, mode: 'insensitive' } },
            { code: { contains: search, mode: 'insensitive' } },
            { barcode: { contains: search, mode: 'insensitive' } }
          ]
        } : {},
        category ? { categoryId: category } : {},
        status !== 'all' ? { isActive: status === 'active' } : {}
      ]
    };

    // Get products with pagination
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: {
            select: { id: true, name: true, nameAr: true }
          }
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take
      }),
      prisma.product.count({ where })
    ]);

    res.json({
      products,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get single product
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const product = await prisma.product.findUnique({
      where: { id: req.params.id },
      include: {
        category: {
          select: { id: true, name: true, nameAr: true }
        }
      }
    });

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json({ product });

  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create new product
router.post('/', authenticateToken, authorizeRoles('ADMIN', 'MANAGER', 'INVENTORY'), async (req, res) => {
  try {
    const {
      code,
      name,
      nameAr,
      description,
      descriptionAr,
      categoryId,
      unitPrice,
      costPrice,
      minStock = 0,
      currentStock = 0,
      unit,
      unitAr,
      barcode,
      image
    } = req.body;

    // Validation
    if (!code || !name || !nameAr || !categoryId || !unitPrice || !costPrice || !unit || !unitAr) {
      return res.status(400).json({ error: 'Required fields are missing' });
    }

    // Check if code already exists
    const existingProduct = await prisma.product.findUnique({
      where: { code }
    });

    if (existingProduct) {
      return res.status(400).json({ error: 'Product code already exists' });
    }

    // Check if category exists
    const category = await prisma.category.findUnique({
      where: { id: categoryId }
    });

    if (!category) {
      return res.status(400).json({ error: 'Category not found' });
    }

    const product = await prisma.product.create({
      data: {
        code,
        name,
        nameAr,
        description,
        descriptionAr,
        categoryId,
        unitPrice: parseFloat(unitPrice),
        costPrice: parseFloat(costPrice),
        minStock: parseInt(minStock),
        currentStock: parseInt(currentStock),
        unit,
        unitAr,
        barcode,
        image
      },
      include: {
        category: {
          select: { id: true, name: true, nameAr: true }
        }
      }
    });

    // Emit real-time update
    const io = req.app.get('io');
    io.emit('inventory_updated', {
      type: 'product_created',
      productId: product.id,
      productName: product.name
    });

    res.status(201).json({
      product,
      message: 'Product created successfully'
    });

  } catch (error) {
    console.error('Create product error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update product
router.put('/:id', authenticateToken, authorizeRoles('ADMIN', 'MANAGER', 'INVENTORY'), async (req, res) => {
  try {
    const {
      code,
      name,
      nameAr,
      description,
      descriptionAr,
      categoryId,
      unitPrice,
      costPrice,
      minStock,
      currentStock,
      unit,
      unitAr,
      barcode,
      image,
      isActive
    } = req.body;

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: req.params.id }
    });

    if (!existingProduct) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Check if code is being changed and already exists
    if (code && code !== existingProduct.code) {
      const codeExists = await prisma.product.findUnique({
        where: { code }
      });

      if (codeExists) {
        return res.status(400).json({ error: 'Product code already exists' });
      }
    }

    // Check if category exists
    if (categoryId) {
      const category = await prisma.category.findUnique({
        where: { id: categoryId }
      });

      if (!category) {
        return res.status(400).json({ error: 'Category not found' });
      }
    }

    const product = await prisma.product.update({
      where: { id: req.params.id },
      data: {
        ...(code && { code }),
        ...(name && { name }),
        ...(nameAr && { nameAr }),
        ...(description !== undefined && { description }),
        ...(descriptionAr !== undefined && { descriptionAr }),
        ...(categoryId && { categoryId }),
        ...(unitPrice && { unitPrice: parseFloat(unitPrice) }),
        ...(costPrice && { costPrice: parseFloat(costPrice) }),
        ...(minStock !== undefined && { minStock: parseInt(minStock) }),
        ...(currentStock !== undefined && { currentStock: parseInt(currentStock) }),
        ...(unit && { unit }),
        ...(unitAr && { unitAr }),
        ...(barcode !== undefined && { barcode }),
        ...(image !== undefined && { image }),
        ...(isActive !== undefined && { isActive })
      },
      include: {
        category: {
          select: { id: true, name: true, nameAr: true }
        }
      }
    });

    // Emit real-time update
    const io = req.app.get('io');
    io.emit('inventory_updated', {
      type: 'product_updated',
      productId: product.id,
      productName: product.name
    });

    res.json({
      product,
      message: 'Product updated successfully'
    });

  } catch (error) {
    console.error('Update product error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete product
router.delete('/:id', authenticateToken, authorizeRoles('ADMIN', 'MANAGER'), async (req, res) => {
  try {
    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: req.params.id }
    });

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Check if product is used in any orders
    const [salesOrderItems, purchaseOrderItems] = await Promise.all([
      prisma.salesOrderItem.findFirst({
        where: { productId: req.params.id }
      }),
      prisma.purchaseOrderItem.findFirst({
        where: { productId: req.params.id }
      })
    ]);

    if (salesOrderItems || purchaseOrderItems) {
      return res.status(400).json({ 
        error: 'Cannot delete product that is used in orders. Deactivate it instead.' 
      });
    }

    await prisma.product.delete({
      where: { id: req.params.id }
    });

    res.json({ message: 'Product deleted successfully' });

  } catch (error) {
    console.error('Delete product error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get low stock products
router.get('/alerts/low-stock', authenticateToken, async (req, res) => {
  try {
    const lowStockProducts = await prisma.product.findMany({
      where: {
        isActive: true,
        currentStock: {
          lte: prisma.product.fields.minStock
        }
      },
      include: {
        category: {
          select: { id: true, name: true, nameAr: true }
        }
      },
      orderBy: { currentStock: 'asc' }
    });

    res.json({ products: lowStockProducts });

  } catch (error) {
    console.error('Low stock products error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get components for customizable products
router.get('/components', authenticateToken, async (req, res) => {
  try {
    // Get all products that are marked as components
    const components = await prisma.product.findMany({
      where: {
        isActive: true,
        // Add a filter for component products
        OR: [
          { code: { startsWith: 'CPU' } },
          { code: { startsWith: 'RAM' } },
          { code: { startsWith: 'SSD' } },
          { code: { startsWith: 'HDD' } },
          { code: { startsWith: 'GPU' } },
          { code: { startsWith: 'MB' } },
          { code: { startsWith: 'PSU' } },
          { code: { startsWith: 'CASE' } },
          { code: { startsWith: 'OS' } },
        ]
      },
      select: {
        id: true,
        code: true,
        name: true,
        nameAr: true,
        category: true,
        unitPrice: true,
        costPrice: true,
        currentStock: true,
        description: true,
        descriptionAr: true,
      }
    });

    // Group components by category based on product codes
    const groupedComponents = {
      cpu: components.filter(c => c.code.startsWith('CPU')),
      ram: components.filter(c => c.code.startsWith('RAM')),
      storage: components.filter(c => c.code.startsWith('SSD') || c.code.startsWith('HDD')),
      gpu: components.filter(c => c.code.startsWith('GPU')),
      motherboard: components.filter(c => c.code.startsWith('MB')),
      psu: components.filter(c => c.code.startsWith('PSU')),
      case: components.filter(c => c.code.startsWith('CASE')),
      os: components.filter(c => c.code.startsWith('OS')),
    };

    res.json({
      components: groupedComponents,
      total: components.length
    });

  } catch (error) {
    console.error('Get components error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create customizable product configuration
router.post('/customizable', authenticateToken, authorizeRoles('ADMIN', 'MANAGER', 'SALES'), async (req, res) => {
  try {
    const {
      baseProduct,
      components,
      quantities,
      assemblyFee,
      serviceFee,
      totalPrice,
      customerId,
      branchId,
      mode = 'sales'
    } = req.body;

    // Create a custom product configuration
    const configuration = await prisma.customProductConfiguration.create({
      data: {
        baseProductId: baseProduct?.id,
        customerId,
        branchId,
        components: JSON.stringify(components),
        quantities: JSON.stringify(quantities),
        assemblyFee: parseFloat(assemblyFee) || 0,
        serviceFee: parseFloat(serviceFee) || 0,
        totalPrice: parseFloat(totalPrice),
        mode,
        status: 'DRAFT',
        createdBy: req.user.userId,
      }
    });

    res.status(201).json({
      configuration,
      message: 'Custom product configuration created successfully'
    });

  } catch (error) {
    console.error('Create custom configuration error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
