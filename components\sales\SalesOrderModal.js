import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { XMarkIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import axios from 'axios';
import toast from 'react-hot-toast';

export default function SalesOrderModal({ isOpen, onClose, onSave, salesOrder = null, fromQuote = null }) {
  const { t } = useTranslation('common');
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  
  const [formData, setFormData] = useState({
    customerId: '',
    dueDate: '',
    notes: '',
    items: []
  });

  useEffect(() => {
    if (isOpen) {
      loadCustomers();
      loadProducts();
      
      if (salesOrder) {
        setFormData({
          customerId: salesOrder.customerId || '',
          dueDate: salesOrder.dueDate ? salesOrder.dueDate.split('T')[0] : '',
          notes: salesOrder.notes || '',
          items: salesOrder.items || []
        });
      } else if (fromQuote) {
        // Convert quote to sales order
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 14); // 14 days from now
        
        setFormData({
          customerId: fromQuote.customerId || '',
          dueDate: dueDate.toISOString().split('T')[0],
          notes: `تم التحويل من عرض السعر: ${fromQuote.quoteNumber}`,
          items: fromQuote.items || []
        });
      } else {
        // New sales order
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 14);
        setFormData(prev => ({
          ...prev,
          dueDate: dueDate.toISOString().split('T')[0]
        }));
      }
    }
  }, [isOpen, salesOrder, fromQuote]);

  const loadCustomers = async () => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/customers`);
      setCustomers(response.data.customers || []);
    } catch (error) {
      console.error('Error loading customers:', error);
    }
  };

  const loadProducts = async () => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/products`);
      setProducts(response.data.products || []);
    } catch (error) {
      console.error('Error loading products:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, {
        productId: '',
        quantity: 1,
        unitPrice: 0,
        discount: 0,
        total: 0
      }]
    }));
  };

  const removeItem = (index) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  const updateItem = (index, field, value) => {
    setFormData(prev => {
      const newItems = [...prev.items];
      newItems[index] = { ...newItems[index], [field]: value };
      
      // Auto-calculate total and check stock
      if (field === 'productId') {
        const product = products.find(p => p.id === value);
        if (product) {
          newItems[index].unitPrice = parseFloat(product.unitPrice);
          newItems[index].availableStock = product.currentStock;
        }
      }
      
      if (field === 'quantity' || field === 'unitPrice' || field === 'discount') {
        const item = newItems[index];
        const subtotal = (parseFloat(item.quantity) || 0) * (parseFloat(item.unitPrice) || 0);
        const discountAmount = subtotal * ((parseFloat(item.discount) || 0) / 100);
        newItems[index].total = subtotal - discountAmount;
      }
      
      return { ...prev, items: newItems };
    });
  };

  const calculateTotals = () => {
    const subtotal = formData.items.reduce((sum, item) => sum + (parseFloat(item.total) || 0), 0);
    const taxAmount = subtotal * 0.14; // 14% tax
    const total = subtotal + taxAmount;
    
    return { subtotal, taxAmount, total };
  };

  const validateStock = () => {
    for (const item of formData.items) {
      const product = products.find(p => p.id === item.productId);
      if (product && product.hasInventory && item.quantity > product.currentStock) {
        toast.error(`الكمية المطلوبة من ${product.name} غير متوفرة في المخزون`);
        return false;
      }
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.customerId) {
      toast.error('يرجى اختيار العميل');
      return;
    }
    
    if (formData.items.length === 0) {
      toast.error('يرجى إضافة عنصر واحد على الأقل');
      return;
    }

    if (!validateStock()) {
      return;
    }

    setLoading(true);
    
    try {
      const { subtotal, taxAmount, total } = calculateTotals();
      
      const orderData = {
        ...formData,
        subtotal,
        taxAmount,
        total,
        status: 'PENDING',
        quoteId: fromQuote?.id || null
      };

      const response = salesOrder 
        ? await axios.put(`${process.env.NEXT_PUBLIC_API_URL}/api/sales-orders/${salesOrder.id}`, orderData)
        : await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/api/sales-orders`, orderData);

      toast.success(response.data.message || (salesOrder ? 'تم تحديث أمر البيع' : 'تم إنشاء أمر البيع'));
      onSave(response.data.salesOrder);
      onClose();
    } catch (error) {
      toast.error(error.response?.data?.error || 'حدث خطأ');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const { subtotal, taxAmount, total } = calculateTotals();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {salesOrder ? 'تعديل أمر البيع' : 'أمر بيع جديد'}
            {fromQuote && (
              <span className="text-sm text-blue-600 block">
                تحويل من عرض السعر: {fromQuote.quoteNumber}
              </span>
            )}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Customer and Date */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="form-label">العميل *</label>
              <select
                name="customerId"
                value={formData.customerId}
                onChange={handleChange}
                className="form-input"
                required
                disabled={fromQuote}
              >
                <option value="">اختر العميل</option>
                {customers.map(customer => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="form-label">تاريخ الاستحقاق *</label>
              <input
                type="date"
                name="dueDate"
                value={formData.dueDate}
                onChange={handleChange}
                className="form-input"
                required
              />
            </div>
          </div>

          {/* Items */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">العناصر</h3>
              <button
                type="button"
                onClick={addItem}
                className="btn-primary flex items-center"
              >
                <PlusIcon className="h-5 w-5 mr-2" />
                إضافة عنصر
              </button>
            </div>

            <div className="space-y-4">
              {formData.items.map((item, index) => {
                const product = products.find(p => p.id === item.productId);
                const stockWarning = product && product.hasInventory && item.quantity > product.currentStock;
                
                return (
                  <div key={index} className={`grid grid-cols-12 gap-4 items-end p-4 border rounded-lg ${stockWarning ? 'border-red-300 bg-red-50' : ''}`}>
                    <div className="col-span-4">
                      <label className="form-label">المنتج</label>
                      <select
                        value={item.productId}
                        onChange={(e) => updateItem(index, 'productId', e.target.value)}
                        className="form-input"
                      >
                        <option value="">اختر المنتج</option>
                        {products.map(product => (
                          <option key={product.id} value={product.id}>
                            {product.name} {product.hasInventory && `(متوفر: ${product.currentStock})`}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div className="col-span-2">
                      <label className="form-label">الكمية</label>
                      <input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => updateItem(index, 'quantity', e.target.value)}
                        className={`form-input ${stockWarning ? 'border-red-500' : ''}`}
                        min="1"
                      />
                      {stockWarning && (
                        <p className="text-xs text-red-600 mt-1">
                          المتوفر: {product.currentStock}
                        </p>
                      )}
                    </div>
                    
                    <div className="col-span-2">
                      <label className="form-label">السعر</label>
                      <input
                        type="number"
                        value={item.unitPrice}
                        onChange={(e) => updateItem(index, 'unitPrice', e.target.value)}
                        className="form-input"
                        step="0.01"
                      />
                    </div>
                    
                    <div className="col-span-2">
                      <label className="form-label">خصم %</label>
                      <input
                        type="number"
                        value={item.discount}
                        onChange={(e) => updateItem(index, 'discount', e.target.value)}
                        className="form-input"
                        min="0"
                        max="100"
                      />
                    </div>
                    
                    <div className="col-span-1">
                      <label className="form-label">الإجمالي</label>
                      <div className="text-sm font-medium text-gray-900 py-2">
                        ${(item.total || 0).toFixed(2)}
                      </div>
                    </div>
                    
                    <div className="col-span-1">
                      <button
                        type="button"
                        onClick={() => removeItem(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Stock Reservation Notice */}
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-orange-800">
                  تنبيه حجز المخزون
                </h3>
                <div className="mt-2 text-sm text-orange-700">
                  <p>
                    عند تأكيد أمر البيع، سيتم حجز الكميات المطلوبة من المخزون تلقائياً.
                    تأكد من توفر الكميات قبل التأكيد.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Totals */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>المجموع الفرعي:</span>
                <span>${subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>الضريبة (14%):</span>
                <span>${taxAmount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between font-bold text-lg border-t pt-2">
                <span>الإجمالي:</span>
                <span>${total.toFixed(2)}</span>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="form-label">ملاحظات</label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              className="form-input"
              rows="3"
              placeholder="ملاحظات إضافية..."
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary"
            >
              {loading ? 'جاري الحفظ...' : (salesOrder ? 'تحديث' : 'إنشاء')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
