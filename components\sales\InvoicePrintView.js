import { forwardRef } from 'react';

const InvoicePrintView = forwardRef(({ invoice, customer, company }, ref) => {
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP'
    }).format(amount);
  };

  return (
    <div ref={ref} className="bg-white p-8 max-w-4xl mx-auto" style={{ fontFamily: 'Arial, sans-serif' }}>
      {/* Header */}
      <div className="border-b-2 border-blue-600 pb-6 mb-6">
        <div className="flex justify-between items-start">
          <div className="text-right">
            <h1 className="text-3xl font-bold text-blue-600 mb-2">
              {company?.nameAr || 'شركة التكنولوجيا المتقدمة'}
            </h1>
            <p className="text-gray-600">{company?.addressAr || 'شارع التحرير، القاهرة، مصر'}</p>
            <p className="text-gray-600">هاتف: {company?.phone || '+20 ************'}</p>
            <p className="text-gray-600">بريد إلكتروني: {company?.email || '<EMAIL>'}</p>
            {company?.taxNumber && (
              <p className="text-gray-600">الرقم الضريبي: {company.taxNumber}</p>
            )}
          </div>
          
          <div className="text-left">
            <div className="bg-blue-600 text-white px-4 py-2 rounded-lg inline-block">
              <h2 className="text-xl font-bold">فاتورة</h2>
              <p className="text-blue-100">INVOICE</p>
            </div>
          </div>
        </div>
      </div>

      {/* Invoice Info */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b border-gray-300 pb-1">
            معلومات الفاتورة
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">رقم الفاتورة:</span>
              <span className="font-semibold">{invoice?.invoiceNumber}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">تاريخ الإصدار:</span>
              <span className="font-semibold">{formatDate(invoice?.createdAt)}</span>
            </div>
            {invoice?.dueDate && (
              <div className="flex justify-between">
                <span className="text-gray-600">تاريخ الاستحقاق:</span>
                <span className="font-semibold">{formatDate(invoice.dueDate)}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-gray-600">الحالة:</span>
              <span className={`font-semibold px-2 py-1 rounded text-xs ${
                invoice?.status === 'PAID' ? 'bg-green-100 text-green-800' :
                invoice?.status === 'PARTIALLY_PAID' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {invoice?.status === 'PAID' ? 'مدفوعة' :
                 invoice?.status === 'PARTIALLY_PAID' ? 'مدفوعة جزئياً' :
                 'غير مدفوعة'}
              </span>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b border-gray-300 pb-1">
            بيانات العميل
          </h3>
          <div className="space-y-2">
            <div>
              <span className="text-gray-600">اسم العميل:</span>
              <p className="font-semibold">{customer?.name || invoice?.customerName}</p>
            </div>
            {customer?.email && (
              <div>
                <span className="text-gray-600">البريد الإلكتروني:</span>
                <p className="font-semibold">{customer.email}</p>
              </div>
            )}
            {customer?.phone && (
              <div>
                <span className="text-gray-600">الهاتف:</span>
                <p className="font-semibold">{customer.phone}</p>
              </div>
            )}
            {customer?.address && (
              <div>
                <span className="text-gray-600">العنوان:</span>
                <p className="font-semibold">{customer.address}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">تفاصيل الفاتورة</h3>
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-100">
              <th className="border border-gray-300 px-4 py-3 text-right font-semibold">#</th>
              <th className="border border-gray-300 px-4 py-3 text-right font-semibold">الصنف</th>
              <th className="border border-gray-300 px-4 py-3 text-center font-semibold">الكمية</th>
              <th className="border border-gray-300 px-4 py-3 text-center font-semibold">السعر</th>
              <th className="border border-gray-300 px-4 py-3 text-center font-semibold">الخصم</th>
              <th className="border border-gray-300 px-4 py-3 text-center font-semibold">الإجمالي</th>
            </tr>
          </thead>
          <tbody>
            {invoice?.items?.map((item, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="border border-gray-300 px-4 py-3 text-center">{index + 1}</td>
                <td className="border border-gray-300 px-4 py-3">
                  <div>
                    <p className="font-medium">{item.productName || item.name}</p>
                    {item.customizationDetails && (
                      <div className="text-xs text-gray-600 mt-1">
                        {item.customizationDetails.map((detail, idx) => (
                          <p key={idx}>• {detail.optionName}: {detail.selectedName}</p>
                        ))}
                      </div>
                    )}
                  </div>
                </td>
                <td className="border border-gray-300 px-4 py-3 text-center">{item.quantity}</td>
                <td className="border border-gray-300 px-4 py-3 text-center">
                  {formatCurrency(item.unitPrice)}
                </td>
                <td className="border border-gray-300 px-4 py-3 text-center">
                  {item.discount ? `${item.discount}%` : '-'}
                </td>
                <td className="border border-gray-300 px-4 py-3 text-center font-semibold">
                  {formatCurrency(item.total)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Totals */}
      <div className="flex justify-end mb-8">
        <div className="w-80">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>المجموع الفرعي:</span>
                <span className="font-semibold">{formatCurrency(invoice?.subtotal || 0)}</span>
              </div>
              <div className="flex justify-between">
                <span>الضريبة (14%):</span>
                <span className="font-semibold">{formatCurrency(invoice?.taxAmount || 0)}</span>
              </div>
              <div className="border-t border-gray-300 pt-2 flex justify-between text-lg font-bold">
                <span>الإجمالي:</span>
                <span className="text-blue-600">{formatCurrency(invoice?.total || 0)}</span>
              </div>
              {invoice?.paidAmount > 0 && (
                <>
                  <div className="flex justify-between text-green-600">
                    <span>المدفوع:</span>
                    <span className="font-semibold">{formatCurrency(invoice.paidAmount)}</span>
                  </div>
                  <div className="flex justify-between text-red-600 font-semibold">
                    <span>المتبقي:</span>
                    <span>{formatCurrency(invoice.total - invoice.paidAmount)}</span>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Payment Details */}
      {invoice?.payments && invoice.payments.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">تفاصيل المدفوعات</h3>
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 px-4 py-3 text-right font-semibold">التاريخ</th>
                <th className="border border-gray-300 px-4 py-3 text-right font-semibold">طريقة الدفع</th>
                <th className="border border-gray-300 px-4 py-3 text-center font-semibold">المبلغ</th>
                <th className="border border-gray-300 px-4 py-3 text-right font-semibold">المرجع</th>
              </tr>
            </thead>
            <tbody>
              {invoice.payments.map((payment, index) => (
                <tr key={index}>
                  <td className="border border-gray-300 px-4 py-3">
                    {formatDate(payment.paidAt)}
                  </td>
                  <td className="border border-gray-300 px-4 py-3">
                    {payment.method === 'CASH' ? 'نقدي' :
                     payment.method === 'INSTAPAY' ? 'إنستاباي' :
                     payment.method === 'VISA' ? 'فيزا' :
                     payment.method === 'BANK_TRANSFER' ? 'تحويل بنكي' :
                     payment.method === 'INSTALLMENT' ? 'أقساط' :
                     payment.method}
                  </td>
                  <td className="border border-gray-300 px-4 py-3 text-center font-semibold">
                    {formatCurrency(payment.amount)}
                  </td>
                  <td className="border border-gray-300 px-4 py-3">
                    {payment.reference || '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Notes */}
      {invoice?.notes && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">ملاحظات</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-gray-700">{invoice.notes}</p>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="border-t-2 border-gray-300 pt-6 mt-8">
        <div className="text-center text-gray-600">
          <p className="mb-2">شكراً لتعاملكم معنا</p>
          <p className="text-sm">هذه فاتورة إلكترونية ولا تحتاج إلى توقيع</p>
          <p className="text-xs mt-4">تم إنشاء هذه الفاتورة بواسطة نظام إدارة الأعمال</p>
        </div>
      </div>
    </div>
  );
});

InvoicePrintView.displayName = 'InvoicePrintView';

export default InvoicePrintView;
