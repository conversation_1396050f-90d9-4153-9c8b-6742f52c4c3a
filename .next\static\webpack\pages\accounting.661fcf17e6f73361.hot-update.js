"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/accounting",{

/***/ "./pages/accounting.js":
/*!*****************************!*\
  !*** ./pages/accounting.js ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: function() { return /* binding */ __N_SSG; },\n/* harmony export */   \"default\": function() { return /* binding */ Accounting; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ArrowsRightLeftIcon,BanknotesIcon,BuildingLibraryIcon,CreditCardIcon,CurrencyDollarIcon,MagnifyingGlassIcon,PlusIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ArrowsRightLeftIcon,BanknotesIcon,BuildingLibraryIcon,CreditCardIcon,CurrencyDollarIcon,MagnifyingGlassIcon,PlusIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/LoadingSpinner */ \"./components/LoadingSpinner.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Accounting() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedBranch, setSelectedBranch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showAddTransactionModal, setShowAddTransactionModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTransferModal, setShowTransferModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dateFrom, setDateFrom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [dateTo, setDateTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Set default date range (last 30 days)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);\n        setDateFrom(thirtyDaysAgo.toISOString().split(\"T\")[0]);\n        setDateTo(today.toISOString().split(\"T\")[0]);\n    }, []);\n    // Fetch cash transactions\n    const { data: transactionsData, isLoading, error, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        \"transactions\",\n        currentPage,\n        searchTerm,\n        selectedType,\n        selectedBranch,\n        dateFrom,\n        dateTo\n    ], async ()=>{\n        const params = new URLSearchParams({\n            page: currentPage.toString(),\n            limit: \"10\",\n            search: searchTerm,\n            type: selectedType,\n            branchId: selectedBranch\n        });\n        if (dateFrom) params.append(\"dateFrom\", dateFrom);\n        if (dateTo) params.append(\"dateTo\", dateTo);\n        const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(\"http://localhost:3001\", \"/api/cash-transactions?\").concat(params));\n        return response.data;\n    }, {\n        keepPreviousData: true,\n        enabled: !!(dateFrom && dateTo)\n    });\n    // Fetch branches for filter\n    const { data: branchesData } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)(\"branches\", async ()=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"\".concat(\"http://localhost:3001\", \"/api/branches\"));\n        return response.data;\n    });\n    const transactions = (transactionsData === null || transactionsData === void 0 ? void 0 : transactionsData.transactions) || [];\n    const metrics = (transactionsData === null || transactionsData === void 0 ? void 0 : transactionsData.metrics) || {};\n    const branches = (branchesData === null || branchesData === void 0 ? void 0 : branchesData.branches) || [];\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        setCurrentPage(1);\n        refetch();\n    };\n    // Add transaction mutation\n    const addTransactionMutation = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)(async (transactionData)=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"\".concat(\"http://localhost:3001\", \"/api/cash-transactions\"), transactionData);\n        return response.data;\n    }, {\n        onSuccess: ()=>{\n            queryClient.invalidateQueries([\n                \"transactions\"\n            ]);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Transaction added successfully\");\n            setShowAddTransactionModal(false);\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"Failed to add transaction\");\n        }\n    });\n    // Transfer funds mutation\n    const transferMutation = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)(async (transferData)=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"\".concat(\"http://localhost:3001\", \"/api/cash-boxes/transfer\"), transferData);\n        return response.data;\n    }, {\n        onSuccess: ()=>{\n            queryClient.invalidateQueries([\n                \"transactions\"\n            ]);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Transfer completed successfully\");\n            setShowTransferModal(false);\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"Failed to transfer funds\");\n        }\n    });\n    const getTransactionIcon = (type)=>{\n        switch(type){\n            case \"INCOME\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.ArrowUpIcon, {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 119,\n                    columnNumber: 16\n                }, this);\n            case \"EXPENSE\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.ArrowDownIcon, {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 121,\n                    columnNumber: 16\n                }, this);\n            case \"TRANSFER_IN\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.ArrowsRightLeftIcon, {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 123,\n                    columnNumber: 16\n                }, this);\n            case \"TRANSFER_OUT\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.ArrowsRightLeftIcon, {\n                    className: \"h-4 w-4 text-orange-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 125,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.CurrencyDollarIcon, {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 127,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getTransactionColor = (type)=>{\n        switch(type){\n            case \"INCOME\":\n                return \"bg-green-100 text-green-800\";\n            case \"EXPENSE\":\n                return \"bg-red-100 text-red-800\";\n            case \"TRANSFER_IN\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"TRANSFER_OUT\":\n                return \"bg-orange-100 text-orange-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getPaymentMethodIcon = (method)=>{\n        switch(method){\n            case \"CASH\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.BanknotesIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 149,\n                    columnNumber: 16\n                }, this);\n            case \"VISA\":\n            case \"INSTAPAY\":\n            case \"VODAFONE_CASH\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.CreditCardIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 153,\n                    columnNumber: 16\n                }, this);\n            case \"BANK_TRANSFER\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.BuildingLibraryIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 155,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.CurrencyDollarIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 157,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (isLoading && !transactions.length) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"mt-2 text-sm font-medium text-gray-900\",\n                    children: t(\"common.error\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: \"Failed to load accounting data\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>refetch(),\n                    className: \"mt-4 btn-primary\",\n                    children: \"Try Again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n            lineNumber: 171,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: t(\"navigation.accounting\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-600\",\n                                children: \"Manage cash flow, transactions, and financial records\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3 rtl:space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowTransferModal(true),\n                                className: \"btn-secondary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.ArrowsRightLeftIcon, {\n                                        className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Transfer Funds\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddTransactionModal(true),\n                                className: \"btn-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.PlusIcon, {\n                                        className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Transaction\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.ArrowUpIcon, {\n                                            className: \"h-6 w-6 text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: \"Total Income\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"text-lg font-medium text-green-600\",\n                                                    children: [\n                                                        \"$\",\n                                                        (metrics.totalIncome || 0).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.ArrowDownIcon, {\n                                            className: \"h-6 w-6 text-red-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: \"Total Expenses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"text-lg font-medium text-red-600\",\n                                                    children: [\n                                                        \"$\",\n                                                        (metrics.totalExpense || 0).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.CurrencyDollarIcon, {\n                                            className: \"h-6 w-6 \".concat((metrics.netCashFlow || 0) >= 0 ? \"text-green-400\" : \"text-red-400\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: \"Net Cash Flow\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"text-lg font-medium \".concat((metrics.netCashFlow || 0) >= 0 ? \"text-green-600\" : \"text-red-600\"),\n                                                    children: [\n                                                        \"$\",\n                                                        (metrics.netCashFlow || 0).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.ArrowsRightLeftIcon, {\n                                            className: \"h-6 w-6 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: \"Total Transactions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: metrics.totalTransactions || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-4 rounded-lg shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col sm:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.MagnifyingGlassIcon, {\n                                        className: \"absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t(\"common.search\"),\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"form-input pl-10 rtl:pl-3 rtl:pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: dateFrom,\n                                onChange: (e)=>setDateFrom(e.target.value),\n                                className: \"form-input\",\n                                placeholder: \"From Date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: dateTo,\n                                onChange: (e)=>setDateTo(e.target.value),\n                                className: \"form-input\",\n                                placeholder: \"To Date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedType,\n                            onChange: (e)=>setSelectedType(e.target.value),\n                            className: \"form-input\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"all\",\n                                    children: \"All Types\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"income\",\n                                    children: \"Income\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"expense\",\n                                    children: \"Expense\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"transfer_in\",\n                                    children: \"Transfer In\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"transfer_out\",\n                                    children: \"Transfer Out\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedBranch,\n                            onChange: (e)=>setSelectedBranch(e.target.value),\n                            className: \"form-input\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"all\",\n                                    children: \"All Branches\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this),\n                                branches.map((branch)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: branch.id,\n                                        children: branch.name\n                                    }, branch.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary\",\n                            children: t(\"common.search\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"table\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Branch\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Payment Method\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Reference\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"common.actions\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: transactions.map((transaction)=>{\n                                        var _transaction_cashBox_branch, _transaction_cashBox, _transaction_cashBox_branch1, _transaction_cashBox1, _transaction_paymentMethod;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: new Date(transaction.createdAt).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            getTransactionIcon(transaction.type),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 rtl:ml-0 rtl:mr-2 badge \".concat(getTransactionColor(transaction.type)),\n                                                                children: transaction.type.replace(\"_\", \" \")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-w-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900 truncate\",\n                                                                children: transaction.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 truncate\",\n                                                                children: transaction.descriptionAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: (_transaction_cashBox = transaction.cashBox) === null || _transaction_cashBox === void 0 ? void 0 : (_transaction_cashBox_branch = _transaction_cashBox.branch) === null || _transaction_cashBox_branch === void 0 ? void 0 : _transaction_cashBox_branch.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: (_transaction_cashBox1 = transaction.cashBox) === null || _transaction_cashBox1 === void 0 ? void 0 : (_transaction_cashBox_branch1 = _transaction_cashBox1.branch) === null || _transaction_cashBox_branch1 === void 0 ? void 0 : _transaction_cashBox_branch1.nameAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            getPaymentMethodIcon(transaction.paymentMethod),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 rtl:ml-0 rtl:mr-2 text-sm\",\n                                                                children: ((_transaction_paymentMethod = transaction.paymentMethod) === null || _transaction_paymentMethod === void 0 ? void 0 : _transaction_paymentMethod.replace(\"_\", \" \")) || \"CASH\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium \".concat(transaction.type === \"INCOME\" || transaction.type === \"TRANSFER_IN\" ? \"text-green-600\" : \"text-red-600\"),\n                                                        children: [\n                                                            transaction.type === \"INCOME\" || transaction.type === \"TRANSFER_IN\" ? \"+\" : \"-\",\n                                                            \"$\",\n                                                            (parseFloat(transaction.amount) || 0).toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: transaction.reference || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 text-gray-400 hover:text-blue-600\",\n                                                            title: t(\"common.view\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.MagnifyingGlassIcon, {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, transaction.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 360,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this),\n                    transactions.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.CurrencyDollarIcon, {\n                                className: \"mx-auto h-12 w-12 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"mt-2 text-sm font-medium text-gray-900\",\n                                children: \"No transactions found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-500\",\n                                children: \"No transactions match your current filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.PlusIcon, {\n                                            className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Add Transaction\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 446,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 440,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n        lineNumber: 185,\n        columnNumber: 5\n    }, this);\n}\n_s(Accounting, \"KAyecOTTtjq6xS097ozOKFoHNfY=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation\n    ];\n});\n_c = Accounting;\nvar _c;\n$RefreshReg$(_c, \"Accounting\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/accounting.js\n"));

/***/ })

});