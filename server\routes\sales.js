const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Get all sales orders with pagination and filters
router.get('/', authenticateToken, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = 'all',
      branchId = 'all',
      customerId = 'all',
      dateFrom,
      dateTo,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Build where clause
    const where = {
      AND: [
        search ? {
          OR: [
            { orderNumber: { contains: search, mode: 'insensitive' } },
            { customer: { name: { contains: search, mode: 'insensitive' } } },
            { customer: { nameAr: { contains: search, mode: 'insensitive' } } }
          ]
        } : {},
        status !== 'all' ? { status: status.toUpperCase() } : {},
        branchId !== 'all' ? { branchId } : {},
        customerId !== 'all' ? { customerId } : {},
        dateFrom ? { orderDate: { gte: new Date(dateFrom) } } : {},
        dateTo ? { orderDate: { lte: new Date(dateTo) } } : {},
        // Filter by user's branch if not admin/manager
        req.user.role !== 'ADMIN' && req.user.role !== 'MANAGER' && req.user.branchId
          ? { branchId: req.user.branchId } : {}
      ]
    };

    const [orders, total] = await Promise.all([
      prisma.salesOrder.findMany({
        where,
        include: {
          customer: {
            select: { id: true, name: true, nameAr: true, code: true }
          },
          user: {
            select: { id: true, firstName: true, lastName: true }
          },
          branch: {
            select: { id: true, name: true, nameAr: true }
          },
          items: {
            include: {
              product: {
                select: { id: true, name: true, nameAr: true, code: true }
              }
            }
          },
          payments: true
        },
        orderBy: { [sortBy]: sortOrder },
        skip,
        take
      }),
      prisma.salesOrder.count({ where })
    ]);

    res.json({
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Get sales orders error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get single sales order
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const order = await prisma.salesOrder.findUnique({
      where: { id: req.params.id },
      include: {
        customer: true,
        user: {
          select: { id: true, firstName: true, lastName: true }
        },
        branch: true,
        items: {
          include: {
            product: true
          }
        },
        payments: {
          include: {
            creator: {
              select: { id: true, firstName: true, lastName: true }
            }
          }
        }
      }
    });

    if (!order) {
      return res.status(404).json({ error: 'Sales order not found' });
    }

    // Check branch access
    if (req.user.role !== 'ADMIN' && req.user.role !== 'MANAGER' &&
        req.user.branchId && order.branchId !== req.user.branchId) {
      return res.status(403).json({ error: 'Access denied to this branch order' });
    }

    res.json({ order });

  } catch (error) {
    console.error('Get sales order error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create new sales order
router.post('/', authenticateToken, authorizeRoles('ADMIN', 'MANAGER', 'SALES'), async (req, res) => {
  try {
    const {
      customerId,
      branchId,
      dueDate,
      items,
      taxAmount = 0,
      discount = 0,
      notes,
      payments = []
    } = req.body;

    // Validation
    if (!customerId || !branchId || !items || items.length === 0) {
      return res.status(400).json({ error: 'Required fields are missing' });
    }

    // Check branch access
    if (req.user.role !== 'ADMIN' && req.user.role !== 'MANAGER' &&
        req.user.branchId && branchId !== req.user.branchId) {
      return res.status(403).json({ error: 'Access denied to this branch' });
    }

    // Verify customer exists
    const customer = await prisma.customer.findUnique({
      where: { id: customerId }
    });

    if (!customer) {
      return res.status(400).json({ error: 'Customer not found' });
    }

    // Verify branch exists
    const branch = await prisma.branch.findUnique({
      where: { id: branchId }
    });

    if (!branch) {
      return res.status(400).json({ error: 'Branch not found' });
    }

    // Generate order number
    const orderCount = await prisma.salesOrder.count();
    const orderNumber = `SO-${String(orderCount + 1).padStart(6, '0')}`;

    // Calculate totals
    let subtotal = 0;
    const validatedItems = [];

    for (const item of items) {
      const product = await prisma.product.findUnique({
        where: { id: item.productId }
      });

      if (!product) {
        return res.status(400).json({ error: `Product ${item.productId} not found` });
      }

      // Check branch inventory
      const branchInventory = await prisma.branchInventory.findUnique({
        where: {
          branchId_productId: {
            branchId,
            productId: item.productId
          }
        }
      });

      if (!branchInventory || branchInventory.quantity < item.quantity) {
        return res.status(400).json({
          error: `Insufficient inventory for ${product.name}. Available: ${branchInventory?.quantity || 0}`
        });
      }

      const itemTotal = item.quantity * item.unitPrice - (item.discount || 0);
      subtotal += itemTotal;

      validatedItems.push({
        productId: item.productId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        discount: item.discount || 0,
        total: itemTotal
      });
    }

    const total = subtotal + parseFloat(taxAmount) - parseFloat(discount);
    const totalPayments = payments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
    const remainingAmount = total - totalPayments;

    // Create sales order with transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the order
      const order = await tx.salesOrder.create({
        data: {
          orderNumber,
          customerId,
          userId: req.user.id,
          branchId,
          dueDate: dueDate ? new Date(dueDate) : null,
          subtotal,
          taxAmount: parseFloat(taxAmount),
          discount: parseFloat(discount),
          total,
          paidAmount: totalPayments,
          remainingAmount,
          paymentStatus: remainingAmount <= 0 ? 'COMPLETED' : totalPayments > 0 ? 'PARTIAL' : 'PENDING',
          notes,
          items: {
            create: validatedItems
          }
        },
        include: {
          customer: true,
          branch: true,
          items: {
            include: {
              product: true
            }
          }
        }
      });

      // Reserve inventory
      for (const item of validatedItems) {
        await tx.branchInventory.update({
          where: {
            branchId_productId: {
              branchId,
              productId: item.productId
            }
          },
          data: {
            reservedQty: {
              increment: item.quantity
            }
          }
        });
      }

      // Process payments
      for (const payment of payments) {
        await tx.payment.create({
          data: {
            salesOrderId: order.id,
            method: payment.method,
            amount: parseFloat(payment.amount),
            reference: payment.reference,
            installmentPlan: payment.installmentPlan,
            status: 'COMPLETED',
            paidAt: new Date(),
            createdBy: req.user.id
          }
        });

        // Add cash transaction if cash payment
        if (payment.method === 'CASH') {
          const cashBox = await tx.cashBox.findUnique({
            where: { branchId }
          });

          if (cashBox) {
            await tx.cashTransaction.create({
              data: {
                cashBoxId: cashBox.id,
                type: 'INCOME',
                amount: parseFloat(payment.amount),
                description: `Sales payment for order ${orderNumber}`,
                descriptionAr: `دفعة مبيعات للطلب ${orderNumber}`,
                referenceId: order.id,
                referenceType: 'SALES',
                userId: req.user.id
              }
            });

            await tx.cashBox.update({
              where: { id: cashBox.id },
              data: {
                currentBalance: {
                  increment: parseFloat(payment.amount)
                }
              }
            });
          }
        }
      }

      return order;
    });

    // Emit real-time update
    const io = req.app.get('io');
    io.emit('new_order', {
      type: 'sales',
      orderNumber: result.orderNumber,
      customerName: result.customer.name,
      total: result.total
    });

    res.status(201).json({
      order: result,
      message: 'Sales order created successfully'
    });

  } catch (error) {
    console.error('Create sales order error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
