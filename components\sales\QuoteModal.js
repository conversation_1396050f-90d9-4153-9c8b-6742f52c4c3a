import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { XMarkIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import axios from 'axios';
import toast from 'react-hot-toast';

export default function QuoteModal({ isOpen, onClose, onSave, quote = null }) {
  const { t } = useTranslation('common');
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  
  const [formData, setFormData] = useState({
    customerId: '',
    validUntil: '',
    notes: '',
    items: []
  });

  // Load customers and products
  useEffect(() => {
    if (isOpen) {
      loadCustomers();
      loadProducts();
      
      if (quote) {
        setFormData({
          customerId: quote.customerId || '',
          validUntil: quote.validUntil ? quote.validUntil.split('T')[0] : '',
          notes: quote.notes || '',
          items: quote.items || []
        });
      } else {
        // Set default valid until date (7 days from now)
        const validUntil = new Date();
        validUntil.setDate(validUntil.getDate() + 7);
        setFormData(prev => ({
          ...prev,
          validUntil: validUntil.toISOString().split('T')[0]
        }));
      }
    }
  }, [isOpen, quote]);

  const loadCustomers = async () => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/customers`);
      setCustomers(response.data.customers || []);
    } catch (error) {
      console.error('Error loading customers:', error);
    }
  };

  const loadProducts = async () => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/products`);
      setProducts(response.data.products || []);
    } catch (error) {
      console.error('Error loading products:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, {
        productId: '',
        productName: '',
        quantity: 1,
        unitPrice: 0,
        discount: 0,
        taxRate: 14,
        hasTax: true,
        total: 0,
        isCustomized: false,
        customizations: null,
        customizationDetails: []
      }]
    }));
  };

  const removeItem = (index) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  const updateItem = (index, field, value) => {
    setFormData(prev => {
      const newItems = [...prev.items];
      newItems[index] = { ...newItems[index], [field]: value };
      
      // Auto-calculate total
      if (field === 'productId') {
        const product = products.find(p => p.id === value);
        if (product) {
          newItems[index].unitPrice = parseFloat(product.unitPrice);
        }
      }
      
      if (field === 'quantity' || field === 'unitPrice' || field === 'discount') {
        const item = newItems[index];
        const subtotal = (parseFloat(item.quantity) || 0) * (parseFloat(item.unitPrice) || 0);
        const discountAmount = subtotal * ((parseFloat(item.discount) || 0) / 100);
        newItems[index].total = subtotal - discountAmount;
      }
      
      return { ...prev, items: newItems };
    });
  };

  const calculateTotals = () => {
    const subtotal = formData.items.reduce((sum, item) => sum + (parseFloat(item.total) || 0), 0);
    const taxAmount = subtotal * 0.14; // 14% tax
    const total = subtotal + taxAmount;
    
    return { subtotal, taxAmount, total };
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.customerId) {
      toast.error('يرجى اختيار العميل');
      return;
    }
    
    if (formData.items.length === 0) {
      toast.error('يرجى إضافة عنصر واحد على الأقل');
      return;
    }

    setLoading(true);
    
    try {
      const { subtotal, taxAmount, total } = calculateTotals();
      
      const quoteData = {
        ...formData,
        subtotal,
        taxAmount,
        total,
        status: 'DRAFT'
      };

      const response = quote 
        ? await axios.put(`${process.env.NEXT_PUBLIC_API_URL}/api/quotes/${quote.id}`, quoteData)
        : await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/api/quotes`, quoteData);

      toast.success(response.data.message || (quote ? 'تم تحديث عرض السعر' : 'تم إنشاء عرض السعر'));
      onSave(response.data.quote);
      onClose();
    } catch (error) {
      toast.error(error.response?.data?.error || 'حدث خطأ');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const { subtotal, taxAmount, total } = calculateTotals();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {quote ? 'تعديل عرض السعر' : 'عرض سعر جديد'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Customer and Date */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="form-label">العميل *</label>
              <select
                name="customerId"
                value={formData.customerId}
                onChange={handleChange}
                className="form-input"
                required
              >
                <option value="">اختر العميل</option>
                {customers.map(customer => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="form-label">صالح حتى *</label>
              <input
                type="date"
                name="validUntil"
                value={formData.validUntil}
                onChange={handleChange}
                className="form-input"
                required
              />
            </div>
          </div>

          {/* Items */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">العناصر</h3>
              <button
                type="button"
                onClick={addItem}
                className="btn-primary flex items-center"
              >
                <PlusIcon className="h-5 w-5 mr-2" />
                إضافة عنصر
              </button>
            </div>

            <div className="space-y-4">
              {formData.items.map((item, index) => (
                <div key={index} className="grid grid-cols-12 gap-4 items-end p-4 border rounded-lg">
                  <div className="col-span-4">
                    <label className="form-label">المنتج</label>
                    <select
                      value={item.productId}
                      onChange={(e) => updateItem(index, 'productId', e.target.value)}
                      className="form-input"
                    >
                      <option value="">اختر المنتج</option>
                      {products.map(product => (
                        <option key={product.id} value={product.id}>
                          {product.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="col-span-2">
                    <label className="form-label">الكمية</label>
                    <input
                      type="number"
                      value={item.quantity}
                      onChange={(e) => updateItem(index, 'quantity', e.target.value)}
                      className="form-input"
                      min="1"
                    />
                  </div>
                  
                  <div className="col-span-2">
                    <label className="form-label">السعر</label>
                    <input
                      type="number"
                      value={item.unitPrice}
                      onChange={(e) => updateItem(index, 'unitPrice', e.target.value)}
                      className="form-input"
                      step="0.01"
                    />
                  </div>
                  
                  <div className="col-span-2">
                    <label className="form-label">خصم %</label>
                    <input
                      type="number"
                      value={item.discount}
                      onChange={(e) => updateItem(index, 'discount', e.target.value)}
                      className="form-input"
                      min="0"
                      max="100"
                    />
                  </div>
                  
                  <div className="col-span-1">
                    <label className="form-label">الإجمالي</label>
                    <div className="text-sm font-medium text-gray-900 py-2">
                      ${(item.total || 0).toFixed(2)}
                    </div>
                  </div>
                  
                  <div className="col-span-1">
                    <button
                      type="button"
                      onClick={() => removeItem(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Totals */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>المجموع الفرعي:</span>
                <span>${subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>الضريبة (14%):</span>
                <span>${taxAmount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between font-bold text-lg border-t pt-2">
                <span>الإجمالي:</span>
                <span>${total.toFixed(2)}</span>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="form-label">ملاحظات</label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              className="form-input"
              rows="3"
              placeholder="ملاحظات إضافية..."
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary"
            >
              {loading ? 'جاري الحفظ...' : (quote ? 'تحديث' : 'إنشاء')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
