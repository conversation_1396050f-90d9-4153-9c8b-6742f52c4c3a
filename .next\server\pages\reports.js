/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/reports";
exports.ids = ["pages/reports"];
exports.modules = {

/***/ "__barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArchiveBoxIcon: () => (/* reexport safe */ _ArchiveBoxIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CalculatorIcon: () => (/* reexport safe */ _CalculatorIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ChevronLeftIcon: () => (/* reexport safe */ _ChevronLeftIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   ChevronRightIcon: () => (/* reexport safe */ _ChevronRightIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Cog6ToothIcon: () => (/* reexport safe */ _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   CubeIcon: () => (/* reexport safe */ _CubeIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   DocumentChartBarIcon: () => (/* reexport safe */ _DocumentChartBarIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   HomeIcon: () => (/* reexport safe */ _HomeIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   ShoppingBagIcon: () => (/* reexport safe */ _ShoppingBagIcon_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   ShoppingCartIcon: () => (/* reexport safe */ _ShoppingCartIcon_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   UsersIcon: () => (/* reexport safe */ _UsersIcon_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   WrenchScrewdriverIcon: () => (/* reexport safe */ _WrenchScrewdriverIcon_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArchiveBoxIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArchiveBoxIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArchiveBoxIcon.js\");\n/* harmony import */ var _CalculatorIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CalculatorIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CalculatorIcon.js\");\n/* harmony import */ var _ChevronLeftIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChevronLeftIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChevronLeftIcon.js\");\n/* harmony import */ var _ChevronRightIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ChevronRightIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Cog6ToothIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _CubeIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CubeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _DocumentChartBarIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DocumentChartBarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/DocumentChartBarIcon.js\");\n/* harmony import */ var _HomeIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./HomeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _ShoppingBagIcon_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ShoppingBagIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _ShoppingCartIcon_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ShoppingCartIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js\");\n/* harmony import */ var _UsersIcon_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./UsersIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _WrenchScrewdriverIcon_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./WrenchScrewdriverIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcmNoaXZlQm94SWNvbixDYWxjdWxhdG9ySWNvbixDaGV2cm9uTGVmdEljb24sQ2hldnJvblJpZ2h0SWNvbixDb2c2VG9vdGhJY29uLEN1YmVJY29uLERvY3VtZW50Q2hhcnRCYXJJY29uLEhvbWVJY29uLFNob3BwaW5nQmFnSWNvbixTaG9wcGluZ0NhcnRJY29uLFVzZXJzSWNvbixXcmVuY2hTY3Jld2RyaXZlckljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDK0Q7QUFDQTtBQUNFO0FBQ0U7QUFDTjtBQUNWO0FBQ3dCO0FBQ3hCO0FBQ2M7QUFDRTtBQUNkIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVzaW5lc3MtbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz9mMDkwIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcmNoaXZlQm94SWNvbiB9IGZyb20gXCIuL0FyY2hpdmVCb3hJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2FsY3VsYXRvckljb24gfSBmcm9tIFwiLi9DYWxjdWxhdG9ySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZXZyb25MZWZ0SWNvbiB9IGZyb20gXCIuL0NoZXZyb25MZWZ0SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZXZyb25SaWdodEljb24gfSBmcm9tIFwiLi9DaGV2cm9uUmlnaHRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29nNlRvb3RoSWNvbiB9IGZyb20gXCIuL0NvZzZUb290aEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDdWJlSWNvbiB9IGZyb20gXCIuL0N1YmVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRG9jdW1lbnRDaGFydEJhckljb24gfSBmcm9tIFwiLi9Eb2N1bWVudENoYXJ0QmFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhvbWVJY29uIH0gZnJvbSBcIi4vSG9tZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTaG9wcGluZ0JhZ0ljb24gfSBmcm9tIFwiLi9TaG9wcGluZ0JhZ0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTaG9wcGluZ0NhcnRJY29uIH0gZnJvbSBcIi4vU2hvcHBpbmdDYXJ0SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXJzSWNvbiB9IGZyb20gXCIuL1VzZXJzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFdyZW5jaFNjcmV3ZHJpdmVySWNvbiB9IGZyb20gXCIuL1dyZW5jaFNjcmV3ZHJpdmVySWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,CubeIcon,CurrencyDollarIcon,DocumentTextIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,CubeIcon,CurrencyDollarIcon,DocumentTextIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowDownTrayIcon: () => (/* reexport safe */ _ArrowDownTrayIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CalendarIcon: () => (/* reexport safe */ _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ChartBarIcon: () => (/* reexport safe */ _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   CubeIcon: () => (/* reexport safe */ _CubeIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   CurrencyDollarIcon: () => (/* reexport safe */ _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   DocumentTextIcon: () => (/* reexport safe */ _DocumentTextIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   ShoppingBagIcon: () => (/* reexport safe */ _ShoppingBagIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   ShoppingCartIcon: () => (/* reexport safe */ _ShoppingCartIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   UsersIcon: () => (/* reexport safe */ _UsersIcon_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   WrenchScrewdriverIcon: () => (/* reexport safe */ _WrenchScrewdriverIcon_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowDownTrayIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowDownTrayIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _CalendarIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CalendarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _ChartBarIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChartBarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _CubeIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CubeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CurrencyDollarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _DocumentTextIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./DocumentTextIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _ShoppingBagIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ShoppingBagIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _ShoppingCartIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ShoppingCartIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js\");\n/* harmony import */ var _UsersIcon_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./UsersIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _WrenchScrewdriverIcon_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./WrenchScrewdriverIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd0Rvd25UcmF5SWNvbixDYWxlbmRhckljb24sQ2hhcnRCYXJJY29uLEN1YmVJY29uLEN1cnJlbmN5RG9sbGFySWNvbixEb2N1bWVudFRleHRJY29uLFNob3BwaW5nQmFnSWNvbixTaG9wcGluZ0NhcnRJY29uLFVzZXJzSWNvbixXcmVuY2hTY3Jld2RyaXZlckljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNxRTtBQUNWO0FBQ0E7QUFDUjtBQUNvQjtBQUNKO0FBQ0Y7QUFDRTtBQUNkIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVzaW5lc3MtbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz84ZWU4Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd0Rvd25UcmF5SWNvbiB9IGZyb20gXCIuL0Fycm93RG93blRyYXlJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2FsZW5kYXJJY29uIH0gZnJvbSBcIi4vQ2FsZW5kYXJJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hhcnRCYXJJY29uIH0gZnJvbSBcIi4vQ2hhcnRCYXJJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ3ViZUljb24gfSBmcm9tIFwiLi9DdWJlSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEN1cnJlbmN5RG9sbGFySWNvbiB9IGZyb20gXCIuL0N1cnJlbmN5RG9sbGFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIERvY3VtZW50VGV4dEljb24gfSBmcm9tIFwiLi9Eb2N1bWVudFRleHRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2hvcHBpbmdCYWdJY29uIH0gZnJvbSBcIi4vU2hvcHBpbmdCYWdJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2hvcHBpbmdDYXJ0SWNvbiB9IGZyb20gXCIuL1Nob3BwaW5nQ2FydEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2Vyc0ljb24gfSBmcm9tIFwiLi9Vc2Vyc0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBXcmVuY2hTY3Jld2RyaXZlckljb24gfSBmcm9tIFwiLi9XcmVuY2hTY3Jld2RyaXZlckljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,CubeIcon,CurrencyDollarIcon,DocumentTextIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRightOnRectangleIcon: () => (/* reexport safe */ _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Bars3Icon: () => (/* reexport safe */ _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   BellIcon: () => (/* reexport safe */ _BellIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   LanguageIcon: () => (/* reexport safe */ _LanguageIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   UserCircleIcon: () => (/* reexport safe */ _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowRightOnRectangleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Bars3Icon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _BellIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BellIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _LanguageIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LanguageIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/LanguageIcon.js\");\n/* harmony import */ var _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserCircleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uLEJhcnMzSWNvbixCZWxsSWNvbixMYW5ndWFnZUljb24sVXNlckNpcmNsZUljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQ3FGO0FBQ2hDO0FBQ0Y7QUFDUSIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/ODA3MiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJyb3dSaWdodE9uUmVjdGFuZ2xlSWNvbiB9IGZyb20gXCIuL0Fycm93UmlnaHRPblJlY3RhbmdsZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCYXJzM0ljb24gfSBmcm9tIFwiLi9CYXJzM0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCZWxsSWNvbiB9IGZyb20gXCIuL0JlbGxJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTGFuZ3VhZ2VJY29uIH0gZnJvbSBcIi4vTGFuZ3VhZ2VJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlckNpcmNsZUljb24gfSBmcm9tIFwiLi9Vc2VyQ2lyY2xlSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Freports&preferredRegion=&absolutePagePath=.%2Fpages%5Creports.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Freports&preferredRegion=&absolutePagePath=.%2Fpages%5Creports.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_reports_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\reports.js */ \"./pages/reports.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_reports_js__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_reports_js__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_reports_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/reports\",\n        pathname: \"/reports\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_reports_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Freports&preferredRegion=&absolutePagePath=.%2Fpages%5Creports.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/Header.js":
/*!******************************!*\
  !*** ./components/Header.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/SocketContext */ \"./contexts/SocketContext.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_5__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction Header({ sidebarOpen, setSidebarOpen }) {\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUserMenu, setShowUserMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { notifications, markNotificationAsRead } = (0,_contexts_SocketContext__WEBPACK_IMPORTED_MODULE_5__.useSocket)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const toggleLanguage = ()=>{\n        const newLocale = router.locale === \"ar\" ? \"en\" : \"ar\";\n        router.push(router.pathname, router.asPath, {\n            locale: newLocale\n        });\n    };\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-surface-primary shadow-soft border-b border-neutral-150\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between px-6 py-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setSidebarOpen(!sidebarOpen),\n                        className: \"p-2.5 rounded-xl text-text-secondary hover:text-text-primary hover:bg-neutral-75 focus-ring transition-all duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.Bars3Icon, {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleLanguage,\n                            className: \"p-2.5 rounded-xl text-text-secondary hover:text-text-primary hover:bg-neutral-75 focus-ring transition-all duration-200\",\n                            title: t(\"common.language\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.LanguageIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowNotifications(!showNotifications),\n                                    className: \"p-2.5 rounded-xl text-text-secondary hover:text-text-primary hover:bg-neutral-75 focus-ring transition-all duration-200 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.BellIcon, {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 bg-status-error text-white text-xs rounded-full h-5 w-5 flex items-center justify-center shadow-soft\",\n                                            children: unreadCount > 9 ? \"9+\" : unreadCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 rtl:right-auto rtl:left-0 mt-3 w-80 bg-surface-primary rounded-2xl shadow-large border border-neutral-100 z-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-5 border-b border-neutral-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-text-primary\",\n                                                children: t(\"dashboard.notifications\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 71,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-96 overflow-y-auto\",\n                                            children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 text-center text-gray-500\",\n                                                children: t(\"common.noData\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 77,\n                                                columnNumber: 21\n                                            }, this) : notifications.slice(0, 10).map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${!notification.read ? \"bg-blue-50\" : \"\"}`,\n                                                    onClick: ()=>markNotificationAsRead(notification.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3 rtl:space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `flex-shrink-0 w-2 h-2 rounded-full mt-2 ${notification.type === \"error\" ? \"bg-red-500\" : notification.type === \"warning\" ? \"bg-yellow-500\" : notification.type === \"success\" ? \"bg-green-500\" : \"bg-blue-500\"}`\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                lineNumber: 90,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: router.locale === \"ar\" ? notification.titleAr : notification.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                        lineNumber: 97,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: router.locale === \"ar\" ? notification.messageAr : notification.message\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                        lineNumber: 100,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: new Date(notification.timestamp).toLocaleString(router.locale)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                        lineNumber: 103,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                lineNumber: 96,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, notification.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowUserMenu(!showUserMenu),\n                                    className: \"flex items-center space-x-2 rtl:space-x-reverse p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.UserCircleIcon, {\n                                            className: \"h-8 w-8\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: [\n                                                user?.firstName,\n                                                \" \",\n                                                user?.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                showUserMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 rtl:right-auto rtl:left-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 py-2 text-sm text-gray-700 border-b border-gray-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            user?.firstName,\n                                                            \" \",\n                                                            user?.lastName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500\",\n                                                        children: user?.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 capitalize\",\n                                                        children: user?.role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowUserMenu(false);\n                                                    router.push(\"/profile\");\n                                                },\n                                                className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                children: t(\"auth.profile\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowUserMenu(false);\n                                                    logout();\n                                                },\n                                                className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ArrowRightOnRectangleIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t(\"auth.logout\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Header.js\n");

/***/ }),

/***/ "./components/Layout.js":
/*!******************************!*\
  !*** ./components/Layout.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Sidebar */ \"./components/Sidebar.js\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Header */ \"./components/Header.js\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./LoadingSpinner */ \"./components/LoadingSpinner.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _Header__WEBPACK_IMPORTED_MODULE_6__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _Header__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction Layout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAuthenticated, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    // Set document direction based on locale\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof document !== \"undefined\") {\n            const isArabic = router.locale === \"ar\";\n            document.documentElement.dir = isArabic ? \"rtl\" : \"ltr\";\n            document.documentElement.lang = router.locale || \"ar\";\n            // Add Arabic font class\n            if (isArabic) {\n                document.body.classList.add(\"font-arabic\");\n                document.body.classList.remove(\"font-english\");\n            } else {\n                document.body.classList.add(\"font-english\");\n                document.body.classList.remove(\"font-arabic\");\n            }\n        }\n    }, [\n        router.locale\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && !isAuthenticated) {\n            router.push(\"/login\");\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null; // Will redirect to login\n    }\n    const isRTL = router.locale === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-h-screen bg-neutral-50 ${isRTL ? \"rtl\" : \"ltr\"}`,\n        dir: isRTL ? \"rtl\" : \"ltr\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                open: sidebarOpen,\n                setOpen: setSidebarOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${sidebarOpen ? isRTL ? \"lg:mr-64\" : \"lg:ml-64\" : isRTL ? \"lg:mr-20\" : \"lg:ml-20\"} transition-all duration-300`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sidebarOpen: sidebarOpen,\n                        setSidebarOpen: setSidebarOpen\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"section-spacing\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"page-container\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"content-spacing\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout.js\n");

/***/ }),

/***/ "./components/LoadingSpinner.js":
/*!**************************************!*\
  !*** ./components/LoadingSpinner.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction LoadingSpinner({ size = \"medium\", className = \"\" }) {\n    const sizeClasses = {\n        small: \"h-4 w-4\",\n        medium: \"h-8 w-8\",\n        large: \"h-12 w-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `animate-spin rounded-full border-2 border-gray-300 border-t-primary-600 ${sizeClasses[size]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\LoadingSpinner.js\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvYWRpbmdTcGlubmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQSxlQUFlLEVBQUVDLE9BQU8sUUFBUSxFQUFFQyxZQUFZLEVBQUUsRUFBRTtJQUN4RSxNQUFNQyxjQUFjO1FBQ2xCQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsT0FBTztJQUNUO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlMLFdBQVcsQ0FBQyx3RUFBd0UsRUFBRUMsV0FBVyxDQUFDRixLQUFLLENBQUMsQ0FBQyxFQUFFQyxVQUFVLENBQUM7Ozs7OztBQUUvSCIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vY29tcG9uZW50cy9Mb2FkaW5nU3Bpbm5lci5qcz9lNjEwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmdTcGlubmVyKHsgc2l6ZSA9ICdtZWRpdW0nLCBjbGFzc05hbWUgPSAnJyB9KSB7XG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xuICAgIHNtYWxsOiAnaC00IHctNCcsXG4gICAgbWVkaXVtOiAnaC04IHctOCcsXG4gICAgbGFyZ2U6ICdoLTEyIHctMTInLFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGJvcmRlci0yIGJvcmRlci1ncmF5LTMwMCBib3JkZXItdC1wcmltYXJ5LTYwMCAke3NpemVDbGFzc2VzW3NpemVdfSAke2NsYXNzTmFtZX1gfSAvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkxvYWRpbmdTcGlubmVyIiwic2l6ZSIsImNsYXNzTmFtZSIsInNpemVDbGFzc2VzIiwic21hbGwiLCJtZWRpdW0iLCJsYXJnZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/LoadingSpinner.js\n");

/***/ }),

/***/ "./components/Sidebar.js":
/*!*******************************!*\
  !*** ./components/Sidebar.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.HomeIcon\n    },\n    {\n        name: \"products\",\n        href: \"/products\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CubeIcon\n    },\n    {\n        name: \"customers\",\n        href: \"/customers\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.UsersIcon\n    },\n    {\n        name: \"sales\",\n        href: \"/sales\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShoppingCartIcon\n    },\n    {\n        name: \"purchases\",\n        href: \"/purchases\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShoppingBagIcon\n    },\n    {\n        name: \"inventory\",\n        href: \"/inventory\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ArchiveBoxIcon\n    },\n    {\n        name: \"accounting\",\n        href: \"/accounting\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CalculatorIcon\n    },\n    {\n        name: \"maintenance\",\n        href: \"/maintenance\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.WrenchScrewdriverIcon\n    },\n    {\n        name: \"reports\",\n        href: \"/reports\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.DocumentChartBarIcon\n    },\n    {\n        name: \"settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.Cog6ToothIcon\n    }\n];\nfunction Sidebar({ open, setOpen }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-y-0 left-0 rtl:left-auto rtl:right-0 z-50 ${open ? \"w-64\" : \"w-20\"} bg-white shadow-lg transition-all duration-300 hidden lg:block`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-bold\",\n                                                children: \"BMS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 46,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: process.env.NEXT_PUBLIC_COMPANY_NAME || \"BMS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setOpen(!open),\n                                    className: \"p-1.5 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: open ? router.locale === \"ar\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronRightIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 58,\n                                        columnNumber: 42\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronLeftIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 58,\n                                        columnNumber: 85\n                                    }, this) : router.locale === \"ar\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronLeftIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 60,\n                                        columnNumber: 42\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronRightIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 60,\n                                        columnNumber: 84\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-2 py-4 space-y-1 overflow-y-auto\",\n                            children: navigation.map((item)=>{\n                                const isActive = router.pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    className: `group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${isActive ? \"bg-primary-100 text-primary-900\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: `${open ? \"mr-3 rtl:mr-0 rtl:ml-3\" : \"mx-auto\"} h-6 w-6 flex-shrink-0 ${isActive ? \"text-primary-600\" : \"text-gray-400 group-hover:text-gray-500\"}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 79,\n                                            columnNumber: 19\n                                        }, this),\n                                        open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: t(`navigation.${item.name}`)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 85,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 70,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-y-0 left-0 rtl:left-auto rtl:right-0 z-50 w-64 bg-white shadow-lg transform ${open ? \"translate-x-0 rtl:-translate-x-0\" : \"-translate-x-full rtl:translate-x-full\"} transition-transform duration-300 lg:hidden`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-bold\",\n                                                children: \"BMS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: process.env.NEXT_PUBLIC_COMPANY_NAME || \"BMS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setOpen(false),\n                                    className: \"p-1.5 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronLeftIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-2 py-4 space-y-1 overflow-y-auto\",\n                            children: navigation.map((item)=>{\n                                const isActive = router.pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    onClick: ()=>setOpen(false),\n                                    className: `group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${isActive ? \"bg-primary-100 text-primary-900\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: `mr-3 rtl:mr-0 rtl:ml-3 h-6 w-6 flex-shrink-0 ${isActive ? \"text-primary-600\" : \"text-gray-400 group-hover:text-gray-500\"}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: t(`navigation.${item.name}`)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Sidebar.js\n");

/***/ }),

/***/ "./contexts/AuthContext.js":
/*!*********************************!*\
  !*** ./contexts/AuthContext.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__]);\n([axios__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst initialState = {\n    user: null,\n    token: null,\n    isLoading: true,\n    isAuthenticated: false\n};\nfunction authReducer(state, action) {\n    switch(action.type){\n        case \"LOGIN_SUCCESS\":\n            return {\n                ...state,\n                user: action.payload.user,\n                token: action.payload.token,\n                isAuthenticated: true,\n                isLoading: false\n            };\n        case \"LOGOUT\":\n            return {\n                ...state,\n                user: null,\n                token: null,\n                isAuthenticated: false,\n                isLoading: false\n            };\n        case \"SET_LOADING\":\n            return {\n                ...state,\n                isLoading: action.payload\n            };\n        case \"UPDATE_USER\":\n            return {\n                ...state,\n                user: {\n                    ...state.user,\n                    ...action.payload\n                }\n            };\n        default:\n            return state;\n    }\n}\nfunction AuthProvider({ children }) {\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(authReducer, initialState);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Configure axios defaults\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const token = localStorage.getItem(\"token\");\n        if (token) {\n            axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n        }\n    }, []);\n    // Check for existing token on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            const token = localStorage.getItem(\"token\");\n            if (token) {\n                try {\n                    axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n                    const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(`${\"http://localhost:3001\"}/api/auth/profile`);\n                    dispatch({\n                        type: \"LOGIN_SUCCESS\",\n                        payload: {\n                            user: response.data.user,\n                            token: token\n                        }\n                    });\n                } catch (error) {\n                    console.error(\"Token validation failed:\", error);\n                    localStorage.removeItem(\"token\");\n                    delete axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"];\n                    dispatch({\n                        type: \"LOGOUT\"\n                    });\n                }\n            } else {\n                dispatch({\n                    type: \"SET_LOADING\",\n                    payload: false\n                });\n            }\n        };\n        checkAuth();\n    }, []);\n    const login = async (username, password)=>{\n        try {\n            dispatch({\n                type: \"SET_LOADING\",\n                payload: true\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(`${\"http://localhost:3001\"}/api/auth/login`, {\n                username,\n                password\n            });\n            const { token, user } = response.data;\n            // Store token in localStorage\n            localStorage.setItem(\"token\", token);\n            // Set axios default header\n            axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n            dispatch({\n                type: \"LOGIN_SUCCESS\",\n                payload: {\n                    user,\n                    token\n                }\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(response.data.message || \"Login successful\");\n            // Redirect to dashboard\n            router.push(\"/\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            dispatch({\n                type: \"SET_LOADING\",\n                payload: false\n            });\n            const errorMessage = error.response?.data?.error || \"Login failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    };\n    const logout = ()=>{\n        // Remove token from localStorage\n        localStorage.removeItem(\"token\");\n        // Remove axios default header\n        delete axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"];\n        dispatch({\n            type: \"LOGOUT\"\n        });\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Logged out successfully\");\n        router.push(\"/login\");\n    };\n    const updateProfile = async (profileData)=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put(`${\"http://localhost:3001\"}/api/auth/profile`, profileData);\n            dispatch({\n                type: \"UPDATE_USER\",\n                payload: response.data.user\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(response.data.message || \"Profile updated successfully\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            const errorMessage = error.response?.data?.error || \"Profile update failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    };\n    const changePassword = async (currentPassword, newPassword)=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put(`${\"http://localhost:3001\"}/api/auth/change-password`, {\n                currentPassword,\n                newPassword\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(response.data.message || \"Password changed successfully\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            const errorMessage = error.response?.data?.error || \"Password change failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    };\n    const value = {\n        ...state,\n        login,\n        logout,\n        updateProfile,\n        changePassword\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\contexts\\\\AuthContext.js\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthContext);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/AuthContext.js\n");

/***/ }),

/***/ "./contexts/SocketContext.js":
/*!***********************************!*\
  !*** ./contexts/SocketContext.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"socket.io-client\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([socket_io_client__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__]);\n([socket_io_client__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst SocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction SocketProvider({ children }) {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { token, isAuthenticated } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isAuthenticated && token) {\n            // Initialize socket connection\n            const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(\"http://localhost:3001\", {\n                auth: {\n                    token: token\n                }\n            });\n            newSocket.on(\"connect\", ()=>{\n                console.log(\"Socket connected\");\n                setIsConnected(true);\n            });\n            newSocket.on(\"disconnect\", ()=>{\n                console.log(\"Socket disconnected\");\n                setIsConnected(false);\n            });\n            // Listen for real-time events\n            newSocket.on(\"inventory_updated\", (data)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(`Inventory updated: ${data.productName}`);\n            // You can dispatch events to update local state here\n            });\n            newSocket.on(\"order_created\", (data)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].info(`New order created: ${data.orderNumber}`);\n                // Add notification\n                setNotifications((prev)=>[\n                        {\n                            id: Date.now(),\n                            type: \"info\",\n                            title: \"New Order\",\n                            message: `Order ${data.orderNumber} has been created`,\n                            timestamp: new Date(),\n                            read: false\n                        },\n                        ...prev\n                    ]);\n            });\n            newSocket.on(\"maintenance_updated\", (data)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].info(`Maintenance order ${data.orderNumber} status updated`);\n                // Add notification\n                setNotifications((prev)=>[\n                        {\n                            id: Date.now(),\n                            type: \"info\",\n                            title: \"Maintenance Update\",\n                            message: `Order ${data.orderNumber} status: ${data.status}`,\n                            timestamp: new Date(),\n                            read: false\n                        },\n                        ...prev\n                    ]);\n            });\n            newSocket.on(\"low_stock_alert\", (data)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].warning(`Low stock alert: ${data.productName}`);\n                // Add notification\n                setNotifications((prev)=>[\n                        {\n                            id: Date.now(),\n                            type: \"warning\",\n                            title: \"Low Stock Alert\",\n                            message: `${data.productName} is running low (${data.currentStock} remaining)`,\n                            timestamp: new Date(),\n                            read: false\n                        },\n                        ...prev\n                    ]);\n            });\n            setSocket(newSocket);\n            return ()=>{\n                newSocket.close();\n            };\n        } else {\n            // Clean up socket when not authenticated\n            if (socket) {\n                socket.close();\n                setSocket(null);\n                setIsConnected(false);\n            }\n        }\n    }, [\n        isAuthenticated,\n        token\n    ]);\n    const emitEvent = (eventName, data)=>{\n        if (socket && isConnected) {\n            socket.emit(eventName, data);\n        }\n    };\n    const markNotificationAsRead = (notificationId)=>{\n        setNotifications((prev)=>prev.map((notification)=>notification.id === notificationId ? {\n                    ...notification,\n                    read: true\n                } : notification));\n    };\n    const clearNotifications = ()=>{\n        setNotifications([]);\n    };\n    const value = {\n        socket,\n        isConnected,\n        notifications,\n        emitEvent,\n        markNotificationAsRead,\n        clearNotifications\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocketContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\contexts\\\\SocketContext.js\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\nfunction useSocket() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SocketContext);\n    if (!context) {\n        throw new Error(\"useSocket must be used within a SocketProvider\");\n    }\n    return context;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SocketContext);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/SocketContext.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../contexts/SocketContext */ \"./contexts/SocketContext.js\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_9__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_5__, _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__, _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_7__, _components_Layout__WEBPACK_IMPORTED_MODULE_8__]);\n([react_hot_toast__WEBPACK_IMPORTED_MODULE_5__, _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__, _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_7__, _components_Layout__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nconst queryClient = new react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n    defaultOptions: {\n        queries: {\n            retry: 1,\n            refetchOnWindowFocus: false\n        }\n    }\n});\nfunction MyApp({ Component, pageProps }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Set document direction based on locale\n        const direction = router.locale === \"ar\" ? \"rtl\" : \"ltr\";\n        document.documentElement.dir = direction;\n        document.documentElement.lang = router.locale;\n        // Set font family based on locale\n        const fontClass = router.locale === \"ar\" ? \"font-arabic\" : \"font-english\";\n        document.body.className = fontClass;\n    }, [\n        router.locale\n    ]);\n    // Check if it's a login page\n    const isLoginPage = router.pathname === \"/login\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_SocketContext__WEBPACK_IMPORTED_MODULE_7__.SocketProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `min-h-screen bg-gray-50 ${router.locale === \"ar\" ? \"font-arabic\" : \"font-english\"}`,\n                    children: [\n                        isLoginPage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...pageProps\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                            lineNumber: 43,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                ...pageProps\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                                lineNumber: 46,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                            lineNumber: 45,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                            position: router.locale === \"ar\" ? \"top-left\" : \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#363636\",\n                                    color: \"#fff\",\n                                    direction: router.locale === \"ar\" ? \"rtl\" : \"ltr\"\n                                },\n                                success: {\n                                    style: {\n                                        background: \"#10B981\"\n                                    }\n                                },\n                                error: {\n                                    style: {\n                                        background: \"#EF4444\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                    lineNumber: 41,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.appWithTranslation)(MyApp));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/reports.js":
/*!**************************!*\
  !*** ./pages/reports.js ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Reports),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next/serverSideTranslations */ \"next-i18next/serverSideTranslations\");\n/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,CubeIcon,CurrencyDollarIcon,DocumentTextIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowDownTrayIcon,CalendarIcon,ChartBarIcon,CubeIcon,CurrencyDollarIcon,DocumentTextIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/LoadingSpinner */ \"./components/LoadingSpinner.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_5__]);\naxios__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\nfunction Reports() {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [selectedReport, setSelectedReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"sales\");\n    const [dateFrom, setDateFrom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [dateTo, setDateTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedBranch, setSelectedBranch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    // Fetch branches for filter\n    const { data: branchesData } = (0,react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)(\"branches\", async ()=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(`${\"http://localhost:3001\"}/api/branches`);\n        return response.data;\n    });\n    // Fetch report data\n    const { data: reportData, isLoading, error, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)([\n        \"reports\",\n        selectedReport,\n        dateFrom,\n        dateTo,\n        selectedBranch\n    ], async ()=>{\n        if (!dateFrom || !dateTo) return null;\n        const params = new URLSearchParams({\n            dateFrom,\n            dateTo,\n            branchId: selectedBranch\n        });\n        const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(`${\"http://localhost:3001\"}/api/reports/${selectedReport}?${params}`);\n        return response.data;\n    }, {\n        enabled: !!(dateFrom && dateTo),\n        keepPreviousData: true\n    });\n    const branches = branchesData?.branches || [];\n    const reportTypes = [\n        {\n            id: \"sales\",\n            name: t(\"reports.salesReport\"),\n            nameAr: \"تقرير المبيعات\",\n            icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.ShoppingCartIcon,\n            color: \"bg-blue-500\",\n            description: \"Sales performance and revenue analysis\"\n        },\n        {\n            id: \"purchases\",\n            name: t(\"reports.purchaseReport\"),\n            nameAr: \"تقرير المشتريات\",\n            icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.ShoppingBagIcon,\n            color: \"bg-green-500\",\n            description: \"Purchase orders and supplier analysis\"\n        },\n        {\n            id: \"inventory\",\n            name: t(\"reports.inventoryReport\"),\n            nameAr: \"تقرير المخزون\",\n            icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.CubeIcon,\n            color: \"bg-yellow-500\",\n            description: \"Stock levels and inventory valuation\"\n        },\n        {\n            id: \"maintenance\",\n            name: t(\"reports.maintenanceReport\"),\n            nameAr: \"تقرير الصيانة\",\n            icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.WrenchScrewdriverIcon,\n            color: \"bg-purple-500\",\n            description: \"Maintenance services and performance\"\n        },\n        {\n            id: \"financial\",\n            name: t(\"reports.financialReport\"),\n            nameAr: \"التقرير المالي\",\n            icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.CurrencyDollarIcon,\n            color: \"bg-indigo-500\",\n            description: \"Cash flow and financial analysis\"\n        },\n        {\n            id: \"customers\",\n            name: t(\"reports.customerReport\"),\n            nameAr: \"تقرير العملاء\",\n            icon: _barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.UsersIcon,\n            color: \"bg-pink-500\",\n            description: \"Customer behavior and revenue analysis\"\n        }\n    ];\n    const handleGenerateReport = ()=>{\n        if (dateFrom && dateTo) {\n            refetch();\n        }\n    };\n    const handleExportReport = (format)=>{\n        // TODO: Implement export functionality\n        console.log(`Exporting ${selectedReport} report as ${format}`);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: t(\"reports.title\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-600\",\n                            children: \"إنشاء تقارير أعمال شاملة وتحليلات\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: t(\"reports.reportType\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: reportTypes.map((report)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `relative rounded-lg border-2 cursor-pointer transition-all ${selectedReport === report.id ? \"border-primary-500 bg-primary-50\" : \"border-gray-200 hover:border-gray-300\"}`,\n                                onClick: ()=>setSelectedReport(report.id),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex-shrink-0 p-2 rounded-lg ${report.color}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(report.icon, {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4 rtl:ml-0 rtl:mr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: report.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: report.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            }, report.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                        children: \"فلاتر التقرير\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.CalendarIcon, {\n                                                className: \"h-4 w-4 inline mr-1 rtl:mr-0 rtl:ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, this),\n                                            t(\"reports.from\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        value: dateFrom,\n                                        onChange: (e)=>setDateFrom(e.target.value),\n                                        className: \"form-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.CalendarIcon, {\n                                                className: \"h-4 w-4 inline mr-1 rtl:mr-0 rtl:ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, this),\n                                            t(\"reports.to\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        value: dateTo,\n                                        onChange: (e)=>setDateTo(e.target.value),\n                                        className: \"form-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Branch\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedBranch,\n                                        onChange: (e)=>setSelectedBranch(e.target.value),\n                                        className: \"form-input\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"All Branches\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this),\n                                            branches.map((branch)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: branch.id,\n                                                    children: branch.name\n                                                }, branch.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleGenerateReport,\n                                    disabled: !dateFrom || !dateTo,\n                                    className: \"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.ChartBarIcon, {\n                                            className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        t(\"reports.generate\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-12 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: \"large\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Generating report...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-12 rounded-lg shadow text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.DocumentTextIcon, {\n                            className: \"h-12 w-12 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium\",\n                            children: \"Error generating report\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm\",\n                            children: \"Please try again or contact support.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                    lineNumber: 228,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                lineNumber: 227,\n                columnNumber: 9\n            }, this),\n            reportData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: reportTypes.find((r)=>r.id === selectedReport)?.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: [\n                                                dateFrom,\n                                                \" to \",\n                                                dateTo,\n                                                selectedBranch !== \"all\" && ` • ${branches.find((b)=>b.id === selectedBranch)?.name}`\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleExportReport(\"pdf\"),\n                                            className: \"btn-secondary btn-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.ArrowDownTrayIcon, {\n                                                    className: \"h-4 w-4 mr-1 rtl:mr-0 rtl:ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"PDF\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleExportReport(\"excel\"),\n                                            className: \"btn-secondary btn-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.ArrowDownTrayIcon, {\n                                                    className: \"h-4 w-4 mr-1 rtl:mr-0 rtl:ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Excel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            reportData.metrics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                                children: Object.entries(reportData.metrics).map(([key, value])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"text-sm font-medium text-gray-500 capitalize\",\n                                                children: key.replace(/([A-Z])/g, \" $1\").trim()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 276,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-1 text-2xl font-semibold text-gray-900\",\n                                                children: typeof value === \"number\" && key.toLowerCase().includes(\"revenue\") || key.toLowerCase().includes(\"cost\") || key.toLowerCase().includes(\"total\") && key.toLowerCase().includes(\"value\") ? `$${value.toFixed(2)}` : typeof value === \"number\" && key.toLowerCase().includes(\"rate\") ? `${value.toFixed(1)}%` : value\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, key, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 275,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, this),\n                            reportData.orders && reportData.orders.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"Order Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"Customer/Supplier\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"Total\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: reportData.orders.slice(0, 10).map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"font-medium\",\n                                                                children: order.orderNumber\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: new Date(order.orderDate || order.receivedDate).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: order.customer?.name || order.supplier?.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    (order.total || order.actualCost || 0).toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"badge badge-secondary\",\n                                                                    children: order.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, order.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 304,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this),\n                                    reportData.orders.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center text-sm text-gray-500\",\n                                        children: [\n                                            \"Showing 10 of \",\n                                            reportData.orders.length,\n                                            \" records\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 319,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 293,\n                                columnNumber: 15\n                            }, this),\n                            reportData.inventory && reportData.inventory.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"table\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"Product\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"Branch\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"Current Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"Value\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                lineNumber: 331,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                            lineNumber: 330,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: reportData.inventory.slice(0, 10).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            children: item.product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            children: item.branch.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            children: [\n                                                                item.quantity,\n                                                                \" \",\n                                                                item.product.unit\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            children: [\n                                                                \"$\",\n                                                                (item.quantity * item.product.costPrice).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `badge ${item.quantity === 0 ? \"badge-danger\" : item.quantity <= item.minStock ? \"badge-warning\" : \"badge-success\"}`,\n                                                                children: item.quantity === 0 ? \"Out of Stock\" : item.quantity <= item.minStock ? \"Low Stock\" : \"In Stock\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                            lineNumber: 339,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                    lineNumber: 329,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 328,\n                                columnNumber: 15\n                            }, this),\n                            (!reportData.orders || reportData.orders.length === 0) && (!reportData.inventory || reportData.inventory.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.DocumentTextIcon, {\n                                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 366,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"mt-2 text-sm font-medium text-gray-900\",\n                                        children: \"No data found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 367,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-gray-500\",\n                                        children: \"No data available for the selected date range and filters.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                        lineNumber: 368,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                                lineNumber: 365,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this),\n            !dateFrom || !dateTo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-12 rounded-lg shadow text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_CalendarIcon_ChartBarIcon_CubeIcon_CurrencyDollarIcon_DocumentTextIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__.ChartBarIcon, {\n                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"mt-2 text-sm font-medium text-gray-900\",\n                        children: \"Select date range\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-sm text-gray-500\",\n                        children: \"Choose a date range and report type to generate your report.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n                lineNumber: 379,\n                columnNumber: 9\n            }, this) : null\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\reports.js\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\nasync function getStaticProps({ locale }) {\n    return {\n        props: {\n            ...await (0,next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__.serverSideTranslations)(locale, [\n                \"common\"\n            ])\n        }\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/reports.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next-i18next":
/*!*******************************!*\
  !*** external "next-i18next" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-i18next");

/***/ }),

/***/ "next-i18next/serverSideTranslations":
/*!******************************************************!*\
  !*** external "next-i18next/serverSideTranslations" ***!
  \******************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-i18next/serverSideTranslations");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-query":
/*!******************************!*\
  !*** external "react-query" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-query");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "socket.io-client":
/*!***********************************!*\
  !*** external "socket.io-client" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("socket.io-client");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Freports&preferredRegion=&absolutePagePath=.%2Fpages%5Creports.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();