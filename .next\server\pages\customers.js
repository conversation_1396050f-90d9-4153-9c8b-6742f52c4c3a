/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/customers";
exports.ids = ["pages/customers"];
exports.modules = {

/***/ "__barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArchiveBoxIcon: () => (/* reexport safe */ _ArchiveBoxIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CalculatorIcon: () => (/* reexport safe */ _CalculatorIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ChevronLeftIcon: () => (/* reexport safe */ _ChevronLeftIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   ChevronRightIcon: () => (/* reexport safe */ _ChevronRightIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Cog6ToothIcon: () => (/* reexport safe */ _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   CubeIcon: () => (/* reexport safe */ _CubeIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   DocumentChartBarIcon: () => (/* reexport safe */ _DocumentChartBarIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   HomeIcon: () => (/* reexport safe */ _HomeIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   ShoppingBagIcon: () => (/* reexport safe */ _ShoppingBagIcon_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   ShoppingCartIcon: () => (/* reexport safe */ _ShoppingCartIcon_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   UsersIcon: () => (/* reexport safe */ _UsersIcon_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   WrenchScrewdriverIcon: () => (/* reexport safe */ _WrenchScrewdriverIcon_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArchiveBoxIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArchiveBoxIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArchiveBoxIcon.js\");\n/* harmony import */ var _CalculatorIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CalculatorIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CalculatorIcon.js\");\n/* harmony import */ var _ChevronLeftIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChevronLeftIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChevronLeftIcon.js\");\n/* harmony import */ var _ChevronRightIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ChevronRightIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Cog6ToothIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _CubeIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CubeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _DocumentChartBarIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DocumentChartBarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/DocumentChartBarIcon.js\");\n/* harmony import */ var _HomeIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./HomeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _ShoppingBagIcon_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ShoppingBagIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _ShoppingCartIcon_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ShoppingCartIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js\");\n/* harmony import */ var _UsersIcon_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./UsersIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _WrenchScrewdriverIcon_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./WrenchScrewdriverIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcmNoaXZlQm94SWNvbixDYWxjdWxhdG9ySWNvbixDaGV2cm9uTGVmdEljb24sQ2hldnJvblJpZ2h0SWNvbixDb2c2VG9vdGhJY29uLEN1YmVJY29uLERvY3VtZW50Q2hhcnRCYXJJY29uLEhvbWVJY29uLFNob3BwaW5nQmFnSWNvbixTaG9wcGluZ0NhcnRJY29uLFVzZXJzSWNvbixXcmVuY2hTY3Jld2RyaXZlckljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDK0Q7QUFDQTtBQUNFO0FBQ0U7QUFDTjtBQUNWO0FBQ3dCO0FBQ3hCO0FBQ2M7QUFDRTtBQUNkIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVzaW5lc3MtbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz9mMDkwIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcmNoaXZlQm94SWNvbiB9IGZyb20gXCIuL0FyY2hpdmVCb3hJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2FsY3VsYXRvckljb24gfSBmcm9tIFwiLi9DYWxjdWxhdG9ySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZXZyb25MZWZ0SWNvbiB9IGZyb20gXCIuL0NoZXZyb25MZWZ0SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZXZyb25SaWdodEljb24gfSBmcm9tIFwiLi9DaGV2cm9uUmlnaHRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29nNlRvb3RoSWNvbiB9IGZyb20gXCIuL0NvZzZUb290aEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDdWJlSWNvbiB9IGZyb20gXCIuL0N1YmVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRG9jdW1lbnRDaGFydEJhckljb24gfSBmcm9tIFwiLi9Eb2N1bWVudENoYXJ0QmFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhvbWVJY29uIH0gZnJvbSBcIi4vSG9tZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTaG9wcGluZ0JhZ0ljb24gfSBmcm9tIFwiLi9TaG9wcGluZ0JhZ0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTaG9wcGluZ0NhcnRJY29uIH0gZnJvbSBcIi4vU2hvcHBpbmdDYXJ0SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXJzSWNvbiB9IGZyb20gXCIuL1VzZXJzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFdyZW5jaFNjcmV3ZHJpdmVySWNvbiB9IGZyb20gXCIuL1dyZW5jaFNjcmV3ZHJpdmVySWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRightOnRectangleIcon: () => (/* reexport safe */ _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Bars3Icon: () => (/* reexport safe */ _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   BellIcon: () => (/* reexport safe */ _BellIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   LanguageIcon: () => (/* reexport safe */ _LanguageIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   UserCircleIcon: () => (/* reexport safe */ _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowRightOnRectangleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Bars3Icon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _BellIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BellIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _LanguageIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LanguageIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/LanguageIcon.js\");\n/* harmony import */ var _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserCircleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uLEJhcnMzSWNvbixCZWxsSWNvbixMYW5ndWFnZUljb24sVXNlckNpcmNsZUljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQ3FGO0FBQ2hDO0FBQ0Y7QUFDUSIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/ODA3MiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJyb3dSaWdodE9uUmVjdGFuZ2xlSWNvbiB9IGZyb20gXCIuL0Fycm93UmlnaHRPblJlY3RhbmdsZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCYXJzM0ljb24gfSBmcm9tIFwiLi9CYXJzM0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCZWxsSWNvbiB9IGZyb20gXCIuL0JlbGxJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTGFuZ3VhZ2VJY29uIH0gZnJvbSBcIi4vTGFuZ3VhZ2VJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlckNpcmNsZUljb24gfSBmcm9tIFwiLi9Vc2VyQ2lyY2xlSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=CurrencyDollarIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,UserIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=CurrencyDollarIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,UserIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CurrencyDollarIcon: () => (/* reexport safe */ _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   EnvelopeIcon: () => (/* reexport safe */ _EnvelopeIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   MapPinIcon: () => (/* reexport safe */ _MapPinIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   PhoneIcon: () => (/* reexport safe */ _PhoneIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   UserIcon: () => (/* reexport safe */ _UserIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   XMarkIcon: () => (/* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CurrencyDollarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _EnvelopeIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EnvelopeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _MapPinIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MapPinIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _PhoneIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PhoneIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _UserIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DdXJyZW5jeURvbGxhckljb24sRW52ZWxvcGVJY29uLE1hcFBpbkljb24sUGhvbmVJY29uLFVzZXJJY29uLFhNYXJrSWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUN1RTtBQUNaO0FBQ0o7QUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVzaW5lc3MtbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz8yNWM3Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDdXJyZW5jeURvbGxhckljb24gfSBmcm9tIFwiLi9DdXJyZW5jeURvbGxhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBFbnZlbG9wZUljb24gfSBmcm9tIFwiLi9FbnZlbG9wZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNYXBQaW5JY29uIH0gZnJvbSBcIi4vTWFwUGluSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFBob25lSWNvbiB9IGZyb20gXCIuL1Bob25lSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXJJY29uIH0gZnJvbSBcIi4vVXNlckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBYTWFya0ljb24gfSBmcm9tIFwiLi9YTWFya0ljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=CurrencyDollarIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,UserIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon,TruckIcon,UserIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!**********************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon,TruckIcon,UserIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \**********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EyeIcon: () => (/* reexport safe */ _EyeIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   MagnifyingGlassIcon: () => (/* reexport safe */ _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   PencilIcon: () => (/* reexport safe */ _PencilIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   PlusIcon: () => (/* reexport safe */ _PlusIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   TrashIcon: () => (/* reexport safe */ _TrashIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   TruckIcon: () => (/* reexport safe */ _TruckIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   UserIcon: () => (/* reexport safe */ _UserIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   UsersIcon: () => (/* reexport safe */ _UsersIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _EyeIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EyeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MagnifyingGlassIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _PencilIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./PencilIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _PlusIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PlusIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _TrashIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TrashIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _TruckIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TruckIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/TruckIcon.js\");\n/* harmony import */ var _UserIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./UserIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _UsersIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./UsersIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1FeWVJY29uLE1hZ25pZnlpbmdHbGFzc0ljb24sUGVuY2lsSWNvbixQbHVzSWNvbixUcmFzaEljb24sVHJ1Y2tJY29uLFVzZXJJY29uLFVzZXJzSWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDaUQ7QUFDd0I7QUFDbEI7QUFDSjtBQUNFO0FBQ0E7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/NzE2MiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXllSWNvbiB9IGZyb20gXCIuL0V5ZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNYWduaWZ5aW5nR2xhc3NJY29uIH0gZnJvbSBcIi4vTWFnbmlmeWluZ0dsYXNzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFBlbmNpbEljb24gfSBmcm9tIFwiLi9QZW5jaWxJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGx1c0ljb24gfSBmcm9tIFwiLi9QbHVzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRyYXNoSWNvbiB9IGZyb20gXCIuL1RyYXNoSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRydWNrSWNvbiB9IGZyb20gXCIuL1RydWNrSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXJJY29uIH0gZnJvbSBcIi4vVXNlckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2Vyc0ljb24gfSBmcm9tIFwiLi9Vc2Vyc0ljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon,TruckIcon,UserIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcustomers&preferredRegion=&absolutePagePath=.%2Fpages%5Ccustomers.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcustomers&preferredRegion=&absolutePagePath=.%2Fpages%5Ccustomers.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_customers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\customers.js */ \"./pages/customers.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_customers_js__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_customers_js__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_customers_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_customers_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_customers_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_customers_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_customers_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_customers_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_customers_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_customers_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_customers_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_customers_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_customers_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/customers\",\n        pathname: \"/customers\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_customers_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcustomers&preferredRegion=&absolutePagePath=.%2Fpages%5Ccustomers.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/CustomerModals.js":
/*!**************************************!*\
  !*** ./components/CustomerModals.js ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomerFormModal: () => (/* binding */ CustomerFormModal),\n/* harmony export */   ViewCustomerModal: () => (/* binding */ ViewCustomerModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _barrel_optimize_names_CurrencyDollarIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CurrencyDollarIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=CurrencyDollarIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,UserIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_4__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__]);\n([axios__WEBPACK_IMPORTED_MODULE_4__, react_hot_toast__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n// View Customer Modal\nfunction ViewCustomerModal({ customer, isOpen, onClose }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    if (!isOpen || !customer) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: t(\"customers.viewCustomer\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                            lineNumber: 29,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CurrencyDollarIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.XMarkIcon, {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                lineNumber: 36,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CurrencyDollarIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.UserIcon, {\n                                                    className: \"h-5 w-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: customer.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                            lineNumber: 44,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: customer.nameAr\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                            lineNumber: 45,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium text-gray-500\",\n                                                        children: \"ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 51,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: customer.code\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CurrencyDollarIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PhoneIcon, {\n                                                    className: \"h-5 w-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: customer.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        customer.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CurrencyDollarIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.EnvelopeIcon, {\n                                                    className: \"h-5 w-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: customer.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, this),\n                                        customer.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CurrencyDollarIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.MapPinIcon, {\n                                                    className: \"h-5 w-5 text-gray-400 mt-0.5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: customer.address\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                            lineNumber: 72,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        customer.addressAr && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: customer.addressAr\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CurrencyDollarIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CurrencyDollarIcon, {\n                                                    className: \"h-5 w-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: [\n                                                                \"Balance: $\",\n                                                                (parseFloat(customer.balance) || 0).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                            lineNumber: 83,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                \"Credit Limit: $\",\n                                                                (parseFloat(customer.creditLimit) || 0).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium text-gray-500\",\n                                                        children: \"TYPE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `badge ${customer.type === \"CUSTOMER\" ? \"badge-primary\" : customer.type === \"SUPPLIER\" ? \"badge-success\" : \"badge-info\"}`,\n                                                    children: customer.type\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium text-gray-500\",\n                                                        children: \"STATUS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `badge ${customer.isActive ? \"badge-success\" : \"badge-secondary\"}`,\n                                                    children: customer.isActive ? \"Active\" : \"Inactive\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"btn-secondary w-full sm:w-auto sm:ml-3\",\n                                children: t(\"common.close\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n// Create/Edit Customer Modal\nfunction CustomerFormModal({ customer, isOpen, onClose, onSuccess }) {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const isEdit = !!customer;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        code: customer?.code || \"\",\n        name: customer?.name || \"\",\n        nameAr: customer?.nameAr || \"\",\n        type: customer?.type || \"CUSTOMER\",\n        email: customer?.email || \"\",\n        phone: customer?.phone || \"\",\n        address: customer?.address || \"\",\n        addressAr: customer?.addressAr || \"\",\n        balance: customer?.balance || 0,\n        creditLimit: customer?.creditLimit || 0,\n        isActive: customer?.isActive ?? true\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const mutation = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)(async (data)=>{\n        if (isEdit) {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].put(`${\"http://localhost:3001\"}/api/customers/${customer.id}`, data);\n            return response.data;\n        } else {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(`${\"http://localhost:3001\"}/api/customers`, data);\n            return response.data;\n        }\n    }, {\n        onSuccess: ()=>{\n            queryClient.invalidateQueries([\n                \"customers\"\n            ]);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(isEdit ? \"Customer updated successfully\" : \"Customer created successfully\");\n            onSuccess?.();\n            onClose();\n        },\n        onError: (error)=>{\n            const errorMessage = error.response?.data?.error || \"Operation failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(errorMessage);\n            if (error.response?.data?.errors) {\n                setErrors(error.response.data.errors);\n            }\n        }\n    });\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        setErrors({});\n        // Basic validation\n        const newErrors = {};\n        if (!formData.code) newErrors.code = \"Code is required\";\n        if (!formData.name) newErrors.name = \"Name is required\";\n        if (!formData.nameAr) newErrors.nameAr = \"Arabic name is required\";\n        if (!formData.phone) newErrors.phone = \"Phone is required\";\n        if (Object.keys(newErrors).length > 0) {\n            setErrors(newErrors);\n            return;\n        }\n        mutation.mutate(formData);\n    };\n    const handleChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: type === \"checkbox\" ? checked : value\n            }));\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: isEdit ? t(\"customers.editCustomer\") : t(\"customers.addCustomer\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: onClose,\n                                                className: \"text-gray-400 hover:text-gray-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CurrencyDollarIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.XMarkIcon, {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: [\n                                                            t(\"customers.customerCode\"),\n                                                            \" *\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"code\",\n                                                        value: formData.code,\n                                                        onChange: handleChange,\n                                                        className: `form-input ${errors.code ? \"border-red-500\" : \"\"}`,\n                                                        placeholder: \"Enter customer code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.code\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 35\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: t(\"customers.customerType\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"type\",\n                                                        value: formData.type,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"CUSTOMER\",\n                                                                children: t(\"customers.customer\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SUPPLIER\",\n                                                                children: t(\"customers.supplier\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"BOTH\",\n                                                                children: t(\"customers.both\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Name (English) *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"name\",\n                                                        value: formData.name,\n                                                        onChange: handleChange,\n                                                        className: `form-input ${errors.name ? \"border-red-500\" : \"\"}`,\n                                                        placeholder: \"Enter name in English\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 35\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Name (Arabic) *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"nameAr\",\n                                                        value: formData.nameAr,\n                                                        onChange: handleChange,\n                                                        className: `form-input ${errors.nameAr ? \"border-red-500\" : \"\"}`,\n                                                        placeholder: \"أدخل الاسم بالعربية\",\n                                                        dir: \"rtl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.nameAr && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.nameAr\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: [\n                                                            t(\"customers.phone\"),\n                                                            \" *\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"tel\",\n                                                        name: \"phone\",\n                                                        value: formData.phone,\n                                                        onChange: handleChange,\n                                                        className: `form-input ${errors.phone ? \"border-red-500\" : \"\"}`,\n                                                        placeholder: \"+1234567890\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.phone\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 36\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: t(\"customers.email\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        name: \"email\",\n                                                        value: formData.email,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        placeholder: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Address (English)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"address\",\n                                                        value: formData.address,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        placeholder: \"Enter address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Address (Arabic)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"addressAr\",\n                                                        value: formData.addressAr,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        placeholder: \"أدخل العنوان\",\n                                                        dir: \"rtl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: t(\"customers.balance\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        name: \"balance\",\n                                                        value: formData.balance,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        placeholder: \"0.00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: t(\"customers.creditLimit\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        name: \"creditLimit\",\n                                                        value: formData.creditLimit,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        placeholder: \"0.00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            name: \"isActive\",\n                                                            checked: formData.isActive,\n                                                            onChange: handleChange,\n                                                            className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm text-gray-700\",\n                                                            children: t(\"common.active\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: mutation.isLoading,\n                                        className: \"btn-primary w-full sm:w-auto sm:ml-3\",\n                                        children: mutation.isLoading ? \"Saving...\" : isEdit ? t(\"common.save\") : t(\"common.add\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: onClose,\n                                        className: \"btn-secondary w-full sm:w-auto mt-3 sm:mt-0\",\n                                        children: t(\"common.cancel\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n            lineNumber: 209,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\CustomerModals.js\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/CustomerModals.js\n");

/***/ }),

/***/ "./components/Header.js":
/*!******************************!*\
  !*** ./components/Header.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/SocketContext */ \"./contexts/SocketContext.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_5__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction Header({ sidebarOpen, setSidebarOpen }) {\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUserMenu, setShowUserMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { notifications, markNotificationAsRead } = (0,_contexts_SocketContext__WEBPACK_IMPORTED_MODULE_5__.useSocket)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const toggleLanguage = ()=>{\n        const newLocale = router.locale === \"ar\" ? \"en\" : \"ar\";\n        router.push(router.pathname, router.asPath, {\n            locale: newLocale\n        });\n    };\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-surface-primary shadow-soft border-b border-neutral-150\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between px-6 py-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setSidebarOpen(!sidebarOpen),\n                        className: \"p-2.5 rounded-xl text-text-secondary hover:text-text-primary hover:bg-neutral-75 focus-ring transition-all duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.Bars3Icon, {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleLanguage,\n                            className: \"p-2.5 rounded-xl text-text-secondary hover:text-text-primary hover:bg-neutral-75 focus-ring transition-all duration-200\",\n                            title: t(\"common.language\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.LanguageIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowNotifications(!showNotifications),\n                                    className: \"p-2.5 rounded-xl text-text-secondary hover:text-text-primary hover:bg-neutral-75 focus-ring transition-all duration-200 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.BellIcon, {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 bg-status-error text-white text-xs rounded-full h-5 w-5 flex items-center justify-center shadow-soft\",\n                                            children: unreadCount > 9 ? \"9+\" : unreadCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 rtl:right-auto rtl:left-0 mt-3 w-80 bg-surface-primary rounded-2xl shadow-large border border-neutral-100 z-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-5 border-b border-neutral-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-text-primary\",\n                                                children: t(\"dashboard.notifications\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 71,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-96 overflow-y-auto\",\n                                            children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 text-center text-gray-500\",\n                                                children: t(\"common.noData\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 77,\n                                                columnNumber: 21\n                                            }, this) : notifications.slice(0, 10).map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${!notification.read ? \"bg-blue-50\" : \"\"}`,\n                                                    onClick: ()=>markNotificationAsRead(notification.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3 rtl:space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `flex-shrink-0 w-2 h-2 rounded-full mt-2 ${notification.type === \"error\" ? \"bg-red-500\" : notification.type === \"warning\" ? \"bg-yellow-500\" : notification.type === \"success\" ? \"bg-green-500\" : \"bg-blue-500\"}`\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                lineNumber: 90,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: router.locale === \"ar\" ? notification.titleAr : notification.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                        lineNumber: 97,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: router.locale === \"ar\" ? notification.messageAr : notification.message\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                        lineNumber: 100,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: new Date(notification.timestamp).toLocaleString(router.locale)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                        lineNumber: 103,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                lineNumber: 96,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, notification.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowUserMenu(!showUserMenu),\n                                    className: \"flex items-center space-x-2 rtl:space-x-reverse p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.UserCircleIcon, {\n                                            className: \"h-8 w-8\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: [\n                                                user?.firstName,\n                                                \" \",\n                                                user?.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                showUserMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 rtl:right-auto rtl:left-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 py-2 text-sm text-gray-700 border-b border-gray-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            user?.firstName,\n                                                            \" \",\n                                                            user?.lastName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500\",\n                                                        children: user?.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 capitalize\",\n                                                        children: user?.role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowUserMenu(false);\n                                                    router.push(\"/profile\");\n                                                },\n                                                className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                children: t(\"auth.profile\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowUserMenu(false);\n                                                    logout();\n                                                },\n                                                className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ArrowRightOnRectangleIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t(\"auth.logout\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Header.js\n");

/***/ }),

/***/ "./components/Layout.js":
/*!******************************!*\
  !*** ./components/Layout.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Sidebar */ \"./components/Sidebar.js\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Header */ \"./components/Header.js\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./LoadingSpinner */ \"./components/LoadingSpinner.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _Header__WEBPACK_IMPORTED_MODULE_6__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _Header__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction Layout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAuthenticated, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    // Set document direction based on locale\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof document !== \"undefined\") {\n            const isArabic = router.locale === \"ar\";\n            document.documentElement.dir = isArabic ? \"rtl\" : \"ltr\";\n            document.documentElement.lang = router.locale || \"ar\";\n            // Add Arabic font class\n            if (isArabic) {\n                document.body.classList.add(\"font-arabic\");\n                document.body.classList.remove(\"font-english\");\n            } else {\n                document.body.classList.add(\"font-english\");\n                document.body.classList.remove(\"font-arabic\");\n            }\n        }\n    }, [\n        router.locale\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && !isAuthenticated) {\n            router.push(\"/login\");\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null; // Will redirect to login\n    }\n    const isRTL = router.locale === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-h-screen bg-neutral-50 ${isRTL ? \"rtl\" : \"ltr\"}`,\n        dir: isRTL ? \"rtl\" : \"ltr\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                open: sidebarOpen,\n                setOpen: setSidebarOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${sidebarOpen ? isRTL ? \"lg:mr-64\" : \"lg:ml-64\" : isRTL ? \"lg:mr-20\" : \"lg:ml-20\"} transition-all duration-300`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sidebarOpen: sidebarOpen,\n                        setSidebarOpen: setSidebarOpen\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"section-spacing\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"page-container\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"content-spacing\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout.js\n");

/***/ }),

/***/ "./components/LoadingSpinner.js":
/*!**************************************!*\
  !*** ./components/LoadingSpinner.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction LoadingSpinner({ size = \"medium\", className = \"\" }) {\n    const sizeClasses = {\n        small: \"h-4 w-4\",\n        medium: \"h-8 w-8\",\n        large: \"h-12 w-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `animate-spin rounded-full border-2 border-gray-300 border-t-primary-600 ${sizeClasses[size]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\LoadingSpinner.js\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvYWRpbmdTcGlubmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQSxlQUFlLEVBQUVDLE9BQU8sUUFBUSxFQUFFQyxZQUFZLEVBQUUsRUFBRTtJQUN4RSxNQUFNQyxjQUFjO1FBQ2xCQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsT0FBTztJQUNUO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlMLFdBQVcsQ0FBQyx3RUFBd0UsRUFBRUMsV0FBVyxDQUFDRixLQUFLLENBQUMsQ0FBQyxFQUFFQyxVQUFVLENBQUM7Ozs7OztBQUUvSCIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vY29tcG9uZW50cy9Mb2FkaW5nU3Bpbm5lci5qcz9lNjEwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmdTcGlubmVyKHsgc2l6ZSA9ICdtZWRpdW0nLCBjbGFzc05hbWUgPSAnJyB9KSB7XG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xuICAgIHNtYWxsOiAnaC00IHctNCcsXG4gICAgbWVkaXVtOiAnaC04IHctOCcsXG4gICAgbGFyZ2U6ICdoLTEyIHctMTInLFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGJvcmRlci0yIGJvcmRlci1ncmF5LTMwMCBib3JkZXItdC1wcmltYXJ5LTYwMCAke3NpemVDbGFzc2VzW3NpemVdfSAke2NsYXNzTmFtZX1gfSAvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkxvYWRpbmdTcGlubmVyIiwic2l6ZSIsImNsYXNzTmFtZSIsInNpemVDbGFzc2VzIiwic21hbGwiLCJtZWRpdW0iLCJsYXJnZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/LoadingSpinner.js\n");

/***/ }),

/***/ "./components/Sidebar.js":
/*!*******************************!*\
  !*** ./components/Sidebar.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.HomeIcon\n    },\n    {\n        name: \"products\",\n        href: \"/products\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CubeIcon\n    },\n    {\n        name: \"customers\",\n        href: \"/customers\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.UsersIcon\n    },\n    {\n        name: \"sales\",\n        href: \"/sales\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShoppingCartIcon\n    },\n    {\n        name: \"purchases\",\n        href: \"/purchases\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShoppingBagIcon\n    },\n    {\n        name: \"inventory\",\n        href: \"/inventory\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ArchiveBoxIcon\n    },\n    {\n        name: \"accounting\",\n        href: \"/accounting\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CalculatorIcon\n    },\n    {\n        name: \"maintenance\",\n        href: \"/maintenance\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.WrenchScrewdriverIcon\n    },\n    {\n        name: \"reports\",\n        href: \"/reports\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.DocumentChartBarIcon\n    },\n    {\n        name: \"settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.Cog6ToothIcon\n    }\n];\nfunction Sidebar({ open, setOpen }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-y-0 left-0 rtl:left-auto rtl:right-0 z-50 ${open ? \"w-64\" : \"w-20\"} bg-white shadow-lg transition-all duration-300 hidden lg:block`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-bold\",\n                                                children: \"BMS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 46,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: process.env.NEXT_PUBLIC_COMPANY_NAME || \"BMS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setOpen(!open),\n                                    className: \"p-1.5 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: open ? router.locale === \"ar\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronRightIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 58,\n                                        columnNumber: 42\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronLeftIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 58,\n                                        columnNumber: 85\n                                    }, this) : router.locale === \"ar\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronLeftIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 60,\n                                        columnNumber: 42\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronRightIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 60,\n                                        columnNumber: 84\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-2 py-4 space-y-1 overflow-y-auto\",\n                            children: navigation.map((item)=>{\n                                const isActive = router.pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    className: `group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${isActive ? \"bg-primary-100 text-primary-900\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: `${open ? \"mr-3 rtl:mr-0 rtl:ml-3\" : \"mx-auto\"} h-6 w-6 flex-shrink-0 ${isActive ? \"text-primary-600\" : \"text-gray-400 group-hover:text-gray-500\"}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 79,\n                                            columnNumber: 19\n                                        }, this),\n                                        open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: t(`navigation.${item.name}`)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 85,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 70,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-y-0 left-0 rtl:left-auto rtl:right-0 z-50 w-64 bg-white shadow-lg transform ${open ? \"translate-x-0 rtl:-translate-x-0\" : \"-translate-x-full rtl:translate-x-full\"} transition-transform duration-300 lg:hidden`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-bold\",\n                                                children: \"BMS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: process.env.NEXT_PUBLIC_COMPANY_NAME || \"BMS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setOpen(false),\n                                    className: \"p-1.5 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronLeftIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-2 py-4 space-y-1 overflow-y-auto\",\n                            children: navigation.map((item)=>{\n                                const isActive = router.pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    onClick: ()=>setOpen(false),\n                                    className: `group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${isActive ? \"bg-primary-100 text-primary-900\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: `mr-3 rtl:mr-0 rtl:ml-3 h-6 w-6 flex-shrink-0 ${isActive ? \"text-primary-600\" : \"text-gray-400 group-hover:text-gray-500\"}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: t(`navigation.${item.name}`)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Sidebar.js\n");

/***/ }),

/***/ "./contexts/AuthContext.js":
/*!*********************************!*\
  !*** ./contexts/AuthContext.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__]);\n([axios__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst initialState = {\n    user: null,\n    token: null,\n    isLoading: true,\n    isAuthenticated: false\n};\nfunction authReducer(state, action) {\n    switch(action.type){\n        case \"LOGIN_SUCCESS\":\n            return {\n                ...state,\n                user: action.payload.user,\n                token: action.payload.token,\n                isAuthenticated: true,\n                isLoading: false\n            };\n        case \"LOGOUT\":\n            return {\n                ...state,\n                user: null,\n                token: null,\n                isAuthenticated: false,\n                isLoading: false\n            };\n        case \"SET_LOADING\":\n            return {\n                ...state,\n                isLoading: action.payload\n            };\n        case \"UPDATE_USER\":\n            return {\n                ...state,\n                user: {\n                    ...state.user,\n                    ...action.payload\n                }\n            };\n        default:\n            return state;\n    }\n}\nfunction AuthProvider({ children }) {\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(authReducer, initialState);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Configure axios defaults\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const token = localStorage.getItem(\"token\");\n        if (token) {\n            axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n        }\n    }, []);\n    // Check for existing token on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            const token = localStorage.getItem(\"token\");\n            if (token) {\n                try {\n                    axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n                    const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(`${\"http://localhost:3001\"}/api/auth/profile`);\n                    dispatch({\n                        type: \"LOGIN_SUCCESS\",\n                        payload: {\n                            user: response.data.user,\n                            token: token\n                        }\n                    });\n                } catch (error) {\n                    console.error(\"Token validation failed:\", error);\n                    localStorage.removeItem(\"token\");\n                    delete axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"];\n                    dispatch({\n                        type: \"LOGOUT\"\n                    });\n                }\n            } else {\n                dispatch({\n                    type: \"SET_LOADING\",\n                    payload: false\n                });\n            }\n        };\n        checkAuth();\n    }, []);\n    const login = async (username, password)=>{\n        try {\n            dispatch({\n                type: \"SET_LOADING\",\n                payload: true\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(`${\"http://localhost:3001\"}/api/auth/login`, {\n                username,\n                password\n            });\n            const { token, user } = response.data;\n            // Store token in localStorage\n            localStorage.setItem(\"token\", token);\n            // Set axios default header\n            axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n            dispatch({\n                type: \"LOGIN_SUCCESS\",\n                payload: {\n                    user,\n                    token\n                }\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(response.data.message || \"Login successful\");\n            // Redirect to dashboard\n            router.push(\"/\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            dispatch({\n                type: \"SET_LOADING\",\n                payload: false\n            });\n            const errorMessage = error.response?.data?.error || \"Login failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    };\n    const logout = ()=>{\n        // Remove token from localStorage\n        localStorage.removeItem(\"token\");\n        // Remove axios default header\n        delete axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"];\n        dispatch({\n            type: \"LOGOUT\"\n        });\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Logged out successfully\");\n        router.push(\"/login\");\n    };\n    const updateProfile = async (profileData)=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put(`${\"http://localhost:3001\"}/api/auth/profile`, profileData);\n            dispatch({\n                type: \"UPDATE_USER\",\n                payload: response.data.user\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(response.data.message || \"Profile updated successfully\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            const errorMessage = error.response?.data?.error || \"Profile update failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    };\n    const changePassword = async (currentPassword, newPassword)=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put(`${\"http://localhost:3001\"}/api/auth/change-password`, {\n                currentPassword,\n                newPassword\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(response.data.message || \"Password changed successfully\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            const errorMessage = error.response?.data?.error || \"Password change failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    };\n    const value = {\n        ...state,\n        login,\n        logout,\n        updateProfile,\n        changePassword\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\contexts\\\\AuthContext.js\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthContext);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/AuthContext.js\n");

/***/ }),

/***/ "./contexts/SocketContext.js":
/*!***********************************!*\
  !*** ./contexts/SocketContext.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"socket.io-client\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([socket_io_client__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__]);\n([socket_io_client__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst SocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction SocketProvider({ children }) {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { token, isAuthenticated } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isAuthenticated && token) {\n            // Initialize socket connection\n            const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(\"http://localhost:3001\", {\n                auth: {\n                    token: token\n                }\n            });\n            newSocket.on(\"connect\", ()=>{\n                console.log(\"Socket connected\");\n                setIsConnected(true);\n            });\n            newSocket.on(\"disconnect\", ()=>{\n                console.log(\"Socket disconnected\");\n                setIsConnected(false);\n            });\n            // Listen for real-time events\n            newSocket.on(\"inventory_updated\", (data)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(`Inventory updated: ${data.productName}`);\n            // You can dispatch events to update local state here\n            });\n            newSocket.on(\"order_created\", (data)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].info(`New order created: ${data.orderNumber}`);\n                // Add notification\n                setNotifications((prev)=>[\n                        {\n                            id: Date.now(),\n                            type: \"info\",\n                            title: \"New Order\",\n                            message: `Order ${data.orderNumber} has been created`,\n                            timestamp: new Date(),\n                            read: false\n                        },\n                        ...prev\n                    ]);\n            });\n            newSocket.on(\"maintenance_updated\", (data)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].info(`Maintenance order ${data.orderNumber} status updated`);\n                // Add notification\n                setNotifications((prev)=>[\n                        {\n                            id: Date.now(),\n                            type: \"info\",\n                            title: \"Maintenance Update\",\n                            message: `Order ${data.orderNumber} status: ${data.status}`,\n                            timestamp: new Date(),\n                            read: false\n                        },\n                        ...prev\n                    ]);\n            });\n            newSocket.on(\"low_stock_alert\", (data)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].warning(`Low stock alert: ${data.productName}`);\n                // Add notification\n                setNotifications((prev)=>[\n                        {\n                            id: Date.now(),\n                            type: \"warning\",\n                            title: \"Low Stock Alert\",\n                            message: `${data.productName} is running low (${data.currentStock} remaining)`,\n                            timestamp: new Date(),\n                            read: false\n                        },\n                        ...prev\n                    ]);\n            });\n            setSocket(newSocket);\n            return ()=>{\n                newSocket.close();\n            };\n        } else {\n            // Clean up socket when not authenticated\n            if (socket) {\n                socket.close();\n                setSocket(null);\n                setIsConnected(false);\n            }\n        }\n    }, [\n        isAuthenticated,\n        token\n    ]);\n    const emitEvent = (eventName, data)=>{\n        if (socket && isConnected) {\n            socket.emit(eventName, data);\n        }\n    };\n    const markNotificationAsRead = (notificationId)=>{\n        setNotifications((prev)=>prev.map((notification)=>notification.id === notificationId ? {\n                    ...notification,\n                    read: true\n                } : notification));\n    };\n    const clearNotifications = ()=>{\n        setNotifications([]);\n    };\n    const value = {\n        socket,\n        isConnected,\n        notifications,\n        emitEvent,\n        markNotificationAsRead,\n        clearNotifications\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocketContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\contexts\\\\SocketContext.js\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\nfunction useSocket() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SocketContext);\n    if (!context) {\n        throw new Error(\"useSocket must be used within a SocketProvider\");\n    }\n    return context;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SocketContext);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/SocketContext.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../contexts/SocketContext */ \"./contexts/SocketContext.js\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_9__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_5__, _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__, _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_7__, _components_Layout__WEBPACK_IMPORTED_MODULE_8__]);\n([react_hot_toast__WEBPACK_IMPORTED_MODULE_5__, _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__, _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_7__, _components_Layout__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nconst queryClient = new react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n    defaultOptions: {\n        queries: {\n            retry: 1,\n            refetchOnWindowFocus: false\n        }\n    }\n});\nfunction MyApp({ Component, pageProps }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Set document direction based on locale\n        const direction = router.locale === \"ar\" ? \"rtl\" : \"ltr\";\n        document.documentElement.dir = direction;\n        document.documentElement.lang = router.locale;\n        // Set font family based on locale\n        const fontClass = router.locale === \"ar\" ? \"font-arabic\" : \"font-english\";\n        document.body.className = fontClass;\n    }, [\n        router.locale\n    ]);\n    // Check if it's a login page\n    const isLoginPage = router.pathname === \"/login\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_SocketContext__WEBPACK_IMPORTED_MODULE_7__.SocketProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `min-h-screen bg-gray-50 ${router.locale === \"ar\" ? \"font-arabic\" : \"font-english\"}`,\n                    children: [\n                        isLoginPage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...pageProps\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                            lineNumber: 43,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                ...pageProps\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                                lineNumber: 46,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                            lineNumber: 45,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                            position: router.locale === \"ar\" ? \"top-left\" : \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#363636\",\n                                    color: \"#fff\",\n                                    direction: router.locale === \"ar\" ? \"rtl\" : \"ltr\"\n                                },\n                                success: {\n                                    style: {\n                                        background: \"#10B981\"\n                                    }\n                                },\n                                error: {\n                                    style: {\n                                        background: \"#EF4444\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                    lineNumber: 41,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.appWithTranslation)(MyApp));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/customers.js":
/*!****************************!*\
  !*** ./pages/customers.js ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Customers),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next/serverSideTranslations */ \"next-i18next/serverSideTranslations\");\n/* harmony import */ var next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon,TruckIcon,UserIcon,UsersIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon,TruckIcon,UserIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/LoadingSpinner */ \"./components/LoadingSpinner.js\");\n/* harmony import */ var _components_CustomerModals__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/CustomerModals */ \"./components/CustomerModals.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_5__, react_hot_toast__WEBPACK_IMPORTED_MODULE_6__, _components_CustomerModals__WEBPACK_IMPORTED_MODULE_8__]);\n([axios__WEBPACK_IMPORTED_MODULE_5__, react_hot_toast__WEBPACK_IMPORTED_MODULE_6__, _components_CustomerModals__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nfunction Customers() {\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_4__.useQueryClient)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showViewModal, setShowViewModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCustomer, setSelectedCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch customers\n    const { data: customersData, isLoading, error, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)([\n        \"customers\",\n        currentPage,\n        searchTerm,\n        selectedType,\n        selectedStatus\n    ], async ()=>{\n        const params = new URLSearchParams({\n            page: currentPage.toString(),\n            limit: \"10\",\n            search: searchTerm,\n            type: selectedType,\n            status: selectedStatus\n        });\n        const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(`${\"http://localhost:3001\"}/api/customers?${params}`);\n        return response.data;\n    }, {\n        keepPreviousData: true\n    });\n    const customers = customersData?.customers || [];\n    const pagination = customersData?.pagination || {};\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        setCurrentPage(1);\n        refetch();\n    };\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n    };\n    // Delete customer mutation\n    const deleteMutation = (0,react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)(async (customerId)=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"][\"delete\"](`${\"http://localhost:3001\"}/api/customers/${customerId}`);\n        return response.data;\n    }, {\n        onSuccess: ()=>{\n            queryClient.invalidateQueries([\n                \"customers\"\n            ]);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success(t(\"customers.deleteSuccess\") || \"Customer deleted successfully\");\n        },\n        onError: (error)=>{\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error(error.response?.data?.error || \"Failed to delete customer\");\n        }\n    });\n    // Handle delete customer\n    const handleDelete = async (customer)=>{\n        if (window.confirm(t(\"customers.confirmDelete\") || `Are you sure you want to delete ${customer.name}?`)) {\n            deleteMutation.mutate(customer.id);\n        }\n    };\n    // Handle view customer\n    const handleView = (customer)=>{\n        setSelectedCustomer(customer);\n        setShowViewModal(true);\n    };\n    // Handle edit customer\n    const handleEdit = (customer)=>{\n        setSelectedCustomer(customer);\n        setShowEditModal(true);\n    };\n    // Handle create customer\n    const handleCreate = ()=>{\n        setSelectedCustomer(null);\n        setShowCreateModal(true);\n    };\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case \"CUSTOMER\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.UserIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 110,\n                    columnNumber: 16\n                }, this);\n            case \"SUPPLIER\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.TruckIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 112,\n                    columnNumber: 16\n                }, this);\n            case \"BOTH\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.UsersIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 114,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.UserIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 116,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getTypeColor = (type)=>{\n        switch(type){\n            case \"CUSTOMER\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"SUPPLIER\":\n                return \"bg-green-100 text-green-800\";\n            case \"BOTH\":\n                return \"bg-purple-100 text-purple-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    if (isLoading && !customers.length) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"mt-2 text-sm font-medium text-gray-900\",\n                    children: t(\"common.error\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: \"Failed to load customers\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>refetch(),\n                    className: \"mt-4 btn-primary\",\n                    children: \"Try Again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: t(\"customers.title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-600\",\n                                children: \"Manage your customers and suppliers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCreate,\n                        className: \"btn-primary\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.PlusIcon, {\n                                className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            t(\"customers.addCustomer\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-4 rounded-lg shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col sm:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.MagnifyingGlassIcon, {\n                                        className: \"absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t(\"common.search\"),\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"form-input pl-10 rtl:pl-3 rtl:pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedType,\n                            onChange: (e)=>setSelectedType(e.target.value),\n                            className: \"form-input\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"all\",\n                                    children: \"All Types\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"customer\",\n                                    children: t(\"customers.customer\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"supplier\",\n                                    children: t(\"customers.supplier\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"both\",\n                                    children: t(\"customers.both\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedStatus,\n                            onChange: (e)=>setSelectedStatus(e.target.value),\n                            className: \"form-input\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"all\",\n                                    children: \"All Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"active\",\n                                    children: \"Active\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"inactive\",\n                                    children: \"Inactive\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary\",\n                            children: t(\"common.search\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"table\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"customers.customerCode\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"customers.customerName\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"customers.customerType\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"customers.phone\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"customers.balance\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"common.actions\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"font-medium\",\n                                                    children: customer.code\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: customer.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: customer.nameAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(customer.type)}`,\n                                                        children: [\n                                                            getTypeIcon(customer.type),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-1 rtl:ml-0 rtl:mr-1\",\n                                                                children: t(`customers.${customer.type.toLowerCase()}`)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: customer.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `font-medium ${(customer.balance || 0) >= 0 ? \"text-green-600\" : \"text-red-600\"}`,\n                                                        children: [\n                                                            \"$\",\n                                                            (parseFloat(customer.balance) || 0).toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `badge ${customer.isActive ? \"badge-success\" : \"badge-secondary\"}`,\n                                                        children: customer.isActive ? \"Active\" : \"Inactive\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleView(customer),\n                                                                className: \"p-1 text-gray-400 hover:text-blue-600 transition-colors\",\n                                                                title: t(\"common.view\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.EyeIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleEdit(customer),\n                                                                className: \"p-1 text-gray-400 hover:text-green-600 transition-colors\",\n                                                                title: t(\"common.edit\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.PencilIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDelete(customer),\n                                                                className: \"p-1 text-gray-400 hover:text-red-600 transition-colors\",\n                                                                title: t(\"common.delete\"),\n                                                                disabled: deleteMutation.isLoading,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.TrashIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, customer.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    pagination.pages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-700\",\n                                    children: [\n                                        \"Showing \",\n                                        (pagination.page - 1) * pagination.limit + 1,\n                                        \" to\",\n                                        \" \",\n                                        Math.min(pagination.page * pagination.limit, pagination.total),\n                                        \" of\",\n                                        \" \",\n                                        pagination.total,\n                                        \" results\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handlePageChange(pagination.page - 1),\n                                            disabled: pagination.page <= 1,\n                                            className: \"btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Previous\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this),\n                                        Array.from({\n                                            length: Math.min(5, pagination.pages)\n                                        }, (_, i)=>{\n                                            const page = i + Math.max(1, pagination.page - 2);\n                                            if (page > pagination.pages) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePageChange(page),\n                                                className: `btn-sm ${page === pagination.page ? \"btn-primary\" : \"btn-secondary\"}`,\n                                                children: page\n                                            }, page, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                                lineNumber: 309,\n                                                columnNumber: 21\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handlePageChange(pagination.page + 1),\n                                            disabled: pagination.page >= pagination.pages,\n                                            className: \"btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Next\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, this),\n                    customers.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.UsersIcon, {\n                                className: \"mx-auto h-12 w-12 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"mt-2 text-sm font-medium text-gray-900\",\n                                children: \"No customers found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-500\",\n                                children: \"Get started by creating your first customer.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCreate,\n                                    className: \"btn-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_TruckIcon_UserIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.PlusIcon, {\n                                            className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this),\n                                        t(\"customers.addCustomer\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomerModals__WEBPACK_IMPORTED_MODULE_8__.ViewCustomerModal, {\n                customer: selectedCustomer,\n                isOpen: showViewModal,\n                onClose: ()=>setShowViewModal(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomerModals__WEBPACK_IMPORTED_MODULE_8__.CustomerFormModal, {\n                customer: selectedCustomer,\n                isOpen: showCreateModal || showEditModal,\n                onClose: ()=>{\n                    setShowCreateModal(false);\n                    setShowEditModal(false);\n                    setSelectedCustomer(null);\n                },\n                onSuccess: ()=>{\n                    setShowCreateModal(false);\n                    setShowEditModal(false);\n                    setSelectedCustomer(null);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\customers.js\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\nasync function getStaticProps({ locale }) {\n    return {\n        props: {\n            ...await (0,next_i18next_serverSideTranslations__WEBPACK_IMPORTED_MODULE_3__.serverSideTranslations)(locale, [\n                \"common\"\n            ])\n        }\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/customers.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next-i18next":
/*!*******************************!*\
  !*** external "next-i18next" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-i18next");

/***/ }),

/***/ "next-i18next/serverSideTranslations":
/*!******************************************************!*\
  !*** external "next-i18next/serverSideTranslations" ***!
  \******************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-i18next/serverSideTranslations");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-query":
/*!******************************!*\
  !*** external "react-query" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-query");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "socket.io-client":
/*!***********************************!*\
  !*** external "socket.io-client" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("socket.io-client");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcustomers&preferredRegion=&absolutePagePath=.%2Fpages%5Ccustomers.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();