"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "./pages/sales/index.js":
/*!******************************!*\
  !*** ./pages/sales/index.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SalesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _components_sales_QuoteModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/sales/QuoteModal */ \"./components/sales/QuoteModal.js\");\n/* harmony import */ var _components_sales_SalesOrderModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/sales/SalesOrderModal */ \"./components/sales/SalesOrderModal.js\");\n/* harmony import */ var _components_sales_InvoiceModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../components/sales/InvoiceModal */ \"./components/sales/InvoiceModal.js\");\n/* harmony import */ var _components_sales_SalesReturnModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../components/sales/SalesReturnModal */ \"./components/sales/SalesReturnModal.js\");\n/* harmony import */ var _components_sales_ProductCustomizer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../components/sales/ProductCustomizer */ \"./components/sales/ProductCustomizer.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUturnLeftIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,ReceiptPercentIcon,ShoppingCartIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightIcon,ArrowUturnLeftIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,ReceiptPercentIcon,ShoppingCartIcon,XCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SalesPage() {\n    _s();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"quotes\");\n    // Modal states\n    const [quoteModal, setQuoteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        quote: null\n    });\n    const [salesOrderModal, setSalesOrderModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        salesOrder: null,\n        fromQuote: null\n    });\n    const [invoiceModal, setInvoiceModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        invoice: null,\n        fromSalesOrder: null\n    });\n    const [returnModal, setReturnModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        salesReturn: null\n    });\n    // Data states\n    const [quotes, setQuotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [salesOrders, setSalesOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [salesReturns, setSalesReturns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // التحقق من تسجيل الدخول\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && !user) {\n            router.push(\"/login\");\n        }\n    }, [\n        user,\n        isLoading,\n        router\n    ]);\n    // عرض شاشة التحميل أثناء التحقق من المصادقة\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 57,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this);\n    }\n    // إذا لم يكن المستخدم مسجل دخول، لا تعرض شيء (سيتم التوجيه)\n    if (!user) {\n        return null;\n    }\n    // Load data when tab changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            loadData();\n        }\n    }, [\n        activeTab,\n        user\n    ]);\n    const loadData = async ()=>{\n        setLoading(true);\n        try {\n            if (activeTab === \"quotes\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/quotes\"));\n                setQuotes(response.data.quotes || []);\n            } else if (activeTab === \"orders\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/sales-orders\"));\n                setSalesOrders(response.data.salesOrders || []);\n            } else if (activeTab === \"invoices\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/invoices\"));\n                setInvoices(response.data.invoices || []);\n            } else if (activeTab === \"returns\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/sales-returns\"));\n                setSalesReturns(response.data.salesReturns || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"خطأ في تحميل البيانات\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Modal handlers\n    const handleQuoteCreate = ()=>{\n        setQuoteModal({\n            isOpen: true,\n            quote: null\n        });\n    };\n    const handleQuoteEdit = (quote)=>{\n        setQuoteModal({\n            isOpen: true,\n            quote\n        });\n    };\n    const handleQuoteConvert = (quote)=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder: null,\n            fromQuote: quote\n        });\n    };\n    const handleSalesOrderCreate = ()=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder: null,\n            fromQuote: null\n        });\n    };\n    const handleSalesOrderEdit = (salesOrder)=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder,\n            fromQuote: null\n        });\n    };\n    const handleSalesOrderConvert = (salesOrder)=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice: null,\n            fromSalesOrder: salesOrder\n        });\n    };\n    const handleInvoiceCreate = ()=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice: null,\n            fromSalesOrder: null\n        });\n    };\n    const handleInvoiceEdit = (invoice)=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice,\n            fromSalesOrder: null\n        });\n    };\n    const handleReturnCreate = ()=>{\n        setReturnModal({\n            isOpen: true,\n            salesReturn: null\n        });\n    };\n    const handleReturnEdit = (salesReturn)=>{\n        setReturnModal({\n            isOpen: true,\n            salesReturn\n        });\n    };\n    // Save handlers\n    const handleQuoteSave = (quote)=>{\n        loadData();\n    };\n    const handleSalesOrderSave = (salesOrder)=>{\n        loadData();\n    };\n    const handleInvoiceSave = (invoice)=>{\n        loadData();\n    };\n    const handleReturnSave = (salesReturn)=>{\n        loadData();\n    };\n    const tabs = [\n        {\n            id: \"quotes\",\n            name: \"عروض الأسعار\",\n            nameEn: \"Quotes\",\n            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.DocumentTextIcon,\n            color: \"blue\",\n            description: \"لا تؤثر على المخزون - صالحة لمدة محددة\"\n        },\n        {\n            id: \"orders\",\n            name: \"أوامر البيع\",\n            nameEn: \"Sales Orders\",\n            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ShoppingCartIcon,\n            color: \"orange\",\n            description: \"تحجز من المخزون - لها مدة صلاحية\"\n        },\n        {\n            id: \"invoices\",\n            name: \"الفواتير\",\n            nameEn: \"Invoices\",\n            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ReceiptPercentIcon,\n            color: \"green\",\n            description: \"تخصم من المخزون نهائياً - تحسب في المبيعات\"\n        },\n        {\n            id: \"returns\",\n            name: \"مرتجع المبيعات\",\n            nameEn: \"Sales Returns\",\n            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowUturnLeftIcon,\n            color: \"red\",\n            description: \"إرجاع المنتجات وإضافة للمخزون\"\n        }\n    ];\n    const getStatusColor = (status)=>{\n        const colors = {\n            \"DRAFT\": \"gray\",\n            \"PENDING\": \"yellow\",\n            \"APPROVED\": \"green\",\n            \"REJECTED\": \"red\",\n            \"EXPIRED\": \"red\",\n            \"CONFIRMED\": \"blue\",\n            \"SHIPPED\": \"purple\",\n            \"DELIVERED\": \"green\",\n            \"CANCELLED\": \"red\",\n            \"PAID\": \"green\",\n            \"PARTIALLY_PAID\": \"yellow\",\n            \"OVERDUE\": \"red\"\n        };\n        return colors[status] || \"gray\";\n    };\n    const getStatusText = (status)=>{\n        const statusTexts = {\n            \"DRAFT\": \"مسودة\",\n            \"PENDING\": \"في الانتظار\",\n            \"APPROVED\": \"موافق عليه\",\n            \"REJECTED\": \"مرفوض\",\n            \"EXPIRED\": \"منتهي الصلاحية\",\n            \"CONFIRMED\": \"مؤكد\",\n            \"SHIPPED\": \"تم الشحن\",\n            \"DELIVERED\": \"تم التسليم\",\n            \"CANCELLED\": \"ملغي\",\n            \"PAID\": \"مدفوع\",\n            \"PARTIALLY_PAID\": \"مدفوع جزئياً\",\n            \"OVERDUE\": \"متأخر\"\n        };\n        return statusTexts[status] || status;\n    };\n    const renderQuotes = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"عروض الأسعار\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 233,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleQuoteCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 238,\n                                    columnNumber: 11\n                                }, this),\n                                \"عرض سعر جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 234,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 232,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم العرض\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"صالح حتى\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: quotes.map((quote)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: quote.quoteNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: quote.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(quote.status), \"-100 text-\").concat(getStatusColor(quote.status), \"-800\"),\n                                                    children: getStatusText(quote.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    quote.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: quote.validUntil\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuoteEdit(quote),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    quote.status === \"APPROVED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuoteConvert(quote),\n                                                        className: \"text-green-600 hover:text-green-900 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                                className: \"h-5 w-5 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"تحويل لأمر\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, quote.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 243,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 231,\n            columnNumber: 5\n        }, this);\n    const renderOrders = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"أوامر البيع\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 315,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSalesOrderCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 320,\n                                    columnNumber: 11\n                                }, this),\n                                \"أمر بيع جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 316,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 314,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم الأمر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: salesOrders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: order.orderNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: order.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(order.status), \"-100 text-\").concat(getStatusColor(order.status), \"-800\"),\n                                                    children: getStatusText(order.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    order.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: order.reservedStock ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ClockIcon, {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"محجوز\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                                    children: \"غير محجوز\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSalesOrderEdit(order),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    order.status === \"CONFIRMED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSalesOrderConvert(order),\n                                                        className: \"text-green-600 hover:text-green-900 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                                className: \"h-5 w-5 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"تحويل لفاتورة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, order.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 325,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 313,\n            columnNumber: 5\n        }, this);\n    const renderInvoices = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"الفواتير\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 406,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleInvoiceCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 411,\n                                    columnNumber: 11\n                                }, this),\n                                \"فاتورة جديدة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 407,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 405,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم الفاتورة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ الإجمالي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المدفوع\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 435,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 418,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: invoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: invoice.invoiceNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: invoice.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(invoice.status), \"-100 text-\").concat(getStatusColor(invoice.status), \"-800\"),\n                                                    children: getStatusText(invoice.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 449,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    invoice.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    invoice.paidAmount.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleInvoiceEdit(invoice),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-green-600 hover:text-green-900\",\n                                                        children: \"طباعة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, invoice.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 416,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 404,\n            columnNumber: 5\n        }, this);\n    const renderReturns = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"مرتجع المبيعات\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 482,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleReturnCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 487,\n                                    columnNumber: 11\n                                }, this),\n                                \"مرتجع جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 483,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 481,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم المرتجع\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم الفاتورة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 505,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"مبلغ الاسترداد\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"السبب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 511,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 494,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: salesReturns.map((salesReturn)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: salesReturn.returnNumber || \"RET-\".concat(salesReturn.id)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: salesReturn.invoiceNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: salesReturn.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(salesReturn.status), \"-100 text-\").concat(getStatusColor(salesReturn.status), \"-800\"),\n                                                    children: getStatusText(salesReturn.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 531,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    parseFloat(salesReturn.refundAmount || 0).toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    salesReturn.reason === \"DEFECTIVE\" && \"عيب في المنتج\",\n                                                    salesReturn.reason === \"WRONG_ITEM\" && \"منتج خاطئ\",\n                                                    salesReturn.reason === \"CUSTOMER_CHANGE\" && \"تغيير رأي العميل\",\n                                                    salesReturn.reason === \"DAMAGED\" && \"تلف أثناء الشحن\",\n                                                    salesReturn.reason === \"WARRANTY\" && \"مطالبة ضمان\",\n                                                    salesReturn.reason === \"OTHER\" && \"أخرى\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 539,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleReturnEdit(salesReturn),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-green-600 hover:text-green-900\",\n                                                        children: \"طباعة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, salesReturn.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 519,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 493,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 492,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 480,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: \"إدارة المبيعات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 571,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"نظام المبيعات بثلاث مراحل: عروض الأسعار → أوامر البيع → الفواتير\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 574,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-8 mb-6\",\n                                children: tabs.map((tab, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center p-4 rounded-lg border-2 \".concat(activeTab === tab.id ? \"border-\".concat(tab.color, \"-500 bg-\").concat(tab.color, \"-50\") : \"border-gray-200 bg-gray-50\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                        className: \"h-8 w-8 mb-2 \".concat(activeTab === tab.id ? \"text-\".concat(tab.color, \"-600\") : \"text-gray-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium \".concat(activeTab === tab.id ? \"text-\".concat(tab.color, \"-900\") : \"text-gray-600\"),\n                                                        children: tab.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 text-center mt-1\",\n                                                        children: tab.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 582,\n                                                columnNumber: 17\n                                            }, this),\n                                            index < tabs.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                className: \"h-6 w-6 text-gray-400 mx-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 600,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 581,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 579,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 570,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8 px-6\",\n                                    children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-\".concat(tab.color, \"-500 text-\").concat(tab.color, \"-600\") : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                    className: \"h-5 w-5 inline-block ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 19\n                                                }, this),\n                                                tab.name\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 612,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 610,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 609,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    activeTab === \"quotes\" && renderQuotes(),\n                                    activeTab === \"orders\" && renderOrders(),\n                                    activeTab === \"invoices\" && renderInvoices(),\n                                    activeTab === \"returns\" && renderReturns()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 628,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 608,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 568,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_QuoteModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: quoteModal.isOpen,\n                onClose: ()=>setQuoteModal({\n                        isOpen: false,\n                        quote: null\n                    }),\n                onSave: handleQuoteSave,\n                quote: quoteModal.quote\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 638,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_SalesOrderModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: salesOrderModal.isOpen,\n                onClose: ()=>setSalesOrderModal({\n                        isOpen: false,\n                        salesOrder: null,\n                        fromQuote: null\n                    }),\n                onSave: handleSalesOrderSave,\n                salesOrder: salesOrderModal.salesOrder,\n                fromQuote: salesOrderModal.fromQuote\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 645,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_InvoiceModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: invoiceModal.isOpen,\n                onClose: ()=>setInvoiceModal({\n                        isOpen: false,\n                        invoice: null,\n                        fromSalesOrder: null\n                    }),\n                onSave: handleInvoiceSave,\n                invoice: invoiceModal.invoice,\n                fromSalesOrder: invoiceModal.fromSalesOrder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 653,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n        lineNumber: 567,\n        columnNumber: 5\n    }, this);\n}\n_s(SalesPage, \"RRWhhBSYkrjK1fRa3K47H2b2dW4=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = SalesPage;\nvar _c;\n$RefreshReg$(_c, \"SalesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/sales/index.js\n"));

/***/ })

});