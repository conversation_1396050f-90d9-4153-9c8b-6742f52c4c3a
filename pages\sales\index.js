import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useTranslation } from 'react-i18next';
import Layout from '../../components/Layout';
import { useAuth } from '../../contexts/AuthContext';
import { 
  DocumentTextIcon, 
  ShoppingCartIcon, 
  ReceiptPercentIcon,
  PlusIcon,
  EyeIcon,
  ArrowRightIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

export default function SalesPage() {
  const { t, i18n } = useTranslation('common');
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const [activeTab, setActiveTab] = useState('quotes');

  // التحقق من تسجيل الدخول
  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login');
    }
  }, [user, isLoading, router]);

  // عرض شاشة التحميل أثناء التحقق من المصادقة
  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    );
  }

  // إذا لم يكن المستخدم مسجل دخول، لا تعرض شيء (سيتم التوجيه)
  if (!user) {
    return null;
  }

  const tabs = [
    { 
      id: 'quotes', 
      name: 'عروض الأسعار', 
      nameEn: 'Quotes',
      icon: DocumentTextIcon,
      color: 'blue',
      description: 'لا تؤثر على المخزون - صالحة لمدة محددة'
    },
    { 
      id: 'orders', 
      name: 'أوامر البيع', 
      nameEn: 'Sales Orders',
      icon: ShoppingCartIcon,
      color: 'orange',
      description: 'تحجز من المخزون - لها مدة صلاحية'
    },
    { 
      id: 'invoices', 
      name: 'الفواتير', 
      nameEn: 'Invoices',
      icon: ReceiptPercentIcon,
      color: 'green',
      description: 'تخصم من المخزون نهائياً - تحسب في المبيعات'
    }
  ];

  const sampleQuotes = [
    {
      id: '1',
      quoteNumber: 'QUO-2024-001',
      customerName: 'شركة ABC',
      status: 'PENDING',
      total: 1500.00,
      validUntil: '2024-12-20',
      createdAt: '2024-12-13'
    },
    {
      id: '2',
      quoteNumber: 'QUO-2024-002',
      customerName: 'مؤسسة XYZ',
      status: 'APPROVED',
      total: 2300.00,
      validUntil: '2024-12-25',
      createdAt: '2024-12-12'
    }
  ];

  const sampleOrders = [
    {
      id: '1',
      orderNumber: 'SO-2024-001',
      customerName: 'شركة ABC',
      status: 'CONFIRMED',
      total: 1500.00,
      reservedStock: true,
      createdAt: '2024-12-13'
    }
  ];

  const sampleInvoices = [
    {
      id: '1',
      invoiceNumber: 'INV-2024-001',
      customerName: 'شركة ABC',
      status: 'PAID',
      total: 1500.00,
      paidAmount: 1500.00,
      createdAt: '2024-12-13'
    }
  ];

  const getStatusColor = (status) => {
    const colors = {
      'DRAFT': 'gray',
      'PENDING': 'yellow',
      'APPROVED': 'green',
      'REJECTED': 'red',
      'EXPIRED': 'red',
      'CONFIRMED': 'blue',
      'SHIPPED': 'purple',
      'DELIVERED': 'green',
      'CANCELLED': 'red',
      'PAID': 'green',
      'PARTIALLY_PAID': 'yellow',
      'OVERDUE': 'red'
    };
    return colors[status] || 'gray';
  };

  const getStatusText = (status) => {
    const statusTexts = {
      'DRAFT': 'مسودة',
      'PENDING': 'في الانتظار',
      'APPROVED': 'موافق عليه',
      'REJECTED': 'مرفوض',
      'EXPIRED': 'منتهي الصلاحية',
      'CONFIRMED': 'مؤكد',
      'SHIPPED': 'تم الشحن',
      'DELIVERED': 'تم التسليم',
      'CANCELLED': 'ملغي',
      'PAID': 'مدفوع',
      'PARTIALLY_PAID': 'مدفوع جزئياً',
      'OVERDUE': 'متأخر'
    };
    return statusTexts[status] || status;
  };

  const renderQuotes = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">عروض الأسعار</h3>
        <button className="btn-primary flex items-center">
          <PlusIcon className="h-5 w-5 mr-2" />
          عرض سعر جديد
        </button>
      </div>
      
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                رقم العرض
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                العميل
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الحالة
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                المبلغ
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                صالح حتى
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الإجراءات
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sampleQuotes.map((quote) => (
              <tr key={quote.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {quote.quoteNumber}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {quote.customerName}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-${getStatusColor(quote.status)}-100 text-${getStatusColor(quote.status)}-800`}>
                    {getStatusText(quote.status)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  ${quote.total.toFixed(2)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {quote.validUntil}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button className="text-blue-600 hover:text-blue-900">
                    <EyeIcon className="h-5 w-5" />
                  </button>
                  {quote.status === 'APPROVED' && (
                    <button className="text-green-600 hover:text-green-900 flex items-center">
                      <ArrowRightIcon className="h-5 w-5 mr-1" />
                      تحويل لأمر
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderOrders = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">أوامر البيع</h3>
        <button className="btn-primary flex items-center">
          <PlusIcon className="h-5 w-5 mr-2" />
          أمر بيع جديد
        </button>
      </div>
      
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                رقم الأمر
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                العميل
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الحالة
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                المبلغ
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                المخزون
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الإجراءات
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sampleOrders.map((order) => (
              <tr key={order.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {order.orderNumber}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {order.customerName}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-${getStatusColor(order.status)}-100 text-${getStatusColor(order.status)}-800`}>
                    {getStatusText(order.status)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  ${order.total.toFixed(2)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {order.reservedStock ? (
                    <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
                      <ClockIcon className="h-4 w-4 mr-1" />
                      محجوز
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                      غير محجوز
                    </span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button className="text-blue-600 hover:text-blue-900">
                    <EyeIcon className="h-5 w-5" />
                  </button>
                  {order.status === 'CONFIRMED' && (
                    <button className="text-green-600 hover:text-green-900 flex items-center">
                      <ArrowRightIcon className="h-5 w-5 mr-1" />
                      تحويل لفاتورة
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderInvoices = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">الفواتير</h3>
        <button className="btn-primary flex items-center">
          <PlusIcon className="h-5 w-5 mr-2" />
          فاتورة جديدة
        </button>
      </div>
      
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                رقم الفاتورة
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                العميل
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الحالة
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                المبلغ الإجمالي
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                المدفوع
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الإجراءات
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sampleInvoices.map((invoice) => (
              <tr key={invoice.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {invoice.invoiceNumber}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {invoice.customerName}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-${getStatusColor(invoice.status)}-100 text-${getStatusColor(invoice.status)}-800`}>
                    {getStatusText(invoice.status)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  ${invoice.total.toFixed(2)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  ${invoice.paidAmount.toFixed(2)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button className="text-blue-600 hover:text-blue-900">
                    <EyeIcon className="h-5 w-5" />
                  </button>
                  <button className="text-green-600 hover:text-green-900">
                    طباعة
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            إدارة المبيعات
          </h1>
          <p className="text-gray-600 mb-6">
            نظام المبيعات بثلاث مراحل: عروض الأسعار → أوامر البيع → الفواتير
          </p>
          
          {/* Process Flow */}
          <div className="flex items-center justify-center space-x-8 mb-6">
            {tabs.map((tab, index) => (
              <div key={tab.id} className="flex items-center">
                <div className={`flex flex-col items-center p-4 rounded-lg border-2 ${
                  activeTab === tab.id 
                    ? `border-${tab.color}-500 bg-${tab.color}-50` 
                    : 'border-gray-200 bg-gray-50'
                }`}>
                  <tab.icon className={`h-8 w-8 mb-2 ${
                    activeTab === tab.id ? `text-${tab.color}-600` : 'text-gray-400'
                  }`} />
                  <span className={`font-medium ${
                    activeTab === tab.id ? `text-${tab.color}-900` : 'text-gray-600'
                  }`}>
                    {tab.name}
                  </span>
                  <span className="text-xs text-gray-500 text-center mt-1">
                    {tab.description}
                  </span>
                </div>
                {index < tabs.length - 1 && (
                  <ArrowRightIcon className="h-6 w-6 text-gray-400 mx-4" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white shadow rounded-lg">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? `border-${tab.color}-500 text-${tab.color}-600`
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="h-5 w-5 inline-block ml-2" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'quotes' && renderQuotes()}
            {activeTab === 'orders' && renderOrders()}
            {activeTab === 'invoices' && renderInvoices()}
          </div>
        </div>
      </div>
    </Layout>
  );
}
