import { useEffect, useState } from 'react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useQuery } from 'react-query';
import axios from 'axios';
import {
  CubeIcon,
  UsersIcon,
  TruckIcon,
  ExclamationTriangleIcon,
  ShoppingCartIcon,
  ShoppingBagIcon,
  WrenchScrewdriverIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import LoadingSpinner from '../components/LoadingSpinner';

export default function Dashboard() {
  const { t } = useTranslation('common');

  // Fetch dashboard data
  const { data: dashboardData, isLoading, error } = useQuery(
    'dashboard-stats',
    async () => {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/dashboard/stats`);
      return response.data;
    },
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">{t('common.error')}</h3>
        <p className="mt-1 text-sm text-gray-500">Failed to load dashboard data</p>
      </div>
    );
  }

  const stats = dashboardData?.stats || {};
  const recentActivities = dashboardData?.recentActivities || {};
  const charts = dashboardData?.charts || {};

  const statCards = [
    {
      name: t('dashboard.totalProducts'),
      value: stats.totalProducts || 0,
      icon: CubeIcon,
      color: 'bg-blue-500',
    },
    {
      name: t('dashboard.totalCustomers'),
      value: stats.totalCustomers || 0,
      icon: UsersIcon,
      color: 'bg-green-500',
    },
    {
      name: t('dashboard.totalSuppliers'),
      value: stats.totalSuppliers || 0,
      icon: TruckIcon,
      color: 'bg-purple-500',
    },
    {
      name: t('dashboard.lowStock'),
      value: stats.lowStockProducts || 0,
      icon: ExclamationTriangleIcon,
      color: 'bg-red-500',
    },
    {
      name: t('dashboard.pendingSales'),
      value: stats.pendingSalesOrders || 0,
      icon: ShoppingCartIcon,
      color: 'bg-yellow-500',
    },
    {
      name: t('dashboard.pendingPurchases'),
      value: stats.pendingPurchaseOrders || 0,
      icon: ShoppingBagIcon,
      color: 'bg-indigo-500',
    },
    {
      name: t('dashboard.pendingMaintenance'),
      value: stats.pendingMaintenanceOrders || 0,
      icon: WrenchScrewdriverIcon,
      color: 'bg-pink-500',
    },
    {
      name: t('dashboard.monthlySales'),
      value: `$${(stats.monthlySales || 0).toLocaleString()}`,
      icon: CurrencyDollarIcon,
      color: 'bg-emerald-500',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">{t('dashboard.title')}</h1>
        <p className="mt-1 text-sm text-gray-600">
          {t('dashboard.welcome')} - {new Date().toLocaleDateString()}
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat, index) => (
          <div key={index} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 ${stat.color} rounded-md flex items-center justify-center`}>
                    <stat.icon className="h-5 w-5 text-white" />
                  </div>
                </div>
                <div className="ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stat.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Chart */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {t('dashboard.salesChart')}
          </h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={charts.salesData || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="month" 
                  tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short' })}
                />
                <YAxis />
                <Tooltip 
                  labelFormatter={(value) => new Date(value).toLocaleDateString()}
                  formatter={(value) => [`$${value.toLocaleString()}`, 'Sales']}
                />
                <Line 
                  type="monotone" 
                  dataKey="total" 
                  stroke="#3B82F6" 
                  strokeWidth={2}
                  dot={{ fill: '#3B82F6' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {t('dashboard.topProducts')}
          </h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={charts.topProducts?.slice(0, 5) || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="name" 
                  tick={{ fontSize: 12 }}
                  interval={0}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [
                    name === 'total_sold' ? `${value} units` : `$${value.toLocaleString()}`,
                    name === 'total_sold' ? 'Quantity Sold' : 'Revenue'
                  ]}
                />
                <Bar dataKey="total_sold" fill="#10B981" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Sales Orders */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              {t('dashboard.recentOrders')} - {t('navigation.sales')}
            </h3>
            <div className="space-y-3">
              {recentActivities.salesOrders?.slice(0, 5).map((order) => (
                <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {order.orderNumber}
                    </p>
                    <p className="text-sm text-gray-500">
                      {order.customer.name} - ${order.total.toLocaleString()}
                    </p>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    order.status === 'CONFIRMED' ? 'bg-green-100 text-green-800' :
                    order.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {order.status}
                  </span>
                </div>
              ))}
              {(!recentActivities.salesOrders || recentActivities.salesOrders.length === 0) && (
                <p className="text-sm text-gray-500 text-center py-4">
                  {t('common.noData')}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Recent Maintenance Orders */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              {t('dashboard.recentOrders')} - {t('navigation.maintenance')}
            </h3>
            <div className="space-y-3">
              {recentActivities.maintenanceOrders?.slice(0, 5).map((order) => (
                <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {order.orderNumber}
                    </p>
                    <p className="text-sm text-gray-500">
                      {order.customer.name} - {order.deviceType}
                    </p>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    order.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                    order.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                    order.status === 'WAITING_PARTS' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {order.status}
                  </span>
                </div>
              ))}
              {(!recentActivities.maintenanceOrders || recentActivities.maintenanceOrders.length === 0) && (
                <p className="text-sm text-gray-500 text-center py-4">
                  {t('common.noData')}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  };
}
