{"name": "business-management-system", "version": "1.0.0", "description": "Comprehensive Business Management System with Arabic/English support", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "node prisma/seed.js", "db:reset": "prisma migrate reset --force", "server": "node server/index.js", "server:dev": "nodemon server/index.js", "setup": "node scripts/setup.js", "setup:quick": "npm install && npm run db:generate && npm run db:push && npm run db:seed", "dev:all": "concurrently \"npm run server:dev\" \"npm run dev\"", "start:both": "node start-servers.js", "start:unified": "node server/unified-server.js"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.3.2", "@prisma/client": "^5.6.0", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "concurrently": "^8.2.2", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.1", "next": "^14.0.0", "next-i18next": "^15.2.0", "prisma": "^5.6.0", "react": "^18.2.0", "react-datepicker": "^4.21.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.4.1", "react-i18next": "^13.5.0", "react-query": "^3.39.3", "react-select": "^5.8.0", "react-table": "^7.8.0", "react-to-print": "^3.1.0", "recharts": "^2.8.0", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "xlsx": "^0.18.5", "yup": "^1.3.3"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-config-next": "^14.0.0", "nodemon": "^3.0.1", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}}