import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import axios from 'axios';
import toast from 'react-hot-toast';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  CurrencyDollarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  BanknotesIcon,
  CreditCardIcon,
  BuildingLibraryIcon,
  ArrowsRightLeftIcon,
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/LoadingSpinner';
import { AddTransactionModal, TransferFundsModal } from '../components/AccountingModals';

export default function Accounting() {
  const { t } = useTranslation('common');
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedType, setSelectedType] = useState('all');
  const [selectedBranch, setSelectedBranch] = useState('all');
  const [showAddTransactionModal, setShowAddTransactionModal] = useState(false);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');

  // Set default date range (last 30 days)
  useState(() => {
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    setDateFrom(thirtyDaysAgo.toISOString().split('T')[0]);
    setDateTo(today.toISOString().split('T')[0]);
  }, []);

  // Fetch cash transactions
  const { data: transactionsData, isLoading, error, refetch } = useQuery(
    ['transactions', currentPage, searchTerm, selectedType, selectedBranch, dateFrom, dateTo],
    async () => {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        search: searchTerm,
        type: selectedType,
        branchId: selectedBranch,
      });

      if (dateFrom) params.append('dateFrom', dateFrom);
      if (dateTo) params.append('dateTo', dateTo);

      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/cash-transactions?${params}`);
      return response.data;
    },
    {
      keepPreviousData: true,
      enabled: !!(dateFrom && dateTo),
    }
  );

  // Fetch branches for filter
  const { data: branchesData } = useQuery('branches', async () => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/branches`);
    return response.data;
  });

  const transactions = transactionsData?.transactions || [];
  const metrics = transactionsData?.metrics || {};
  const branches = branchesData?.branches || [];

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    refetch();
  };

  // Add transaction mutation
  const addTransactionMutation = useMutation(
    async (transactionData) => {
      const response = await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/api/cash-transactions`, transactionData);
      return response.data;
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['transactions']);
        toast.success('Transaction added successfully');
        setShowAddTransactionModal(false);
      },
      onError: (error) => {
        toast.error(error.response?.data?.error || 'Failed to add transaction');
      }
    }
  );

  // Transfer funds mutation
  const transferMutation = useMutation(
    async (transferData) => {
      const response = await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/api/cash-boxes/transfer`, transferData);
      return response.data;
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['transactions']);
        toast.success('Transfer completed successfully');
        setShowTransferModal(false);
      },
      onError: (error) => {
        toast.error(error.response?.data?.error || 'Failed to transfer funds');
      }
    }
  );

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'INCOME':
        return <ArrowUpIcon className="h-4 w-4 text-green-600" />;
      case 'EXPENSE':
        return <ArrowDownIcon className="h-4 w-4 text-red-600" />;
      case 'TRANSFER_IN':
        return <ArrowsRightLeftIcon className="h-4 w-4 text-blue-600" />;
      case 'TRANSFER_OUT':
        return <ArrowsRightLeftIcon className="h-4 w-4 text-orange-600" />;
      default:
        return <CurrencyDollarIcon className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTransactionColor = (type) => {
    switch (type) {
      case 'INCOME':
        return 'bg-green-100 text-green-800';
      case 'EXPENSE':
        return 'bg-red-100 text-red-800';
      case 'TRANSFER_IN':
        return 'bg-blue-100 text-blue-800';
      case 'TRANSFER_OUT':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentMethodIcon = (method) => {
    switch (method) {
      case 'CASH':
        return <BanknotesIcon className="h-4 w-4" />;
      case 'VISA':
      case 'INSTAPAY':
      case 'VODAFONE_CASH':
        return <CreditCardIcon className="h-4 w-4" />;
      case 'BANK_TRANSFER':
        return <BuildingLibraryIcon className="h-4 w-4" />;
      default:
        return <CurrencyDollarIcon className="h-4 w-4" />;
    }
  };

  if (isLoading && !transactions.length) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h3 className="mt-2 text-sm font-medium text-gray-900">{t('common.error')}</h3>
        <p className="mt-1 text-sm text-gray-500">Failed to load accounting data</p>
        <button
          onClick={() => refetch()}
          className="mt-4 btn-primary"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{t('navigation.accounting')}</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage cash flow, transactions, and financial records
          </p>
        </div>
        <div className="flex space-x-3 rtl:space-x-reverse">
          <button
            onClick={() => setShowTransferModal(true)}
            className="btn-secondary"
          >
            <ArrowsRightLeftIcon className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2" />
            Transfer Funds
          </button>
          <button
            onClick={() => setShowAddTransactionModal(true)}
            className="btn-primary"
          >
            <PlusIcon className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2" />
            Add Transaction
          </button>
        </div>
      </div>

      {/* Financial Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArrowUpIcon className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Income
                  </dt>
                  <dd className="text-lg font-medium text-green-600">
                    ${(metrics.totalIncome || 0).toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArrowDownIcon className="h-6 w-6 text-red-400" />
              </div>
              <div className="ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Expenses
                  </dt>
                  <dd className="text-lg font-medium text-red-600">
                    ${(metrics.totalExpense || 0).toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className={`h-6 w-6 ${(metrics.netCashFlow || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`} />
              </div>
              <div className="ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Net Cash Flow
                  </dt>
                  <dd className={`text-lg font-medium ${(metrics.netCashFlow || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    ${(metrics.netCashFlow || 0).toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArrowsRightLeftIcon className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Transactions
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {metrics.totalTransactions || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder={t('common.search')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input pl-10 rtl:pl-3 rtl:pr-10"
              />
            </div>
          </div>
          <div>
            <input
              type="date"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
              className="form-input"
              placeholder="From Date"
            />
          </div>
          <div>
            <input
              type="date"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
              className="form-input"
              placeholder="To Date"
            />
          </div>
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="form-input"
          >
            <option value="all">All Types</option>
            <option value="income">Income</option>
            <option value="expense">Expense</option>
            <option value="transfer_in">Transfer In</option>
            <option value="transfer_out">Transfer Out</option>
          </select>
          <select
            value={selectedBranch}
            onChange={(e) => setSelectedBranch(e.target.value)}
            className="form-input"
          >
            <option value="all">All Branches</option>
            {branches.map((branch) => (
              <option key={branch.id} value={branch.id}>
                {branch.name}
              </option>
            ))}
          </select>
          <button type="submit" className="btn-primary">
            {t('common.search')}
          </button>
        </form>
      </div>

      {/* Transactions Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Type</th>
                <th>Description</th>
                <th>Branch</th>
                <th>Payment Method</th>
                <th>Amount</th>
                <th>Reference</th>
                <th>{t('common.actions')}</th>
              </tr>
            </thead>
            <tbody>
              {transactions.map((transaction) => (
                <tr key={transaction.id}>
                  <td>
                    {new Date(transaction.createdAt).toLocaleDateString()}
                  </td>
                  <td>
                    <div className="flex items-center">
                      {getTransactionIcon(transaction.type)}
                      <span className={`ml-2 rtl:ml-0 rtl:mr-2 badge ${getTransactionColor(transaction.type)}`}>
                        {transaction.type.replace('_', ' ')}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div className="max-w-xs">
                      <div className="text-sm text-gray-900 truncate">{transaction.description}</div>
                      <div className="text-xs text-gray-500 truncate">{transaction.descriptionAr}</div>
                    </div>
                  </td>
                  <td>
                    <div>
                      <div className="font-medium text-gray-900">{transaction.cashBox?.branch?.name}</div>
                      <div className="text-sm text-gray-500">{transaction.cashBox?.branch?.nameAr}</div>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center">
                      {getPaymentMethodIcon(transaction.paymentMethod)}
                      <span className="ml-2 rtl:ml-0 rtl:mr-2 text-sm">
                        {transaction.paymentMethod?.replace('_', ' ') || 'CASH'}
                      </span>
                    </div>
                  </td>
                  <td>
                    <span className={`font-medium ${
                      transaction.type === 'INCOME' || transaction.type === 'TRANSFER_IN'
                        ? 'text-green-600'
                        : 'text-red-600'
                    }`}>
                      {transaction.type === 'INCOME' || transaction.type === 'TRANSFER_IN' ? '+' : '-'}
                      ${(parseFloat(transaction.amount) || 0).toFixed(2)}
                    </span>
                  </td>
                  <td>
                    <span className="text-sm text-gray-500">
                      {transaction.reference || '-'}
                    </span>
                  </td>
                  <td>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <button
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title={t('common.view')}
                      >
                        <MagnifyingGlassIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Empty State */}
        {transactions.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <CurrencyDollarIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
            <p className="mt-1 text-sm text-gray-500">
              No transactions match your current filters.
            </p>
            <div className="mt-6">
              <button
                onClick={() => setShowAddTransactionModal(true)}
                className="btn-primary"
              >
                <PlusIcon className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2" />
                Add Transaction
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <AddTransactionModal
        isOpen={showAddTransactionModal}
        onClose={() => setShowAddTransactionModal(false)}
        onSubmit={(data) => addTransactionMutation.mutate(data)}
      />

      <TransferFundsModal
        isOpen={showTransferModal}
        onClose={() => setShowTransferModal(false)}
        onSubmit={(data) => transferMutation.mutate(data)}
      />
    </div>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  };
}
