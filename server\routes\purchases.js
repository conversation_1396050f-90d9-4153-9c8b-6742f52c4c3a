const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Placeholder for purchase routes
router.get('/', authenticateToken, async (req, res) => {
  res.json({ message: 'Purchase routes - Coming soon' });
});

module.exports = router;
