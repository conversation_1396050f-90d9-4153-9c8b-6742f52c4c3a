import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  ShoppingCartIcon, 
  CogIcon, 
  DocumentTextIcon, 
  ArrowPathIcon,
  CalculatorIcon,
  PrinterIcon,
  UserPlusIcon,
  PhoneIcon,
  CreditCardIcon
} from '@heroicons/react/24/outline';
import axios from 'axios';
import toast from 'react-hot-toast';

export default function ComputerStorePOS({
  cart,
  customer,
  products,
  customers,
  categories,
  dailySummary,
  saleType,
  setSaleType,
  customerSearch,
  setCustomerSearch,
  productSearch,
  setProductSearch,
  selectedCategory,
  setSelectedCategory,
  searchCustomers,
  selectCustomer,
  clearCustomer,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
  calculateTotals,
  onShowPayment,
  onShowCustomerModal,
  onShowCustomizer
}) {
  const { t } = useTranslation();

  const totals = calculateTotals();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">متجر الكمبيوتر</h1>
              <div className="flex space-x-2">
                <button
                  onClick={() => setSaleType('DIRECT')}
                  className={`px-4 py-2 rounded-lg font-medium ${
                    saleType === 'DIRECT'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <ShoppingCartIcon className="h-5 w-5 inline ml-2" />
                  بيع مباشر
                </button>
                <button
                  onClick={() => setSaleType('CUSTOM_ORDER')}
                  className={`px-4 py-2 rounded-lg font-medium ${
                    saleType === 'CUSTOM_ORDER'
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <CogIcon className="h-5 w-5 inline ml-2" />
                  طلب مخصص
                </button>
                <button
                  onClick={() => setSaleType('QUOTE')}
                  className={`px-4 py-2 rounded-lg font-medium ${
                    saleType === 'QUOTE'
                      ? 'bg-yellow-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <DocumentTextIcon className="h-5 w-5 inline ml-2" />
                  عرض سعر
                </button>
                <button
                  onClick={() => window.location.href = '/sales'}
                  className="px-4 py-2 rounded-lg font-medium bg-gray-100 text-gray-700 hover:bg-gray-200"
                >
                  <ArrowPathIcon className="h-5 w-5 inline ml-2" />
                  مرتجعات
                </button>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                المبيعات اليوم: <span className="font-bold text-green-600">
                  ${dailySummary?.invoices?.total?.toFixed(2) || '0.00'}
                </span>
              </div>
              <div className="text-sm text-gray-600">
                الصندوق: <span className="font-bold text-blue-600">
                  ${dailySummary?.cashBox?.balance?.toFixed(2) || '0.00'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          
          {/* Products Section */}
          <div className="lg:col-span-2 space-y-6">
            {/* Customer Search */}
            <div className="bg-white rounded-lg shadow p-4">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <PhoneIcon className="h-4 w-4 inline ml-1" />
                    بحث العميل (رقم الهاتف أو الاسم)
                  </label>
                  <input
                    type="text"
                    value={customerSearch}
                    onChange={(e) => setCustomerSearch(e.target.value)}
                    placeholder="ادخل رقم الهاتف أو اسم العميل..."
                    className="form-input"
                  />
                  
                  {/* Customer Search Results */}
                  {customerSearch.length >= 3 && !customer && (
                    <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                      {searchCustomers(customerSearch).map(c => (
                        <button
                          key={c.id}
                          onClick={() => selectCustomer(c)}
                          className="w-full text-right px-4 py-2 hover:bg-gray-50 border-b border-gray-100"
                        >
                          <div className="font-medium">{c.nameAr || c.name}</div>
                          <div className="text-sm text-gray-600">{c.phone}</div>
                        </button>
                      ))}
                      <button
                        onClick={onShowCustomerModal}
                        className="w-full text-right px-4 py-2 hover:bg-blue-50 text-blue-600 font-medium"
                      >
                        <UserPlusIcon className="h-4 w-4 inline ml-1" />
                        إضافة عميل جديد
                      </button>
                    </div>
                  )}
                </div>
                
                {customer && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-green-900">{customer.nameAr || customer.name}</div>
                        <div className="text-sm text-green-700">{customer.phone}</div>
                      </div>
                      <button
                        onClick={clearCustomer}
                        className="text-green-600 hover:text-green-800"
                      >
                        ✕
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Product Search and Categories */}
            <div className="bg-white rounded-lg shadow p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">بحث المنتجات</label>
                  <input
                    type="text"
                    value={productSearch}
                    onChange={(e) => setProductSearch(e.target.value)}
                    placeholder="ابحث عن منتج..."
                    className="form-input"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="form-input"
                  >
                    <option value="all">جميع الفئات</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.nameAr || category.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Products Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                {products.map(product => (
                  <div
                    key={product.id}
                    className="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => {
                      if (product.isCustomizable) {
                        onShowCustomizer(product);
                      } else {
                        addToCart(product);
                      }
                    }}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 text-sm">
                          {product.nameAr || product.name}
                        </h3>
                        <p className="text-xs text-gray-600">{product.code}</p>
                      </div>
                      {product.isCustomizable && (
                        <CogIcon className="h-4 w-4 text-blue-500" />
                      )}
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-green-600">
                        ${parseFloat(product.unitPrice || product.basePrice || 0).toFixed(2)}
                      </span>
                      <span className="text-xs text-gray-500">
                        المخزون: {product.currentStock || 0}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Cart Section */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold text-gray-900">
                  السلة ({totals.itemCount} عنصر)
                </h2>
                {cart.length > 0 && (
                  <button
                    onClick={clearCart}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    مسح الكل
                  </button>
                )}
              </div>
            </div>

            {/* Cart Items */}
            <div className="p-4 space-y-3 max-h-96 overflow-y-auto">
              {cart.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  <ShoppingCartIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                  <p>السلة فارغة</p>
                  <p className="text-sm">اضغط على المنتجات لإضافتها</p>
                </div>
              ) : (
                cart.map(item => (
                  <div key={item.id} className="border border-gray-200 rounded-lg p-3">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{item.productName}</h4>
                        <p className="text-xs text-gray-600">{item.productCode}</p>
                        {item.customization && (
                          <div className="text-xs text-green-600 mt-1">
                            ✓ منتج مخصص
                          </div>
                        )}
                      </div>
                      <button
                        onClick={() => removeFromCart(item.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        ✕
                      </button>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <label className="text-xs text-gray-600">الكمية</label>
                        <input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateCartItem(item.id, 'quantity', e.target.value)}
                          className="form-input text-sm"
                          min="1"
                        />
                      </div>
                      <div>
                        <label className="text-xs text-gray-600">السعر</label>
                        <input
                          type="number"
                          value={item.unitPrice}
                          onChange={(e) => updateCartItem(item.id, 'unitPrice', e.target.value)}
                          className="form-input text-sm"
                          step="0.01"
                        />
                      </div>
                    </div>
                    
                    <div className="mt-2 text-right">
                      <span className="font-bold text-green-600">
                        ${item.total.toFixed(2)}
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Cart Summary */}
            {cart.length > 0 && (
              <div className="p-4 border-t border-gray-200">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>المجموع الفرعي:</span>
                    <span>${totals.subtotal.toFixed(2)}</span>
                  </div>
                  {totals.totalDiscount > 0 && (
                    <div className="flex justify-between text-red-600">
                      <span>الخصم:</span>
                      <span>-${totals.totalDiscount.toFixed(2)}</span>
                    </div>
                  )}
                  {totals.totalTax > 0 && (
                    <div className="flex justify-between text-blue-600">
                      <span>الضريبة:</span>
                      <span>+${totals.totalTax.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="flex justify-between font-bold text-lg border-t pt-2">
                    <span>الإجمالي:</span>
                    <span className="text-green-600">${totals.total.toFixed(2)}</span>
                  </div>
                </div>

                {/* Payment Button */}
                <button
                  onClick={onShowPayment}
                  className={`w-full mt-4 py-3 rounded-lg font-medium transition-colors ${
                    saleType === 'DIRECT'
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : saleType === 'CUSTOM_ORDER'
                      ? 'bg-green-600 hover:bg-green-700 text-white'
                      : 'bg-yellow-600 hover:bg-yellow-700 text-white'
                  }`}
                  disabled={cart.length === 0}
                >
                  <CreditCardIcon className="h-5 w-5 inline ml-2" />
                  {saleType === 'DIRECT' ? 'الدفع والإنهاء' :
                   saleType === 'CUSTOM_ORDER' ? 'إنشاء الطلب' :
                   'إنشاء عرض السعر'}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
