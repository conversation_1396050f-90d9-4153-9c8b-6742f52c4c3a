"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "./components/sales/SalesReturnModal.js":
/*!**********************************************!*\
  !*** ./components/sales/SalesReturnModal.js ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SalesReturnModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=MagnifyingGlassIcon,PlusIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SalesReturnModal(param) {\n    let { isOpen, onClose, onSave, salesReturn = null } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedInvoice, setSelectedInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchInvoice, setSearchInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: \"\",\n        invoiceId: \"\",\n        invoiceNumber: \"\",\n        customerName: \"\",\n        returnDate: new Date().toISOString().split(\"T\")[0],\n        reason: \"\",\n        notes: \"\",\n        items: [],\n        refundMethod: \"CASH\",\n        refundAmount: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            loadCustomers();\n            if (salesReturn) {\n                setFormData({\n                    customerId: salesReturn.customerId || \"\",\n                    invoiceId: salesReturn.invoiceId || \"\",\n                    invoiceNumber: salesReturn.invoiceNumber || \"\",\n                    customerName: salesReturn.customerName || \"\",\n                    returnDate: salesReturn.returnDate ? salesReturn.returnDate.split(\"T\")[0] : new Date().toISOString().split(\"T\")[0],\n                    reason: salesReturn.reason || \"\",\n                    notes: salesReturn.notes || \"\",\n                    items: salesReturn.items || [],\n                    refundMethod: salesReturn.refundMethod || \"CASH\",\n                    refundAmount: salesReturn.refundAmount || 0\n                });\n            }\n        }\n    }, [\n        isOpen,\n        salesReturn\n    ]);\n    const loadCustomers = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/customers\"));\n            setCustomers(response.data.customers || []);\n        } catch (error) {\n            console.error(\"Error loading customers:\", error);\n        }\n    };\n    const searchInvoices = async (query)=>{\n        if (!query || query.length < 3) {\n            setInvoices([]);\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/invoices/search?q=\").concat(query));\n            setInvoices(response.data.invoices || []);\n        } catch (error) {\n            console.error(\"Error searching invoices:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"خطأ في البحث عن الفواتير\");\n        }\n    };\n    const handleInvoiceSearch = (value)=>{\n        setSearchInvoice(value);\n        searchInvoices(value);\n    };\n    const selectInvoice = (invoice)=>{\n        setSelectedInvoice(invoice);\n        setFormData((prev)=>({\n                ...prev,\n                customerId: invoice.customerId,\n                invoiceId: invoice.id,\n                invoiceNumber: invoice.invoiceNumber,\n                customerName: invoice.customerName,\n                items: invoice.items.map((item)=>({\n                        ...item,\n                        returnQuantity: 0,\n                        returnReason: \"\",\n                        canReturn: true,\n                        maxReturnQuantity: item.quantity\n                    }))\n            }));\n        setInvoices([]);\n        setSearchInvoice(invoice.invoiceNumber);\n    };\n    const updateReturnItem = (index, field, value)=>{\n        setFormData((prev)=>{\n            const newItems = [\n                ...prev.items\n            ];\n            newItems[index] = {\n                ...newItems[index],\n                [field]: value\n            };\n            // Validate return quantity\n            if (field === \"returnQuantity\") {\n                const maxQty = newItems[index].maxReturnQuantity;\n                if (parseFloat(value) > maxQty) {\n                    newItems[index].returnQuantity = maxQty;\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].warning(\"الحد الأقصى للإرجاع: \".concat(maxQty));\n                }\n            }\n            return {\n                ...prev,\n                items: newItems\n            };\n        });\n    };\n    const calculateRefundAmount = ()=>{\n        return formData.items.reduce((total, item)=>{\n            const returnQty = parseFloat(item.returnQuantity) || 0;\n            if (returnQty <= 0) return total;\n            const unitPrice = parseFloat(item.unitPrice) || 0;\n            const discount = parseFloat(item.discount) || 0;\n            const taxRate = parseFloat(item.taxRate) || 0;\n            // Calculate based on original item calculation\n            const itemSubtotal = returnQty * unitPrice;\n            const discountAmount = itemSubtotal * (discount / 100);\n            const afterDiscount = itemSubtotal - discountAmount;\n            // Add tax if applicable\n            const taxAmount = item.hasTax ? afterDiscount * (taxRate / 100) : 0;\n            return total + afterDiscount + taxAmount;\n        }, 0);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.invoiceId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى اختيار الفاتورة\");\n            return;\n        }\n        const returningItems = formData.items.filter((item)=>parseFloat(item.returnQuantity) > 0);\n        if (returningItems.length === 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى تحديد العناصر المراد إرجاعها\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const refundAmount = calculateRefundAmount();\n            const returnData = {\n                ...formData,\n                items: returningItems,\n                refundAmount,\n                status: \"PENDING\"\n            };\n            const response = salesReturn ? await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].put(\"\".concat(\"http://localhost:3070\", \"/api/sales-returns/\").concat(salesReturn.id), returnData) : await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(\"\".concat(\"http://localhost:3070\", \"/api/sales-returns\"), returnData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.data.message || (salesReturn ? \"تم تحديث المرتجع\" : \"تم إنشاء المرتجع\"));\n            onSave(response.data.salesReturn);\n            onClose();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"حدث خطأ\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    const refundAmount = calculateRefundAmount();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: salesReturn ? \"تعديل مرتجع المبيعات\" : \"مرتجع مبيعات جديد\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.XMarkIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-blue-900 mb-3\",\n                                    children: \"البحث عن الفاتورة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.MagnifyingGlassIcon, {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchInvoice,\n                                            onChange: (e)=>handleInvoiceSearch(e.target.value),\n                                            className: \"form-input pl-10\",\n                                            placeholder: \"ابحث برقم الفاتورة أو اسم العميل...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        invoices.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto\",\n                                            children: invoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    onClick: ()=>selectInvoice(invoice),\n                                                    className: \"p-3 hover:bg-gray-50 cursor-pointer border-b\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: invoice.invoiceNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: invoice.customerName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            parseFloat(invoice.finalTotal || 0).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                        lineNumber: 224,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: new Date(invoice.createdAt).toLocaleDateString(\"ar-EG\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, invoice.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        selectedInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-green-900 mb-3\",\n                                    children: \"معلومات الفاتورة المختارة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 238,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"رقم الفاتورة:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" \",\n                                                        selectedInvoice.invoiceNumber\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"العميل:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" \",\n                                                        selectedInvoice.customerName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"التاريخ:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" \",\n                                                        new Date(selectedInvoice.createdAt).toLocaleDateString(\"ar-EG\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"الإجمالي:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" $\",\n                                                        parseFloat(selectedInvoice.finalTotal || 0).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"تاريخ الإرجاع\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: formData.returnDate,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        returnDate: e.target.value\n                                                    })),\n                                            className: \"form-input\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"طريقة الاسترداد\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.refundMethod,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        refundMethod: e.target.value\n                                                    })),\n                                            className: \"form-input\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"CASH\",\n                                                    children: \"نقدي\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"CREDIT\",\n                                                    children: \"رصيد للعميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"EXCHANGE\",\n                                                    children: \"استبدال\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        formData.items.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                    children: \"عناصر الإرجاع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: formData.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-12 gap-4 items-end p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"المنتج\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900 py-2\",\n                                                            children: [\n                                                                item.productName,\n                                                                item.customizationDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 mt-1\",\n                                                                    children: item.customizationDetails.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"• \",\n                                                                                detail.optionName,\n                                                                                \": \",\n                                                                                detail.selectedName\n                                                                            ]\n                                                                        }, idx, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                            lineNumber: 293,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"الكمية الأصلية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900 py-2\",\n                                                            children: item.quantity\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"كمية الإرجاع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.returnQuantity,\n                                                            onChange: (e)=>updateReturnItem(index, \"returnQuantity\", e.target.value),\n                                                            className: \"form-input\",\n                                                            min: \"0\",\n                                                            max: item.maxReturnQuantity\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"السعر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900 py-2\",\n                                                            children: [\n                                                                \"$\",\n                                                                parseFloat(item.unitPrice).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"سبب الإرجاع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: item.returnReason,\n                                                            onChange: (e)=>updateReturnItem(index, \"returnReason\", e.target.value),\n                                                            className: \"form-input\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر السبب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"DEFECTIVE\",\n                                                                    children: \"عيب في المنتج\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"WRONG_ITEM\",\n                                                                    children: \"منتج خاطئ\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"CUSTOMER_CHANGE\",\n                                                                    children: \"تغيير رأي العميل\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"DAMAGED\",\n                                                                    children: \"تلف أثناء الشحن\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"OTHER\",\n                                                                    children: \"أخرى\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 285,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, this),\n                        refundAmount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-3\",\n                                    children: \"ملخص الاسترداد\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"إجمالي المبلغ المسترد:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-green-600\",\n                                            children: [\n                                                \"$\",\n                                                refundAmount.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 349,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"سبب الإرجاع العام\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.reason,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        reason: e.target.value\n                                                    })),\n                                            className: \"form-input\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"اختر السبب\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"DEFECTIVE\",\n                                                    children: \"عيب في المنتج\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"WRONG_ITEM\",\n                                                    children: \"منتج خاطئ\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"CUSTOMER_CHANGE\",\n                                                    children: \"تغيير رأي العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"DAMAGED\",\n                                                    children: \"تلف أثناء الشحن\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"WARRANTY\",\n                                                    children: \"مطالبة ضمان\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"OTHER\",\n                                                    children: \"أخرى\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"ملاحظات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 379,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.notes,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        notes: e.target.value\n                                                    })),\n                                            className: \"form-input\",\n                                            rows: \"3\",\n                                            placeholder: \"ملاحظات إضافية...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"btn-secondary\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading || !selectedInvoice,\n                                    className: \"btn-primary\",\n                                    children: loading ? \"جاري الحفظ...\" : salesReturn ? \"تحديث\" : \"إنشاء المرتجع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\n_s(SalesReturnModal, \"jTQxBLV2Y2W2/T1v0aBuwUCeuBA=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = SalesReturnModal;\nvar _c;\n$RefreshReg$(_c, \"SalesReturnModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3NhbGVzL1NhbGVzUmV0dXJuTW9kYWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBNEM7QUFDRztBQUNtRDtBQUN4RTtBQUNVO0FBRXJCLFNBQVNTLGlCQUFpQixLQUErQztRQUEvQyxFQUFFQyxNQUFNLEVBQUVDLE9BQU8sRUFBRUMsTUFBTSxFQUFFQyxjQUFjLElBQUksRUFBRSxHQUEvQzs7SUFDdkMsTUFBTSxFQUFFQyxDQUFDLEVBQUUsR0FBR1osNkRBQWNBLENBQUM7SUFDN0IsTUFBTSxDQUFDYSxTQUFTQyxXQUFXLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNpQixXQUFXQyxhQUFhLEdBQUdsQiwrQ0FBUUEsQ0FBQyxFQUFFO0lBQzdDLE1BQU0sQ0FBQ21CLFVBQVVDLFlBQVksR0FBR3BCLCtDQUFRQSxDQUFDLEVBQUU7SUFDM0MsTUFBTSxDQUFDcUIsaUJBQWlCQyxtQkFBbUIsR0FBR3RCLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ3VCLGVBQWVDLGlCQUFpQixHQUFHeEIsK0NBQVFBLENBQUM7SUFFbkQsTUFBTSxDQUFDeUIsVUFBVUMsWUFBWSxHQUFHMUIsK0NBQVFBLENBQUM7UUFDdkMyQixZQUFZO1FBQ1pDLFdBQVc7UUFDWEMsZUFBZTtRQUNmQyxjQUFjO1FBQ2RDLFlBQVksSUFBSUMsT0FBT0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7UUFDbERDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxPQUFPLEVBQUU7UUFDVEMsY0FBYztRQUNkQyxjQUFjO0lBQ2hCO0lBRUF0QyxnREFBU0EsQ0FBQztRQUNSLElBQUlTLFFBQVE7WUFDVjhCO1lBRUEsSUFBSTNCLGFBQWE7Z0JBQ2ZhLFlBQVk7b0JBQ1ZDLFlBQVlkLFlBQVljLFVBQVUsSUFBSTtvQkFDdENDLFdBQVdmLFlBQVllLFNBQVMsSUFBSTtvQkFDcENDLGVBQWVoQixZQUFZZ0IsYUFBYSxJQUFJO29CQUM1Q0MsY0FBY2pCLFlBQVlpQixZQUFZLElBQUk7b0JBQzFDQyxZQUFZbEIsWUFBWWtCLFVBQVUsR0FBR2xCLFlBQVlrQixVQUFVLENBQUNHLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxHQUFHLElBQUlGLE9BQU9DLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO29CQUNsSEMsUUFBUXRCLFlBQVlzQixNQUFNLElBQUk7b0JBQzlCQyxPQUFPdkIsWUFBWXVCLEtBQUssSUFBSTtvQkFDNUJDLE9BQU94QixZQUFZd0IsS0FBSyxJQUFJLEVBQUU7b0JBQzlCQyxjQUFjekIsWUFBWXlCLFlBQVksSUFBSTtvQkFDMUNDLGNBQWMxQixZQUFZMEIsWUFBWSxJQUFJO2dCQUM1QztZQUNGO1FBQ0Y7SUFDRixHQUFHO1FBQUM3QjtRQUFRRztLQUFZO0lBRXhCLE1BQU0yQixnQkFBZ0I7UUFDcEIsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTWxDLGlEQUFTLENBQUMsR0FBbUMsT0FBaENvQyx1QkFBK0IsRUFBQztZQUNwRXpCLGFBQWF1QixTQUFTSyxJQUFJLENBQUM3QixTQUFTLElBQUksRUFBRTtRQUM1QyxFQUFFLE9BQU84QixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1FBQzVDO0lBQ0Y7SUFFQSxNQUFNRSxpQkFBaUIsT0FBT0M7UUFDNUIsSUFBSSxDQUFDQSxTQUFTQSxNQUFNQyxNQUFNLEdBQUcsR0FBRztZQUM5Qi9CLFlBQVksRUFBRTtZQUNkO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsTUFBTXFCLFdBQVcsTUFBTWxDLGlEQUFTLENBQUMsR0FBNEQyQyxPQUF6RFAsdUJBQStCLEVBQUMsMkJBQStCLE9BQU5PO1lBQzdGOUIsWUFBWXFCLFNBQVNLLElBQUksQ0FBQzNCLFFBQVEsSUFBSSxFQUFFO1FBQzFDLEVBQUUsT0FBTzRCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0N2Qyw2REFBVyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLE1BQU00QyxzQkFBc0IsQ0FBQ0M7UUFDM0I3QixpQkFBaUI2QjtRQUNqQkosZUFBZUk7SUFDakI7SUFFQSxNQUFNQyxnQkFBZ0IsQ0FBQ0M7UUFDckJqQyxtQkFBbUJpQztRQUNuQjdCLFlBQVk4QixDQUFBQSxPQUFTO2dCQUNuQixHQUFHQSxJQUFJO2dCQUNQN0IsWUFBWTRCLFFBQVE1QixVQUFVO2dCQUM5QkMsV0FBVzJCLFFBQVFFLEVBQUU7Z0JBQ3JCNUIsZUFBZTBCLFFBQVExQixhQUFhO2dCQUNwQ0MsY0FBY3lCLFFBQVF6QixZQUFZO2dCQUNsQ08sT0FBT2tCLFFBQVFsQixLQUFLLENBQUNxQixHQUFHLENBQUNDLENBQUFBLE9BQVM7d0JBQ2hDLEdBQUdBLElBQUk7d0JBQ1BDLGdCQUFnQjt3QkFDaEJDLGNBQWM7d0JBQ2RDLFdBQVc7d0JBQ1hDLG1CQUFtQkosS0FBS0ssUUFBUTtvQkFDbEM7WUFDRjtRQUNBNUMsWUFBWSxFQUFFO1FBQ2RJLGlCQUFpQitCLFFBQVExQixhQUFhO0lBQ3hDO0lBRUEsTUFBTW9DLG1CQUFtQixDQUFDQyxPQUFPQyxPQUFPZDtRQUN0QzNCLFlBQVk4QixDQUFBQTtZQUNWLE1BQU1ZLFdBQVc7bUJBQUlaLEtBQUtuQixLQUFLO2FBQUM7WUFDaEMrQixRQUFRLENBQUNGLE1BQU0sR0FBRztnQkFBRSxHQUFHRSxRQUFRLENBQUNGLE1BQU07Z0JBQUUsQ0FBQ0MsTUFBTSxFQUFFZDtZQUFNO1lBRXZELDJCQUEyQjtZQUMzQixJQUFJYyxVQUFVLGtCQUFrQjtnQkFDOUIsTUFBTUUsU0FBU0QsUUFBUSxDQUFDRixNQUFNLENBQUNILGlCQUFpQjtnQkFDaEQsSUFBSU8sV0FBV2pCLFNBQVNnQixRQUFRO29CQUM5QkQsUUFBUSxDQUFDRixNQUFNLENBQUNOLGNBQWMsR0FBR1M7b0JBQ2pDN0QsK0RBQWEsQ0FBQyx3QkFBK0IsT0FBUDZEO2dCQUN4QztZQUNGO1lBRUEsT0FBTztnQkFBRSxHQUFHYixJQUFJO2dCQUFFbkIsT0FBTytCO1lBQVM7UUFDcEM7SUFDRjtJQUVBLE1BQU1JLHdCQUF3QjtRQUM1QixPQUFPL0MsU0FBU1ksS0FBSyxDQUFDb0MsTUFBTSxDQUFDLENBQUNDLE9BQU9mO1lBQ25DLE1BQU1nQixZQUFZTCxXQUFXWCxLQUFLQyxjQUFjLEtBQUs7WUFDckQsSUFBSWUsYUFBYSxHQUFHLE9BQU9EO1lBRTNCLE1BQU1FLFlBQVlOLFdBQVdYLEtBQUtpQixTQUFTLEtBQUs7WUFDaEQsTUFBTUMsV0FBV1AsV0FBV1gsS0FBS2tCLFFBQVEsS0FBSztZQUM5QyxNQUFNQyxVQUFVUixXQUFXWCxLQUFLbUIsT0FBTyxLQUFLO1lBRTVDLCtDQUErQztZQUMvQyxNQUFNQyxlQUFlSixZQUFZQztZQUNqQyxNQUFNSSxpQkFBaUJELGVBQWdCRixDQUFBQSxXQUFXLEdBQUU7WUFDcEQsTUFBTUksZ0JBQWdCRixlQUFlQztZQUVyQyx3QkFBd0I7WUFDeEIsTUFBTUUsWUFBWXZCLEtBQUt3QixNQUFNLEdBQUlGLGdCQUFpQkgsQ0FBQUEsVUFBVSxHQUFFLElBQU07WUFFcEUsT0FBT0osUUFBUU8sZ0JBQWdCQztRQUNqQyxHQUFHO0lBQ0w7SUFFQSxNQUFNRSxlQUFlLE9BQU9DO1FBQzFCQSxFQUFFQyxjQUFjO1FBRWhCLElBQUksQ0FBQzdELFNBQVNHLFNBQVMsRUFBRTtZQUN2QnBCLDZEQUFXLENBQUM7WUFDWjtRQUNGO1FBRUEsTUFBTStFLGlCQUFpQjlELFNBQVNZLEtBQUssQ0FBQ21ELE1BQU0sQ0FBQzdCLENBQUFBLE9BQVFXLFdBQVdYLEtBQUtDLGNBQWMsSUFBSTtRQUN2RixJQUFJMkIsZUFBZXBDLE1BQU0sS0FBSyxHQUFHO1lBQy9CM0MsNkRBQVcsQ0FBQztZQUNaO1FBQ0Y7UUFFQVEsV0FBVztRQUVYLElBQUk7WUFDRixNQUFNdUIsZUFBZWlDO1lBRXJCLE1BQU1pQixhQUFhO2dCQUNqQixHQUFHaEUsUUFBUTtnQkFDWFksT0FBT2tEO2dCQUNQaEQ7Z0JBQ0FtRCxRQUFRO1lBQ1Y7WUFFQSxNQUFNakQsV0FBVzVCLGNBQ2IsTUFBTU4saURBQVMsQ0FBQyxHQUF3RE0sT0FBckQ4Qix1QkFBK0IsRUFBQyx1QkFBb0MsT0FBZjlCLFlBQVk0QyxFQUFFLEdBQUlnQyxjQUMxRixNQUFNbEYsa0RBQVUsQ0FBQyxHQUFtQyxPQUFoQ29DLHVCQUErQixFQUFDLHVCQUFxQjhDO1lBRTdFakYsK0RBQWEsQ0FBQ2lDLFNBQVNLLElBQUksQ0FBQ2dELE9BQU8sSUFBS2pGLENBQUFBLGNBQWMscUJBQXFCLGtCQUFpQjtZQUM1RkQsT0FBTzZCLFNBQVNLLElBQUksQ0FBQ2pDLFdBQVc7WUFDaENGO1FBQ0YsRUFBRSxPQUFPb0MsT0FBTztnQkFDRkEsc0JBQUFBO1lBQVp2Qyw2REFBVyxDQUFDdUMsRUFBQUEsa0JBQUFBLE1BQU1OLFFBQVEsY0FBZE0sdUNBQUFBLHVCQUFBQSxnQkFBZ0JELElBQUksY0FBcEJDLDJDQUFBQSxxQkFBc0JBLEtBQUssS0FBSTtRQUM3QyxTQUFVO1lBQ1IvQixXQUFXO1FBQ2I7SUFDRjtJQUVBLElBQUksQ0FBQ04sUUFBUSxPQUFPO0lBRXBCLE1BQU02QixlQUFlaUM7SUFFckIscUJBQ0UsOERBQUN1QjtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FDWG5GLGNBQWMseUJBQXlCOzs7Ozs7c0NBRTFDLDhEQUFDcUY7NEJBQ0NDLFNBQVN4Rjs0QkFDVHFGLFdBQVU7c0NBRVYsNEVBQUM3Rix5SUFBU0E7Z0NBQUM2RixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFJekIsOERBQUNJO29CQUFLQyxVQUFVakI7b0JBQWNZLFdBQVU7O3NDQUV0Qyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDTTtvQ0FBR04sV0FBVTs4Q0FBeUM7Ozs7Ozs4Q0FDdkQsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQzFGLG1KQUFtQkE7NENBQUMwRixXQUFVOzs7Ozs7c0RBQy9CLDhEQUFDTzs0Q0FDQ0MsTUFBSzs0Q0FDTG5ELE9BQU85Qjs0Q0FDUGtGLFVBQVUsQ0FBQ3BCLElBQU1qQyxvQkFBb0JpQyxFQUFFcUIsTUFBTSxDQUFDckQsS0FBSzs0Q0FDbkQyQyxXQUFVOzRDQUNWVyxhQUFZOzs7Ozs7d0NBR2J4RixTQUFTZ0MsTUFBTSxHQUFHLG1CQUNqQiw4REFBQzRDOzRDQUFJQyxXQUFVO3NEQUNaN0UsU0FBU3VDLEdBQUcsQ0FBQ0gsQ0FBQUEsd0JBQ1osOERBQUN3QztvREFFQ0ksU0FBUyxJQUFNN0MsY0FBY0M7b0RBQzdCeUMsV0FBVTs4REFFViw0RUFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDs7a0ZBQ0MsOERBQUNhO3dFQUFFWixXQUFVO2tGQUFlekMsUUFBUTFCLGFBQWE7Ozs7OztrRkFDakQsOERBQUMrRTt3RUFBRVosV0FBVTtrRkFBeUJ6QyxRQUFRekIsWUFBWTs7Ozs7Ozs7Ozs7OzBFQUU1RCw4REFBQ2lFO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ1k7d0VBQUVaLFdBQVU7OzRFQUFjOzRFQUFFMUIsV0FBV2YsUUFBUXNELFVBQVUsSUFBSSxHQUFHQyxPQUFPLENBQUM7Ozs7Ozs7a0ZBQ3pFLDhEQUFDRjt3RUFBRVosV0FBVTtrRkFBeUIsSUFBSWhFLEtBQUt1QixRQUFRd0QsU0FBUyxFQUFFQyxrQkFBa0IsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O21EQVhwRnpELFFBQVFFLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBc0IxQnBDLGlDQUNDLDhEQUFDMEU7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDTTtvQ0FBR04sV0FBVTs4Q0FBMEM7Ozs7Ozs4Q0FDeEQsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7OzhEQUNDLDhEQUFDYTs7c0VBQUUsOERBQUNLO3NFQUFPOzs7Ozs7d0RBQXNCO3dEQUFFNUYsZ0JBQWdCUSxhQUFhOzs7Ozs7OzhEQUNoRSw4REFBQytFOztzRUFBRSw4REFBQ0s7c0VBQU87Ozs7Ozt3REFBZ0I7d0RBQUU1RixnQkFBZ0JTLFlBQVk7Ozs7Ozs7Ozs7Ozs7c0RBRTNELDhEQUFDaUU7OzhEQUNDLDhEQUFDYTs7c0VBQUUsOERBQUNLO3NFQUFPOzs7Ozs7d0RBQWlCO3dEQUFFLElBQUlqRixLQUFLWCxnQkFBZ0IwRixTQUFTLEVBQUVDLGtCQUFrQixDQUFDOzs7Ozs7OzhEQUNyRiw4REFBQ0o7O3NFQUFFLDhEQUFDSztzRUFBTzs7Ozs7O3dEQUFrQjt3REFBRzNDLFdBQVdqRCxnQkFBZ0J3RixVQUFVLElBQUksR0FBR0MsT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU81Riw4REFBQ2Y7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDs7c0RBQ0MsOERBQUNtQjs0Q0FBTWxCLFdBQVU7c0RBQWE7Ozs7OztzREFDOUIsOERBQUNPOzRDQUNDQyxNQUFLOzRDQUNMbkQsT0FBTzVCLFNBQVNNLFVBQVU7NENBQzFCMEUsVUFBVSxDQUFDcEIsSUFBTTNELFlBQVk4QixDQUFBQSxPQUFTO3dEQUFFLEdBQUdBLElBQUk7d0RBQUV6QixZQUFZc0QsRUFBRXFCLE1BQU0sQ0FBQ3JELEtBQUs7b0RBQUM7NENBQzVFMkMsV0FBVTs0Q0FDVm1CLFFBQVE7Ozs7Ozs7Ozs7Ozs4Q0FJWiw4REFBQ3BCOztzREFDQyw4REFBQ21COzRDQUFNbEIsV0FBVTtzREFBYTs7Ozs7O3NEQUM5Qiw4REFBQ29COzRDQUNDL0QsT0FBTzVCLFNBQVNhLFlBQVk7NENBQzVCbUUsVUFBVSxDQUFDcEIsSUFBTTNELFlBQVk4QixDQUFBQSxPQUFTO3dEQUFFLEdBQUdBLElBQUk7d0RBQUVsQixjQUFjK0MsRUFBRXFCLE1BQU0sQ0FBQ3JELEtBQUs7b0RBQUM7NENBQzlFMkMsV0FBVTs7OERBRVYsOERBQUNxQjtvREFBT2hFLE9BQU07OERBQU87Ozs7Ozs4REFDckIsOERBQUNnRTtvREFBT2hFLE9BQU07OERBQVM7Ozs7Ozs4REFDdkIsOERBQUNnRTtvREFBT2hFLE9BQU07OERBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFNOUI1QixTQUFTWSxLQUFLLENBQUNjLE1BQU0sR0FBRyxtQkFDdkIsOERBQUM0Qzs7OENBQ0MsOERBQUNPO29DQUFHTixXQUFVOzhDQUF5Qzs7Ozs7OzhDQUN2RCw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1p2RSxTQUFTWSxLQUFLLENBQUNxQixHQUFHLENBQUMsQ0FBQ0MsTUFBTU8sc0JBQ3pCLDhEQUFDNkI7NENBQWdCQyxXQUFVOzs4REFDekIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ2tCOzREQUFNbEIsV0FBVTtzRUFBYTs7Ozs7O3NFQUM5Qiw4REFBQ0Q7NERBQUlDLFdBQVU7O2dFQUNackMsS0FBSzJELFdBQVc7Z0VBQ2hCM0QsS0FBSzRELG9CQUFvQixrQkFDeEIsOERBQUN4QjtvRUFBSUMsV0FBVTs4RUFDWnJDLEtBQUs0RCxvQkFBb0IsQ0FBQzdELEdBQUcsQ0FBQyxDQUFDOEQsUUFBUUMsb0JBQ3RDLDhEQUFDMUI7O2dGQUFjO2dGQUFHeUIsT0FBT0UsVUFBVTtnRkFBQztnRkFBR0YsT0FBT0csWUFBWTs7MkVBQWhERjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFPcEIsOERBQUMxQjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNrQjs0REFBTWxCLFdBQVU7c0VBQWE7Ozs7OztzRUFDOUIsOERBQUNEOzREQUFJQyxXQUFVO3NFQUNackMsS0FBS0ssUUFBUTs7Ozs7Ozs7Ozs7OzhEQUlsQiw4REFBQytCO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ2tCOzREQUFNbEIsV0FBVTtzRUFBYTs7Ozs7O3NFQUM5Qiw4REFBQ087NERBQ0NDLE1BQUs7NERBQ0xuRCxPQUFPTSxLQUFLQyxjQUFjOzREQUMxQjZDLFVBQVUsQ0FBQ3BCLElBQU1wQixpQkFBaUJDLE9BQU8sa0JBQWtCbUIsRUFBRXFCLE1BQU0sQ0FBQ3JELEtBQUs7NERBQ3pFMkMsV0FBVTs0REFDVjRCLEtBQUk7NERBQ0pDLEtBQUtsRSxLQUFLSSxpQkFBaUI7Ozs7Ozs7Ozs7Ozs4REFJL0IsOERBQUNnQztvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNrQjs0REFBTWxCLFdBQVU7c0VBQWE7Ozs7OztzRUFDOUIsOERBQUNEOzREQUFJQyxXQUFVOztnRUFBeUM7Z0VBQ3BEMUIsV0FBV1gsS0FBS2lCLFNBQVMsRUFBRWtDLE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7Ozs4REFJekMsOERBQUNmO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ2tCOzREQUFNbEIsV0FBVTtzRUFBYTs7Ozs7O3NFQUM5Qiw4REFBQ29COzREQUNDL0QsT0FBT00sS0FBS0UsWUFBWTs0REFDeEI0QyxVQUFVLENBQUNwQixJQUFNcEIsaUJBQWlCQyxPQUFPLGdCQUFnQm1CLEVBQUVxQixNQUFNLENBQUNyRCxLQUFLOzREQUN2RTJDLFdBQVU7OzhFQUVWLDhEQUFDcUI7b0VBQU9oRSxPQUFNOzhFQUFHOzs7Ozs7OEVBQ2pCLDhEQUFDZ0U7b0VBQU9oRSxPQUFNOzhFQUFZOzs7Ozs7OEVBQzFCLDhEQUFDZ0U7b0VBQU9oRSxPQUFNOzhFQUFhOzs7Ozs7OEVBQzNCLDhEQUFDZ0U7b0VBQU9oRSxPQUFNOzhFQUFrQjs7Ozs7OzhFQUNoQyw4REFBQ2dFO29FQUFPaEUsT0FBTTs4RUFBVTs7Ozs7OzhFQUN4Qiw4REFBQ2dFO29FQUFPaEUsT0FBTTs4RUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyQ0FyRGxCYTs7Ozs7Ozs7Ozs7Ozs7Ozt3QkErRGpCM0IsZUFBZSxtQkFDZCw4REFBQ3dEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ007b0NBQUdOLFdBQVU7OENBQXlDOzs7Ozs7OENBQ3ZELDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUM4Qjs0Q0FBSzlCLFdBQVU7c0RBQXdCOzs7Ozs7c0RBQ3hDLDhEQUFDOEI7NENBQUs5QixXQUFVOztnREFBbUM7Z0RBQUV6RCxhQUFhdUUsT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1oRiw4REFBQ2Y7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDs7c0RBQ0MsOERBQUNtQjs0Q0FBTWxCLFdBQVU7c0RBQWE7Ozs7OztzREFDOUIsOERBQUNvQjs0Q0FDQy9ELE9BQU81QixTQUFTVSxNQUFNOzRDQUN0QnNFLFVBQVUsQ0FBQ3BCLElBQU0zRCxZQUFZOEIsQ0FBQUEsT0FBUzt3REFBRSxHQUFHQSxJQUFJO3dEQUFFckIsUUFBUWtELEVBQUVxQixNQUFNLENBQUNyRCxLQUFLO29EQUFDOzRDQUN4RTJDLFdBQVU7NENBQ1ZtQixRQUFROzs4REFFUiw4REFBQ0U7b0RBQU9oRSxPQUFNOzhEQUFHOzs7Ozs7OERBQ2pCLDhEQUFDZ0U7b0RBQU9oRSxPQUFNOzhEQUFZOzs7Ozs7OERBQzFCLDhEQUFDZ0U7b0RBQU9oRSxPQUFNOzhEQUFhOzs7Ozs7OERBQzNCLDhEQUFDZ0U7b0RBQU9oRSxPQUFNOzhEQUFrQjs7Ozs7OzhEQUNoQyw4REFBQ2dFO29EQUFPaEUsT0FBTTs4REFBVTs7Ozs7OzhEQUN4Qiw4REFBQ2dFO29EQUFPaEUsT0FBTTs4REFBVzs7Ozs7OzhEQUN6Qiw4REFBQ2dFO29EQUFPaEUsT0FBTTs4REFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUkxQiw4REFBQzBDOztzREFDQyw4REFBQ21COzRDQUFNbEIsV0FBVTtzREFBYTs7Ozs7O3NEQUM5Qiw4REFBQytCOzRDQUNDMUUsT0FBTzVCLFNBQVNXLEtBQUs7NENBQ3JCcUUsVUFBVSxDQUFDcEIsSUFBTTNELFlBQVk4QixDQUFBQSxPQUFTO3dEQUFFLEdBQUdBLElBQUk7d0RBQUVwQixPQUFPaUQsRUFBRXFCLE1BQU0sQ0FBQ3JELEtBQUs7b0RBQUM7NENBQ3ZFMkMsV0FBVTs0Q0FDVmdDLE1BQUs7NENBQ0xyQixhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTWxCLDhEQUFDWjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNFO29DQUNDTSxNQUFLO29DQUNMTCxTQUFTeEY7b0NBQ1RxRixXQUFVOzhDQUNYOzs7Ozs7OENBR0QsOERBQUNFO29DQUNDTSxNQUFLO29DQUNMeUIsVUFBVWxILFdBQVcsQ0FBQ007b0NBQ3RCMkUsV0FBVTs4Q0FFVGpGLFVBQVUsa0JBQW1CRixjQUFjLFVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT3BFO0dBcFp3Qko7O1FBQ1JQLHlEQUFjQTs7O0tBRE5PIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvc2FsZXMvU2FsZXNSZXR1cm5Nb2RhbC5qcz83OTY5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJ3JlYWN0LWkxOG5leHQnO1xuaW1wb3J0IHsgWE1hcmtJY29uLCBQbHVzSWNvbiwgVHJhc2hJY29uLCBNYWduaWZ5aW5nR2xhc3NJY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcbmltcG9ydCBheGlvcyBmcm9tICdheGlvcyc7XG5pbXBvcnQgdG9hc3QgZnJvbSAncmVhY3QtaG90LXRvYXN0JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2FsZXNSZXR1cm5Nb2RhbCh7IGlzT3Blbiwgb25DbG9zZSwgb25TYXZlLCBzYWxlc1JldHVybiA9IG51bGwgfSkge1xuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKCdjb21tb24nKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbY3VzdG9tZXJzLCBzZXRDdXN0b21lcnNdID0gdXNlU3RhdGUoW10pO1xuICBjb25zdCBbaW52b2ljZXMsIHNldEludm9pY2VzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW3NlbGVjdGVkSW52b2ljZSwgc2V0U2VsZWN0ZWRJbnZvaWNlXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbc2VhcmNoSW52b2ljZSwgc2V0U2VhcmNoSW52b2ljZV0gPSB1c2VTdGF0ZSgnJyk7XG4gIFxuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlKHtcbiAgICBjdXN0b21lcklkOiAnJyxcbiAgICBpbnZvaWNlSWQ6ICcnLFxuICAgIGludm9pY2VOdW1iZXI6ICcnLFxuICAgIGN1c3RvbWVyTmFtZTogJycsXG4gICAgcmV0dXJuRGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sXG4gICAgcmVhc29uOiAnJyxcbiAgICBub3RlczogJycsXG4gICAgaXRlbXM6IFtdLFxuICAgIHJlZnVuZE1ldGhvZDogJ0NBU0gnLCAvLyBDQVNILCBDUkVESVQsIEVYQ0hBTkdFXG4gICAgcmVmdW5kQW1vdW50OiAwXG4gIH0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlzT3Blbikge1xuICAgICAgbG9hZEN1c3RvbWVycygpO1xuICAgICAgXG4gICAgICBpZiAoc2FsZXNSZXR1cm4pIHtcbiAgICAgICAgc2V0Rm9ybURhdGEoe1xuICAgICAgICAgIGN1c3RvbWVySWQ6IHNhbGVzUmV0dXJuLmN1c3RvbWVySWQgfHwgJycsXG4gICAgICAgICAgaW52b2ljZUlkOiBzYWxlc1JldHVybi5pbnZvaWNlSWQgfHwgJycsXG4gICAgICAgICAgaW52b2ljZU51bWJlcjogc2FsZXNSZXR1cm4uaW52b2ljZU51bWJlciB8fCAnJyxcbiAgICAgICAgICBjdXN0b21lck5hbWU6IHNhbGVzUmV0dXJuLmN1c3RvbWVyTmFtZSB8fCAnJyxcbiAgICAgICAgICByZXR1cm5EYXRlOiBzYWxlc1JldHVybi5yZXR1cm5EYXRlID8gc2FsZXNSZXR1cm4ucmV0dXJuRGF0ZS5zcGxpdCgnVCcpWzBdIDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sXG4gICAgICAgICAgcmVhc29uOiBzYWxlc1JldHVybi5yZWFzb24gfHwgJycsXG4gICAgICAgICAgbm90ZXM6IHNhbGVzUmV0dXJuLm5vdGVzIHx8ICcnLFxuICAgICAgICAgIGl0ZW1zOiBzYWxlc1JldHVybi5pdGVtcyB8fCBbXSxcbiAgICAgICAgICByZWZ1bmRNZXRob2Q6IHNhbGVzUmV0dXJuLnJlZnVuZE1ldGhvZCB8fCAnQ0FTSCcsXG4gICAgICAgICAgcmVmdW5kQW1vdW50OiBzYWxlc1JldHVybi5yZWZ1bmRBbW91bnQgfHwgMFxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtpc09wZW4sIHNhbGVzUmV0dXJuXSk7XG5cbiAgY29uc3QgbG9hZEN1c3RvbWVycyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTH0vYXBpL2N1c3RvbWVyc2ApO1xuICAgICAgc2V0Q3VzdG9tZXJzKHJlc3BvbnNlLmRhdGEuY3VzdG9tZXJzIHx8IFtdKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBjdXN0b21lcnM6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBzZWFyY2hJbnZvaWNlcyA9IGFzeW5jIChxdWVyeSkgPT4ge1xuICAgIGlmICghcXVlcnkgfHwgcXVlcnkubGVuZ3RoIDwgMykge1xuICAgICAgc2V0SW52b2ljZXMoW10pO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLmdldChgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMfS9hcGkvaW52b2ljZXMvc2VhcmNoP3E9JHtxdWVyeX1gKTtcbiAgICAgIHNldEludm9pY2VzKHJlc3BvbnNlLmRhdGEuaW52b2ljZXMgfHwgW10pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZWFyY2hpbmcgaW52b2ljZXM6JywgZXJyb3IpO1xuICAgICAgdG9hc3QuZXJyb3IoJ9iu2LfYoyDZgdmKINin2YTYqNit2Ksg2LnZhiDYp9mE2YHZiNin2KrZitixJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUludm9pY2VTZWFyY2ggPSAodmFsdWUpID0+IHtcbiAgICBzZXRTZWFyY2hJbnZvaWNlKHZhbHVlKTtcbiAgICBzZWFyY2hJbnZvaWNlcyh2YWx1ZSk7XG4gIH07XG5cbiAgY29uc3Qgc2VsZWN0SW52b2ljZSA9IChpbnZvaWNlKSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRJbnZvaWNlKGludm9pY2UpO1xuICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBjdXN0b21lcklkOiBpbnZvaWNlLmN1c3RvbWVySWQsXG4gICAgICBpbnZvaWNlSWQ6IGludm9pY2UuaWQsXG4gICAgICBpbnZvaWNlTnVtYmVyOiBpbnZvaWNlLmludm9pY2VOdW1iZXIsXG4gICAgICBjdXN0b21lck5hbWU6IGludm9pY2UuY3VzdG9tZXJOYW1lLFxuICAgICAgaXRlbXM6IGludm9pY2UuaXRlbXMubWFwKGl0ZW0gPT4gKHtcbiAgICAgICAgLi4uaXRlbSxcbiAgICAgICAgcmV0dXJuUXVhbnRpdHk6IDAsXG4gICAgICAgIHJldHVyblJlYXNvbjogJycsXG4gICAgICAgIGNhblJldHVybjogdHJ1ZSxcbiAgICAgICAgbWF4UmV0dXJuUXVhbnRpdHk6IGl0ZW0ucXVhbnRpdHlcbiAgICAgIH0pKVxuICAgIH0pKTtcbiAgICBzZXRJbnZvaWNlcyhbXSk7XG4gICAgc2V0U2VhcmNoSW52b2ljZShpbnZvaWNlLmludm9pY2VOdW1iZXIpO1xuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZVJldHVybkl0ZW0gPSAoaW5kZXgsIGZpZWxkLCB2YWx1ZSkgPT4ge1xuICAgIHNldEZvcm1EYXRhKHByZXYgPT4ge1xuICAgICAgY29uc3QgbmV3SXRlbXMgPSBbLi4ucHJldi5pdGVtc107XG4gICAgICBuZXdJdGVtc1tpbmRleF0gPSB7IC4uLm5ld0l0ZW1zW2luZGV4XSwgW2ZpZWxkXTogdmFsdWUgfTtcbiAgICAgIFxuICAgICAgLy8gVmFsaWRhdGUgcmV0dXJuIHF1YW50aXR5XG4gICAgICBpZiAoZmllbGQgPT09ICdyZXR1cm5RdWFudGl0eScpIHtcbiAgICAgICAgY29uc3QgbWF4UXR5ID0gbmV3SXRlbXNbaW5kZXhdLm1heFJldHVyblF1YW50aXR5O1xuICAgICAgICBpZiAocGFyc2VGbG9hdCh2YWx1ZSkgPiBtYXhRdHkpIHtcbiAgICAgICAgICBuZXdJdGVtc1tpbmRleF0ucmV0dXJuUXVhbnRpdHkgPSBtYXhRdHk7XG4gICAgICAgICAgdG9hc3Qud2FybmluZyhg2KfZhNit2K8g2KfZhNij2YLYtdmJINmE2YTYpdix2KzYp9i5OiAke21heFF0eX1gKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgXG4gICAgICByZXR1cm4geyAuLi5wcmV2LCBpdGVtczogbmV3SXRlbXMgfTtcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBjYWxjdWxhdGVSZWZ1bmRBbW91bnQgPSAoKSA9PiB7XG4gICAgcmV0dXJuIGZvcm1EYXRhLml0ZW1zLnJlZHVjZSgodG90YWwsIGl0ZW0pID0+IHtcbiAgICAgIGNvbnN0IHJldHVyblF0eSA9IHBhcnNlRmxvYXQoaXRlbS5yZXR1cm5RdWFudGl0eSkgfHwgMDtcbiAgICAgIGlmIChyZXR1cm5RdHkgPD0gMCkgcmV0dXJuIHRvdGFsO1xuXG4gICAgICBjb25zdCB1bml0UHJpY2UgPSBwYXJzZUZsb2F0KGl0ZW0udW5pdFByaWNlKSB8fCAwO1xuICAgICAgY29uc3QgZGlzY291bnQgPSBwYXJzZUZsb2F0KGl0ZW0uZGlzY291bnQpIHx8IDA7XG4gICAgICBjb25zdCB0YXhSYXRlID0gcGFyc2VGbG9hdChpdGVtLnRheFJhdGUpIHx8IDA7XG5cbiAgICAgIC8vIENhbGN1bGF0ZSBiYXNlZCBvbiBvcmlnaW5hbCBpdGVtIGNhbGN1bGF0aW9uXG4gICAgICBjb25zdCBpdGVtU3VidG90YWwgPSByZXR1cm5RdHkgKiB1bml0UHJpY2U7XG4gICAgICBjb25zdCBkaXNjb3VudEFtb3VudCA9IGl0ZW1TdWJ0b3RhbCAqIChkaXNjb3VudCAvIDEwMCk7XG4gICAgICBjb25zdCBhZnRlckRpc2NvdW50ID0gaXRlbVN1YnRvdGFsIC0gZGlzY291bnRBbW91bnQ7XG5cbiAgICAgIC8vIEFkZCB0YXggaWYgYXBwbGljYWJsZVxuICAgICAgY29uc3QgdGF4QW1vdW50ID0gaXRlbS5oYXNUYXggPyAoYWZ0ZXJEaXNjb3VudCAqICh0YXhSYXRlIC8gMTAwKSkgOiAwO1xuXG4gICAgICByZXR1cm4gdG90YWwgKyBhZnRlckRpc2NvdW50ICsgdGF4QW1vdW50O1xuICAgIH0sIDApO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IGFzeW5jIChlKSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIFxuICAgIGlmICghZm9ybURhdGEuaW52b2ljZUlkKSB7XG4gICAgICB0b2FzdC5lcnJvcign2YrYsdis2Ykg2KfYrtiq2YrYp9ixINin2YTZgdin2KrZiNix2KknKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgXG4gICAgY29uc3QgcmV0dXJuaW5nSXRlbXMgPSBmb3JtRGF0YS5pdGVtcy5maWx0ZXIoaXRlbSA9PiBwYXJzZUZsb2F0KGl0ZW0ucmV0dXJuUXVhbnRpdHkpID4gMCk7XG4gICAgaWYgKHJldHVybmluZ0l0ZW1zLmxlbmd0aCA9PT0gMCkge1xuICAgICAgdG9hc3QuZXJyb3IoJ9mK2LHYrNmJINiq2K3Yr9mK2K8g2KfZhNi52YbYp9i12LEg2KfZhNmF2LHYp9ivINil2LHYrNin2LnZh9inJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVmdW5kQW1vdW50ID0gY2FsY3VsYXRlUmVmdW5kQW1vdW50KCk7XG4gICAgICBcbiAgICAgIGNvbnN0IHJldHVybkRhdGEgPSB7XG4gICAgICAgIC4uLmZvcm1EYXRhLFxuICAgICAgICBpdGVtczogcmV0dXJuaW5nSXRlbXMsXG4gICAgICAgIHJlZnVuZEFtb3VudCxcbiAgICAgICAgc3RhdHVzOiAnUEVORElORydcbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gc2FsZXNSZXR1cm4gXG4gICAgICAgID8gYXdhaXQgYXhpb3MucHV0KGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkx9L2FwaS9zYWxlcy1yZXR1cm5zLyR7c2FsZXNSZXR1cm4uaWR9YCwgcmV0dXJuRGF0YSlcbiAgICAgICAgOiBhd2FpdCBheGlvcy5wb3N0KGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkx9L2FwaS9zYWxlcy1yZXR1cm5zYCwgcmV0dXJuRGF0YSk7XG5cbiAgICAgIHRvYXN0LnN1Y2Nlc3MocmVzcG9uc2UuZGF0YS5tZXNzYWdlIHx8IChzYWxlc1JldHVybiA/ICfYqtmFINiq2K3Yr9mK2Ksg2KfZhNmF2LHYqtis2LknIDogJ9iq2YUg2KXZhti02KfYoSDYp9mE2YXYsdiq2KzYuScpKTtcbiAgICAgIG9uU2F2ZShyZXNwb25zZS5kYXRhLnNhbGVzUmV0dXJuKTtcbiAgICAgIG9uQ2xvc2UoKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdG9hc3QuZXJyb3IoZXJyb3IucmVzcG9uc2U/LmRhdGE/LmVycm9yIHx8ICfYrdiv2Ksg2K7Yt9ijJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBpZiAoIWlzT3BlbikgcmV0dXJuIG51bGw7XG5cbiAgY29uc3QgcmVmdW5kQW1vdW50ID0gY2FsY3VsYXRlUmVmdW5kQW1vdW50KCk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LXhsIHctZnVsbCBtYXgtdy01eGwgbWF4LWgtWzkwdmhdIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTYgYm9yZGVyLWJcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgIHtzYWxlc1JldHVybiA/ICfYqti52K/ZitmEINmF2LHYqtis2Lkg2KfZhNmF2KjZiti52KfYqicgOiAn2YXYsdiq2KzYuSDZhdio2YrYudin2Kog2KzYr9mK2K8nfVxuICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFhNYXJrSWNvbiBjbGFzc05hbWU9XCJoLTYgdy02XCIgLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwicC02IHNwYWNlLXktNlwiPlxuICAgICAgICAgIHsvKiBJbnZvaWNlIFNlYXJjaCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsdWUtNTAgcC00IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS05MDAgbWItM1wiPtin2YTYqNit2Ksg2LnZhiDYp9mE2YHYp9iq2YjYsdipPC9oMz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgPE1hZ25pZnlpbmdHbGFzc0ljb24gY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0zIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgaC01IHctNSB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtzZWFyY2hJbnZvaWNlfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW52b2ljZVNlYXJjaChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dCBwbC0xMFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLYp9io2K3YqyDYqNix2YLZhSDYp9mE2YHYp9iq2YjYsdipINij2Ygg2KfYs9mFINin2YTYudmF2YrZhC4uLlwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICB7aW52b2ljZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtZnVsbCBsZWZ0LTAgcmlnaHQtMCBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgc2hhZG93LWxnIHotMTAgbWF4LWgtNjAgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICB7aW52b2ljZXMubWFwKGludm9pY2UgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAga2V5PXtpbnZvaWNlLmlkfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNlbGVjdEludm9pY2UoaW52b2ljZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0zIGhvdmVyOmJnLWdyYXktNTAgY3Vyc29yLXBvaW50ZXIgYm9yZGVyLWJcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2ludm9pY2UuaW52b2ljZU51bWJlcn08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPntpbnZvaWNlLmN1c3RvbWVyTmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPiR7cGFyc2VGbG9hdChpbnZvaWNlLmZpbmFsVG90YWwgfHwgMCkudG9GaXhlZCgyKX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPntuZXcgRGF0ZShpbnZvaWNlLmNyZWF0ZWRBdCkudG9Mb2NhbGVEYXRlU3RyaW5nKCdhci1FRycpfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU2VsZWN0ZWQgSW52b2ljZSBJbmZvICovfVxuICAgICAgICAgIHtzZWxlY3RlZEludm9pY2UgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBwLTQgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTkwMCBtYi0zXCI+2YXYudmE2YjZhdin2Kog2KfZhNmB2KfYqtmI2LHYqSDYp9mE2YXYrtiq2KfYsdipPC9oMz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxwPjxzdHJvbmc+2LHZgtmFINin2YTZgdin2KrZiNix2Kk6PC9zdHJvbmc+IHtzZWxlY3RlZEludm9pY2UuaW52b2ljZU51bWJlcn08L3A+XG4gICAgICAgICAgICAgICAgICA8cD48c3Ryb25nPtin2YTYudmF2YrZhDo8L3N0cm9uZz4ge3NlbGVjdGVkSW52b2ljZS5jdXN0b21lck5hbWV9PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8cD48c3Ryb25nPtin2YTYqtin2LHZitiuOjwvc3Ryb25nPiB7bmV3IERhdGUoc2VsZWN0ZWRJbnZvaWNlLmNyZWF0ZWRBdCkudG9Mb2NhbGVEYXRlU3RyaW5nKCdhci1FRycpfTwvcD5cbiAgICAgICAgICAgICAgICAgIDxwPjxzdHJvbmc+2KfZhNil2KzZhdin2YTZijo8L3N0cm9uZz4gJHtwYXJzZUZsb2F0KHNlbGVjdGVkSW52b2ljZS5maW5hbFRvdGFsIHx8IDApLnRvRml4ZWQoMil9PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogUmV0dXJuIERldGFpbHMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZm9ybS1sYWJlbFwiPtiq2KfYsdmK2K4g2KfZhNil2LHYrNin2Lk8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnJldHVybkRhdGV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHJldHVybkRhdGU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmb3JtLWlucHV0XCJcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmb3JtLWxhYmVsXCI+2LfYsdmK2YLYqSDYp9mE2KfYs9iq2LHYr9in2K88L2xhYmVsPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnJlZnVuZE1ldGhvZH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgcmVmdW5kTWV0aG9kOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQ0FTSFwiPtmG2YLYr9mKPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkNSRURJVFwiPtix2LXZitivINmE2YTYudmF2YrZhDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJFWENIQU5HRVwiPtin2LPYqtio2K/Yp9mEPC9vcHRpb24+XG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUmV0dXJuIEl0ZW1zICovfVxuICAgICAgICAgIHtmb3JtRGF0YS5pdGVtcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItNFwiPti52YbYp9i12LEg2KfZhNil2LHYrNin2Lk8L2gzPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIHtmb3JtRGF0YS5pdGVtcy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEyIGdhcC00IGl0ZW1zLWVuZCBwLTQgYm9yZGVyIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZvcm0tbGFiZWxcIj7Yp9mE2YXZhtiq2Kw8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIHB5LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLnByb2R1Y3ROYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uY3VzdG9taXphdGlvbkRldGFpbHMgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTYwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uY3VzdG9taXphdGlvbkRldGFpbHMubWFwKChkZXRhaWwsIGlkeCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2lkeH0+4oCiIHtkZXRhaWwub3B0aW9uTmFtZX06IHtkZXRhaWwuc2VsZWN0ZWROYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZvcm0tbGFiZWxcIj7Yp9mE2YPZhdmK2Kkg2KfZhNij2LXZhNmK2Kk8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIHB5LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLnF1YW50aXR5fVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmb3JtLWxhYmVsXCI+2YPZhdmK2Kkg2KfZhNil2LHYrNin2Lk8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aXRlbS5yZXR1cm5RdWFudGl0eX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlUmV0dXJuSXRlbShpbmRleCwgJ3JldHVyblF1YW50aXR5JywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1heD17aXRlbS5tYXhSZXR1cm5RdWFudGl0eX1cbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmb3JtLWxhYmVsXCI+2KfZhNiz2LnYsTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgcHktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgJHtwYXJzZUZsb2F0KGl0ZW0udW5pdFByaWNlKS50b0ZpeGVkKDIpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmb3JtLWxhYmVsXCI+2LPYqNioINin2YTYpdix2KzYp9i5PC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aXRlbS5yZXR1cm5SZWFzb259XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZVJldHVybkl0ZW0oaW5kZXgsICdyZXR1cm5SZWFzb24nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmb3JtLWlucHV0XCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+2KfYrtiq2LEg2KfZhNiz2KjYqDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkRFRkVDVElWRVwiPti52YrYqCDZgdmKINin2YTZhdmG2KrYrDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIldST05HX0lURU1cIj7ZhdmG2KrYrCDYrtin2LfYpjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkNVU1RPTUVSX0NIQU5HRVwiPtiq2LrZitmK2LEg2LHYo9mKINin2YTYudmF2YrZhDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkRBTUFHRURcIj7YqtmE2YEg2KPYq9mG2KfYoSDYp9mE2LTYrdmGPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiT1RIRVJcIj7Yo9iu2LHZiTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogUmVmdW5kIFN1bW1hcnkgKi99XG4gICAgICAgICAge3JlZnVuZEFtb3VudCA+IDAgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHAtNCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItM1wiPtmF2YTYrti1INin2YTYp9iz2KrYsdiv2KfYrzwvaDM+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+2KXYrNmF2KfZhNmKINin2YTZhdio2YTYuiDYp9mE2YXYs9iq2LHYrzo8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj4ke3JlZnVuZEFtb3VudC50b0ZpeGVkKDIpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAgey8qIFJlYXNvbiBhbmQgTm90ZXMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmb3JtLWxhYmVsXCI+2LPYqNioINin2YTYpdix2KzYp9i5INin2YTYudin2YU8L2xhYmVsPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnJlYXNvbn1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgcmVhc29uOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dFwiXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj7Yp9iu2KrYsSDYp9mE2LPYqNioPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkRFRkVDVElWRVwiPti52YrYqCDZgdmKINin2YTZhdmG2KrYrDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJXUk9OR19JVEVNXCI+2YXZhtiq2Kwg2K7Yp9i32KY8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQ1VTVE9NRVJfQ0hBTkdFXCI+2KrYutmK2YrYsSDYsdij2Yog2KfZhNi52YXZitmEPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkRBTUFHRURcIj7YqtmE2YEg2KPYq9mG2KfYoSDYp9mE2LTYrdmGPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIldBUlJBTlRZXCI+2YXYt9in2YTYqNipINi22YXYp9mGPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIk9USEVSXCI+2KPYrtix2Yk8L29wdGlvbj5cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZvcm0tbGFiZWxcIj7ZhdmE2KfYrdi42KfYqjwvbGFiZWw+XG4gICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5ub3Rlc31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgbm90ZXM6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmb3JtLWlucHV0XCJcbiAgICAgICAgICAgICAgICByb3dzPVwiM1wiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLZhdmE2KfYrdi42KfYqiDYpdi22KfZgdmK2KkuLi5cIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogQWN0aW9ucyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgc3BhY2UteC00XCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tc2Vjb25kYXJ5XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg2KXZhNi62KfYoVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZyB8fCAhc2VsZWN0ZWRJbnZvaWNlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tcHJpbWFyeVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtsb2FkaW5nID8gJ9is2KfYsdmKINin2YTYrdmB2LguLi4nIDogKHNhbGVzUmV0dXJuID8gJ9iq2K3Yr9mK2KsnIDogJ9il2YbYtNin2KEg2KfZhNmF2LHYqtis2LknKX1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Zvcm0+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVRyYW5zbGF0aW9uIiwiWE1hcmtJY29uIiwiUGx1c0ljb24iLCJUcmFzaEljb24iLCJNYWduaWZ5aW5nR2xhc3NJY29uIiwiYXhpb3MiLCJ0b2FzdCIsIlNhbGVzUmV0dXJuTW9kYWwiLCJpc09wZW4iLCJvbkNsb3NlIiwib25TYXZlIiwic2FsZXNSZXR1cm4iLCJ0IiwibG9hZGluZyIsInNldExvYWRpbmciLCJjdXN0b21lcnMiLCJzZXRDdXN0b21lcnMiLCJpbnZvaWNlcyIsInNldEludm9pY2VzIiwic2VsZWN0ZWRJbnZvaWNlIiwic2V0U2VsZWN0ZWRJbnZvaWNlIiwic2VhcmNoSW52b2ljZSIsInNldFNlYXJjaEludm9pY2UiLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwiY3VzdG9tZXJJZCIsImludm9pY2VJZCIsImludm9pY2VOdW1iZXIiLCJjdXN0b21lck5hbWUiLCJyZXR1cm5EYXRlIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJyZWFzb24iLCJub3RlcyIsIml0ZW1zIiwicmVmdW5kTWV0aG9kIiwicmVmdW5kQW1vdW50IiwibG9hZEN1c3RvbWVycyIsInJlc3BvbnNlIiwiZ2V0IiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJkYXRhIiwiZXJyb3IiLCJjb25zb2xlIiwic2VhcmNoSW52b2ljZXMiLCJxdWVyeSIsImxlbmd0aCIsImhhbmRsZUludm9pY2VTZWFyY2giLCJ2YWx1ZSIsInNlbGVjdEludm9pY2UiLCJpbnZvaWNlIiwicHJldiIsImlkIiwibWFwIiwiaXRlbSIsInJldHVyblF1YW50aXR5IiwicmV0dXJuUmVhc29uIiwiY2FuUmV0dXJuIiwibWF4UmV0dXJuUXVhbnRpdHkiLCJxdWFudGl0eSIsInVwZGF0ZVJldHVybkl0ZW0iLCJpbmRleCIsImZpZWxkIiwibmV3SXRlbXMiLCJtYXhRdHkiLCJwYXJzZUZsb2F0Iiwid2FybmluZyIsImNhbGN1bGF0ZVJlZnVuZEFtb3VudCIsInJlZHVjZSIsInRvdGFsIiwicmV0dXJuUXR5IiwidW5pdFByaWNlIiwiZGlzY291bnQiLCJ0YXhSYXRlIiwiaXRlbVN1YnRvdGFsIiwiZGlzY291bnRBbW91bnQiLCJhZnRlckRpc2NvdW50IiwidGF4QW1vdW50IiwiaGFzVGF4IiwiaGFuZGxlU3VibWl0IiwiZSIsInByZXZlbnREZWZhdWx0IiwicmV0dXJuaW5nSXRlbXMiLCJmaWx0ZXIiLCJyZXR1cm5EYXRhIiwic3RhdHVzIiwicHV0IiwicG9zdCIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJidXR0b24iLCJvbkNsaWNrIiwiZm9ybSIsIm9uU3VibWl0IiwiaDMiLCJpbnB1dCIsInR5cGUiLCJvbkNoYW5nZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwicCIsImZpbmFsVG90YWwiLCJ0b0ZpeGVkIiwiY3JlYXRlZEF0IiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwic3Ryb25nIiwibGFiZWwiLCJyZXF1aXJlZCIsInNlbGVjdCIsIm9wdGlvbiIsInByb2R1Y3ROYW1lIiwiY3VzdG9taXphdGlvbkRldGFpbHMiLCJkZXRhaWwiLCJpZHgiLCJvcHRpb25OYW1lIiwic2VsZWN0ZWROYW1lIiwibWluIiwibWF4Iiwic3BhbiIsInRleHRhcmVhIiwicm93cyIsImRpc2FibGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/sales/SalesReturnModal.js\n"));

/***/ })

});