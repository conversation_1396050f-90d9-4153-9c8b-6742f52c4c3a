import { useState, useEffect } from 'react';
import { XMarkIcon, PlusIcon, MinusIcon, TrashIcon } from '@heroicons/react/24/outline';

export default function ProductCustomizerAdvanced({ isOpen, onClose, product, onSave }) {
  const [customizations, setCustomizations] = useState({});
  const [totalPrice, setTotalPrice] = useState(0);

  useEffect(() => {
    if (isOpen && product) {
      // Initialize with default selections
      const defaultCustomizations = {};
      product.customizationOptions?.forEach(option => {
        if (option.required && option.options.length > 0) {
          if (option.allowMultiple) {
            defaultCustomizations[option.id] = [{ 
              optionId: option.options[0].id, 
              quantity: 1 
            }];
          } else {
            defaultCustomizations[option.id] = option.options[0].id;
          }
        }
      });
      setCustomizations(defaultCustomizations);
    }
  }, [isOpen, product]);

  useEffect(() => {
    calculateTotalPrice();
  }, [customizations, product]);

  const calculateTotalPrice = () => {
    if (!product) return;
    
    let total = parseFloat(product.basePrice) || 0;
    
    Object.entries(customizations).forEach(([optionId, selection]) => {
      const option = product.customizationOptions?.find(opt => opt.id === optionId);
      if (option) {
        if (option.allowMultiple && Array.isArray(selection)) {
          selection.forEach(item => {
            const selected = option.options.find(opt => opt.id === item.optionId);
            if (selected) {
              total += (parseFloat(selected.price) || 0) * (parseInt(item.quantity) || 1);
            }
          });
        } else {
          const selected = option.options.find(opt => opt.id === selection);
          if (selected) {
            total += parseFloat(selected.price) || 0;
          }
        }
      }
    });
    
    setTotalPrice(total);
  };

  // Handle single selection options
  const handleSingleOptionChange = (optionId, selectedId) => {
    setCustomizations(prev => ({
      ...prev,
      [optionId]: selectedId
    }));
  };

  // Handle multiple selection options
  const handleMultipleOptionChange = (optionId, index, field, value) => {
    setCustomizations(prev => {
      const newCustomizations = { ...prev };
      if (!newCustomizations[optionId]) {
        newCustomizations[optionId] = [];
      }
      
      const newArray = [...newCustomizations[optionId]];
      if (field === 'optionId') {
        newArray[index] = { ...newArray[index], optionId: value };
      } else if (field === 'quantity') {
        newArray[index] = { ...newArray[index], quantity: parseInt(value) || 1 };
      }
      
      newCustomizations[optionId] = newArray;
      return newCustomizations;
    });
  };

  // Add new item to multiple selection
  const addMultipleItem = (optionId) => {
    const option = product.customizationOptions?.find(opt => opt.id === optionId);
    if (!option) return;
    
    setCustomizations(prev => {
      const current = prev[optionId] || [];
      if (current.length >= (option.maxQuantity || 10)) return prev;
      
      return {
        ...prev,
        [optionId]: [...current, { optionId: option.options[0].id, quantity: 1 }]
      };
    });
  };

  // Remove item from multiple selection
  const removeMultipleItem = (optionId, index) => {
    setCustomizations(prev => {
      const newArray = [...(prev[optionId] || [])];
      newArray.splice(index, 1);
      return {
        ...prev,
        [optionId]: newArray
      };
    });
  };

  const handleSave = () => {
    const customizationDetails = [];
    
    Object.entries(customizations).forEach(([optionId, selection]) => {
      const option = product.customizationOptions?.find(opt => opt.id === optionId);
      if (!option) return;
      
      if (option.allowMultiple && Array.isArray(selection)) {
        selection.forEach(item => {
          const selectedOption = option.options.find(opt => opt.id === item.optionId);
          if (selectedOption) {
            customizationDetails.push({
              optionId,
              optionName: option.name,
              selectedId: item.optionId,
              selectedName: selectedOption.nameAr || selectedOption.name,
              price: selectedOption.price,
              quantity: item.quantity,
              componentId: selectedOption.componentId
            });
          }
        });
      } else {
        const selectedOption = option.options.find(opt => opt.id === selection);
        if (selectedOption) {
          customizationDetails.push({
            optionId,
            optionName: option.name,
            selectedId: selection,
            selectedName: selectedOption.nameAr || selectedOption.name,
            price: selectedOption.price,
            quantity: 1,
            componentId: selectedOption.componentId
          });
        }
      }
    });

    onSave({
      customizations,
      customizationDetails,
      totalPrice
    });
    
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            تخصيص المنتج: {product?.nameAr || product?.name}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Base Price */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium text-blue-900 mb-2">السعر الأساسي</h3>
            <p className="text-2xl font-bold text-blue-600">${parseFloat(product?.basePrice || 0).toFixed(2)}</p>
          </div>

          {/* Customization Options */}
          {product?.customizationOptions?.map((option) => (
            <div key={option.id} className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {option.name}
                  {option.required && <span className="text-red-500 mr-1">*</span>}
                </h3>
                {option.allowMultiple && (
                  <button
                    onClick={() => addMultipleItem(option.id)}
                    className="btn-secondary flex items-center text-sm"
                    disabled={(customizations[option.id] || []).length >= (option.maxQuantity || 10)}
                  >
                    <PlusIcon className="h-4 w-4 mr-1" />
                    إضافة
                  </button>
                )}
              </div>

              {option.allowMultiple ? (
                // Multiple selection with quantities
                <div className="space-y-3">
                  {(customizations[option.id] || []).map((item, index) => (
                    <div key={index} className="grid grid-cols-12 gap-4 items-center p-3 bg-white rounded border">
                      <div className="col-span-6">
                        <select
                          value={item.optionId}
                          onChange={(e) => handleMultipleOptionChange(option.id, index, 'optionId', e.target.value)}
                          className="form-input"
                        >
                          {option.options.map((opt) => (
                            <option key={opt.id} value={opt.id}>
                              {opt.nameAr || opt.name} - ${opt.price}
                            </option>
                          ))}
                        </select>
                      </div>
                      
                      <div className="col-span-3">
                        <div className="flex items-center">
                          <button
                            onClick={() => handleMultipleOptionChange(option.id, index, 'quantity', Math.max(1, item.quantity - 1))}
                            className="p-1 text-gray-500 hover:text-gray-700"
                          >
                            <MinusIcon className="h-4 w-4" />
                          </button>
                          <input
                            type="number"
                            value={item.quantity}
                            onChange={(e) => handleMultipleOptionChange(option.id, index, 'quantity', e.target.value)}
                            className="form-input mx-2 text-center w-16"
                            min="1"
                          />
                          <button
                            onClick={() => handleMultipleOptionChange(option.id, index, 'quantity', item.quantity + 1)}
                            className="p-1 text-gray-500 hover:text-gray-700"
                          >
                            <PlusIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                      
                      <div className="col-span-2">
                        <div className="text-sm font-medium text-gray-900">
                          ${((option.options.find(opt => opt.id === item.optionId)?.price || 0) * item.quantity).toFixed(2)}
                        </div>
                      </div>
                      
                      <div className="col-span-1">
                        <button
                          onClick={() => removeMultipleItem(option.id, index)}
                          className="text-red-600 hover:text-red-800"
                          disabled={(customizations[option.id] || []).length <= 1 && option.required}
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                // Single selection
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {option.options.map((opt) => (
                    <label
                      key={opt.id}
                      className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                        customizations[option.id] === opt.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <input
                        type="radio"
                        name={option.id}
                        value={opt.id}
                        checked={customizations[option.id] === opt.id}
                        onChange={(e) => handleSingleOptionChange(option.id, e.target.value)}
                        className="sr-only"
                      />
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">
                          {opt.nameAr || opt.name}
                        </div>
                        <div className="text-sm text-gray-600">
                          +${opt.price.toFixed(2)}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              )}
            </div>
          ))}

          {/* Total Price */}
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-green-900">السعر الإجمالي</h3>
              <p className="text-2xl font-bold text-green-600">${totalPrice.toFixed(2)}</p>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            <button
              onClick={onClose}
              className="btn-secondary"
            >
              إلغاء
            </button>
            <button
              onClick={handleSave}
              className="btn-primary"
            >
              حفظ التخصيص
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
