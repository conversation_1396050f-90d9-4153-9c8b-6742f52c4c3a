import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Layout from '../components/Layout';
import ComputerStorePOS from '../components/sales/ComputerStorePOS';
import PaymentManagerAdvanced from '../components/sales/PaymentManagerAdvanced';
import QuickCustomerModal from '../components/sales/QuickCustomerModal';
import ProductCustomizerAdvanced from '../components/sales/ProductCustomizerAdvanced';
import axios from 'axios';
import toast from 'react-hot-toast';

export default function StorePOS() {
  const router = useRouter();
  
  // Main POS states
  const [cart, setCart] = useState([]);
  const [customer, setCustomer] = useState(null);
  const [products, setProducts] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [categories, setCategories] = useState([]);
  
  // Modal states
  const [showPayment, setShowPayment] = useState(false);
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [showCustomizer, setShowCustomizer] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [currentItemIndex, setCurrentItemIndex] = useState(null);
  
  // Sale type
  const [saleType, setSaleType] = useState('DIRECT'); // DIRECT, CUSTOM_ORDER, QUOTE
  
  // Customer search
  const [customerSearch, setCustomerSearch] = useState('');
  
  // Product search and filters
  const [productSearch, setProductSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  
  // Daily summary
  const [dailySummary, setDailySummary] = useState(null);

  // Load initial data
  useEffect(() => {
    loadProducts();
    loadCustomers();
    loadCategories();
    loadDailySummary();
  }, []);

  const loadProducts = async () => {
    try {
      const response = await axios.get('/api/products');
      setProducts(response.data.products || response.data);
    } catch (error) {
      toast.error('خطأ في تحميل المنتجات');
    }
  };

  const loadCustomers = async () => {
    try {
      const response = await axios.get('/api/customers');
      setCustomers(response.data.customers || response.data);
    } catch (error) {
      toast.error('خطأ في تحميل العملاء');
    }
  };

  const loadCategories = async () => {
    try {
      const response = await axios.get('/api/categories');
      setCategories(response.data.categories || response.data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const loadDailySummary = async () => {
    try {
      const response = await axios.get('/api/store-sales/daily-summary');
      setDailySummary(response.data);
    } catch (error) {
      console.error('Error loading daily summary:', error);
    }
  };

  // Customer functions
  const searchCustomers = (phone) => {
    if (phone.length < 3) return [];
    return customers.filter(c => 
      c.phone.includes(phone) || 
      c.name.toLowerCase().includes(phone.toLowerCase()) ||
      (c.nameAr && c.nameAr.includes(phone))
    );
  };

  const selectCustomer = (selectedCustomer) => {
    setCustomer(selectedCustomer);
    setCustomerSearch(selectedCustomer.phone);
  };

  const clearCustomer = () => {
    setCustomer(null);
    setCustomerSearch('');
  };

  const handleCustomerCreated = (newCustomer) => {
    setCustomers([...customers, newCustomer]);
    selectCustomer(newCustomer);
    setShowCustomerModal(false);
  };

  // Cart functions
  const addToCart = (product, quantity = 1, customization = null) => {
    // Check if product is customizable and needs customization
    if (product.isCustomizable && !customization) {
      setSelectedProduct(product);
      setCurrentItemIndex(null);
      setShowCustomizer(true);
      return;
    }

    const existingIndex = cart.findIndex(item => 
      item.productId === product.id && 
      JSON.stringify(item.customization) === JSON.stringify(customization)
    );

    if (existingIndex >= 0) {
      const newCart = [...cart];
      newCart[existingIndex].quantity += quantity;
      newCart[existingIndex].total = newCart[existingIndex].quantity * newCart[existingIndex].unitPrice;
      setCart(newCart);
    } else {
      const unitPrice = customization ? customization.totalPrice : parseFloat(product.unitPrice || product.basePrice);
      const newItem = {
        id: Date.now() + Math.random(),
        productId: product.id,
        productName: product.nameAr || product.name,
        productCode: product.code,
        quantity,
        unitPrice,
        total: quantity * unitPrice,
        customization,
        hasTax: false,
        taxRate: 14,
        discount: 0
      };
      setCart([...cart, newItem]);
    }
    toast.success('تم إضافة المنتج للسلة');
  };

  const updateCartItem = (itemId, field, value) => {
    setCart(cart.map(item => {
      if (item.id === itemId) {
        const updatedItem = { ...item, [field]: value };
        
        // Recalculate total
        const quantity = parseFloat(updatedItem.quantity) || 0;
        const unitPrice = parseFloat(updatedItem.unitPrice) || 0;
        const discount = parseFloat(updatedItem.discount) || 0;
        const taxRate = parseFloat(updatedItem.taxRate) || 0;
        
        const subtotal = quantity * unitPrice;
        const discountAmount = subtotal * (discount / 100);
        const afterDiscount = subtotal - discountAmount;
        const taxAmount = updatedItem.hasTax ? (afterDiscount * (taxRate / 100)) : 0;
        
        updatedItem.total = afterDiscount + taxAmount;
        updatedItem.subtotal = subtotal;
        updatedItem.discountAmount = discountAmount;
        updatedItem.taxAmount = taxAmount;
        
        return updatedItem;
      }
      return item;
    }));
  };

  const removeFromCart = (itemId) => {
    setCart(cart.filter(item => item.id !== itemId));
    toast.success('تم حذف المنتج من السلة');
  };

  const clearCart = () => {
    setCart([]);
    setCustomer(null);
    setCustomerSearch('');
  };

  // Handle customization
  const handleCustomizationSave = (customizationData) => {
    if (currentItemIndex !== null) {
      // Update existing item
      updateCartItem(cart[currentItemIndex].id, 'unitPrice', customizationData.totalPrice);
      updateCartItem(cart[currentItemIndex].id, 'customization', customizationData);
    } else {
      // Add new item
      addToCart(selectedProduct, 1, customizationData);
    }
    
    setShowCustomizer(false);
    setSelectedProduct(null);
    setCurrentItemIndex(null);
  };

  // Calculate totals
  const calculateTotals = () => {
    const subtotal = cart.reduce((sum, item) => sum + (item.subtotal || item.total), 0);
    const totalDiscount = cart.reduce((sum, item) => sum + (item.discountAmount || 0), 0);
    const totalTax = cart.reduce((sum, item) => sum + (item.taxAmount || 0), 0);
    const total = cart.reduce((sum, item) => sum + item.total, 0);
    
    return {
      subtotal,
      totalDiscount,
      totalTax,
      total,
      itemCount: cart.reduce((sum, item) => sum + item.quantity, 0)
    };
  };

  // Handle payment completion
  const handlePaymentComplete = async (paymentData) => {
    try {
      const totals = calculateTotals();
      
      const saleData = {
        customerId: customer?.id || null,
        items: cart,
        payments: paymentData.payments,
        notes: '',
        subtotal: totals.subtotal,
        total: totals.total
      };

      let response;
      let successMessage;

      switch (saleType) {
        case 'DIRECT':
          response = await axios.post('/api/store-sales/direct-sale', saleData);
          successMessage = 'تم إتمام البيع بنجاح';
          break;
        case 'CUSTOM_ORDER':
          if (!customer) {
            toast.error('العميل مطلوب للطلبات المخصصة');
            return;
          }
          response = await axios.post('/api/store-sales/custom-order', {
            ...saleData,
            expectedDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
          });
          successMessage = 'تم إنشاء الطلب المخصص بنجاح';
          break;
        case 'QUOTE':
          response = await axios.post('/api/store-sales/quick-quote', {
            ...saleData,
            validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
          });
          successMessage = 'تم إنشاء عرض السعر بنجاح';
          break;
        default:
          throw new Error('نوع البيع غير صحيح');
      }

      toast.success(successMessage);
      
      // Clear cart and reset
      clearCart();
      setShowPayment(false);
      
      // Reload daily summary
      loadDailySummary();
      
      // Optionally print receipt or redirect
      console.log('Sale completed:', response.data);
      
    } catch (error) {
      console.error('Payment completion error:', error);
      toast.error(error.response?.data?.error || 'خطأ في إتمام العملية');
    }
  };

  // Filter products
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(productSearch.toLowerCase()) ||
                         (product.nameAr && product.nameAr.includes(productSearch)) ||
                         product.code.toLowerCase().includes(productSearch.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || product.categoryId === selectedCategory;
    return matchesSearch && matchesCategory && product.isActive !== false;
  });

  const totals = calculateTotals();

  return (
    <>
      <Head>
        <title>نقطة البيع - متجر الكمبيوتر</title>
      </Head>
      
      <Layout>
        <ComputerStorePOS
          cart={cart}
          customer={customer}
          products={filteredProducts}
          customers={customers}
          categories={categories}
          dailySummary={dailySummary}
          saleType={saleType}
          setSaleType={setSaleType}
          customerSearch={customerSearch}
          setCustomerSearch={setCustomerSearch}
          productSearch={productSearch}
          setProductSearch={setProductSearch}
          selectedCategory={selectedCategory}
          setSelectedCategory={setSelectedCategory}
          searchCustomers={searchCustomers}
          selectCustomer={selectCustomer}
          clearCustomer={clearCustomer}
          addToCart={addToCart}
          updateCartItem={updateCartItem}
          removeFromCart={removeFromCart}
          clearCart={clearCart}
          calculateTotals={calculateTotals}
          onShowPayment={() => setShowPayment(true)}
          onShowCustomerModal={() => setShowCustomerModal(true)}
          onShowCustomizer={(product, itemIndex = null) => {
            setSelectedProduct(product);
            setCurrentItemIndex(itemIndex);
            setShowCustomizer(true);
          }}
        />

        {/* Payment Manager */}
        <PaymentManagerAdvanced
          isOpen={showPayment}
          onClose={() => setShowPayment(false)}
          totalAmount={totals.total}
          customer={customer}
          onPaymentComplete={handlePaymentComplete}
          saleType={saleType}
        />

        {/* Quick Customer Modal */}
        <QuickCustomerModal
          isOpen={showCustomerModal}
          onClose={() => setShowCustomerModal(false)}
          onCustomerCreated={handleCustomerCreated}
          initialPhone={customerSearch}
        />

        {/* Product Customizer */}
        <ProductCustomizerAdvanced
          isOpen={showCustomizer}
          onClose={() => {
            setShowCustomizer(false);
            setSelectedProduct(null);
            setCurrentItemIndex(null);
          }}
          product={selectedProduct}
          onSave={handleCustomizationSave}
        />
      </Layout>
    </>
  );
}
