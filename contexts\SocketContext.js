import { createContext, useContext, useEffect, useState } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from './AuthContext';
import toast from 'react-hot-toast';

const SocketContext = createContext();

export function SocketProvider({ children }) {
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const { token, isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated && token) {
      // Initialize socket connection
      const newSocket = io(process.env.NEXT_PUBLIC_SOCKET_URL, {
        auth: {
          token: token
        }
      });

      newSocket.on('connect', () => {
        console.log('Socket connected');
        setIsConnected(true);
      });

      newSocket.on('disconnect', () => {
        console.log('Socket disconnected');
        setIsConnected(false);
      });

      // Listen for real-time events
      newSocket.on('inventory_updated', (data) => {
        toast.success(`Inventory updated: ${data.productName}`);
        // You can dispatch events to update local state here
      });

      newSocket.on('order_created', (data) => {
        toast.info(`New order created: ${data.orderNumber}`);
        // Add notification
        setNotifications(prev => [{
          id: Date.now(),
          type: 'info',
          title: 'New Order',
          message: `Order ${data.orderNumber} has been created`,
          timestamp: new Date(),
          read: false
        }, ...prev]);
      });

      newSocket.on('maintenance_updated', (data) => {
        toast.info(`Maintenance order ${data.orderNumber} status updated`);
        // Add notification
        setNotifications(prev => [{
          id: Date.now(),
          type: 'info',
          title: 'Maintenance Update',
          message: `Order ${data.orderNumber} status: ${data.status}`,
          timestamp: new Date(),
          read: false
        }, ...prev]);
      });

      newSocket.on('low_stock_alert', (data) => {
        toast.warning(`Low stock alert: ${data.productName}`);
        // Add notification
        setNotifications(prev => [{
          id: Date.now(),
          type: 'warning',
          title: 'Low Stock Alert',
          message: `${data.productName} is running low (${data.currentStock} remaining)`,
          timestamp: new Date(),
          read: false
        }, ...prev]);
      });

      setSocket(newSocket);

      return () => {
        newSocket.close();
      };
    } else {
      // Clean up socket when not authenticated
      if (socket) {
        socket.close();
        setSocket(null);
        setIsConnected(false);
      }
    }
  }, [isAuthenticated, token]);

  const emitEvent = (eventName, data) => {
    if (socket && isConnected) {
      socket.emit(eventName, data);
    }
  };

  const markNotificationAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  const clearNotifications = () => {
    setNotifications([]);
  };

  const value = {
    socket,
    isConnected,
    notifications,
    emitEvent,
    markNotificationAsRead,
    clearNotifications,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
}

export function useSocket() {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
}

export default SocketContext;
