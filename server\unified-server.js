const express = require('express');
const next = require('next');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const PORT = process.env.PORT || 3070;
const dev = process.env.NODE_ENV !== 'production';

// إنشاء تطبيق Next.js
const nextApp = next({ dev, dir: path.join(__dirname, '..') });
const handle = nextApp.getRequestHandler();

// إنشاء خادم Express
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// API Routes
// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    port: PORT,
    mode: 'unified'
  });
});

// Auth endpoints
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;
  
  // Simple test credentials
  const users = {
    'admin': { password: 'admin123', role: 'ADMIN', name: 'Admin User' },
    'manager': { password: 'manager123', role: 'MANAGER', name: 'Business Manager' },
    'sales': { password: 'sales123', role: 'SALES', name: 'Sales Representative' }
  };
  
  const user = users[username];
  
  if (user && user.password === password) {
    res.json({
      token: `token-${username}-${Date.now()}`,
      user: {
        id: username,
        username: username,
        firstName: user.name.split(' ')[0],
        lastName: user.name.split(' ')[1] || 'User',
        role: user.role
      },
      message: 'Login successful'
    });
  } else {
    res.status(401).json({ error: 'Invalid credentials' });
  }
});

// Profile endpoint for auth verification
app.get('/api/auth/profile', (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'No token provided' });
  }

  const token = authHeader.substring(7);

  // Simple token validation (in real app, verify JWT)
  if (token.startsWith('token-')) {
    const username = token.split('-')[1];
    const users = {
      'admin': { role: 'ADMIN', name: 'Admin User' },
      'manager': { role: 'MANAGER', name: 'Business Manager' },
      'sales': { role: 'SALES', name: 'Sales Representative' }
    };

    const user = users[username];
    if (user) {
      res.json({
        user: {
          id: username,
          username: username,
          firstName: user.name.split(' ')[0],
          lastName: user.name.split(' ')[1] || 'User',
          role: user.role
        }
      });
    } else {
      res.status(401).json({ error: 'Invalid token' });
    }
  } else {
    res.status(401).json({ error: 'Invalid token format' });
  }
});

// Products endpoints
app.get('/api/products', (req, res) => {
  const { productType } = req.query;
  
  const allProducts = [
    {
      id: '1',
      code: 'LAPTOP001',
      name: 'Custom Business Laptop',
      nameAr: 'لابتوب أعمال مخصص',
      productType: 'CUSTOMIZABLE',
      unitPrice: 999.99,
      costPrice: 750.00,
      currentStock: 15,
      categoryId: 'computers',
      hasInventory: true,
      customizationOptions: {
        ram: { required: true, min: 1, max: 2, label: 'RAM Slots', labelAr: 'فتحات الذاكرة' },
        storage: { required: true, min: 1, max: 2, label: 'Storage', labelAr: 'التخزين' },
        cpu: { required: true, min: 1, max: 1, label: 'Processor', labelAr: 'المعالج' }
      },
      compatibleComponents: ['RAM001', 'RAM002', 'SSD001', 'HDD001', 'CPU001', 'CPU002']
    },
    {
      id: '2',
      code: 'PC001',
      name: 'Custom Gaming PC',
      nameAr: 'جهاز كمبيوتر ألعاب مخصص',
      productType: 'CUSTOMIZABLE',
      unitPrice: 1299.99,
      costPrice: 1000.00,
      currentStock: 8,
      categoryId: 'computers',
      hasInventory: true
    },
    {
      id: '3',
      code: 'CPU001',
      name: 'Intel Core i7-13700K',
      nameAr: 'معالج إنتل كور i7-13700K',
      productType: 'COMPONENT',
      unitPrice: 399.99,
      costPrice: 320.00,
      currentStock: 15,
      categoryId: 'electronics',
      hasInventory: true
    },
    {
      id: '4',
      code: 'RAM001',
      name: 'Corsair Vengeance 16GB DDR4',
      nameAr: 'ذاكرة كورسير فينجانس 16 جيجا DDR4',
      productType: 'COMPONENT',
      unitPrice: 79.99,
      costPrice: 60.00,
      currentStock: 25,
      categoryId: 'electronics',
      hasInventory: true
    },
    {
      id: '5',
      code: 'SRV001',
      name: 'PC Assembly Service',
      nameAr: 'خدمة تجميع الكمبيوتر',
      productType: 'SERVICE',
      unitPrice: 50.00,
      costPrice: 25.00,
      currentStock: 0,
      categoryId: 'services',
      hasInventory: false
    },
    // منتجات قابلة للتخصيص
    {
      id: '6',
      code: 'CUSTOM001',
      name: 'Custom Gaming PC',
      nameAr: 'جهاز كمبيوتر ألعاب مخصص',
      productType: 'CUSTOMIZABLE',
      basePrice: 800.00,
      unitPrice: 800.00,
      costPrice: 600.00,
      currentStock: 0,
      categoryId: 'computers',
      hasInventory: false,
      isCustomizable: true,
      customizationOptions: [
        {
          id: 'cpu',
          name: 'المعالج',
          nameEn: 'Processor',
          required: true,
          options: [
            { id: 'cpu1', name: 'Intel i5-12400F', price: 200, componentId: '3' },
            { id: 'cpu2', name: 'Intel i7-12700F', price: 350, componentId: '3' },
            { id: 'cpu3', name: 'AMD Ryzen 5 5600X', price: 250, componentId: '3' }
          ]
        },
        {
          id: 'ram',
          name: 'الذاكرة',
          nameEn: 'Memory',
          required: true,
          options: [
            { id: 'ram1', name: '16GB DDR4', price: 80, componentId: '4' },
            { id: 'ram2', name: '32GB DDR4', price: 160, componentId: '4' },
            { id: 'ram3', name: '64GB DDR4', price: 320, componentId: '4' }
          ]
        },
        {
          id: 'gpu',
          name: 'كارت الشاشة',
          nameEn: 'Graphics Card',
          required: false,
          options: [
            { id: 'gpu1', name: 'RTX 3060', price: 400, componentId: '2' },
            { id: 'gpu2', name: 'RTX 3070', price: 600, componentId: '2' },
            { id: 'gpu3', name: 'RTX 4080', price: 1200, componentId: '2' }
          ]
        }
      ]
    },
    {
      id: '7',
      code: 'CUSTOM002',
      name: 'Custom Business Laptop',
      nameAr: 'لابتوب أعمال مخصص',
      productType: 'CUSTOMIZABLE',
      basePrice: 600.00,
      unitPrice: 600.00,
      costPrice: 450.00,
      currentStock: 0,
      categoryId: 'laptops',
      hasInventory: false,
      isCustomizable: true,
      customizationOptions: [
        {
          id: 'cpu',
          name: 'المعالج',
          nameEn: 'Processor',
          required: true,
          options: [
            { id: 'cpu1', name: 'Intel i5-1235U', price: 150, componentId: '3' },
            { id: 'cpu2', name: 'Intel i7-1255U', price: 250, componentId: '3' }
          ]
        },
        {
          id: 'ram',
          name: 'الذاكرة',
          nameEn: 'Memory',
          required: true,
          options: [
            { id: 'ram1', name: '8GB DDR4', price: 40, componentId: '4' },
            { id: 'ram2', name: '16GB DDR4', price: 80, componentId: '4' }
          ]
        },
        {
          id: 'storage',
          name: 'التخزين',
          nameEn: 'Storage',
          required: true,
          options: [
            { id: 'ssd1', name: '256GB SSD', price: 60, componentId: '4' },
            { id: 'ssd2', name: '512GB SSD', price: 120, componentId: '4' },
            { id: 'ssd3', name: '1TB SSD', price: 200, componentId: '4' }
          ]
        }
      ]
    }
  ];
  
  let filteredProducts = allProducts;
  
  if (productType) {
    filteredProducts = allProducts.filter(p => p.productType === productType);
  }
  
  res.json({ products: filteredProducts });
});

// Customers endpoints
app.get('/api/customers', (req, res) => {
  res.json({
    customers: [
      {
        id: '1',
        code: 'CUST001',
        name: 'شركة ABC للتكنولوجيا',
        nameAr: 'شركة ABC للتكنولوجيا',
        type: 'CUSTOMER',
        email: '<EMAIL>',
        phone: '+201234567890',
        address: 'شارع التحرير، القاهرة'
      },
      {
        id: '2',
        code: 'CUST002',
        name: 'مؤسسة XYZ التجارية',
        nameAr: 'مؤسسة XYZ التجارية',
        type: 'CUSTOMER',
        email: '<EMAIL>',
        phone: '+201098765432',
        address: 'شارع الجمهورية، الإسكندرية'
      },
      {
        id: '3',
        code: 'CUST003',
        name: 'شركة النيل للحاسوب',
        nameAr: 'شركة النيل للحاسوب',
        type: 'CUSTOMER',
        email: '<EMAIL>',
        phone: '+201555666777',
        address: 'شارع النيل، الجيزة'
      }
    ]
  });
});

// Dashboard endpoints
app.get('/api/dashboard/stats', (req, res) => {
  res.json({
    totalProducts: 25,
    totalCustomers: 15,
    totalSuppliers: 8,
    lowStock: 3,
    pendingQuotes: 3,
    pendingSales: 5,
    pendingInvoices: 7,
    pendingPurchases: 2,
    pendingMaintenance: 4,
    monthlySales: 45000,
    monthlyPurchases: 32000,
    yearlySales: 540000
  });
});

// عروض الأسعار - Quotes
app.get('/api/quotes', (req, res) => {
  res.json({
    quotes: [
      {
        id: '1',
        quoteNumber: 'QUO-2024-001',
        customerId: '1',
        customerName: 'ABC Corporation',
        status: 'PENDING',
        total: 1500.00,
        validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        createdAt: new Date()
      }
    ]
  });
});

app.post('/api/quotes', (req, res) => {
  const quote = {
    id: Date.now().toString(),
    quoteNumber: `QUO-2024-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
    ...req.body,
    status: 'DRAFT',
    createdAt: new Date().toISOString()
  };
  res.json({ quote, message: 'تم إنشاء عرض السعر بنجاح' });
});

app.put('/api/quotes/:id', (req, res) => {
  const { id } = req.params;
  const updatedQuote = {
    id,
    ...req.body,
    updatedAt: new Date().toISOString()
  };
  res.json({ quote: updatedQuote, message: 'تم تحديث عرض السعر بنجاح' });
});

// تحويل عرض السعر إلى أمر بيع
app.post('/api/quotes/:id/convert-to-order', (req, res) => {
  const { id } = req.params;
  const salesOrder = {
    id: Date.now().toString(),
    orderNumber: `SO-2024-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
    quoteId: id,
    status: 'PENDING',
    createdAt: new Date()
  };
  res.json({ salesOrder, message: 'Quote converted to sales order successfully' });
});

// أوامر البيع - Sales Orders (محدث)
app.get('/api/sales-orders', (req, res) => {
  res.json({
    salesOrders: [
      {
        id: '1',
        orderNumber: 'SO-2024-001',
        customerId: '1',
        customerName: 'ABC Corporation',
        status: 'CONFIRMED',
        total: 1500.00,
        createdAt: new Date()
      }
    ]
  });
});

app.post('/api/sales-orders', (req, res) => {
  const salesOrder = {
    id: Date.now().toString(),
    orderNumber: `SO-2024-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
    ...req.body,
    status: 'PENDING',
    createdAt: new Date().toISOString()
  };
  res.json({ salesOrder, message: 'تم إنشاء أمر البيع بنجاح' });
});

app.put('/api/sales-orders/:id', (req, res) => {
  const { id } = req.params;
  const updatedSalesOrder = {
    id,
    ...req.body,
    updatedAt: new Date().toISOString()
  };
  res.json({ salesOrder: updatedSalesOrder, message: 'تم تحديث أمر البيع بنجاح' });
});

// تحويل أمر البيع إلى فاتورة
app.post('/api/sales-orders/:id/convert-to-invoice', (req, res) => {
  const { id } = req.params;
  const invoice = {
    id: Date.now().toString(),
    invoiceNumber: `INV-2024-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
    salesOrderId: id,
    status: 'PENDING',
    createdAt: new Date()
  };
  res.json({ invoice, message: 'Sales order converted to invoice successfully' });
});

// الفواتير - Invoices
app.get('/api/invoices', (req, res) => {
  res.json({
    invoices: [
      {
        id: '1',
        invoiceNumber: 'INV-2024-001',
        customerId: '1',
        customerName: 'ABC Corporation',
        status: 'PENDING',
        total: 1500.00,
        paidAmount: 0,
        remainingAmount: 1500.00,
        createdAt: new Date()
      }
    ]
  });
});

app.post('/api/invoices', (req, res) => {
  const invoice = {
    id: Date.now().toString(),
    invoiceNumber: `INV-2024-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
    ...req.body,
    status: req.body.paidAmount >= req.body.total ? 'PAID' : req.body.paidAmount > 0 ? 'PARTIALLY_PAID' : 'PENDING',
    createdAt: new Date().toISOString()
  };
  res.json({ invoice, message: 'تم إنشاء الفاتورة بنجاح' });
});

app.put('/api/invoices/:id', (req, res) => {
  const { id } = req.params;
  const updatedInvoice = {
    id,
    ...req.body,
    status: req.body.paidAmount >= req.body.total ? 'PAID' : req.body.paidAmount > 0 ? 'PARTIALLY_PAID' : 'PENDING',
    updatedAt: new Date().toISOString()
  };
  res.json({ invoice: updatedInvoice, message: 'تم تحديث الفاتورة بنجاح' });
});

// Error handling for API routes
app.use('/api/*', (req, res) => {
  res.status(404).json({ error: 'API route not found' });
});

// تحضير Next.js وبدء الخادم
nextApp.prepare().then(() => {
  // Handle all other routes with Next.js
  app.all('*', (req, res) => {
    return handle(req, res);
  });

  app.listen(PORT, (err) => {
    if (err) throw err;
    console.log(`🚀 Unified Server running on port ${PORT}`);
    console.log(`📱 Frontend: http://localhost:${PORT}`);
    console.log(`🔧 Backend API: http://localhost:${PORT}/api`);
    console.log(`💡 Health check: http://localhost:${PORT}/api/health`);
  });
}).catch((ex) => {
  console.error('Error starting server:', ex.stack);
  process.exit(1);
});

module.exports = app;
