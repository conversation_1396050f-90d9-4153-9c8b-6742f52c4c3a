"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/store-pos",{

/***/ "__barrel_optimize__?names=ArrowPathIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DocumentTextIcon,MagnifyingGlassIcon,MinusIcon,PhoneIcon,PlusIcon,ShoppingCartIcon,TrashIcon,UserIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowPathIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DocumentTextIcon,MagnifyingGlassIcon,MinusIcon,PhoneIcon,PlusIcon,ShoppingCartIcon,TrashIcon,UserIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowPathIcon: function() { return /* reexport safe */ _ArrowPathIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   ComputerDesktopIcon: function() { return /* reexport safe */ _ComputerDesktopIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   DevicePhoneMobileIcon: function() { return /* reexport safe */ _DevicePhoneMobileIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   DocumentTextIcon: function() { return /* reexport safe */ _DocumentTextIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   MagnifyingGlassIcon: function() { return /* reexport safe */ _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; },\n/* harmony export */   MinusIcon: function() { return /* reexport safe */ _MinusIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]; },\n/* harmony export */   PhoneIcon: function() { return /* reexport safe */ _PhoneIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]; },\n/* harmony export */   PlusIcon: function() { return /* reexport safe */ _PlusIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]; },\n/* harmony export */   ShoppingCartIcon: function() { return /* reexport safe */ _ShoppingCartIcon_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]; },\n/* harmony export */   TrashIcon: function() { return /* reexport safe */ _TrashIcon_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]; },\n/* harmony export */   UserIcon: function() { return /* reexport safe */ _UserIcon_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]; },\n/* harmony export */   WrenchScrewdriverIcon: function() { return /* reexport safe */ _WrenchScrewdriverIcon_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _ArrowPathIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowPathIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _ComputerDesktopIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ComputerDesktopIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _DevicePhoneMobileIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DevicePhoneMobileIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _DocumentTextIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./DocumentTextIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MagnifyingGlassIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _MinusIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MinusIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js\");\n/* harmony import */ var _PhoneIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./PhoneIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _PlusIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PlusIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _ShoppingCartIcon_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ShoppingCartIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js\");\n/* harmony import */ var _TrashIcon_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./TrashIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _UserIcon_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./UserIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _WrenchScrewdriverIcon_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./WrenchScrewdriverIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1BhdGhJY29uLENvbXB1dGVyRGVza3RvcEljb24sRGV2aWNlUGhvbmVNb2JpbGVJY29uLERvY3VtZW50VGV4dEljb24sTWFnbmlmeWluZ0dsYXNzSWNvbixNaW51c0ljb24sUGhvbmVJY29uLFBsdXNJY29uLFNob3BwaW5nQ2FydEljb24sVHJhc2hJY29uLFVzZXJJY29uLFdyZW5jaFNjcmV3ZHJpdmVySWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUM2RDtBQUNZO0FBQ0k7QUFDVjtBQUNNO0FBQ3BCO0FBQ0E7QUFDRjtBQUNnQjtBQUNkO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/ZTU0OSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJyb3dQYXRoSWNvbiB9IGZyb20gXCIuL0Fycm93UGF0aEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb21wdXRlckRlc2t0b3BJY29uIH0gZnJvbSBcIi4vQ29tcHV0ZXJEZXNrdG9wSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIERldmljZVBob25lTW9iaWxlSWNvbiB9IGZyb20gXCIuL0RldmljZVBob25lTW9iaWxlSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIERvY3VtZW50VGV4dEljb24gfSBmcm9tIFwiLi9Eb2N1bWVudFRleHRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWFnbmlmeWluZ0dsYXNzSWNvbiB9IGZyb20gXCIuL01hZ25pZnlpbmdHbGFzc0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNaW51c0ljb24gfSBmcm9tIFwiLi9NaW51c0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQaG9uZUljb24gfSBmcm9tIFwiLi9QaG9uZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQbHVzSWNvbiB9IGZyb20gXCIuL1BsdXNJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2hvcHBpbmdDYXJ0SWNvbiB9IGZyb20gXCIuL1Nob3BwaW5nQ2FydEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUcmFzaEljb24gfSBmcm9tIFwiLi9UcmFzaEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VySWNvbiB9IGZyb20gXCIuL1VzZXJJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgV3JlbmNoU2NyZXdkcml2ZXJJY29uIH0gZnJvbSBcIi4vV3JlbmNoU2NyZXdkcml2ZXJJY29uLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowPathIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DocumentTextIcon,MagnifyingGlassIcon,MinusIcon,PhoneIcon,PlusIcon,ShoppingCartIcon,TrashIcon,UserIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "__barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!***********************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \***********************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ComputerDesktopIcon: function() { return /* reexport safe */ _ComputerDesktopIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   DevicePhoneMobileIcon: function() { return /* reexport safe */ _DevicePhoneMobileIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   XMarkIcon: function() { return /* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _ComputerDesktopIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ComputerDesktopIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _DevicePhoneMobileIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DevicePhoneMobileIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Db21wdXRlckRlc2t0b3BJY29uLERldmljZVBob25lTW9iaWxlSWNvbixYTWFya0ljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFDeUU7QUFDSSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz9iY2RkIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb21wdXRlckRlc2t0b3BJY29uIH0gZnJvbSBcIi4vQ29tcHV0ZXJEZXNrdG9wSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIERldmljZVBob25lTW9iaWxlSWNvbiB9IGZyb20gXCIuL0RldmljZVBob25lTW9iaWxlSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFhNYXJrSWNvbiB9IGZyb20gXCIuL1hNYXJrSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "./components/sales/ComputerBuilderAdvanced.js":
/*!*****************************************************!*\
  !*** ./components/sales/ComputerBuilderAdvanced.js ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ComputerBuilderAdvanced; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ComputerDesktopIcon,DevicePhoneMobileIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction ComputerBuilderAdvanced(param) {\n    let { isOpen, onClose, onSave, buildType = \"DESKTOP\" } = param;\n    _s();\n    const [selectedComponents, setSelectedComponents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        motherboard: null,\n        cpu: null,\n        ram: [],\n        storage: [],\n        gpu: null,\n        psu: null,\n        case: null,\n        cooling: null\n    });\n    const [totalPrice, setTotalPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [compatibility, setCompatibility] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        valid: true,\n        warnings: [],\n        errors: []\n    });\n    // Component categories for desktop computers\n    const desktopComponents = {\n        motherboard: {\n            name: \"اللوحة الأم\",\n            required: true,\n            options: [\n                {\n                    id: \"mb1\",\n                    name: \"ASUS PRIME B450M-A\",\n                    socket: \"AM4\",\n                    ramType: \"DDR4\",\n                    maxRam: 64,\n                    price: 85,\n                    stock: 5\n                },\n                {\n                    id: \"mb2\",\n                    name: \"MSI B550M PRO-B\",\n                    socket: \"AM4\",\n                    ramType: \"DDR4\",\n                    maxRam: 128,\n                    price: 120,\n                    stock: 3\n                },\n                {\n                    id: \"mb3\",\n                    name: \"ASUS ROG STRIX B550-F\",\n                    socket: \"AM4\",\n                    ramType: \"DDR4\",\n                    maxRam: 128,\n                    price: 180,\n                    stock: 2\n                },\n                {\n                    id: \"mb4\",\n                    name: \"MSI Z690-A PRO\",\n                    socket: \"LGA1700\",\n                    ramType: \"DDR4\",\n                    maxRam: 128,\n                    price: 200,\n                    stock: 4\n                },\n                {\n                    id: \"mb5\",\n                    name: \"ASUS PRIME Z690-P\",\n                    socket: \"LGA1700\",\n                    ramType: \"DDR5\",\n                    maxRam: 128,\n                    price: 250,\n                    stock: 2\n                }\n            ]\n        },\n        cpu: {\n            name: \"المعالج\",\n            required: true,\n            options: [\n                {\n                    id: \"cpu1\",\n                    name: \"AMD Ryzen 5 5600\",\n                    socket: \"AM4\",\n                    cores: 6,\n                    price: 150,\n                    stock: 8\n                },\n                {\n                    id: \"cpu2\",\n                    name: \"AMD Ryzen 7 5700X\",\n                    socket: \"AM4\",\n                    cores: 8,\n                    price: 200,\n                    stock: 5\n                },\n                {\n                    id: \"cpu3\",\n                    name: \"AMD Ryzen 9 5900X\",\n                    socket: \"AM4\",\n                    cores: 12,\n                    price: 350,\n                    stock: 2\n                },\n                {\n                    id: \"cpu4\",\n                    name: \"Intel Core i5-12400F\",\n                    socket: \"LGA1700\",\n                    cores: 6,\n                    price: 180,\n                    stock: 6\n                },\n                {\n                    id: \"cpu5\",\n                    name: \"Intel Core i7-12700F\",\n                    socket: \"LGA1700\",\n                    cores: 12,\n                    price: 280,\n                    stock: 4\n                },\n                {\n                    id: \"cpu6\",\n                    name: \"Intel Core i9-12900F\",\n                    socket: \"LGA1700\",\n                    cores: 16,\n                    price: 450,\n                    stock: 1\n                }\n            ]\n        },\n        ram: {\n            name: \"الذاكرة العشوائية\",\n            required: true,\n            allowMultiple: true,\n            maxSlots: 4,\n            options: [\n                {\n                    id: \"ram1\",\n                    name: \"Corsair 8GB DDR4-3200\",\n                    type: \"DDR4\",\n                    size: 8,\n                    speed: 3200,\n                    price: 35,\n                    stock: 15\n                },\n                {\n                    id: \"ram2\",\n                    name: \"Corsair 16GB DDR4-3200\",\n                    type: \"DDR4\",\n                    size: 16,\n                    speed: 3200,\n                    price: 65,\n                    stock: 12\n                },\n                {\n                    id: \"ram3\",\n                    name: \"G.Skill 32GB DDR4-3600\",\n                    type: \"DDR4\",\n                    size: 32,\n                    speed: 3600,\n                    price: 120,\n                    stock: 8\n                },\n                {\n                    id: \"ram4\",\n                    name: \"Corsair 16GB DDR5-5600\",\n                    type: \"DDR5\",\n                    size: 16,\n                    speed: 5600,\n                    price: 90,\n                    stock: 6\n                },\n                {\n                    id: \"ram5\",\n                    name: \"G.Skill 32GB DDR5-6000\",\n                    type: \"DDR5\",\n                    size: 32,\n                    speed: 6000,\n                    price: 180,\n                    stock: 4\n                }\n            ]\n        },\n        storage: {\n            name: \"وحدات التخزين\",\n            required: true,\n            allowMultiple: true,\n            maxSlots: 6,\n            options: [\n                {\n                    id: \"ssd1\",\n                    name: \"Samsung 980 250GB NVMe\",\n                    type: \"NVMe\",\n                    size: 250,\n                    price: 45,\n                    stock: 20\n                },\n                {\n                    id: \"ssd2\",\n                    name: \"Samsung 980 500GB NVMe\",\n                    type: \"NVMe\",\n                    size: 500,\n                    price: 75,\n                    stock: 15\n                },\n                {\n                    id: \"ssd3\",\n                    name: \"Samsung 980 PRO 1TB NVMe\",\n                    type: \"NVMe\",\n                    size: 1000,\n                    price: 150,\n                    stock: 10\n                },\n                {\n                    id: \"ssd4\",\n                    name: \"Kingston 500GB SATA SSD\",\n                    type: \"SATA_SSD\",\n                    size: 500,\n                    price: 55,\n                    stock: 12\n                },\n                {\n                    id: \"ssd5\",\n                    name: \"Crucial 1TB SATA SSD\",\n                    type: \"SATA_SSD\",\n                    size: 1000,\n                    price: 95,\n                    stock: 8\n                },\n                {\n                    id: \"hdd1\",\n                    name: \"Seagate 1TB HDD\",\n                    type: \"HDD\",\n                    size: 1000,\n                    price: 45,\n                    stock: 25\n                },\n                {\n                    id: \"hdd2\",\n                    name: \"WD Blue 2TB HDD\",\n                    type: \"HDD\",\n                    size: 2000,\n                    price: 65,\n                    stock: 18\n                }\n            ]\n        },\n        gpu: {\n            name: \"كارت الشاشة\",\n            required: false,\n            options: [\n                {\n                    id: \"gpu1\",\n                    name: \"NVIDIA GTX 1650\",\n                    memory: 4,\n                    price: 180,\n                    stock: 8\n                },\n                {\n                    id: \"gpu2\",\n                    name: \"NVIDIA RTX 3060\",\n                    memory: 12,\n                    price: 350,\n                    stock: 5\n                },\n                {\n                    id: \"gpu3\",\n                    name: \"NVIDIA RTX 3070\",\n                    memory: 8,\n                    price: 550,\n                    stock: 3\n                },\n                {\n                    id: \"gpu4\",\n                    name: \"NVIDIA RTX 4060\",\n                    memory: 8,\n                    price: 400,\n                    stock: 4\n                },\n                {\n                    id: \"gpu5\",\n                    name: \"NVIDIA RTX 4070\",\n                    memory: 12,\n                    price: 650,\n                    stock: 2\n                },\n                {\n                    id: \"gpu6\",\n                    name: \"AMD RX 6600\",\n                    memory: 8,\n                    price: 280,\n                    stock: 6\n                }\n            ]\n        },\n        psu: {\n            name: \"مزود الطاقة\",\n            required: true,\n            options: [\n                {\n                    id: \"psu1\",\n                    name: \"Corsair CV450 450W\",\n                    wattage: 450,\n                    price: 55,\n                    stock: 12\n                },\n                {\n                    id: \"psu2\",\n                    name: \"Corsair CV650 650W\",\n                    wattage: 650,\n                    price: 85,\n                    stock: 8\n                },\n                {\n                    id: \"psu3\",\n                    name: \"Corsair RM750 750W Gold\",\n                    wattage: 750,\n                    price: 120,\n                    stock: 5\n                },\n                {\n                    id: \"psu4\",\n                    name: \"Corsair RM850 850W Gold\",\n                    wattage: 850,\n                    price: 150,\n                    stock: 3\n                }\n            ]\n        },\n        case: {\n            name: \"صندوق الجهاز\",\n            required: true,\n            options: [\n                {\n                    id: \"case1\",\n                    name: \"Cooler Master MasterBox Q300L\",\n                    size: \"Mini-ITX\",\n                    price: 45,\n                    stock: 10\n                },\n                {\n                    id: \"case2\",\n                    name: \"Corsair 4000D Mid-Tower\",\n                    size: \"ATX\",\n                    price: 95,\n                    stock: 6\n                },\n                {\n                    id: \"case3\",\n                    name: \"NZXT H510 Mid-Tower\",\n                    size: \"ATX\",\n                    price: 85,\n                    stock: 8\n                },\n                {\n                    id: \"case4\",\n                    name: \"Fractal Design Define 7\",\n                    size: \"ATX\",\n                    price: 150,\n                    stock: 3\n                }\n            ]\n        },\n        cooling: {\n            name: \"نظام التبريد\",\n            required: false,\n            options: [\n                {\n                    id: \"cool1\",\n                    name: \"AMD Stock Cooler\",\n                    type: \"Air\",\n                    price: 0,\n                    stock: 999\n                },\n                {\n                    id: \"cool2\",\n                    name: \"Intel Stock Cooler\",\n                    type: \"Air\",\n                    price: 0,\n                    stock: 999\n                },\n                {\n                    id: \"cool3\",\n                    name: \"Cooler Master Hyper 212\",\n                    type: \"Air\",\n                    price: 35,\n                    stock: 15\n                },\n                {\n                    id: \"cool4\",\n                    name: \"Noctua NH-D15\",\n                    type: \"Air\",\n                    price: 95,\n                    stock: 5\n                },\n                {\n                    id: \"cool5\",\n                    name: \"Corsair H100i AIO\",\n                    type: \"Liquid\",\n                    price: 120,\n                    stock: 4\n                }\n            ]\n        }\n    };\n    // Laptop upgrade options (more limited)\n    const laptopComponents = {\n        ram: {\n            name: \"ترقية الذاكرة\",\n            required: false,\n            allowMultiple: true,\n            maxSlots: 2,\n            options: [\n                {\n                    id: \"lram1\",\n                    name: \"Corsair 8GB DDR4-3200 SO-DIMM\",\n                    type: \"DDR4\",\n                    size: 8,\n                    price: 40,\n                    stock: 20\n                },\n                {\n                    id: \"lram2\",\n                    name: \"Corsair 16GB DDR4-3200 SO-DIMM\",\n                    type: \"DDR4\",\n                    size: 16,\n                    price: 75,\n                    stock: 15\n                },\n                {\n                    id: \"lram3\",\n                    name: \"Corsair 32GB DDR4-3200 SO-DIMM\",\n                    type: \"DDR4\",\n                    size: 32,\n                    price: 140,\n                    stock: 8\n                },\n                {\n                    id: \"lram4\",\n                    name: \"Corsair 16GB DDR5-4800 SO-DIMM\",\n                    type: \"DDR5\",\n                    size: 16,\n                    price: 95,\n                    stock: 10\n                }\n            ]\n        },\n        storage: {\n            name: \"ترقية التخزين\",\n            required: false,\n            allowMultiple: true,\n            maxSlots: 2,\n            options: [\n                {\n                    id: \"lssd1\",\n                    name: \"Samsung 980 250GB M.2\",\n                    type: \"M.2\",\n                    size: 250,\n                    price: 50,\n                    stock: 25\n                },\n                {\n                    id: \"lssd2\",\n                    name: \"Samsung 980 500GB M.2\",\n                    type: \"M.2\",\n                    size: 500,\n                    price: 80,\n                    stock: 20\n                },\n                {\n                    id: \"lssd3\",\n                    name: \"Samsung 980 PRO 1TB M.2\",\n                    type: \"M.2\",\n                    size: 1000,\n                    price: 160,\n                    stock: 12\n                },\n                {\n                    id: \"lssd4\",\n                    name: 'Crucial 500GB SATA SSD 2.5\"',\n                    type: \"SATA\",\n                    size: 500,\n                    price: 60,\n                    stock: 15\n                }\n            ]\n        }\n    };\n    const components = buildType === \"DESKTOP\" ? desktopComponents : laptopComponents;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        calculateTotalPrice();\n        checkCompatibility();\n    }, [\n        selectedComponents\n    ]);\n    const calculateTotalPrice = ()=>{\n        let total = 0;\n        Object.entries(selectedComponents).forEach((param)=>{\n            let [category, selection] = param;\n            if (Array.isArray(selection)) {\n                selection.forEach((item)=>{\n                    var _components_category;\n                    const component = (_components_category = components[category]) === null || _components_category === void 0 ? void 0 : _components_category.options.find((opt)=>opt.id === item.id);\n                    if (component) {\n                        total += component.price * item.quantity;\n                    }\n                });\n            } else if (selection) {\n                var _components_category;\n                const component = (_components_category = components[category]) === null || _components_category === void 0 ? void 0 : _components_category.options.find((opt)=>opt.id === selection);\n                if (component) {\n                    total += component.price;\n                }\n            }\n        });\n        setTotalPrice(total);\n    };\n    const checkCompatibility = ()=>{\n        const warnings = [];\n        const errors = [];\n        if (buildType === \"DESKTOP\") {\n            var _components_motherboard, _components_cpu, _components_psu, _components_gpu;\n            // Check CPU and Motherboard compatibility\n            const motherboard = (_components_motherboard = components.motherboard) === null || _components_motherboard === void 0 ? void 0 : _components_motherboard.options.find((mb)=>mb.id === selectedComponents.motherboard);\n            const cpu = (_components_cpu = components.cpu) === null || _components_cpu === void 0 ? void 0 : _components_cpu.options.find((c)=>c.id === selectedComponents.cpu);\n            if (motherboard && cpu && motherboard.socket !== cpu.socket) {\n                errors.push(\"المعالج \".concat(cpu.name, \" غير متوافق مع اللوحة الأم \").concat(motherboard.name));\n            }\n            // Check RAM compatibility\n            if (motherboard && selectedComponents.ram.length > 0) {\n                const totalRamSize = selectedComponents.ram.reduce((sum, ram)=>{\n                    var _components_ram;\n                    const ramComponent = (_components_ram = components.ram) === null || _components_ram === void 0 ? void 0 : _components_ram.options.find((r)=>r.id === ram.id);\n                    return sum + (ramComponent ? ramComponent.size * ram.quantity : 0);\n                }, 0);\n                if (totalRamSize > motherboard.maxRam) {\n                    errors.push(\"إجمالي الذاكرة \".concat(totalRamSize, \"GB يتجاوز الحد الأقصى للوحة الأم \").concat(motherboard.maxRam, \"GB\"));\n                }\n                // Check RAM type compatibility\n                selectedComponents.ram.forEach((ram)=>{\n                    var _components_ram;\n                    const ramComponent = (_components_ram = components.ram) === null || _components_ram === void 0 ? void 0 : _components_ram.options.find((r)=>r.id === ram.id);\n                    if (ramComponent && motherboard.ramType !== ramComponent.type) {\n                        errors.push(\"نوع الذاكرة \".concat(ramComponent.type, \" غير متوافق مع اللوحة الأم \").concat(motherboard.ramType));\n                    }\n                });\n            }\n            // Check power requirements\n            const psu = (_components_psu = components.psu) === null || _components_psu === void 0 ? void 0 : _components_psu.options.find((p)=>p.id === selectedComponents.psu);\n            const gpu = (_components_gpu = components.gpu) === null || _components_gpu === void 0 ? void 0 : _components_gpu.options.find((g)=>g.id === selectedComponents.gpu);\n            if (psu && gpu) {\n                const estimatedPower = 150 + (gpu ? 250 : 50); // Basic estimation\n                if (psu.wattage < estimatedPower) {\n                    warnings.push(\"مزود الطاقة قد لا يكون كافياً. يُنصح بـ \".concat(estimatedPower, \"W على الأقل\"));\n                }\n            }\n        }\n        setCompatibility({\n            valid: errors.length === 0,\n            warnings,\n            errors\n        });\n    };\n    const handleComponentSelect = (category, componentId)=>{\n        var _components_category;\n        if ((_components_category = components[category]) === null || _components_category === void 0 ? void 0 : _components_category.allowMultiple) {\n            // Handle multiple selection (RAM, Storage)\n            const existing = selectedComponents[category].find((item)=>item.id === componentId);\n            if (existing) {\n                // Increase quantity\n                setSelectedComponents((prev)=>({\n                        ...prev,\n                        [category]: prev[category].map((item)=>item.id === componentId ? {\n                                ...item,\n                                quantity: item.quantity + 1\n                            } : item)\n                    }));\n            } else {\n                // Add new component\n                setSelectedComponents((prev)=>({\n                        ...prev,\n                        [category]: [\n                            ...prev[category],\n                            {\n                                id: componentId,\n                                quantity: 1\n                            }\n                        ]\n                    }));\n            }\n        } else {\n            // Handle single selection\n            setSelectedComponents((prev)=>({\n                    ...prev,\n                    [category]: componentId\n                }));\n        }\n    };\n    const handleComponentRemove = (category, componentId)=>{\n        var _components_category;\n        if ((_components_category = components[category]) === null || _components_category === void 0 ? void 0 : _components_category.allowMultiple) {\n            setSelectedComponents((prev)=>({\n                    ...prev,\n                    [category]: prev[category].filter((item)=>item.id !== componentId)\n                }));\n        } else {\n            setSelectedComponents((prev)=>({\n                    ...prev,\n                    [category]: null\n                }));\n        }\n    };\n    const handleQuantityChange = (category, componentId, newQuantity)=>{\n        if (newQuantity <= 0) {\n            handleComponentRemove(category, componentId);\n            return;\n        }\n        setSelectedComponents((prev)=>({\n                ...prev,\n                [category]: prev[category].map((item)=>item.id === componentId ? {\n                        ...item,\n                        quantity: newQuantity\n                    } : item)\n            }));\n    };\n    const handleSave = ()=>{\n        // Check if all required components are selected\n        const missingRequired = Object.entries(components).filter((param)=>{\n            let [category, config] = param;\n            if (!config.required) return false;\n            if (config.allowMultiple) {\n                return selectedComponents[category].length === 0;\n            } else {\n                return !selectedComponents[category];\n            }\n        });\n        if (missingRequired.length > 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"يجب اختيار: \".concat(missingRequired.map((param)=>{\n                let [cat, config] = param;\n                return config.name;\n            }).join(\", \")));\n            return;\n        }\n        if (!compatibility.valid) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(\"يوجد مشاكل في التوافق يجب حلها أولاً\");\n            return;\n        }\n        // Prepare build details\n        const buildDetails = {\n            buildType,\n            components: selectedComponents,\n            totalPrice,\n            compatibility,\n            summary: generateBuildSummary()\n        };\n        onSave(buildDetails);\n    };\n    const generateBuildSummary = ()=>{\n        const summary = [];\n        Object.entries(selectedComponents).forEach((param)=>{\n            let [category, selection] = param;\n            const categoryConfig = components[category];\n            if (!categoryConfig) return;\n            if (Array.isArray(selection)) {\n                selection.forEach((item)=>{\n                    const component = categoryConfig.options.find((opt)=>opt.id === item.id);\n                    if (component) {\n                        summary.push({\n                            category: categoryConfig.name,\n                            name: component.name,\n                            quantity: item.quantity,\n                            price: component.price,\n                            total: component.price * item.quantity\n                        });\n                    }\n                });\n            } else if (selection) {\n                const component = categoryConfig.options.find((opt)=>opt.id === selection);\n                if (component) {\n                    summary.push({\n                        category: categoryConfig.name,\n                        name: component.name,\n                        quantity: 1,\n                        price: component.price,\n                        total: component.price\n                    });\n                }\n            }\n        });\n        return summary;\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 flex items-center\",\n                            children: [\n                                buildType === \"DESKTOP\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.ComputerDesktopIcon, {\n                                    className: \"h-6 w-6 ml-2 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.DevicePhoneMobileIcon, {\n                                    className: \"h-6 w-6 ml-2 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this),\n                                buildType === \"DESKTOP\" ? \"تجميع جهاز كمبيوتر\" : \"ترقية لابتوب\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.XMarkIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                lineNumber: 377,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                            lineNumber: 376,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 space-y-6\",\n                                children: Object.entries(components).map((param)=>{\n                                    let [category, config] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n                                                children: [\n                                                    config.name,\n                                                    config.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500 mr-1\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    config.allowMultiple && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500 mr-2\",\n                                                        children: \"(يمكن اختيار أكثر من واحد)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                lineNumber: 388,\n                                                columnNumber: 19\n                                            }, this),\n                                            config.allowMultiple && selectedComponents[category].length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4 space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"المختار:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    selectedComponents[category].map((item)=>{\n                                                        const component = config.options.find((opt)=>opt.id === item.id);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between bg-white p-2 rounded border\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: component === null || component === void 0 ? void 0 : component.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleQuantityChange(category, item.id, item.quantity - 1),\n                                                                            className: \"w-6 h-6 bg-gray-200 rounded text-xs\",\n                                                                            children: \"-\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                            lineNumber: 408,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm w-8 text-center\",\n                                                                            children: item.quantity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                            lineNumber: 414,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleQuantityChange(category, item.id, item.quantity + 1),\n                                                                            className: \"w-6 h-6 bg-gray-200 rounded text-xs\",\n                                                                            children: \"+\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                            lineNumber: 415,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-green-600 w-16 text-right\",\n                                                                            children: [\n                                                                                \"$\",\n                                                                                ((component === null || component === void 0 ? void 0 : component.price) * item.quantity).toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleComponentRemove(category, item.id),\n                                                                            className: \"text-red-500 hover:text-red-700\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ComputerDesktopIcon_DevicePhoneMobileIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__.XMarkIcon, {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                            lineNumber: 424,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, item.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 27\n                                                        }, this);\n                                                    })\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                lineNumber: 400,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                                children: config.options.map((component)=>{\n                                                    const isSelected = config.allowMultiple ? selectedComponents[category].some((item)=>item.id === component.id) : selectedComponents[category] === component.id;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleComponentSelect(category, component.id),\n                                                        disabled: component.stock === 0,\n                                                        className: \"p-3 border rounded-lg text-right transition-colors \".concat(isSelected ? \"border-blue-500 bg-blue-50\" : component.stock === 0 ? \"border-gray-200 bg-gray-100 text-gray-400\" : \"border-gray-300 hover:border-gray-400\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: component.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-600 mt-1\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    component.price.toFixed(2),\n                                                                    \" - المخزون: \",\n                                                                    component.stock\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            component.socket && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-600\",\n                                                                children: [\n                                                                    \"Socket: \",\n                                                                    component.socket\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            component.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-green-600\",\n                                                                children: [\n                                                                    \"Type: \",\n                                                                    component.type\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, component.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                lineNumber: 438,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, category, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                        lineNumber: 387,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    !compatibility.valid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-red-900 mb-2\",\n                                                children: \"مشاكل التوافق:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this),\n                                            compatibility.errors.map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-red-700\",\n                                                    children: [\n                                                        \"• \",\n                                                        error\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 21\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                        lineNumber: 479,\n                                        columnNumber: 17\n                                    }, this),\n                                    compatibility.warnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-yellow-900 mb-2\",\n                                                children: \"تحذيرات:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                lineNumber: 489,\n                                                columnNumber: 19\n                                            }, this),\n                                            compatibility.warnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-yellow-700\",\n                                                    children: [\n                                                        \"• \",\n                                                        warning\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 21\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                        lineNumber: 488,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-green-900 mb-2\",\n                                                children: \"ملخص السعر:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1 text-sm\",\n                                                children: [\n                                                    generateBuildSummary().map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        item.name,\n                                                                        \" \",\n                                                                        item.quantity > 1 && \"(\".concat(item.quantity, \")\")\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"$\",\n                                                                        item.total.toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 21\n                                                        }, this)),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t pt-2 font-bold flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"الإجمالي:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"$\",\n                                                                    totalPrice.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                lineNumber: 499,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSave,\n                                                disabled: !compatibility.valid,\n                                                className: \"w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: \"حفظ التجميعة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                lineNumber: 515,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onClose,\n                                                className: \"w-full btn-secondary\",\n                                                children: \"إلغاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n            lineNumber: 366,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ComputerBuilderAdvanced.js\",\n        lineNumber: 365,\n        columnNumber: 5\n    }, this);\n}\n_s(ComputerBuilderAdvanced, \"GBmRb+0Xpfb6GTXGQecrlYLSL2k=\");\n_c = ComputerBuilderAdvanced;\nvar _c;\n$RefreshReg$(_c, \"ComputerBuilderAdvanced\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/sales/ComputerBuilderAdvanced.js\n"));

/***/ }),

/***/ "./components/sales/OrganizedStorePOS.js":
/*!***********************************************!*\
  !*** ./components/sales/OrganizedStorePOS.js ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OrganizedStorePOS; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DocumentTextIcon,MagnifyingGlassIcon,MinusIcon,PhoneIcon,PlusIcon,ShoppingCartIcon,TrashIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowPathIcon,ComputerDesktopIcon,DevicePhoneMobileIcon,DocumentTextIcon,MagnifyingGlassIcon,MinusIcon,PhoneIcon,PlusIcon,ShoppingCartIcon,TrashIcon,UserIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction OrganizedStorePOS(param) {\n    let { cart, customer, products, customers, categories, dailySummary, saleType, setSaleType, customerSearch, setCustomerSearch, productSearch, setProductSearch, selectedCategory, setSelectedCategory, searchCustomers, selectCustomer, clearCustomer, addToCart, updateCartItem, removeFromCart, clearCart, calculateTotals, onShowPayment, onShowCustomerModal, onShowComputerBuilder } = param;\n    var _dailySummary_invoices_total, _dailySummary_invoices, _dailySummary_cashBox_balance, _dailySummary_cashBox;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"products\"); // products, cart, customer\n    const totals = calculateTotals();\n    // Organize products by type\n    const organizedProducts = {\n        desktops: products.filter((p)=>p.categoryId === \"computers\" && p.name.toLowerCase().includes(\"desktop\")),\n        laptops: products.filter((p)=>p.categoryId === \"laptops\"),\n        components: products.filter((p)=>[\n                \"cpu\",\n                \"gpu\",\n                \"ram\",\n                \"storage\",\n                \"motherboard\"\n            ].includes(p.categoryId)),\n        accessories: products.filter((p)=>[\n                \"accessories\",\n                \"peripherals\"\n            ].includes(p.categoryId)),\n        services: products.filter((p)=>p.productType === \"SERVICE\")\n    };\n    const saleTypes = [\n        {\n            id: \"DIRECT\",\n            name: \"بيع مباشر\",\n            icon: _barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShoppingCartIcon,\n            color: \"blue\",\n            description: \"للعملاء الموجودين - دفع كامل\"\n        },\n        {\n            id: \"CUSTOM_BUILD\",\n            name: \"تجميع جهاز\",\n            icon: _barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.WrenchScrewdriverIcon,\n            color: \"green\",\n            description: \"تجميع كمبيوتر أو ترقية لابتوب\"\n        },\n        {\n            id: \"CUSTOM_ORDER\",\n            name: \"طلب مخصص\",\n            icon: _barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ComputerDesktopIcon,\n            color: \"purple\",\n            description: \"طلبات خاصة - دفعة مقدمة\"\n        },\n        {\n            id: \"QUOTE\",\n            name: \"عرض سعر\",\n            icon: _barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.DocumentTextIcon,\n            color: \"yellow\",\n            description: \"استفسار أسعار - بدون دفع\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"\\uD83D\\uDCBB متجر الكمبيوتر\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: saleTypes.map((type)=>{\n                                            const Icon = type.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSaleType(type.id),\n                                                className: \"px-3 py-2 rounded-lg font-medium text-sm transition-colors \".concat(saleType === type.id ? \"bg-\".concat(type.color, \"-600 text-white\") : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"),\n                                                title: type.description,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-4 w-4 inline ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    type.name\n                                                ]\n                                            }, type.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                lineNumber: 105,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            \"\\uD83D\\uDCCA اليوم: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold text-green-600\",\n                                                children: [\n                                                    \"$\",\n                                                    (dailySummary === null || dailySummary === void 0 ? void 0 : (_dailySummary_invoices = dailySummary.invoices) === null || _dailySummary_invoices === void 0 ? void 0 : (_dailySummary_invoices_total = _dailySummary_invoices.total) === null || _dailySummary_invoices_total === void 0 ? void 0 : _dailySummary_invoices_total.toFixed(2)) || \"0.00\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                lineNumber: 126,\n                                                columnNumber: 27\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            \"\\uD83D\\uDCB0 الصندوق: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold text-blue-600\",\n                                                children: [\n                                                    \"$\",\n                                                    (dailySummary === null || dailySummary === void 0 ? void 0 : (_dailySummary_cashBox = dailySummary.cashBox) === null || _dailySummary_cashBox === void 0 ? void 0 : (_dailySummary_cashBox_balance = _dailySummary_cashBox.balance) === null || _dailySummary_cashBox_balance === void 0 ? void 0 : _dailySummary_cashBox_balance.toFixed(2)) || \"0.00\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                lineNumber: 131,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = \"/sales\",\n                                        className: \"px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ArrowPathIcon, {\n                                                className: \"h-4 w-4 inline ml-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"مرتجعات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.UserIcon, {\n                                                            className: \"h-5 w-5 ml-2 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"معلومات العميل\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: clearCustomer,\n                                                    className: \"text-red-600 hover:text-red-800 text-sm\",\n                                                    children: \"مسح العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        customer ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-green-900\",\n                                                                children: customer.nameAr || customer.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-green-700 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.PhoneIcon, {\n                                                                        className: \"h-4 w-4 ml-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                        lineNumber: 177,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    customer.phone\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-green-600 mt-1\",\n                                                                children: [\n                                                                    \"النوع: \",\n                                                                    customer.type,\n                                                                    \" | الرصيد: $\",\n                                                                    parseFloat(customer.balance || 0).toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-green-700\",\n                                                                children: \"حد الائتمان\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-green-900\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    parseFloat(customer.creditLimit || 0).toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: customerSearch,\n                                                    onChange: (e)=>setCustomerSearch(e.target.value),\n                                                    placeholder: \"ابحث بالهاتف أو الاسم... (اختياري للبيع النقدي)\",\n                                                    className: \"form-input pl-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.MagnifyingGlassIcon, {\n                                                    className: \"h-5 w-5 text-gray-400 absolute left-3 top-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, this),\n                                                customerSearch.length >= 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto\",\n                                                    children: [\n                                                        searchCustomers(customerSearch).map((c)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>selectCustomer(c),\n                                                                className: \"w-full text-right px-4 py-3 hover:bg-gray-50 border-b border-gray-100\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: c.nameAr || c.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                        lineNumber: 210,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: c.phone\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: c.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                        lineNumber: 212,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, c.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 25\n                                                            }, this)),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: onShowCustomerModal,\n                                                            className: \"w-full text-right px-4 py-3 hover:bg-blue-50 text-blue-600 font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.PlusIcon, {\n                                                                    className: \"h-4 w-4 inline ml-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"إضافة عميل جديد\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 border-b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                            children: \"المنتجات والخدمات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>onShowComputerBuilder(\"DESKTOP\"),\n                                                                    className: \"btn-secondary text-sm flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ComputerDesktopIcon, {\n                                                                            className: \"h-4 w-4 ml-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                            lineNumber: 240,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        \"تجميع كمبيوتر\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>onShowComputerBuilder(\"LAPTOP\"),\n                                                                    className: \"btn-secondary text-sm flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.DevicePhoneMobileIcon, {\n                                                                            className: \"h-4 w-4 ml-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                            lineNumber: 247,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        \"ترقية لابتوب\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: productSearch,\n                                                            onChange: (e)=>setProductSearch(e.target.value),\n                                                            placeholder: \"ابحث عن منتج...\",\n                                                            className: \"form-input pl-10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.MagnifyingGlassIcon, {\n                                                            className: \"h-5 w-5 text-gray-400 absolute left-3 top-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-gray-900 mb-3 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ComputerDesktopIcon, {\n                                                                        className: \"h-4 w-4 ml-1 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"أجهزة كمبيوتر مكتبية\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 max-h-40 overflow-y-auto\",\n                                                                children: organizedProducts.desktops.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>addToCart(product),\n                                                                        className: \"w-full text-right p-2 hover:bg-gray-50 rounded border text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: product.nameAr || product.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 283,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-green-600\",\n                                                                                children: [\n                                                                                    \"$\",\n                                                                                    parseFloat(product.unitPrice || 0).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 284,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"المخزون: \",\n                                                                                    product.currentStock || 0\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 285,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, product.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-gray-900 mb-3 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.DevicePhoneMobileIcon, {\n                                                                        className: \"h-4 w-4 ml-1 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"أجهزة لابتوب\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 max-h-40 overflow-y-auto\",\n                                                                children: organizedProducts.laptops.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>addToCart(product),\n                                                                        className: \"w-full text-right p-2 hover:bg-gray-50 rounded border text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: product.nameAr || product.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 304,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-green-600\",\n                                                                                children: [\n                                                                                    \"$\",\n                                                                                    parseFloat(product.unitPrice || 0).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 305,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"المخزون: \",\n                                                                                    product.currentStock || 0\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, product.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-gray-900 mb-3 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.WrenchScrewdriverIcon, {\n                                                                        className: \"h-4 w-4 ml-1 text-purple-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"قطع غيار ومكونات\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 max-h-40 overflow-y-auto\",\n                                                                children: organizedProducts.components.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>addToCart(product),\n                                                                        className: \"w-full text-right p-2 hover:bg-gray-50 rounded border text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: product.nameAr || product.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 325,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-green-600\",\n                                                                                children: [\n                                                                                    \"$\",\n                                                                                    parseFloat(product.unitPrice || 0).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 326,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"المخزون: \",\n                                                                                    product.currentStock || 0\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 327,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, product.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-gray-900 mb-3\",\n                                                                children: \"إكسسوارات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 max-h-40 overflow-y-auto\",\n                                                                children: organizedProducts.accessories.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>addToCart(product),\n                                                                        className: \"w-full text-right p-2 hover:bg-gray-50 rounded border text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: product.nameAr || product.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 343,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-green-600\",\n                                                                                children: [\n                                                                                    \"$\",\n                                                                                    parseFloat(product.unitPrice || 0).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 344,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"المخزون: \",\n                                                                                    product.currentStock || 0\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, product.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border border-gray-200 rounded-lg p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-gray-900 mb-3\",\n                                                                children: \"خدمات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 max-h-40 overflow-y-auto\",\n                                                                children: organizedProducts.services.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>addToCart(product),\n                                                                        className: \"w-full text-right p-2 hover:bg-gray-50 rounded border text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: product.nameAr || product.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 361,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-green-600\",\n                                                                                children: [\n                                                                                    \"$\",\n                                                                                    parseFloat(product.unitPrice || 0).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 362,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, product.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShoppingCartIcon, {\n                                                        className: \"h-5 w-5 ml-2 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"السلة (\",\n                                                    totals.itemCount,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this),\n                                            cart.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearCart,\n                                                className: \"text-red-600 hover:text-red-800 text-sm\",\n                                                children: \"مسح الكل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 space-y-3 max-h-96 overflow-y-auto\",\n                                    children: cart.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-gray-500 py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShoppingCartIcon, {\n                                                className: \"h-12 w-12 mx-auto mb-2 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"السلة فارغة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: \"اضغط على المنتجات لإضافتها\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                        lineNumber: 394,\n                                        columnNumber: 17\n                                    }, this) : cart.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-gray-200 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-sm\",\n                                                                    children: item.productName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: item.productCode\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                item.buildDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-green-600 mt-1\",\n                                                                    children: [\n                                                                        \"✓ \",\n                                                                        item.buildDetails.buildType === \"DESKTOP\" ? \"تجميعة كمبيوتر\" : \"ترقية لابتوب\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeFromCart(item.id),\n                                                            className: \"text-red-500 hover:text-red-700\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.TrashIcon, {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-3 gap-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: \"الكمية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>updateCartItem(item.id, \"quantity\", Math.max(1, item.quantity - 1)),\n                                                                            className: \"w-6 h-6 bg-gray-200 rounded text-xs hover:bg-gray-300\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.MinusIcon, {\n                                                                                className: \"h-3 w-3 mx-auto\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                            lineNumber: 424,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"mx-2 w-8 text-center\",\n                                                                            children: item.quantity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                            lineNumber: 430,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>updateCartItem(item.id, \"quantity\", item.quantity + 1),\n                                                                            className: \"w-6 h-6 bg-gray-200 rounded text-xs hover:bg-gray-300\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_ComputerDesktopIcon_DevicePhoneMobileIcon_DocumentTextIcon_MagnifyingGlassIcon_MinusIcon_PhoneIcon_PlusIcon_ShoppingCartIcon_TrashIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.PlusIcon, {\n                                                                                className: \"h-3 w-3 mx-auto\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                                lineNumber: 435,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                            lineNumber: 431,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                    lineNumber: 423,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: \"السعر\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: item.unitPrice,\n                                                                    onChange: (e)=>updateCartItem(item.id, \"unitPrice\", parseFloat(e.target.value) || 0),\n                                                                    className: \"form-input text-sm h-8\",\n                                                                    step: \"0.01\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: \"خصم %\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: item.discount || 0,\n                                                                    onChange: (e)=>updateCartItem(item.id, \"discount\", parseFloat(e.target.value) || 0),\n                                                                    className: \"form-input text-sm h-8\",\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: item.hasTax || false,\n                                                                    onChange: (e)=>updateCartItem(item.id, \"hasTax\", e.target.checked),\n                                                                    className: \"ml-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"ضريبة \",\n                                                                item.taxRate || 14,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold text-green-600\",\n                                                            children: [\n                                                                \"$\",\n                                                                item.total.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, item.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                            lineNumber: 401,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this),\n                                cart.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-t border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"المجموع الفرعي:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"$\",\n                                                                totals.subtotal.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 19\n                                                }, this),\n                                                totals.totalDiscount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-red-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"الخصم:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"-$\",\n                                                                totals.totalDiscount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 21\n                                                }, this),\n                                                totals.totalTax > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-blue-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"الضريبة:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"+$\",\n                                                                totals.totalTax.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between font-bold text-lg border-t pt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"الإجمالي:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-600\",\n                                                            children: [\n                                                                \"$\",\n                                                                totals.total.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                            lineNumber: 486,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onShowPayment,\n                                            className: \"w-full mt-4 py-3 rounded-lg font-medium transition-colors \".concat(saleType === \"DIRECT\" ? \"bg-blue-600 hover:bg-blue-700 text-white\" : saleType === \"CUSTOM_BUILD\" ? \"bg-green-600 hover:bg-green-700 text-white\" : saleType === \"CUSTOM_ORDER\" ? \"bg-purple-600 hover:bg-purple-700 text-white\" : \"bg-yellow-600 hover:bg-yellow-700 text-white\"),\n                                            disabled: cart.length === 0,\n                                            children: saleType === \"DIRECT\" ? \"\\uD83D\\uDCB3 الدفع والإنهاء\" : saleType === \"CUSTOM_BUILD\" ? \"\\uD83D\\uDD27 حفظ التجميعة\" : saleType === \"CUSTOM_ORDER\" ? \"\\uD83D\\uDCCB إنشاء الطلب\" : \"\\uD83D\\uDCC4 إنشاء عرض السعر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                            lineNumber: 510,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                                    lineNumber: 485,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\OrganizedStorePOS.js\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(OrganizedStorePOS, \"re27P1B4QR/F5R71f7cETv6szOQ=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = OrganizedStorePOS;\nvar _c;\n$RefreshReg$(_c, \"OrganizedStorePOS\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/sales/OrganizedStorePOS.js\n"));

/***/ }),

/***/ "./pages/store-pos.js":
/*!****************************!*\
  !*** ./pages/store-pos.js ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StorePOS; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _components_sales_OrganizedStorePOS__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/sales/OrganizedStorePOS */ \"./components/sales/OrganizedStorePOS.js\");\n/* harmony import */ var _components_sales_PaymentManagerAdvanced__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/sales/PaymentManagerAdvanced */ \"./components/sales/PaymentManagerAdvanced.js\");\n/* harmony import */ var _components_sales_QuickCustomerModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/sales/QuickCustomerModal */ \"./components/sales/QuickCustomerModal.js\");\n/* harmony import */ var _components_sales_ComputerBuilderAdvanced__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/sales/ComputerBuilderAdvanced */ \"./components/sales/ComputerBuilderAdvanced.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction StorePOS() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Main POS states\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customer, setCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Modal states\n    const [showPayment, setShowPayment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCustomerModal, setShowCustomerModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCustomizer, setShowCustomizer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Sale type\n    const [saleType, setSaleType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"DIRECT\"); // DIRECT, CUSTOM_ORDER, QUOTE\n    // Customer search\n    const [customerSearch, setCustomerSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Product search and filters\n    const [productSearch, setProductSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    // Daily summary\n    const [dailySummary, setDailySummary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadProducts();\n        loadCustomers();\n        loadCategories();\n        loadDailySummary();\n    }, []);\n    const loadProducts = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].get(\"/api/products\");\n            setProducts(response.data.products || response.data);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(\"خطأ في تحميل المنتجات\");\n        }\n    };\n    const loadCustomers = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].get(\"/api/customers\");\n            setCustomers(response.data.customers || response.data);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(\"خطأ في تحميل العملاء\");\n        }\n    };\n    const loadCategories = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].get(\"/api/categories\");\n            setCategories(response.data.categories || response.data || []);\n        } catch (error) {\n            console.error(\"Error loading categories:\", error);\n        }\n    };\n    const loadDailySummary = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].get(\"/api/store-sales/daily-summary\");\n            setDailySummary(response.data);\n        } catch (error) {\n            console.error(\"Error loading daily summary:\", error);\n        }\n    };\n    // Customer functions\n    const searchCustomers = (phone)=>{\n        if (phone.length < 3) return [];\n        return customers.filter((c)=>c.phone.includes(phone) || c.name.toLowerCase().includes(phone.toLowerCase()) || c.nameAr && c.nameAr.includes(phone));\n    };\n    const selectCustomer = (selectedCustomer)=>{\n        setCustomer(selectedCustomer);\n        setCustomerSearch(selectedCustomer.phone);\n    };\n    const clearCustomer = ()=>{\n        setCustomer(null);\n        setCustomerSearch(\"\");\n    };\n    const handleCustomerCreated = (newCustomer)=>{\n        setCustomers([\n            ...customers,\n            newCustomer\n        ]);\n        selectCustomer(newCustomer);\n        setShowCustomerModal(false);\n    };\n    // Cart functions\n    const addToCart = function(product) {\n        let quantity = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, customization = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : null;\n        // Check if product is customizable and needs customization\n        if (product.isCustomizable && !customization) {\n            setSelectedProduct(product);\n            setCurrentItemIndex(null);\n            setShowCustomizer(true);\n            return;\n        }\n        const existingIndex = cart.findIndex((item)=>item.productId === product.id && JSON.stringify(item.customization) === JSON.stringify(customization));\n        if (existingIndex >= 0) {\n            const newCart = [\n                ...cart\n            ];\n            newCart[existingIndex].quantity += quantity;\n            newCart[existingIndex].total = newCart[existingIndex].quantity * newCart[existingIndex].unitPrice;\n            setCart(newCart);\n        } else {\n            const unitPrice = customization ? customization.totalPrice : parseFloat(product.unitPrice || product.basePrice);\n            const newItem = {\n                id: Date.now() + Math.random(),\n                productId: product.id,\n                productName: product.nameAr || product.name,\n                productCode: product.code,\n                quantity,\n                unitPrice,\n                total: quantity * unitPrice,\n                customization,\n                hasTax: false,\n                taxRate: 14,\n                discount: 0\n            };\n            setCart([\n                ...cart,\n                newItem\n            ]);\n        }\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success(\"تم إضافة المنتج للسلة\");\n    };\n    const updateCartItem = (itemId, field, value)=>{\n        setCart(cart.map((item)=>{\n            if (item.id === itemId) {\n                const updatedItem = {\n                    ...item,\n                    [field]: value\n                };\n                // Recalculate total\n                const quantity = parseFloat(updatedItem.quantity) || 0;\n                const unitPrice = parseFloat(updatedItem.unitPrice) || 0;\n                const discount = parseFloat(updatedItem.discount) || 0;\n                const taxRate = parseFloat(updatedItem.taxRate) || 0;\n                const subtotal = quantity * unitPrice;\n                const discountAmount = subtotal * (discount / 100);\n                const afterDiscount = subtotal - discountAmount;\n                const taxAmount = updatedItem.hasTax ? afterDiscount * (taxRate / 100) : 0;\n                updatedItem.total = afterDiscount + taxAmount;\n                updatedItem.subtotal = subtotal;\n                updatedItem.discountAmount = discountAmount;\n                updatedItem.taxAmount = taxAmount;\n                return updatedItem;\n            }\n            return item;\n        }));\n    };\n    const removeFromCart = (itemId)=>{\n        setCart(cart.filter((item)=>item.id !== itemId));\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success(\"تم حذف المنتج من السلة\");\n    };\n    const clearCart = ()=>{\n        setCart([]);\n        setCustomer(null);\n        setCustomerSearch(\"\");\n    };\n    // Handle customization\n    const handleCustomizationSave = (customizationData)=>{\n        if (currentItemIndex !== null) {\n            // Update existing item\n            updateCartItem(cart[currentItemIndex].id, \"unitPrice\", customizationData.totalPrice);\n            updateCartItem(cart[currentItemIndex].id, \"customization\", customizationData);\n        } else {\n            // Add new item\n            addToCart(selectedProduct, 1, customizationData);\n        }\n        setShowCustomizer(false);\n        setSelectedProduct(null);\n        setCurrentItemIndex(null);\n    };\n    // Calculate totals\n    const calculateTotals = ()=>{\n        const subtotal = cart.reduce((sum, item)=>sum + (item.subtotal || item.total), 0);\n        const totalDiscount = cart.reduce((sum, item)=>sum + (item.discountAmount || 0), 0);\n        const totalTax = cart.reduce((sum, item)=>sum + (item.taxAmount || 0), 0);\n        const total = cart.reduce((sum, item)=>sum + item.total, 0);\n        return {\n            subtotal,\n            totalDiscount,\n            totalTax,\n            total,\n            itemCount: cart.reduce((sum, item)=>sum + item.quantity, 0)\n        };\n    };\n    // Handle payment completion\n    const handlePaymentComplete = async (paymentData)=>{\n        try {\n            const totals = calculateTotals();\n            const saleData = {\n                customerId: (customer === null || customer === void 0 ? void 0 : customer.id) || null,\n                items: cart,\n                payments: paymentData.payments,\n                notes: \"\",\n                subtotal: totals.subtotal,\n                total: totals.total\n            };\n            let response;\n            let successMessage;\n            switch(saleType){\n                case \"DIRECT\":\n                    response = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].post(\"/api/store-sales/direct-sale\", saleData);\n                    successMessage = \"تم إتمام البيع بنجاح\";\n                    break;\n                case \"CUSTOM_ORDER\":\n                    if (!customer) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(\"العميل مطلوب للطلبات المخصصة\");\n                        return;\n                    }\n                    response = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].post(\"/api/store-sales/custom-order\", {\n                        ...saleData,\n                        expectedDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now\n                    });\n                    successMessage = \"تم إنشاء الطلب المخصص بنجاح\";\n                    break;\n                case \"QUOTE\":\n                    response = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].post(\"/api/store-sales/quick-quote\", {\n                        ...saleData,\n                        validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now\n                    });\n                    successMessage = \"تم إنشاء عرض السعر بنجاح\";\n                    break;\n                default:\n                    throw new Error(\"نوع البيع غير صحيح\");\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success(successMessage);\n            // Clear cart and reset\n            clearCart();\n            setShowPayment(false);\n            // Reload daily summary\n            loadDailySummary();\n            // Optionally print receipt or redirect\n            console.log(\"Sale completed:\", response.data);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Payment completion error:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"خطأ في إتمام العملية\");\n        }\n    };\n    // Filter products\n    const filteredProducts = products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(productSearch.toLowerCase()) || product.nameAr && product.nameAr.includes(productSearch) || product.code.toLowerCase().includes(productSearch.toLowerCase());\n        const matchesCategory = selectedCategory === \"all\" || product.categoryId === selectedCategory;\n        return matchesSearch && matchesCategory && product.isActive !== false;\n    });\n    const totals = calculateTotals();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"نقطة البيع - متجر الكمبيوتر\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\store-pos.js\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\store-pos.js\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComputerStorePOS, {\n                        cart: cart,\n                        customer: customer,\n                        products: filteredProducts,\n                        customers: customers,\n                        categories: categories,\n                        dailySummary: dailySummary,\n                        saleType: saleType,\n                        setSaleType: setSaleType,\n                        customerSearch: customerSearch,\n                        setCustomerSearch: setCustomerSearch,\n                        productSearch: productSearch,\n                        setProductSearch: setProductSearch,\n                        selectedCategory: selectedCategory,\n                        setSelectedCategory: setSelectedCategory,\n                        searchCustomers: searchCustomers,\n                        selectCustomer: selectCustomer,\n                        clearCustomer: clearCustomer,\n                        addToCart: addToCart,\n                        updateCartItem: updateCartItem,\n                        removeFromCart: removeFromCart,\n                        clearCart: clearCart,\n                        calculateTotals: calculateTotals,\n                        onShowPayment: ()=>setShowPayment(true),\n                        onShowCustomerModal: ()=>setShowCustomerModal(true),\n                        onShowCustomizer: function(product) {\n                            let itemIndex = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n                            setSelectedProduct(product);\n                            setCurrentItemIndex(itemIndex);\n                            setShowCustomizer(true);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\store-pos.js\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_PaymentManagerAdvanced__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        isOpen: showPayment,\n                        onClose: ()=>setShowPayment(false),\n                        totalAmount: totals.total,\n                        customer: customer,\n                        onPaymentComplete: handlePaymentComplete,\n                        saleType: saleType\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\store-pos.js\",\n                        lineNumber: 335,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_QuickCustomerModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        isOpen: showCustomerModal,\n                        onClose: ()=>setShowCustomerModal(false),\n                        onCustomerCreated: handleCustomerCreated,\n                        initialPhone: customerSearch\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\store-pos.js\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCustomizerAdvanced, {\n                        isOpen: showCustomizer,\n                        onClose: ()=>{\n                            setShowCustomizer(false);\n                            setSelectedProduct(null);\n                            setCurrentItemIndex(null);\n                        },\n                        product: selectedProduct,\n                        onSave: handleCustomizationSave\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\store-pos.js\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\store-pos.js\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(StorePOS, \"5Ipl7nFEyZHhnAhvCCToAUpybms=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = StorePOS;\nvar _c;\n$RefreshReg$(_c, \"StorePOS\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/store-pos.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js ***!
  \*****************************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction ComputerDesktopIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0 1 15 18.257V17.25m6-12V15a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 15V5.25m18 0A2.25 2.25 0 0 0 18.75 3H5.25A2.25 2.25 0 0 0 3 5.25m18 0V12a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 12V5.25\"\n    }));\n}\n_c = ComputerDesktopIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ComputerDesktopIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ComputerDesktopIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js ***!
  \*****************************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction MagnifyingGlassIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n    }));\n}\n_c = MagnifyingGlassIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(MagnifyingGlassIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"MagnifyingGlassIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/UserIcon.js":
/*!******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/UserIcon.js ***!
  \******************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction UserIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n    }));\n}\n_c = UserIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(UserIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"UserIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9Vc2VySWNvbi5qcyIsIm1hcHBpbmdzIjoiOztBQUErQjtBQUMvQixTQUFTQyxTQUFTLEtBSWpCLEVBQUVDLE1BQU07UUFKUyxFQUNoQkMsS0FBSyxFQUNMQyxPQUFPLEVBQ1AsR0FBR0MsT0FDSixHQUppQjtJQUtoQixPQUFPLFdBQVcsR0FBRUwsZ0RBQW1CLENBQUMsT0FBT08sT0FBT0MsTUFBTSxDQUFDO1FBQzNEQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLFFBQVE7UUFDUixlQUFlO1FBQ2YsYUFBYTtRQUNiQyxLQUFLWjtRQUNMLG1CQUFtQkU7SUFDckIsR0FBR0MsUUFBUUYsUUFBUSxXQUFXLEdBQUVILGdEQUFtQixDQUFDLFNBQVM7UUFDM0RlLElBQUlYO0lBQ04sR0FBR0QsU0FBUyxNQUFNLFdBQVcsR0FBRUgsZ0RBQW1CLENBQUMsUUFBUTtRQUN6RGdCLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxHQUFHO0lBQ0w7QUFDRjtLQXRCU2pCO0FBdUJULE1BQU1rQixhQUFhLFdBQVcsR0FBR25CLDZDQUFnQixDQUFDQzs7QUFDbEQsK0RBQWVrQixVQUFVQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL1VzZXJJY29uLmpzP2M0MjQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiBVc2VySWNvbih7XG4gIHRpdGxlLFxuICB0aXRsZUlkLFxuICAuLi5wcm9wc1xufSwgc3ZnUmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCBPYmplY3QuYXNzaWduKHtcbiAgICB4bWxuczogXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiLFxuICAgIGZpbGw6IFwibm9uZVwiLFxuICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgc3Ryb2tlV2lkdGg6IDEuNSxcbiAgICBzdHJva2U6IFwiY3VycmVudENvbG9yXCIsXG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIixcbiAgICBcImRhdGEtc2xvdFwiOiBcImljb25cIixcbiAgICByZWY6IHN2Z1JlZixcbiAgICBcImFyaWEtbGFiZWxsZWRieVwiOiB0aXRsZUlkXG4gIH0sIHByb3BzKSwgdGl0bGUgPyAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInRpdGxlXCIsIHtcbiAgICBpZDogdGl0bGVJZFxuICB9LCB0aXRsZSkgOiBudWxsLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInBhdGhcIiwge1xuICAgIHN0cm9rZUxpbmVjYXA6IFwicm91bmRcIixcbiAgICBzdHJva2VMaW5lam9pbjogXCJyb3VuZFwiLFxuICAgIGQ6IFwiTTE1Ljc1IDZhMy43NSAzLjc1IDAgMSAxLTcuNSAwIDMuNzUgMy43NSAwIDAgMSA3LjUgMFpNNC41MDEgMjAuMTE4YTcuNSA3LjUgMCAwIDEgMTQuOTk4IDBBMTcuOTMzIDE3LjkzMyAwIDAgMSAxMiAyMS43NWMtMi42NzYgMC01LjIxNi0uNTg0LTcuNDk5LTEuNjMyWlwiXG4gIH0pKTtcbn1cbmNvbnN0IEZvcndhcmRSZWYgPSAvKiNfX1BVUkVfXyovIFJlYWN0LmZvcndhcmRSZWYoVXNlckljb24pO1xuZXhwb3J0IGRlZmF1bHQgRm9yd2FyZFJlZjsiXSwibmFtZXMiOlsiUmVhY3QiLCJVc2VySWNvbiIsInN2Z1JlZiIsInRpdGxlIiwidGl0bGVJZCIsInByb3BzIiwiY3JlYXRlRWxlbWVudCIsIk9iamVjdCIsImFzc2lnbiIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJzdHJva2VXaWR0aCIsInN0cm9rZSIsInJlZiIsImlkIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwiZCIsIkZvcndhcmRSZWYiLCJmb3J3YXJkUmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\n"));

/***/ })

});