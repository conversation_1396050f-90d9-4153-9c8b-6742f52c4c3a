import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { useMutation, useQueryClient } from 'react-query';
import axios from 'axios';
import toast from 'react-hot-toast';
import {
  XMarkIcon,
  UserIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';

// View Customer Modal
export function ViewCustomerModal({ customer, isOpen, onClose }) {
  const { t } = useTranslation('common');

  if (!isOpen || !customer) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                {t('customers.viewCustomer')}
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <UserIcon className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{customer.name}</p>
                  <p className="text-sm text-gray-500">{customer.nameAr}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="w-5 h-5 flex items-center justify-center">
                  <span className="text-xs font-medium text-gray-500">ID</span>
                </div>
                <p className="text-sm text-gray-900">{customer.code}</p>
              </div>

              <div className="flex items-center space-x-3">
                <PhoneIcon className="h-5 w-5 text-gray-400" />
                <p className="text-sm text-gray-900">{customer.phone}</p>
              </div>

              {customer.email && (
                <div className="flex items-center space-x-3">
                  <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                  <p className="text-sm text-gray-900">{customer.email}</p>
                </div>
              )}

              {customer.address && (
                <div className="flex items-start space-x-3">
                  <MapPinIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-900">{customer.address}</p>
                    {customer.addressAr && (
                      <p className="text-sm text-gray-500">{customer.addressAr}</p>
                    )}
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-3">
                <CurrencyDollarIcon className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Balance: ${(parseFloat(customer.balance) || 0).toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-500">
                    Credit Limit: ${(parseFloat(customer.creditLimit) || 0).toFixed(2)}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="w-5 h-5 flex items-center justify-center">
                  <span className="text-xs font-medium text-gray-500">TYPE</span>
                </div>
                <span className={`badge ${
                  customer.type === 'CUSTOMER' ? 'badge-primary' :
                  customer.type === 'SUPPLIER' ? 'badge-success' : 'badge-info'
                }`}>
                  {customer.type}
                </span>
              </div>

              <div className="flex items-center space-x-3">
                <div className="w-5 h-5 flex items-center justify-center">
                  <span className="text-xs font-medium text-gray-500">STATUS</span>
                </div>
                <span className={`badge ${customer.isActive ? 'badge-success' : 'badge-secondary'}`}>
                  {customer.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              onClick={onClose}
              className="btn-secondary w-full sm:w-auto sm:ml-3"
            >
              {t('common.close')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Create/Edit Customer Modal
export function CustomerFormModal({ customer, isOpen, onClose, onSuccess }) {
  const { t } = useTranslation('common');
  const queryClient = useQueryClient();
  const isEdit = !!customer;

  const [formData, setFormData] = useState({
    code: customer?.code || '',
    name: customer?.name || '',
    nameAr: customer?.nameAr || '',
    type: customer?.type || 'CUSTOMER',
    email: customer?.email || '',
    phone: customer?.phone || '',
    address: customer?.address || '',
    addressAr: customer?.addressAr || '',
    balance: customer?.balance || 0,
    creditLimit: customer?.creditLimit || 0,
    isActive: customer?.isActive ?? true,
  });

  const [errors, setErrors] = useState({});

  const mutation = useMutation(
    async (data) => {
      if (isEdit) {
        const response = await axios.put(`${process.env.NEXT_PUBLIC_API_URL}/api/customers/${customer.id}`, data);
        return response.data;
      } else {
        const response = await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/api/customers`, data);
        return response.data;
      }
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['customers']);
        toast.success(isEdit ? 'Customer updated successfully' : 'Customer created successfully');
        onSuccess?.();
        onClose();
      },
      onError: (error) => {
        const errorMessage = error.response?.data?.error || 'Operation failed';
        toast.error(errorMessage);
        if (error.response?.data?.errors) {
          setErrors(error.response.data.errors);
        }
      }
    }
  );

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrors({});
    
    // Basic validation
    const newErrors = {};
    if (!formData.code) newErrors.code = 'Code is required';
    if (!formData.name) newErrors.name = 'Name is required';
    if (!formData.nameAr) newErrors.nameAr = 'Arabic name is required';
    if (!formData.phone) newErrors.phone = 'Phone is required';

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    mutation.mutate(formData);
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {isEdit ? t('customers.editCustomer') : t('customers.addCustomer')}
                </h3>
                <button
                  type="button"
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="form-label">
                    {t('customers.customerCode')} *
                  </label>
                  <input
                    type="text"
                    name="code"
                    value={formData.code}
                    onChange={handleChange}
                    className={`form-input ${errors.code ? 'border-red-500' : ''}`}
                    placeholder="Enter customer code"
                  />
                  {errors.code && <p className="form-error">{errors.code}</p>}
                </div>

                <div>
                  <label className="form-label">
                    {t('customers.customerType')}
                  </label>
                  <select
                    name="type"
                    value={formData.type}
                    onChange={handleChange}
                    className="form-input"
                  >
                    <option value="CUSTOMER">{t('customers.customer')}</option>
                    <option value="SUPPLIER">{t('customers.supplier')}</option>
                    <option value="BOTH">{t('customers.both')}</option>
                  </select>
                </div>

                <div>
                  <label className="form-label">
                    Name (English) *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className={`form-input ${errors.name ? 'border-red-500' : ''}`}
                    placeholder="Enter name in English"
                  />
                  {errors.name && <p className="form-error">{errors.name}</p>}
                </div>

                <div>
                  <label className="form-label">
                    Name (Arabic) *
                  </label>
                  <input
                    type="text"
                    name="nameAr"
                    value={formData.nameAr}
                    onChange={handleChange}
                    className={`form-input ${errors.nameAr ? 'border-red-500' : ''}`}
                    placeholder="أدخل الاسم بالعربية"
                    dir="rtl"
                  />
                  {errors.nameAr && <p className="form-error">{errors.nameAr}</p>}
                </div>

                <div>
                  <label className="form-label">
                    {t('customers.phone')} *
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className={`form-input ${errors.phone ? 'border-red-500' : ''}`}
                    placeholder="+1234567890"
                  />
                  {errors.phone && <p className="form-error">{errors.phone}</p>}
                </div>

                <div>
                  <label className="form-label">
                    {t('customers.email')}
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="form-label">
                    Address (English)
                  </label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="Enter address"
                  />
                </div>

                <div>
                  <label className="form-label">
                    Address (Arabic)
                  </label>
                  <input
                    type="text"
                    name="addressAr"
                    value={formData.addressAr}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="أدخل العنوان"
                    dir="rtl"
                  />
                </div>

                <div>
                  <label className="form-label">
                    {t('customers.balance')}
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    name="balance"
                    value={formData.balance}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="form-label">
                    {t('customers.creditLimit')}
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    name="creditLimit"
                    value={formData.creditLimit}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="0.00"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleChange}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      {t('common.active')}
                    </span>
                  </label>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={mutation.isLoading}
                className="btn-primary w-full sm:w-auto sm:ml-3"
              >
                {mutation.isLoading ? 'Saving...' : (isEdit ? t('common.save') : t('common.add'))}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary w-full sm:w-auto mt-3 sm:mt-0"
              >
                {t('common.cancel')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
