#!/bin/bash

echo "🚀 Starting Business Management System..."
echo ""

echo "📡 Starting Backend Server on port 3070..."
PORT=3070 node server/simple-server.js &
BACKEND_PID=$!

echo "⏳ Waiting 3 seconds for backend to start..."
sleep 3

echo "🌐 Starting Frontend Server on port 3071..."
npm run dev -- -p 3071 &
FRONTEND_PID=$!

echo ""
echo "✅ Both servers are running..."
echo "📱 Frontend: http://localhost:3071"
echo "🔧 Backend: http://localhost:3070"
echo ""
echo "💡 Press Ctrl+C to stop both servers"

# Function to cleanup processes
cleanup() {
    echo ""
    echo "🛑 Shutting down servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    exit 0
}

# Trap Ctrl+C
trap cleanup SIGINT

# Wait for processes
wait
