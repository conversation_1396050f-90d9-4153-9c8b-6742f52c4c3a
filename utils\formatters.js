/**
 * Utility functions for formatting data safely
 */

/**
 * Format currency value safely
 * @param {any} value - The value to format
 * @param {string} currency - Currency symbol (default: '$')
 * @param {number} decimals - Number of decimal places (default: 2)
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (value, currency = '$', decimals = 2) => {
  const numValue = parseFloat(value) || 0;
  return `${currency}${numValue.toFixed(decimals)}`;
};

/**
 * Format number safely
 * @param {any} value - The value to format
 * @param {number} decimals - Number of decimal places (default: 2)
 * @returns {number} Formatted number
 */
export const formatNumber = (value, decimals = 2) => {
  const numValue = parseFloat(value) || 0;
  return parseFloat(numValue.toFixed(decimals));
};

/**
 * Format percentage safely
 * @param {any} value - The value to format
 * @param {number} decimals - Number of decimal places (default: 1)
 * @returns {string} Formatted percentage string
 */
export const formatPercentage = (value, decimals = 1) => {
  const numValue = parseFloat(value) || 0;
  return `${numValue.toFixed(decimals)}%`;
};

/**
 * Format date safely
 * @param {any} date - The date to format
 * @param {string} locale - Locale for formatting (default: 'en-US')
 * @returns {string} Formatted date string
 */
export const formatDate = (date, locale = 'en-US') => {
  if (!date) return '-';
  try {
    return new Date(date).toLocaleDateString(locale);
  } catch (error) {
    return '-';
  }
};

/**
 * Format date and time safely
 * @param {any} date - The date to format
 * @param {string} locale - Locale for formatting (default: 'en-US')
 * @returns {string} Formatted date and time string
 */
export const formatDateTime = (date, locale = 'en-US') => {
  if (!date) return '-';
  try {
    return new Date(date).toLocaleString(locale);
  } catch (error) {
    return '-';
  }
};

/**
 * Truncate text safely
 * @param {string} text - The text to truncate
 * @param {number} maxLength - Maximum length (default: 50)
 * @returns {string} Truncated text
 */
export const truncateText = (text, maxLength = 50) => {
  if (!text || typeof text !== 'string') return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

/**
 * Get safe string value
 * @param {any} value - The value to convert
 * @param {string} fallback - Fallback value (default: '-')
 * @returns {string} Safe string value
 */
export const safeString = (value, fallback = '-') => {
  if (value === null || value === undefined || value === '') return fallback;
  return String(value);
};

/**
 * Get safe number value
 * @param {any} value - The value to convert
 * @param {number} fallback - Fallback value (default: 0)
 * @returns {number} Safe number value
 */
export const safeNumber = (value, fallback = 0) => {
  const numValue = parseFloat(value);
  return isNaN(numValue) ? fallback : numValue;
};

/**
 * Format phone number
 * @param {string} phone - Phone number to format
 * @returns {string} Formatted phone number
 */
export const formatPhone = (phone) => {
  if (!phone) return '-';
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Format based on length
  if (cleaned.length === 10) {
    return cleaned.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
  } else if (cleaned.length === 11) {
    return cleaned.replace(/(\d{1})(\d{3})(\d{3})(\d{4})/, '+$1 ($2) $3-$4');
  }
  
  return phone; // Return original if can't format
};

/**
 * Get status color class
 * @param {string} status - Status value
 * @param {object} colorMap - Color mapping object
 * @returns {string} CSS class for status color
 */
export const getStatusColor = (status, colorMap = {}) => {
  const defaultColors = {
    ACTIVE: 'bg-green-100 text-green-800',
    INACTIVE: 'bg-gray-100 text-gray-800',
    PENDING: 'bg-yellow-100 text-yellow-800',
    COMPLETED: 'bg-green-100 text-green-800',
    CANCELLED: 'bg-red-100 text-red-800',
    DRAFT: 'bg-gray-100 text-gray-800',
    CONFIRMED: 'bg-blue-100 text-blue-800',
    SHIPPED: 'bg-indigo-100 text-indigo-800',
    DELIVERED: 'bg-green-100 text-green-800',
  };
  
  const colors = { ...defaultColors, ...colorMap };
  return colors[status?.toUpperCase()] || 'bg-gray-100 text-gray-800';
};

/**
 * Calculate percentage
 * @param {number} value - Current value
 * @param {number} total - Total value
 * @param {number} decimals - Decimal places (default: 1)
 * @returns {number} Percentage value
 */
export const calculatePercentage = (value, total, decimals = 1) => {
  if (!total || total === 0) return 0;
  const percentage = (safeNumber(value) / safeNumber(total)) * 100;
  return parseFloat(percentage.toFixed(decimals));
};

/**
 * Format file size
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Validate and format email
 * @param {string} email - Email to validate
 * @returns {string|null} Formatted email or null if invalid
 */
export const formatEmail = (email) => {
  if (!email) return null;
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const trimmedEmail = email.trim().toLowerCase();
  
  return emailRegex.test(trimmedEmail) ? trimmedEmail : null;
};

/**
 * Generate initials from name
 * @param {string} name - Full name
 * @returns {string} Initials
 */
export const getInitials = (name) => {
  if (!name) return '';
  
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
};

/**
 * Format address for display
 * @param {object} address - Address object
 * @returns {string} Formatted address
 */
export const formatAddress = (address) => {
  if (!address) return '-';
  
  const parts = [];
  if (address.street) parts.push(address.street);
  if (address.city) parts.push(address.city);
  if (address.state) parts.push(address.state);
  if (address.zipCode) parts.push(address.zipCode);
  if (address.country) parts.push(address.country);
  
  return parts.length > 0 ? parts.join(', ') : '-';
};
