{"c": ["webpack"], "r": ["pages/customers", "pages/sales", "pages/purchases", "pages/inventory", "pages/accounting"], "m": ["./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js", "./node_modules/@heroicons/react/24/outline/esm/UserIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Ccustomers.js&page=%2Fcustomers!", "./pages/customers.js", "__barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon,TruckIcon,UserIcon,UsersIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Csales.js&page=%2Fsales!", "./pages/sales.js", "__barrel_optimize__?names=CurrencyDollarIcon,EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,ShoppingCartIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Cpurchases.js&page=%2Fpurchases!", "./pages/purchases.js", "__barrel_optimize__?names=CurrencyDollarIcon,EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,ShoppingBagIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./node_modules/@heroicons/react/24/outline/esm/ArrowsRightLeftIcon.js", "./node_modules/@heroicons/react/24/outline/esm/BuildingStorefrontIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Cinventory.js&page=%2Finventory!", "./pages/inventory.js", "__barrel_optimize__?names=ArrowsRightLeftIcon,BuildingStorefrontIcon,CubeIcon,ExclamationTriangleIcon,MagnifyingGlassIcon,PlusIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js", "./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js", "./node_modules/@heroicons/react/24/outline/esm/BanknotesIcon.js", "./node_modules/@heroicons/react/24/outline/esm/BuildingLibraryIcon.js", "./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Caccounting.js&page=%2Faccounting!", "./pages/accounting.js", "__barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ArrowsRightLeftIcon,BanknotesIcon,BuildingLibraryIcon,CreditCardIcon,CurrencyDollarIcon,MagnifyingGlassIcon,PlusIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js"]}