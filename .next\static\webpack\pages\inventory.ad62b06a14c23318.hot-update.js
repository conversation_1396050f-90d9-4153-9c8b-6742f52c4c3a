"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/inventory",{

/***/ "./pages/inventory.js":
/*!****************************!*\
  !*** ./pages/inventory.js ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: function() { return /* binding */ __N_SSG; },\n/* harmony export */   \"default\": function() { return /* binding */ Inventory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowsRightLeftIcon_BuildingStorefrontIcon_CubeIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowsRightLeftIcon,BuildingStorefrontIcon,CubeIcon,ExclamationTriangleIcon,MagnifyingGlassIcon,PlusIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowsRightLeftIcon,BuildingStorefrontIcon,CubeIcon,ExclamationTriangleIcon,MagnifyingGlassIcon,PlusIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/LoadingSpinner */ \"./components/LoadingSpinner.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Inventory() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedBranch, setSelectedBranch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showLowStock, setShowLowStock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch inventory data\n    const { data: inventoryData, isLoading, error, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        \"inventory\",\n        searchTerm,\n        selectedBranch,\n        showLowStock\n    ], async ()=>{\n        const params = new URLSearchParams({\n            search: searchTerm,\n            branchId: selectedBranch,\n            lowStock: showLowStock.toString()\n        });\n        const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"\".concat(\"http://localhost:3001\", \"/api/reports/inventory?\").concat(params));\n        return response.data;\n    }, {\n        keepPreviousData: true\n    });\n    // Fetch branches for filter\n    const { data: branchesData } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)(\"branches\", async ()=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"\".concat(\"http://localhost:3001\", \"/api/branches\"));\n        return response.data;\n    });\n    const inventory = (inventoryData === null || inventoryData === void 0 ? void 0 : inventoryData.inventory) || [];\n    const metrics = (inventoryData === null || inventoryData === void 0 ? void 0 : inventoryData.metrics) || {};\n    const branches = (branchesData === null || branchesData === void 0 ? void 0 : branchesData.branches) || [];\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        refetch();\n    };\n    const getStockStatusColor = (item)=>{\n        if (item.quantity === 0) {\n            return \"bg-red-100 text-red-800\";\n        } else if (item.quantity <= item.minStock) {\n            return \"bg-yellow-100 text-yellow-800\";\n        } else if (item.quantity > item.maxStock) {\n            return \"bg-blue-100 text-blue-800\";\n        }\n        return \"bg-green-100 text-green-800\";\n    };\n    const getStockStatus = (item)=>{\n        if (item.quantity === 0) {\n            return \"Out of Stock\";\n        } else if (item.quantity <= item.minStock) {\n            return \"Low Stock\";\n        } else if (item.quantity > item.maxStock) {\n            return \"Overstock\";\n        }\n        return \"In Stock\";\n    };\n    if (isLoading && !inventory.length) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"mt-2 text-sm font-medium text-gray-900\",\n                    children: t(\"common.error\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: \"Failed to load inventory data\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>refetch(),\n                    className: \"mt-4 btn-primary\",\n                    children: \"Try Again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: t(\"inventory.title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-600\",\n                                children: \"مراقبة مستويات المخزون عبر جميع الفروع\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3 rtl:space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"btn-secondary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowsRightLeftIcon_BuildingStorefrontIcon_CubeIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ArrowsRightLeftIcon, {\n                                        className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    t(\"inventory.transfer\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"btn-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowsRightLeftIcon_BuildingStorefrontIcon_CubeIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PlusIcon, {\n                                        className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this),\n                                    t(\"inventory.adjustment\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowsRightLeftIcon_BuildingStorefrontIcon_CubeIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CubeIcon, {\n                                            className: \"h-6 w-6 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: t(\"inventory.totalProducts\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: metrics.totalProducts || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowsRightLeftIcon_BuildingStorefrontIcon_CubeIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ExclamationTriangleIcon, {\n                                            className: \"h-6 w-6 text-yellow-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: t(\"inventory.lowStock\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"text-lg font-medium text-yellow-600\",\n                                                    children: metrics.lowStockItems || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowsRightLeftIcon_BuildingStorefrontIcon_CubeIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ExclamationTriangleIcon, {\n                                            className: \"h-6 w-6 text-red-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: t(\"inventory.outOfStock\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"text-lg font-medium text-red-600\",\n                                                    children: metrics.outOfStockItems || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowsRightLeftIcon_BuildingStorefrontIcon_CubeIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.BuildingStorefrontIcon, {\n                                            className: \"h-6 w-6 text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: t(\"inventory.totalValue\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"text-lg font-medium text-green-600\",\n                                                    children: [\n                                                        \"$\",\n                                                        (metrics.totalValue || 0).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-4 rounded-lg shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col sm:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowsRightLeftIcon_BuildingStorefrontIcon_CubeIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.MagnifyingGlassIcon, {\n                                        className: \"absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t(\"common.search\"),\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"form-input pl-10 rtl:pl-3 rtl:pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedBranch,\n                            onChange: (e)=>setSelectedBranch(e.target.value),\n                            className: \"form-input\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"all\",\n                                    children: \"All Branches\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                branches.map((branch)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: branch.id,\n                                        children: branch.name\n                                    }, branch.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: showLowStock,\n                                    onChange: (e)=>setShowLowStock(e.target.checked),\n                                    className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 rtl:ml-0 rtl:mr-2 text-sm text-gray-700\",\n                                    children: t(\"inventory.lowStockAlert\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary\",\n                            children: t(\"common.search\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"table\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Branch\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Current Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Min Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Max Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Value\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"common.actions\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: inventory.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: item.product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: item.product.nameAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: item.product.code\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: item.branch.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: item.branch.nameAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            item.quantity,\n                                                            \" \",\n                                                            item.product.unit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: item.minStock\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: item.maxStock\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            \"$\",\n                                                            (item.quantity * item.product.costPrice).toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"badge \".concat(getStockStatusColor(item)),\n                                                        children: getStockStatus(item)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 text-gray-400 hover:text-blue-600\",\n                                                                title: \"Adjust Stock\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowsRightLeftIcon_BuildingStorefrontIcon_CubeIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PlusIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 text-gray-400 hover:text-green-600\",\n                                                                title: \"Transfer\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowsRightLeftIcon_BuildingStorefrontIcon_CubeIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ArrowsRightLeftIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, \"\".concat(item.branchId, \"-\").concat(item.productId), true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    inventory.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowsRightLeftIcon_BuildingStorefrontIcon_CubeIcon_ExclamationTriangleIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CubeIcon, {\n                                className: \"mx-auto h-12 w-12 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"mt-2 text-sm font-medium text-gray-900\",\n                                children: \"No inventory found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-500\",\n                                children: \"No products match your current filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\inventory.js\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n_s(Inventory, \"1MApGnFA4b2i4mYKdPFHkLSOrts=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery\n    ];\n});\n_c = Inventory;\nvar _c;\n$RefreshReg$(_c, \"Inventory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/inventory.js\n"));

/***/ })

});