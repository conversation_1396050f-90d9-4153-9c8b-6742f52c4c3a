#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Business Management System Setup');
console.log('=====================================\n');

// Check if .env exists
const envPath = path.join(process.cwd(), '.env');
if (!fs.existsSync(envPath)) {
  console.log('📝 Creating .env file from template...');
  const envExamplePath = path.join(process.cwd(), '.env.example');
  if (fs.existsSync(envExamplePath)) {
    fs.copyFileSync(envExamplePath, envPath);
    console.log('✅ .env file created');
    console.log('⚠️  Please update the DATABASE_URL and JWT_SECRET in .env file\n');
  } else {
    console.log('❌ .env.example not found');
  }
}

// Check Node.js version
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
if (majorVersion < 18) {
  console.log(`❌ Node.js ${nodeVersion} detected. Please upgrade to Node.js 18 or higher.`);
  process.exit(1);
}
console.log(`✅ Node.js ${nodeVersion} detected`);

// Check if PostgreSQL is available
try {
  execSync('psql --version', { stdio: 'ignore' });
  console.log('✅ PostgreSQL detected');
} catch (error) {
  console.log('⚠️  PostgreSQL not found in PATH. Please ensure PostgreSQL is installed and accessible.');
}

console.log('\n📦 Installing dependencies...');
try {
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ Dependencies installed');
} catch (error) {
  console.log('❌ Failed to install dependencies');
  process.exit(1);
}

console.log('\n🔧 Setting up database...');
try {
  console.log('Generating Prisma client...');
  execSync('npm run db:generate', { stdio: 'inherit' });
  
  console.log('Pushing database schema...');
  execSync('npm run db:push', { stdio: 'inherit' });
  
  console.log('Seeding database...');
  execSync('npm run db:seed', { stdio: 'inherit' });
  
  console.log('✅ Database setup complete');
} catch (error) {
  console.log('❌ Database setup failed. Please check your DATABASE_URL in .env file');
  console.log('Make sure PostgreSQL is running and the database exists.');
  process.exit(1);
}

console.log('\n🎉 Setup completed successfully!');
console.log('\n📋 Next steps:');
console.log('1. Update your .env file with correct database credentials');
console.log('2. Start the development servers:');
console.log('   npm run dev:all');
console.log('3. Open http://localhost:3000 in your browser');
console.log('\n🔑 Demo credentials:');
console.log('   Admin: admin / admin123');
console.log('   Manager: manager / manager123');
console.log('   Sales: sales / sales123');
console.log('\n📚 For more information, see SETUP.md');
