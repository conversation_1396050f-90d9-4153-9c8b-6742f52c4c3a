/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/test-sales"],{

/***/ "__barrel_optimize__?names=BanknotesIcon,BuildingLibraryIcon,CalendarDaysIcon,CreditCardIcon,DevicePhoneMobileIcon,PlusIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!******************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BanknotesIcon,BuildingLibraryIcon,CalendarDaysIcon,CreditCardIcon,DevicePhoneMobileIcon,PlusIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \******************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BanknotesIcon: function() { return /* reexport safe */ _BanknotesIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   BuildingLibraryIcon: function() { return /* reexport safe */ _BuildingLibraryIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   CalendarDaysIcon: function() { return /* reexport safe */ _CalendarDaysIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   CreditCardIcon: function() { return /* reexport safe */ _CreditCardIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   DevicePhoneMobileIcon: function() { return /* reexport safe */ _DevicePhoneMobileIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; },\n/* harmony export */   PlusIcon: function() { return /* reexport safe */ _PlusIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]; },\n/* harmony export */   XMarkIcon: function() { return /* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _BanknotesIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BanknotesIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BanknotesIcon.js\");\n/* harmony import */ var _BuildingLibraryIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BuildingLibraryIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BuildingLibraryIcon.js\");\n/* harmony import */ var _CalendarDaysIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CalendarDaysIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js\");\n/* harmony import */ var _CreditCardIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CreditCardIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _DevicePhoneMobileIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DevicePhoneMobileIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _PlusIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PlusIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYW5rbm90ZXNJY29uLEJ1aWxkaW5nTGlicmFyeUljb24sQ2FsZW5kYXJEYXlzSWNvbixDcmVkaXRDYXJkSWNvbixEZXZpY2VQaG9uZU1vYmlsZUljb24sUGx1c0ljb24sWE1hcmtJY29uIT0hLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNkQ7QUFDWTtBQUNOO0FBQ0o7QUFDYztBQUMxQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz85ZWJiIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCYW5rbm90ZXNJY29uIH0gZnJvbSBcIi4vQmFua25vdGVzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJ1aWxkaW5nTGlicmFyeUljb24gfSBmcm9tIFwiLi9CdWlsZGluZ0xpYnJhcnlJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2FsZW5kYXJEYXlzSWNvbiB9IGZyb20gXCIuL0NhbGVuZGFyRGF5c0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDcmVkaXRDYXJkSWNvbiB9IGZyb20gXCIuL0NyZWRpdENhcmRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRGV2aWNlUGhvbmVNb2JpbGVJY29uIH0gZnJvbSBcIi4vRGV2aWNlUGhvbmVNb2JpbGVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGx1c0ljb24gfSBmcm9tIFwiLi9QbHVzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFhNYXJrSWNvbiB9IGZyb20gXCIuL1hNYXJrSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BanknotesIcon,BuildingLibraryIcon,CalendarDaysIcon,CreditCardIcon,DevicePhoneMobileIcon,PlusIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "__barrel_optimize__?names=CogIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*************************************************************************************************************!*\
  !*** __barrel_optimize__?names=CogIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CogIcon: function() { return /* reexport safe */ _CogIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   XMarkIcon: function() { return /* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _CogIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CogIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Db2dJY29uLFhNYXJrSWNvbiE9IS4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDaUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/YmNiZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29nSWNvbiB9IGZyb20gXCIuL0NvZ0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBYTWFya0ljb24gfSBmcm9tIFwiLi9YTWFya0ljb24uanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=CogIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Ctest-sales.js&page=%2Ftest-sales!":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Ctest-sales.js&page=%2Ftest-sales! ***!
  \**********************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/test-sales\",\n      function () {\n        return __webpack_require__(/*! ./pages/test-sales.js */ \"./pages/test-sales.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/test-sales\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDVXNlcnMlNUNWaWN0b3IlNUNEZXNrdG9wJTVDTmV3JTIwZm9sZGVyJTVDcGFnZXMlNUN0ZXN0LXNhbGVzLmpzJnBhZ2U9JTJGdGVzdC1zYWxlcyEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyxvREFBdUI7QUFDOUM7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzk5M2EiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi90ZXN0LXNhbGVzXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwiLi9wYWdlcy90ZXN0LXNhbGVzLmpzXCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi90ZXN0LXNhbGVzXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Ctest-sales.js&page=%2Ftest-sales!\n"));

/***/ }),

/***/ "./components/sales/InvoicePrintView.js":
/*!**********************************************!*\
  !*** ./components/sales/InvoicePrintView.js ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst InvoicePrintView = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = (param, ref)=>{\n    let { invoice, customer, company } = param;\n    var _invoice_items;\n    const formatDate = (date)=>{\n        return new Date(date).toLocaleDateString(\"ar-EG\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"ar-EG\", {\n            style: \"currency\",\n            currency: \"EGP\"\n        }).format(amount);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: \"bg-white p-8 max-w-4xl mx-auto\",\n        style: {\n            fontFamily: \"Arial, sans-serif\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b-2 border-blue-600 pb-6 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-blue-600 mb-2\",\n                                    children: (company === null || company === void 0 ? void 0 : company.nameAr) || \"شركة التكنولوجيا المتقدمة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: (company === null || company === void 0 ? void 0 : company.addressAr) || \"شارع التحرير، القاهرة، مصر\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: [\n                                        \"هاتف: \",\n                                        (company === null || company === void 0 ? void 0 : company.phone) || \"+20 ************\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: [\n                                        \"بريد إلكتروني: \",\n                                        (company === null || company === void 0 ? void 0 : company.email) || \"<EMAIL>\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, undefined),\n                                (company === null || company === void 0 ? void 0 : company.taxNumber) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: [\n                                        \"الرقم الضريبي: \",\n                                        company.taxNumber\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                    lineNumber: 32,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-left\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-600 text-white px-4 py-2 rounded-lg inline-block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold\",\n                                        children: \"فاتورة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-100\",\n                                        children: \"INVOICE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-8 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-3 border-b border-gray-300 pb-1\",\n                                children: \"معلومات الفاتورة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"رقم الفاتورة:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 53,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: invoice === null || invoice === void 0 ? void 0 : invoice.invoiceNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 54,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                        lineNumber: 52,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"تاريخ الإصدار:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 57,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: formatDate(invoice === null || invoice === void 0 ? void 0 : invoice.createdAt)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 58,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (invoice === null || invoice === void 0 ? void 0 : invoice.dueDate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"تاريخ الاستحقاق:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: formatDate(invoice.dueDate)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"الحالة:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 67,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold px-2 py-1 rounded text-xs \".concat((invoice === null || invoice === void 0 ? void 0 : invoice.status) === \"PAID\" ? \"bg-green-100 text-green-800\" : (invoice === null || invoice === void 0 ? void 0 : invoice.status) === \"PARTIALLY_PAID\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-red-100 text-red-800\"),\n                                                children: (invoice === null || invoice === void 0 ? void 0 : invoice.status) === \"PAID\" ? \"مدفوعة\" : (invoice === null || invoice === void 0 ? void 0 : invoice.status) === \"PARTIALLY_PAID\" ? \"مدفوعة جزئياً\" : \"غير مدفوعة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 68,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-3 border-b border-gray-300 pb-1\",\n                                children: \"بيانات العميل\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"اسم العميل:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 87,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: (customer === null || customer === void 0 ? void 0 : customer.name) || (invoice === null || invoice === void 0 ? void 0 : invoice.customerName)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 88,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (customer === null || customer === void 0 ? void 0 : customer.email) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"البريد الإلكتروني:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: customer.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (customer === null || customer === void 0 ? void 0 : customer.phone) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"الهاتف:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: customer.phone\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (customer === null || customer === void 0 ? void 0 : customer.address) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"العنوان:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-semibold\",\n                                                children: customer.address\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                        children: \"تفاصيل الفاتورة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"w-full border-collapse border border-gray-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"bg-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-gray-300 px-4 py-3 text-right font-semibold\",\n                                            children: \"#\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-gray-300 px-4 py-3 text-right font-semibold\",\n                                            children: \"الصنف\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                            children: \"الكمية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                            children: \"السعر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                            children: \"الخصم\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                            children: \"الإجمالي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                children: invoice === null || invoice === void 0 ? void 0 : (_invoice_items = invoice.items) === null || _invoice_items === void 0 ? void 0 : _invoice_items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"hover:bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                children: index + 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-gray-300 px-4 py-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: item.productName || item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        item.customizationDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600 mt-1\",\n                                                            children: item.customizationDetails.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: [\n                                                                        \"• \",\n                                                                        detail.optionName,\n                                                                        \": \",\n                                                                        detail.selectedName\n                                                                    ]\n                                                                }, idx, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                                    lineNumber: 136,\n                                                                    columnNumber: 27\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                children: item.quantity\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                children: formatCurrency(item.unitPrice)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                children: item.discount ? \"\".concat(item.discount, \"%\") : \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                                children: formatCurrency(item.total)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-80\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 p-4 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"المجموع الفرعي:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold\",\n                                            children: formatCurrency((invoice === null || invoice === void 0 ? void 0 : invoice.subtotal) || 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"الضريبة (14%):\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold\",\n                                            children: formatCurrency((invoice === null || invoice === void 0 ? void 0 : invoice.taxAmount) || 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-300 pt-2 flex justify-between text-lg font-bold\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"الإجمالي:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-600\",\n                                            children: formatCurrency((invoice === null || invoice === void 0 ? void 0 : invoice.total) || 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, undefined),\n                                (invoice === null || invoice === void 0 ? void 0 : invoice.paidAmount) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between text-green-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"المدفوع:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold\",\n                                                    children: formatCurrency(invoice.paidAmount)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 177,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between text-red-600 font-semibold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"المتبقي:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: formatCurrency(invoice.total - invoice.paidAmount)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, undefined),\n            (invoice === null || invoice === void 0 ? void 0 : invoice.payments) && invoice.payments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-4\",\n                        children: \"تفاصيل المدفوعات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"w-full border-collapse border border-gray-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"bg-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-gray-300 px-4 py-3 text-right font-semibold\",\n                                            children: \"التاريخ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-gray-300 px-4 py-3 text-right font-semibold\",\n                                            children: \"طريقة الدفع\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                            children: \"المبلغ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"border border-gray-300 px-4 py-3 text-right font-semibold\",\n                                            children: \"المرجع\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                children: invoice.payments.map((payment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-gray-300 px-4 py-3\",\n                                                children: formatDate(payment.paidAt)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-gray-300 px-4 py-3\",\n                                                children: payment.method === \"CASH\" ? \"نقدي\" : payment.method === \"INSTAPAY\" ? \"إنستاباي\" : payment.method === \"VISA\" ? \"فيزا\" : payment.method === \"BANK_TRANSFER\" ? \"تحويل بنكي\" : payment.method === \"INSTALLMENT\" ? \"أقساط\" : payment.method\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                                children: formatCurrency(payment.amount)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"border border-gray-300 px-4 py-3\",\n                                                children: payment.reference || \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, undefined),\n            (invoice === null || invoice === void 0 ? void 0 : invoice.notes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800 mb-3\",\n                        children: \"ملاحظات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 p-4 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-700\",\n                            children: invoice.notes\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                lineNumber: 234,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t-2 border-gray-300 pt-6 mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-2\",\n                            children: \"شكراً لتعاملكم معنا\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: \"هذه فاتورة إلكترونية ولا تحتاج إلى توقيع\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-4\",\n                            children: \"تم إنشاء هذه الفاتورة بواسطة نظام إدارة الأعمال\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoicePrintView.js\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = InvoicePrintView;\nInvoicePrintView.displayName = \"InvoicePrintView\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (InvoicePrintView);\nvar _c, _c1;\n$RefreshReg$(_c, \"InvoicePrintView$forwardRef\");\n$RefreshReg$(_c1, \"InvoicePrintView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/sales/InvoicePrintView.js\n"));

/***/ }),

/***/ "./components/sales/PaymentManager.js":
/*!********************************************!*\
  !*** ./components/sales/PaymentManager.js ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PaymentManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BanknotesIcon_BuildingLibraryIcon_CalendarDaysIcon_CreditCardIcon_DevicePhoneMobileIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BanknotesIcon,BuildingLibraryIcon,CalendarDaysIcon,CreditCardIcon,DevicePhoneMobileIcon,PlusIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=BanknotesIcon,BuildingLibraryIcon,CalendarDaysIcon,CreditCardIcon,DevicePhoneMobileIcon,PlusIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\nfunction PaymentManager(param) {\n    let { isOpen, onClose, totalAmount, paidAmount = 0, onPaymentAdd, existingPayments = [] } = param;\n    _s();\n    const [paymentMethod, setPaymentMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"CASH\");\n    const [amount, setAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [reference, setReference] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [installmentPlan, setInstallmentPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        months: 3,\n        monthlyAmount: 0,\n        startDate: new Date().toISOString().split(\"T\")[0]\n    });\n    const [showInstallmentDetails, setShowInstallmentDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const remainingAmount = totalAmount - paidAmount;\n    const paymentMethods = [\n        {\n            id: \"CASH\",\n            name: \"نقدي\",\n            nameEn: \"Cash\",\n            icon: _barrel_optimize_names_BanknotesIcon_BuildingLibraryIcon_CalendarDaysIcon_CreditCardIcon_DevicePhoneMobileIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.BanknotesIcon,\n            color: \"green\",\n            description: \"دفع نقدي فوري\"\n        },\n        {\n            id: \"INSTAPAY\",\n            name: \"إنستاباي\",\n            nameEn: \"InstaPay\",\n            icon: _barrel_optimize_names_BanknotesIcon_BuildingLibraryIcon_CalendarDaysIcon_CreditCardIcon_DevicePhoneMobileIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.DevicePhoneMobileIcon,\n            color: \"blue\",\n            description: \"تحويل فوري عبر إنستاباي\"\n        },\n        {\n            id: \"VODAFONE_CASH\",\n            name: \"فودافون كاش\",\n            nameEn: \"Vodafone Cash\",\n            icon: _barrel_optimize_names_BanknotesIcon_BuildingLibraryIcon_CalendarDaysIcon_CreditCardIcon_DevicePhoneMobileIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.DevicePhoneMobileIcon,\n            color: \"red\",\n            description: \"محفظة فودافون الإلكترونية\"\n        },\n        {\n            id: \"VISA\",\n            name: \"فيزا\",\n            nameEn: \"Visa\",\n            icon: _barrel_optimize_names_BanknotesIcon_BuildingLibraryIcon_CalendarDaysIcon_CreditCardIcon_DevicePhoneMobileIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.CreditCardIcon,\n            color: \"purple\",\n            description: \"بطاقة ائتمان أو خصم\"\n        },\n        {\n            id: \"BANK_TRANSFER\",\n            name: \"تحويل بنكي\",\n            nameEn: \"Bank Transfer\",\n            icon: _barrel_optimize_names_BanknotesIcon_BuildingLibraryIcon_CalendarDaysIcon_CreditCardIcon_DevicePhoneMobileIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.BuildingLibraryIcon,\n            color: \"indigo\",\n            description: \"تحويل مصرفي\"\n        },\n        {\n            id: \"INSTALLMENT\",\n            name: \"أقساط\",\n            nameEn: \"Installments\",\n            icon: _barrel_optimize_names_BanknotesIcon_BuildingLibraryIcon_CalendarDaysIcon_CreditCardIcon_DevicePhoneMobileIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.CalendarDaysIcon,\n            color: \"orange\",\n            description: \"دفع على أقساط شهرية\"\n        }\n    ];\n    const handleAmountChange = (value)=>{\n        const numValue = parseFloat(value) || 0;\n        setAmount(Math.min(numValue, remainingAmount));\n        if (paymentMethod === \"INSTALLMENT\") {\n            const monthlyAmount = numValue / installmentPlan.months;\n            setInstallmentPlan((prev)=>({\n                    ...prev,\n                    monthlyAmount: monthlyAmount\n                }));\n        }\n    };\n    const handleInstallmentMonthsChange = (months)=>{\n        const monthlyAmount = amount / months;\n        setInstallmentPlan((prev)=>({\n                ...prev,\n                months: months,\n                monthlyAmount: monthlyAmount\n            }));\n    };\n    const handlePaymentMethodChange = (method)=>{\n        setPaymentMethod(method);\n        setShowInstallmentDetails(method === \"INSTALLMENT\");\n        if (method === \"INSTALLMENT\") {\n            setAmount(remainingAmount); // Default to full amount for installments\n            const monthlyAmount = remainingAmount / installmentPlan.months;\n            setInstallmentPlan((prev)=>({\n                    ...prev,\n                    monthlyAmount: monthlyAmount\n                }));\n        }\n    };\n    const handleAddPayment = ()=>{\n        if (amount <= 0) return;\n        const payment = {\n            method: paymentMethod,\n            amount: amount,\n            reference: reference,\n            paidAt: new Date().toISOString(),\n            ...paymentMethod === \"INSTALLMENT\" && {\n                installmentPlan: installmentPlan\n            }\n        };\n        onPaymentAdd(payment);\n        // Reset form\n        setAmount(0);\n        setReference(\"\");\n        setPaymentMethod(\"CASH\");\n        setShowInstallmentDetails(false);\n    };\n    const getMethodIcon = (methodId)=>{\n        const method = paymentMethods.find((m)=>m.id === methodId);\n        return (method === null || method === void 0 ? void 0 : method.icon) || _barrel_optimize_names_BanknotesIcon_BuildingLibraryIcon_CalendarDaysIcon_CreditCardIcon_DevicePhoneMobileIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.BanknotesIcon;\n    };\n    const getMethodColor = (methodId)=>{\n        const method = paymentMethods.find((m)=>m.id === methodId);\n        return (method === null || method === void 0 ? void 0 : method.color) || \"gray\";\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: \"إدارة المدفوعات\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BanknotesIcon_BuildingLibraryIcon_CalendarDaysIcon_CreditCardIcon_DevicePhoneMobileIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.XMarkIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"ملخص المدفوعات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 p-4 rounded-lg mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"إجمالي الفاتورة:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: [\n                                                                \"$\",\n                                                                totalAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-green-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"المدفوع:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: [\n                                                                \"$\",\n                                                                paidAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-red-600 font-bold border-t pt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"المتبقي:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"$\",\n                                                                remainingAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    existingPayments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 mb-3\",\n                                                children: \"المدفوعات السابقة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                lineNumber: 192,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: existingPayments.map((payment, index)=>{\n                                                    var _paymentMethods_find;\n                                                    const Icon = getMethodIcon(payment.method);\n                                                    const color = getMethodColor(payment.method);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                        className: \"h-5 w-5 text-\".concat(color, \"-600 mr-3\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                        lineNumber: 201,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium\",\n                                                                                children: (_paymentMethods_find = paymentMethods.find((m)=>m.id === payment.method)) === null || _paymentMethods_find === void 0 ? void 0 : _paymentMethods_find.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                                lineNumber: 203,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            payment.reference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"المرجع: \",\n                                                                                    payment.reference\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                                lineNumber: 207,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                        lineNumber: 202,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-semibold text-green-600\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    parseFloat(payment.amount).toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"إضافة دفعة جديدة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                                children: \"طريقة الدفع\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-3\",\n                                                children: paymentMethods.map((method)=>{\n                                                    const Icon = method.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handlePaymentMethodChange(method.id),\n                                                        className: \"p-3 border-2 rounded-lg text-left transition-colors \".concat(paymentMethod === method.id ? \"border-\".concat(method.color, \"-500 bg-\").concat(method.color, \"-50\") : \"border-gray-200 hover:border-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-5 w-5 mr-2 \".concat(paymentMethod === method.id ? \"text-\".concat(method.color, \"-600\") : \"text-gray-400\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium text-sm\",\n                                                                            children: method.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                            lineNumber: 251,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: method.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                            lineNumber: 252,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, method.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"المبلغ\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: amount,\n                                                        onChange: (e)=>handleAmountChange(e.target.value),\n                                                        className: \"form-input\",\n                                                        placeholder: \"0.00\",\n                                                        min: \"0\",\n                                                        max: remainingAmount,\n                                                        step: \"0.01\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleAmountChange(remainingAmount),\n                                                        className: \"absolute left-2 top-1/2 transform -translate-y-1/2 text-xs text-blue-600 hover:text-blue-800\",\n                                                        children: \"الكل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: [\n                                                    \"الحد الأقصى: $\",\n                                                    remainingAmount.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"المرجع (اختياري)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: reference,\n                                                onChange: (e)=>setReference(e.target.value),\n                                                className: \"form-input\",\n                                                placeholder: \"رقم المرجع أو الإيصال\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    showInstallmentDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 p-4 bg-orange-50 border border-orange-200 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-orange-900 mb-3\",\n                                                children: \"تفاصيل الأقساط\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                lineNumber: 306,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-orange-700 mb-1\",\n                                                                children: \"عدد الأقساط\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: installmentPlan.months,\n                                                                onChange: (e)=>handleInstallmentMonthsChange(parseInt(e.target.value)),\n                                                                className: \"form-input\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: 3,\n                                                                        children: \"3 أشهر\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                        lineNumber: 318,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: 6,\n                                                                        children: \"6 أشهر\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: 12,\n                                                                        children: \"12 شهر\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: 24,\n                                                                        children: \"24 شهر\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-orange-700 mb-1\",\n                                                                children: \"القسط الشهري\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: \"$\".concat(installmentPlan.monthlyAmount.toFixed(2)),\n                                                                className: \"form-input bg-gray-100\",\n                                                                readOnly: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                lineNumber: 308,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-orange-700 mb-1\",\n                                                        children: \"تاريخ بداية الأقساط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: installmentPlan.startDate,\n                                                        onChange: (e)=>setInstallmentPlan((prev)=>({\n                                                                    ...prev,\n                                                                    startDate: e.target.value\n                                                                })),\n                                                        className: \"form-input\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                lineNumber: 338,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAddPayment,\n                                        disabled: amount <= 0,\n                                        className: \"w-full flex items-center justify-center py-3 px-4 rounded-lg font-medium \".concat(amount > 0 ? \"bg-green-600 text-white hover:bg-green-700\" : \"bg-gray-300 text-gray-500 cursor-not-allowed\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BanknotesIcon_BuildingLibraryIcon_CalendarDaysIcon_CreditCardIcon_DevicePhoneMobileIcon_PlusIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.PlusIcon, {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة الدفعة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end space-x-4 p-6 border-t bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"btn-secondary\",\n                        children: \"إغلاق\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\PaymentManager.js\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s(PaymentManager, \"oape6eEDEhWsPeKRnDCVtpjcO9I=\");\n_c = PaymentManager;\nvar _c;\n$RefreshReg$(_c, \"PaymentManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/sales/PaymentManager.js\n"));

/***/ }),

/***/ "./components/sales/ProductCustomizer.js":
/*!***********************************************!*\
  !*** ./components/sales/ProductCustomizer.js ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductCustomizer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CogIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CogIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=CogIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\nfunction ProductCustomizer(param) {\n    let { isOpen, onClose, product, onSave } = param;\n    var _product_basePrice, _product_customizationOptions, _product_basePrice1;\n    _s();\n    const [selectedOptions, setSelectedOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [totalPrice, setTotalPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen && product) {\n            var _product_customizationOptions;\n            // Initialize with default selections\n            const defaultSelections = {};\n            (_product_customizationOptions = product.customizationOptions) === null || _product_customizationOptions === void 0 ? void 0 : _product_customizationOptions.forEach((option)=>{\n                if (option.required && option.options.length > 0) {\n                    defaultSelections[option.id] = option.options[0].id;\n                }\n            });\n            setSelectedOptions(defaultSelections);\n        }\n    }, [\n        isOpen,\n        product\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        calculateTotalPrice();\n    }, [\n        selectedOptions,\n        product\n    ]);\n    const calculateTotalPrice = ()=>{\n        if (!product) return;\n        let total = product.basePrice || 0;\n        Object.entries(selectedOptions).forEach((param)=>{\n            let [optionId, selectedId] = param;\n            var _product_customizationOptions;\n            const option = (_product_customizationOptions = product.customizationOptions) === null || _product_customizationOptions === void 0 ? void 0 : _product_customizationOptions.find((opt)=>opt.id === optionId);\n            const selectedOption = option === null || option === void 0 ? void 0 : option.options.find((opt)=>opt.id === selectedId);\n            if (selectedOption) {\n                total += selectedOption.price;\n            }\n        });\n        setTotalPrice(total);\n    };\n    const handleOptionChange = (optionId, selectedId)=>{\n        setSelectedOptions((prev)=>({\n                ...prev,\n                [optionId]: selectedId\n            }));\n    };\n    const handleSave = ()=>{\n        const customizedProduct = {\n            ...product,\n            customizations: selectedOptions,\n            finalPrice: totalPrice,\n            customizationDetails: getCustomizationDetails()\n        };\n        onSave(customizedProduct);\n        onClose();\n    };\n    const getCustomizationDetails = ()=>{\n        const details = [];\n        Object.entries(selectedOptions).forEach((param)=>{\n            let [optionId, selectedId] = param;\n            var _product_customizationOptions;\n            const option = (_product_customizationOptions = product.customizationOptions) === null || _product_customizationOptions === void 0 ? void 0 : _product_customizationOptions.find((opt)=>opt.id === optionId);\n            const selectedOption = option === null || option === void 0 ? void 0 : option.options.find((opt)=>opt.id === selectedId);\n            if (selectedOption) {\n                details.push({\n                    optionName: option.name,\n                    selectedName: selectedOption.name,\n                    price: selectedOption.price,\n                    componentId: selectedOption.componentId\n                });\n            }\n        });\n        return details;\n    };\n    const isValid = ()=>{\n        if (!(product === null || product === void 0 ? void 0 : product.customizationOptions)) return true;\n        return product.customizationOptions.every((option)=>{\n            if (option.required) {\n                return selectedOptions[option.id];\n            }\n            return true;\n        });\n    };\n    if (!isOpen || !product) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.CogIcon, {\n                                    className: \"h-6 w-6 text-blue-600 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"تخصيص المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: product.nameAr || product.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__.XMarkIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-blue-900 mb-2\",\n                                    children: \"معلومات المنتج الأساسي\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-700\",\n                                                    children: \"الكود:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium mr-2\",\n                                                    children: product.code\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-700\",\n                                                    children: \"السعر الأساسي:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium mr-2\",\n                                                    children: [\n                                                        \"$\",\n                                                        (_product_basePrice = product.basePrice) === null || _product_basePrice === void 0 ? void 0 : _product_basePrice.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    children: \"خيارات التخصيص\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                (_product_customizationOptions = product.customizationOptions) === null || _product_customizationOptions === void 0 ? void 0 : _product_customizationOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: [\n                                                            option.name,\n                                                            option.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 mr-1\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                        children: option.required ? \"مطلوب\" : \"اختياري\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: option.options.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors \".concat(selectedOptions[option.id] === opt.id ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"radio\",\n                                                                        name: option.id,\n                                                                        value: opt.id,\n                                                                        checked: selectedOptions[option.id] === opt.id,\n                                                                        onChange: ()=>handleOptionChange(option.id, opt.id),\n                                                                        className: \"text-blue-600 focus:ring-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                                        lineNumber: 153,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"mr-3 font-medium\",\n                                                                        children: opt.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                                        lineNumber: 161,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-600 font-medium\",\n                                                                    children: [\n                                                                        \"+$\",\n                                                                        opt.price.toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                                    lineNumber: 164,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, opt.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, option.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-gray-900 mb-3\",\n                                    children: \"ملخص السعر\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"السعر الأساسي:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"$\",\n                                                        (_product_basePrice1 = product.basePrice) === null || _product_basePrice1 === void 0 ? void 0 : _product_basePrice1.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        Object.entries(selectedOptions).map((param)=>{\n                                            let [optionId, selectedId] = param;\n                                            var _product_customizationOptions;\n                                            const option = (_product_customizationOptions = product.customizationOptions) === null || _product_customizationOptions === void 0 ? void 0 : _product_customizationOptions.find((opt)=>opt.id === optionId);\n                                            const selectedOption = option === null || option === void 0 ? void 0 : option.options.find((opt)=>opt.id === selectedId);\n                                            if (!selectedOption) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            option.name,\n                                                            \": \",\n                                                            selectedOption.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"+$\",\n                                                            selectedOption.price.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, optionId, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-t pt-2 flex justify-between font-bold text-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"الإجمالي:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600\",\n                                                    children: [\n                                                        \"$\",\n                                                        totalPrice.toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        !isValid() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-700 text-sm\",\n                                children: \"يرجى اختيار جميع الخيارات المطلوبة قبل الحفظ\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end space-x-4 p-6 border-t bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: onClose,\n                            className: \"btn-secondary\",\n                            children: \"إلغاء\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: handleSave,\n                            disabled: !isValid(),\n                            className: \"btn-primary \".concat(!isValid() ? \"opacity-50 cursor-not-allowed\" : \"\"),\n                            children: \"حفظ التخصيص\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\ProductCustomizer.js\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCustomizer, \"uWGbUvpOzP4lzSV65mYIYIr21D8=\");\n_c = ProductCustomizer;\nvar _c;\n$RefreshReg$(_c, \"ProductCustomizer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/sales/ProductCustomizer.js\n"));

/***/ }),

/***/ "./pages/test-sales.js":
/*!*****************************!*\
  !*** ./pages/test-sales.js ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestSalesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _components_sales_ProductCustomizer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/sales/ProductCustomizer */ \"./components/sales/ProductCustomizer.js\");\n/* harmony import */ var _components_sales_PaymentManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/sales/PaymentManager */ \"./components/sales/PaymentManager.js\");\n/* harmony import */ var _components_sales_InvoicePrintView__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/sales/InvoicePrintView */ \"./components/sales/InvoicePrintView.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TestSalesPage() {\n    _s();\n    const [showCustomizer, setShowCustomizer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPaymentManager, setShowPaymentManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showInvoicePreview, setShowInvoicePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Sample customizable product\n    const sampleProduct = {\n        id: \"6\",\n        code: \"CUSTOM001\",\n        name: \"Custom Gaming PC\",\n        nameAr: \"جهاز كمبيوتر ألعاب مخصص\",\n        basePrice: 800.00,\n        isCustomizable: true,\n        customizationOptions: [\n            {\n                id: \"cpu\",\n                name: \"المعالج\",\n                nameEn: \"Processor\",\n                required: true,\n                options: [\n                    {\n                        id: \"cpu1\",\n                        name: \"Intel i5-12400F\",\n                        price: 200,\n                        componentId: \"3\"\n                    },\n                    {\n                        id: \"cpu2\",\n                        name: \"Intel i7-12700F\",\n                        price: 350,\n                        componentId: \"3\"\n                    },\n                    {\n                        id: \"cpu3\",\n                        name: \"AMD Ryzen 5 5600X\",\n                        price: 250,\n                        componentId: \"3\"\n                    }\n                ]\n            },\n            {\n                id: \"ram\",\n                name: \"الذاكرة\",\n                nameEn: \"Memory\",\n                required: true,\n                options: [\n                    {\n                        id: \"ram1\",\n                        name: \"16GB DDR4\",\n                        price: 80,\n                        componentId: \"4\"\n                    },\n                    {\n                        id: \"ram2\",\n                        name: \"32GB DDR4\",\n                        price: 160,\n                        componentId: \"4\"\n                    },\n                    {\n                        id: \"ram3\",\n                        name: \"64GB DDR4\",\n                        price: 320,\n                        componentId: \"4\"\n                    }\n                ]\n            },\n            {\n                id: \"gpu\",\n                name: \"كارت الشاشة\",\n                nameEn: \"Graphics Card\",\n                required: false,\n                options: [\n                    {\n                        id: \"gpu1\",\n                        name: \"RTX 3060\",\n                        price: 400,\n                        componentId: \"2\"\n                    },\n                    {\n                        id: \"gpu2\",\n                        name: \"RTX 3070\",\n                        price: 600,\n                        componentId: \"2\"\n                    },\n                    {\n                        id: \"gpu3\",\n                        name: \"RTX 4080\",\n                        price: 1200,\n                        componentId: \"2\"\n                    }\n                ]\n            }\n        ]\n    };\n    // Sample invoice\n    const sampleInvoice = {\n        id: \"1\",\n        invoiceNumber: \"INV-2024-001\",\n        customerId: \"1\",\n        customerName: \"شركة ABC للتكنولوجيا\",\n        status: \"PARTIALLY_PAID\",\n        createdAt: new Date().toISOString(),\n        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),\n        items: [\n            {\n                id: \"1\",\n                productName: \"جهاز كمبيوتر ألعاب مخصص\",\n                quantity: 1,\n                unitPrice: 1450.00,\n                discount: 5,\n                total: 1377.50,\n                isCustomized: true,\n                customizationDetails: [\n                    {\n                        optionName: \"المعالج\",\n                        selectedName: \"Intel i7-12700F\"\n                    },\n                    {\n                        optionName: \"الذاكرة\",\n                        selectedName: \"32GB DDR4\"\n                    },\n                    {\n                        optionName: \"كارت الشاشة\",\n                        selectedName: \"RTX 3070\"\n                    }\n                ]\n            },\n            {\n                id: \"2\",\n                productName: \"خدمة التركيب والإعداد\",\n                quantity: 1,\n                unitPrice: 100.00,\n                discount: 0,\n                total: 100.00\n            }\n        ],\n        subtotal: 1477.50,\n        taxAmount: 206.85,\n        total: 1684.35,\n        paidAmount: 800.00,\n        remainingAmount: 884.35,\n        payments: [\n            {\n                id: \"1\",\n                method: \"CASH\",\n                amount: 500.00,\n                paidAt: new Date().toISOString(),\n                reference: \"\"\n            },\n            {\n                id: \"2\",\n                method: \"INSTAPAY\",\n                amount: 300.00,\n                paidAt: new Date().toISOString(),\n                reference: \"IP123456789\"\n            }\n        ],\n        notes: \"فاتورة تجريبية لاختبار النظام\"\n    };\n    const sampleCustomer = {\n        id: \"1\",\n        name: \"شركة ABC للتكنولوجيا\",\n        email: \"<EMAIL>\",\n        phone: \"+201234567890\",\n        address: \"شارع التحرير، القاهرة، مصر\"\n    };\n    const handleCustomizedProduct = (customizedProduct)=>{\n        console.log(\"Customized Product:\", customizedProduct);\n        alert(\"تم تخصيص المنتج بنجاح! السعر النهائي: $\".concat(customizedProduct.finalPrice.toFixed(2)));\n    };\n    const handlePaymentAdd = (payment)=>{\n        console.log(\"New Payment:\", payment);\n        alert(\"تم إضافة دفعة بقيمة $\".concat(payment.amount, \" بطريقة \").concat(payment.method));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                children: \"اختبار مكونات نظام المبيعات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-3\",\n                                                children: \"تخصيص المنتجات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"اختبار نظام تخصيص المنتجات مع خيارات متعددة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowCustomizer(true),\n                                                className: \"btn-primary w-full\",\n                                                children: \"فتح مخصص المنتجات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-3\",\n                                                children: \"إدارة المدفوعات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"اختبار نظام الدفع المتقدم مع طرق دفع متعددة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowPaymentManager(true),\n                                                className: \"btn-primary w-full\",\n                                                children: \"فتح إدارة المدفوعات\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-3\",\n                                                children: \"معاينة الفاتورة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"اختبار تصميم الفاتورة المحسن للطباعة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowInvoicePreview(true),\n                                                className: \"btn-primary w-full\",\n                                                children: \"معاينة الفاتورة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                lineNumber: 175,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"البيانات التجريبية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium mb-2\",\n                                                children: \"منتج قابل للتخصيص:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-3 rounded text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"الاسم:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 20\n                                                            }, this),\n                                                            \" \",\n                                                            sampleProduct.nameAr\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"السعر الأساسي:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 20\n                                                            }, this),\n                                                            \" $\",\n                                                            sampleProduct.basePrice\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"خيارات التخصيص:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 20\n                                                            }, this),\n                                                            \" \",\n                                                            sampleProduct.customizationOptions.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                lineNumber: 192,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium mb-2\",\n                                                children: \"فاتورة تجريبية:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-3 rounded text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"رقم الفاتورة:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 20\n                                                            }, this),\n                                                            \" \",\n                                                            sampleInvoice.invoiceNumber\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"الإجمالي:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 20\n                                                            }, this),\n                                                            \" $\",\n                                                            sampleInvoice.total.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"المدفوع:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 20\n                                                            }, this),\n                                                            \" $\",\n                                                            sampleInvoice.paidAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"المتبقي:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 20\n                                                            }, this),\n                                                            \" $\",\n                                                            sampleInvoice.remainingAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    showInvoicePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border-b\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"معاينة الفاتورة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowInvoicePreview(false),\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: \"✕\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_InvoicePrintView__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        invoice: sampleInvoice,\n                                        customer: sampleCustomer\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_ProductCustomizer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showCustomizer,\n                onClose: ()=>setShowCustomizer(false),\n                product: sampleProduct,\n                onSave: handleCustomizedProduct\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_PaymentManager__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showPaymentManager,\n                onClose: ()=>setShowPaymentManager(false),\n                totalAmount: sampleInvoice.total,\n                paidAmount: sampleInvoice.paidAmount,\n                onPaymentAdd: handlePaymentAdd,\n                existingPayments: sampleInvoice.payments\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\test-sales.js\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n_s(TestSalesPage, \"EKkD1o0oN3uiXu1Ow/zHmivbj9E=\");\n_c = TestSalesPage;\nvar _c;\n$RefreshReg$(_c, \"TestSalesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/test-sales.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/BanknotesIcon.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/BanknotesIcon.js ***!
  \***********************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction BanknotesIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"\n    }));\n}\n_c = BanknotesIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(BanknotesIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"BanknotesIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/BanknotesIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/BuildingLibraryIcon.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/BuildingLibraryIcon.js ***!
  \*****************************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction BuildingLibraryIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 21v-8.25M15.75 21v-8.25M8.25 21v-8.25M3 9l9-6 9 6m-1.5 12V10.332A48.36 48.36 0 0 0 12 9.75c-2.551 0-5.056.2-7.5.582V21M3 21h18M12 6.75h.008v.008H12V6.75Z\"\n    }));\n}\n_c = BuildingLibraryIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(BuildingLibraryIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"BuildingLibraryIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/BuildingLibraryIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js ***!
  \**************************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction CalendarDaysIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"\n    }));\n}\n_c = CalendarDaysIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CalendarDaysIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CalendarDaysIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/CogIcon.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CogIcon.js ***!
  \*****************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction CogIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495\"\n    }));\n}\n_c = CogIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CogIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CogIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js":
/*!************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js ***!
  \************************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction CreditCardIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z\"\n    }));\n}\n_c = CreditCardIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CreditCardIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CreditCardIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js ***!
  \*******************************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction DevicePhoneMobileIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3\"\n    }));\n}\n_c = DevicePhoneMobileIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(DevicePhoneMobileIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"DevicePhoneMobileIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js":
/*!******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js ***!
  \******************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction PlusIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 4.5v15m7.5-7.5h-15\"\n    }));\n}\n_c = PlusIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(PlusIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"PlusIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js ***!
  \*******************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction XMarkIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M6 18 18 6M6 6l12 12\"\n    }));\n}\n_c = XMarkIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(XMarkIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"XMarkIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Ctest-sales.js&page=%2Ftest-sales!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);