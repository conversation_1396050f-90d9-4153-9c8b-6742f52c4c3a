import { useState } from 'react';
import { 
  CreditCardIcon, 
  BanknotesIcon, 
  DevicePhoneMobileIcon,
  BuildingLibraryIcon,
  CalendarDaysIcon,
  XMarkIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

export default function PaymentManager({ 
  isOpen, 
  onClose, 
  totalAmount, 
  paidAmount = 0, 
  onPaymentAdd,
  existingPayments = []
}) {
  const [paymentMethod, setPaymentMethod] = useState('CASH');
  const [amount, setAmount] = useState(0);
  const [reference, setReference] = useState('');
  const [installmentPlan, setInstallmentPlan] = useState({
    months: 3,
    monthlyAmount: 0,
    startDate: new Date().toISOString().split('T')[0]
  });
  const [showInstallmentDetails, setShowInstallmentDetails] = useState(false);

  const remainingAmount = totalAmount - paidAmount;

  const paymentMethods = [
    {
      id: 'CASH',
      name: 'نقدي',
      nameEn: 'Cash',
      icon: BanknotesIcon,
      color: 'green',
      description: 'دفع نقدي فوري'
    },
    {
      id: 'INSTAPAY',
      name: 'إنستاباي',
      nameEn: 'InstaPay',
      icon: DevicePhoneMobileIcon,
      color: 'blue',
      description: 'تحويل فوري عبر إنستاباي'
    },
    {
      id: 'VODAFONE_CASH',
      name: 'فودافون كاش',
      nameEn: 'Vodafone Cash',
      icon: DevicePhoneMobileIcon,
      color: 'red',
      description: 'محفظة فودافون الإلكترونية'
    },
    {
      id: 'VISA',
      name: 'فيزا',
      nameEn: 'Visa',
      icon: CreditCardIcon,
      color: 'purple',
      description: 'بطاقة ائتمان أو خصم'
    },
    {
      id: 'BANK_TRANSFER',
      name: 'تحويل بنكي',
      nameEn: 'Bank Transfer',
      icon: BuildingLibraryIcon,
      color: 'indigo',
      description: 'تحويل مصرفي'
    },
    {
      id: 'INSTALLMENT',
      name: 'أقساط',
      nameEn: 'Installments',
      icon: CalendarDaysIcon,
      color: 'orange',
      description: 'دفع على أقساط شهرية'
    }
  ];

  const handleAmountChange = (value) => {
    const numValue = parseFloat(value) || 0;
    setAmount(Math.min(numValue, remainingAmount));
    
    if (paymentMethod === 'INSTALLMENT') {
      const monthlyAmount = numValue / installmentPlan.months;
      setInstallmentPlan(prev => ({
        ...prev,
        monthlyAmount: monthlyAmount
      }));
    }
  };

  const handleInstallmentMonthsChange = (months) => {
    const monthlyAmount = amount / months;
    setInstallmentPlan(prev => ({
      ...prev,
      months: months,
      monthlyAmount: monthlyAmount
    }));
  };

  const handlePaymentMethodChange = (method) => {
    setPaymentMethod(method);
    setShowInstallmentDetails(method === 'INSTALLMENT');
    
    if (method === 'INSTALLMENT') {
      setAmount(remainingAmount); // Default to full amount for installments
      const monthlyAmount = remainingAmount / installmentPlan.months;
      setInstallmentPlan(prev => ({
        ...prev,
        monthlyAmount: monthlyAmount
      }));
    }
  };

  const handleAddPayment = () => {
    if (amount <= 0) return;

    const payment = {
      method: paymentMethod,
      amount: amount,
      reference: reference,
      paidAt: new Date().toISOString(),
      ...(paymentMethod === 'INSTALLMENT' && {
        installmentPlan: installmentPlan
      })
    };

    onPaymentAdd(payment);
    
    // Reset form
    setAmount(0);
    setReference('');
    setPaymentMethod('CASH');
    setShowInstallmentDetails(false);
  };

  const getMethodIcon = (methodId) => {
    const method = paymentMethods.find(m => m.id === methodId);
    return method?.icon || BanknotesIcon;
  };

  const getMethodColor = (methodId) => {
    const method = paymentMethods.find(m => m.id === methodId);
    return method?.color || 'gray';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">إدارة المدفوعات</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Payment Summary */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">ملخص المدفوعات</h3>
              
              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>إجمالي الفاتورة:</span>
                    <span className="font-semibold">${totalAmount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-green-600">
                    <span>المدفوع:</span>
                    <span className="font-semibold">${paidAmount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-red-600 font-bold border-t pt-2">
                    <span>المتبقي:</span>
                    <span>${remainingAmount.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {/* Existing Payments */}
              {existingPayments.length > 0 && (
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">المدفوعات السابقة</h4>
                  <div className="space-y-2">
                    {existingPayments.map((payment, index) => {
                      const Icon = getMethodIcon(payment.method);
                      const color = getMethodColor(payment.method);
                      
                      return (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center">
                            <Icon className={`h-5 w-5 text-${color}-600 mr-3`} />
                            <div>
                              <span className="font-medium">
                                {paymentMethods.find(m => m.id === payment.method)?.name}
                              </span>
                              {payment.reference && (
                                <p className="text-xs text-gray-500">المرجع: {payment.reference}</p>
                              )}
                            </div>
                          </div>
                          <span className="font-semibold text-green-600">
                            ${parseFloat(payment.amount).toFixed(2)}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>

            {/* Add New Payment */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">إضافة دفعة جديدة</h3>
              
              {/* Payment Methods */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  طريقة الدفع
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {paymentMethods.map((method) => {
                    const Icon = method.icon;
                    return (
                      <button
                        key={method.id}
                        onClick={() => handlePaymentMethodChange(method.id)}
                        className={`p-3 border-2 rounded-lg text-left transition-colors ${
                          paymentMethod === method.id
                            ? `border-${method.color}-500 bg-${method.color}-50`
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center">
                          <Icon className={`h-5 w-5 mr-2 ${
                            paymentMethod === method.id 
                              ? `text-${method.color}-600` 
                              : 'text-gray-400'
                          }`} />
                          <div>
                            <p className="font-medium text-sm">{method.name}</p>
                            <p className="text-xs text-gray-500">{method.description}</p>
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Amount */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المبلغ
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => handleAmountChange(e.target.value)}
                    className="form-input"
                    placeholder="0.00"
                    min="0"
                    max={remainingAmount}
                    step="0.01"
                  />
                  <button
                    onClick={() => handleAmountChange(remainingAmount)}
                    className="absolute left-2 top-1/2 transform -translate-y-1/2 text-xs text-blue-600 hover:text-blue-800"
                  >
                    الكل
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  الحد الأقصى: ${remainingAmount.toFixed(2)}
                </p>
              </div>

              {/* Reference */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المرجع (اختياري)
                </label>
                <input
                  type="text"
                  value={reference}
                  onChange={(e) => setReference(e.target.value)}
                  className="form-input"
                  placeholder="رقم المرجع أو الإيصال"
                />
              </div>

              {/* Installment Details */}
              {showInstallmentDetails && (
                <div className="mb-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                  <h4 className="font-medium text-orange-900 mb-3">تفاصيل الأقساط</h4>
                  
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-orange-700 mb-1">
                        عدد الأقساط
                      </label>
                      <select
                        value={installmentPlan.months}
                        onChange={(e) => handleInstallmentMonthsChange(parseInt(e.target.value))}
                        className="form-input"
                      >
                        <option value={3}>3 أشهر</option>
                        <option value={6}>6 أشهر</option>
                        <option value={12}>12 شهر</option>
                        <option value={24}>24 شهر</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-orange-700 mb-1">
                        القسط الشهري
                      </label>
                      <input
                        type="text"
                        value={`$${installmentPlan.monthlyAmount.toFixed(2)}`}
                        className="form-input bg-gray-100"
                        readOnly
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-orange-700 mb-1">
                      تاريخ بداية الأقساط
                    </label>
                    <input
                      type="date"
                      value={installmentPlan.startDate}
                      onChange={(e) => setInstallmentPlan(prev => ({
                        ...prev,
                        startDate: e.target.value
                      }))}
                      className="form-input"
                    />
                  </div>
                </div>
              )}

              {/* Add Payment Button */}
              <button
                onClick={handleAddPayment}
                disabled={amount <= 0}
                className={`w-full flex items-center justify-center py-3 px-4 rounded-lg font-medium ${
                  amount > 0
                    ? 'bg-green-600 text-white hover:bg-green-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                <PlusIcon className="h-5 w-5 mr-2" />
                إضافة الدفعة
              </button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-4 p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            إغلاق
          </button>
        </div>
      </div>
    </div>
  );
}
