import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { useQuery } from 'react-query';
import axios from 'axios';
import {
  XMarkIcon,
  CurrencyDollarIcon,
  ArrowsRightLeftIcon,
} from '@heroicons/react/24/outline';

// Add Transaction Modal
export function AddTransactionModal({ isOpen, onClose, onSubmit }) {
  const { t } = useTranslation('common');
  const [formData, setFormData] = useState({
    type: 'INCOME',
    amount: '',
    description: '',
    descriptionAr: '',
    paymentMethod: 'CASH',
    reference: '',
    branchId: '',
  });

  const [errors, setErrors] = useState({});

  // Fetch branches
  const { data: branchesData } = useQuery('branches', async () => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/branches`);
    return response.data;
  });

  const branches = branchesData?.branches || [];

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrors({});

    // Validation
    const newErrors = {};
    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Amount is required and must be greater than 0';
    }
    if (!formData.description) newErrors.description = 'Description is required';
    if (!formData.branchId) newErrors.branchId = 'Branch is required';

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    onSubmit({
      ...formData,
      amount: parseFloat(formData.amount),
    });
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const resetForm = () => {
    setFormData({
      type: 'INCOME',
      amount: '',
      description: '',
      descriptionAr: '',
      paymentMethod: 'CASH',
      reference: '',
      branchId: '',
    });
    setErrors({});
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Add Transaction
                </h3>
                <button
                  type="button"
                  onClick={() => {
                    resetForm();
                    onClose();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="form-label">Transaction Type *</label>
                  <select
                    name="type"
                    value={formData.type}
                    onChange={handleChange}
                    className="form-input"
                  >
                    <option value="INCOME">Income</option>
                    <option value="EXPENSE">Expense</option>
                  </select>
                </div>

                <div>
                  <label className="form-label">Amount *</label>
                  <input
                    type="number"
                    step="0.01"
                    name="amount"
                    value={formData.amount}
                    onChange={handleChange}
                    className={`form-input ${errors.amount ? 'border-red-500' : ''}`}
                    placeholder="0.00"
                  />
                  {errors.amount && <p className="form-error">{errors.amount}</p>}
                </div>

                <div>
                  <label className="form-label">Branch *</label>
                  <select
                    name="branchId"
                    value={formData.branchId}
                    onChange={handleChange}
                    className={`form-input ${errors.branchId ? 'border-red-500' : ''}`}
                  >
                    <option value="">Select Branch</option>
                    {branches.map((branch) => (
                      <option key={branch.id} value={branch.id}>
                        {branch.name}
                      </option>
                    ))}
                  </select>
                  {errors.branchId && <p className="form-error">{errors.branchId}</p>}
                </div>

                <div>
                  <label className="form-label">Payment Method</label>
                  <select
                    name="paymentMethod"
                    value={formData.paymentMethod}
                    onChange={handleChange}
                    className="form-input"
                  >
                    <option value="CASH">Cash</option>
                    <option value="INSTAPAY">InstaPay</option>
                    <option value="VODAFONE_CASH">Vodafone Cash</option>
                    <option value="VISA">Visa</option>
                    <option value="BANK_TRANSFER">Bank Transfer</option>
                  </select>
                </div>

                <div>
                  <label className="form-label">Description (English) *</label>
                  <input
                    type="text"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    className={`form-input ${errors.description ? 'border-red-500' : ''}`}
                    placeholder="Enter description"
                  />
                  {errors.description && <p className="form-error">{errors.description}</p>}
                </div>

                <div>
                  <label className="form-label">Description (Arabic)</label>
                  <input
                    type="text"
                    name="descriptionAr"
                    value={formData.descriptionAr}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="أدخل الوصف"
                    dir="rtl"
                  />
                </div>

                <div>
                  <label className="form-label">Reference</label>
                  <input
                    type="text"
                    name="reference"
                    value={formData.reference}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="Reference number or note"
                  />
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                className="btn-primary w-full sm:w-auto sm:ml-3"
              >
                Add Transaction
              </button>
              <button
                type="button"
                onClick={() => {
                  resetForm();
                  onClose();
                }}
                className="btn-secondary w-full sm:w-auto mt-3 sm:mt-0"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

// Transfer Funds Modal
export function TransferFundsModal({ isOpen, onClose, onSubmit }) {
  const { t } = useTranslation('common');
  const [formData, setFormData] = useState({
    fromBranchId: '',
    toBranchId: '',
    amount: '',
    description: '',
    descriptionAr: '',
  });

  const [errors, setErrors] = useState({});

  // Fetch branches
  const { data: branchesData } = useQuery('branches', async () => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/branches`);
    return response.data;
  });

  const branches = branchesData?.branches || [];

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrors({});

    // Validation
    const newErrors = {};
    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Amount is required and must be greater than 0';
    }
    if (!formData.fromBranchId) newErrors.fromBranchId = 'From branch is required';
    if (!formData.toBranchId) newErrors.toBranchId = 'To branch is required';
    if (formData.fromBranchId === formData.toBranchId) {
      newErrors.toBranchId = 'To branch must be different from from branch';
    }
    if (!formData.description) newErrors.description = 'Description is required';

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    onSubmit({
      ...formData,
      amount: parseFloat(formData.amount),
    });
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const resetForm = () => {
    setFormData({
      fromBranchId: '',
      toBranchId: '',
      amount: '',
      description: '',
      descriptionAr: '',
    });
    setErrors({});
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Transfer Funds
                </h3>
                <button
                  type="button"
                  onClick={() => {
                    resetForm();
                    onClose();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="form-label">From Branch *</label>
                  <select
                    name="fromBranchId"
                    value={formData.fromBranchId}
                    onChange={handleChange}
                    className={`form-input ${errors.fromBranchId ? 'border-red-500' : ''}`}
                  >
                    <option value="">Select From Branch</option>
                    {branches.map((branch) => (
                      <option key={branch.id} value={branch.id}>
                        {branch.name}
                      </option>
                    ))}
                  </select>
                  {errors.fromBranchId && <p className="form-error">{errors.fromBranchId}</p>}
                </div>

                <div>
                  <label className="form-label">To Branch *</label>
                  <select
                    name="toBranchId"
                    value={formData.toBranchId}
                    onChange={handleChange}
                    className={`form-input ${errors.toBranchId ? 'border-red-500' : ''}`}
                  >
                    <option value="">Select To Branch</option>
                    {branches.filter(branch => branch.id !== formData.fromBranchId).map((branch) => (
                      <option key={branch.id} value={branch.id}>
                        {branch.name}
                      </option>
                    ))}
                  </select>
                  {errors.toBranchId && <p className="form-error">{errors.toBranchId}</p>}
                </div>

                <div>
                  <label className="form-label">Amount *</label>
                  <input
                    type="number"
                    step="0.01"
                    name="amount"
                    value={formData.amount}
                    onChange={handleChange}
                    className={`form-input ${errors.amount ? 'border-red-500' : ''}`}
                    placeholder="0.00"
                  />
                  {errors.amount && <p className="form-error">{errors.amount}</p>}
                </div>

                <div>
                  <label className="form-label">Description (English) *</label>
                  <input
                    type="text"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    className={`form-input ${errors.description ? 'border-red-500' : ''}`}
                    placeholder="Enter transfer description"
                  />
                  {errors.description && <p className="form-error">{errors.description}</p>}
                </div>

                <div>
                  <label className="form-label">Description (Arabic)</label>
                  <input
                    type="text"
                    name="descriptionAr"
                    value={formData.descriptionAr}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="أدخل وصف التحويل"
                    dir="rtl"
                  />
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                className="btn-primary w-full sm:w-auto sm:ml-3"
              >
                Transfer Funds
              </button>
              <button
                type="button"
                onClick={() => {
                  resetForm();
                  onClose();
                }}
                className="btn-secondary w-full sm:w-auto mt-3 sm:mt-0"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
