import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  ShoppingCartIcon, 
  ComputerDesktopIcon, 
  DevicePhoneMobileIcon,
  WrenchScrewdriverIcon,
  DocumentTextIcon,
  ArrowPathIcon,
  UserIcon,
  PhoneIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  MinusIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

export default function OrganizedStorePOS({
  cart,
  customer,
  products,
  customers,
  categories,
  dailySummary,
  saleType,
  setSaleType,
  customerSearch,
  setCustomerSearch,
  productSearch,
  setProductSearch,
  selectedCategory,
  setSelectedCategory,
  searchCustomers,
  selectCustomer,
  clearCustomer,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
  calculateTotals,
  onShowPayment,
  onShowCustomerModal,
  onShowComputerBuilder
}) {
  const { t } = useTranslation();
  const [activeSection, setActiveSection] = useState('products'); // products, cart, customer

  const totals = calculateTotals();

  // Organize products by type
  const organizedProducts = {
    desktops: products.filter(p => p.categoryId === 'computers' && p.name.toLowerCase().includes('desktop')),
    laptops: products.filter(p => p.categoryId === 'laptops'),
    components: products.filter(p => ['cpu', 'gpu', 'ram', 'storage', 'motherboard'].includes(p.categoryId)),
    accessories: products.filter(p => ['accessories', 'peripherals'].includes(p.categoryId)),
    services: products.filter(p => p.productType === 'SERVICE')
  };

  const saleTypes = [
    {
      id: 'DIRECT',
      name: 'بيع مباشر',
      icon: ShoppingCartIcon,
      color: 'blue',
      description: 'للعملاء الموجودين - دفع كامل'
    },
    {
      id: 'CUSTOM_BUILD',
      name: 'تجميع جهاز',
      icon: WrenchScrewdriverIcon,
      color: 'green',
      description: 'تجميع كمبيوتر أو ترقية لابتوب'
    },
    {
      id: 'CUSTOM_ORDER',
      name: 'طلب مخصص',
      icon: ComputerDesktopIcon,
      color: 'purple',
      description: 'طلبات خاصة - دفعة مقدمة'
    },
    {
      id: 'QUOTE',
      name: 'عرض سعر',
      icon: DocumentTextIcon,
      color: 'yellow',
      description: 'استفسار أسعار - بدون دفع'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">💻 متجر الكمبيوتر</h1>
              
              {/* Sale Type Selector */}
              <div className="flex space-x-2">
                {saleTypes.map(type => {
                  const Icon = type.icon;
                  return (
                    <button
                      key={type.id}
                      onClick={() => setSaleType(type.id)}
                      className={`px-3 py-2 rounded-lg font-medium text-sm transition-colors ${
                        saleType === type.id 
                          ? `bg-${type.color}-600 text-white` 
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                      title={type.description}
                    >
                      <Icon className="h-4 w-4 inline ml-1" />
                      {type.name}
                    </button>
                  );
                })}
              </div>
            </div>
            
            {/* Daily Summary */}
            <div className="flex items-center space-x-6 text-sm">
              <div className="text-gray-600">
                📊 اليوم: <span className="font-bold text-green-600">
                  ${dailySummary?.invoices?.total?.toFixed(2) || '0.00'}
                </span>
              </div>
              <div className="text-gray-600">
                💰 الصندوق: <span className="font-bold text-blue-600">
                  ${dailySummary?.cashBox?.balance?.toFixed(2) || '0.00'}
                </span>
              </div>
              <button
                onClick={() => window.location.href = '/sales'}
                className="px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200"
              >
                <ArrowPathIcon className="h-4 w-4 inline ml-1" />
                مرتجعات
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          
          {/* Left Panel - Customer & Products */}
          <div className="lg:col-span-3 space-y-6">
            
            {/* Customer Section */}
            <div className="bg-white rounded-lg shadow p-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  <UserIcon className="h-5 w-5 ml-2 text-blue-600" />
                  معلومات العميل
                </h2>
                {customer && (
                  <button
                    onClick={clearCustomer}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    مسح العميل
                  </button>
                )}
              </div>

              {customer ? (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-green-900">{customer.nameAr || customer.name}</div>
                      <div className="text-sm text-green-700 flex items-center">
                        <PhoneIcon className="h-4 w-4 ml-1" />
                        {customer.phone}
                      </div>
                      <div className="text-xs text-green-600 mt-1">
                        النوع: {customer.type} | الرصيد: ${parseFloat(customer.balance || 0).toFixed(2)}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-green-700">حد الائتمان</div>
                      <div className="font-bold text-green-900">${parseFloat(customer.creditLimit || 0).toFixed(2)}</div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="relative">
                  <input
                    type="text"
                    value={customerSearch}
                    onChange={(e) => setCustomerSearch(e.target.value)}
                    placeholder="ابحث بالهاتف أو الاسم... (اختياري للبيع النقدي)"
                    className="form-input pl-10"
                  />
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-3" />
                  
                  {/* Customer Search Results */}
                  {customerSearch.length >= 3 && (
                    <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                      {searchCustomers(customerSearch).map(c => (
                        <button
                          key={c.id}
                          onClick={() => selectCustomer(c)}
                          className="w-full text-right px-4 py-3 hover:bg-gray-50 border-b border-gray-100"
                        >
                          <div className="font-medium">{c.nameAr || c.name}</div>
                          <div className="text-sm text-gray-600">{c.phone}</div>
                          <div className="text-xs text-gray-500">{c.type}</div>
                        </button>
                      ))}
                      <button
                        onClick={onShowCustomerModal}
                        className="w-full text-right px-4 py-3 hover:bg-blue-50 text-blue-600 font-medium"
                      >
                        <PlusIcon className="h-4 w-4 inline ml-1" />
                        إضافة عميل جديد
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Products Section */}
            <div className="bg-white rounded-lg shadow">
              <div className="p-4 border-b">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-900">المنتجات والخدمات</h2>
                  
                  {/* Special Actions */}
                  <div className="flex space-x-2">
                    <button
                      onClick={() => onShowComputerBuilder('DESKTOP')}
                      className="btn-secondary text-sm flex items-center"
                    >
                      <ComputerDesktopIcon className="h-4 w-4 ml-1" />
                      تجميع كمبيوتر
                    </button>
                    <button
                      onClick={() => onShowComputerBuilder('LAPTOP')}
                      className="btn-secondary text-sm flex items-center"
                    >
                      <DevicePhoneMobileIcon className="h-4 w-4 ml-1" />
                      ترقية لابتوب
                    </button>
                  </div>
                </div>

                {/* Product Search */}
                <div className="relative">
                  <input
                    type="text"
                    value={productSearch}
                    onChange={(e) => setProductSearch(e.target.value)}
                    placeholder="ابحث عن منتج..."
                    className="form-input pl-10"
                  />
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-3" />
                </div>
              </div>

              {/* Product Categories */}
              <div className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  
                  {/* Desktop Computers */}
                  <div className="border border-gray-200 rounded-lg p-3">
                    <h3 className="font-medium text-gray-900 mb-3 flex items-center">
                      <ComputerDesktopIcon className="h-4 w-4 ml-1 text-blue-600" />
                      أجهزة كمبيوتر مكتبية
                    </h3>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {organizedProducts.desktops.map(product => (
                        <button
                          key={product.id}
                          onClick={() => addToCart(product)}
                          className="w-full text-right p-2 hover:bg-gray-50 rounded border text-sm"
                        >
                          <div className="font-medium">{product.nameAr || product.name}</div>
                          <div className="text-green-600">${parseFloat(product.unitPrice || 0).toFixed(2)}</div>
                          <div className="text-xs text-gray-500">المخزون: {product.currentStock || 0}</div>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Laptops */}
                  <div className="border border-gray-200 rounded-lg p-3">
                    <h3 className="font-medium text-gray-900 mb-3 flex items-center">
                      <DevicePhoneMobileIcon className="h-4 w-4 ml-1 text-green-600" />
                      أجهزة لابتوب
                    </h3>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {organizedProducts.laptops.map(product => (
                        <button
                          key={product.id}
                          onClick={() => addToCart(product)}
                          className="w-full text-right p-2 hover:bg-gray-50 rounded border text-sm"
                        >
                          <div className="font-medium">{product.nameAr || product.name}</div>
                          <div className="text-green-600">${parseFloat(product.unitPrice || 0).toFixed(2)}</div>
                          <div className="text-xs text-gray-500">المخزون: {product.currentStock || 0}</div>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Components */}
                  <div className="border border-gray-200 rounded-lg p-3">
                    <h3 className="font-medium text-gray-900 mb-3 flex items-center">
                      <WrenchScrewdriverIcon className="h-4 w-4 ml-1 text-purple-600" />
                      قطع غيار ومكونات
                    </h3>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {organizedProducts.components.map(product => (
                        <button
                          key={product.id}
                          onClick={() => addToCart(product)}
                          className="w-full text-right p-2 hover:bg-gray-50 rounded border text-sm"
                        >
                          <div className="font-medium">{product.nameAr || product.name}</div>
                          <div className="text-green-600">${parseFloat(product.unitPrice || 0).toFixed(2)}</div>
                          <div className="text-xs text-gray-500">المخزون: {product.currentStock || 0}</div>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Accessories */}
                  <div className="border border-gray-200 rounded-lg p-3">
                    <h3 className="font-medium text-gray-900 mb-3">إكسسوارات</h3>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {organizedProducts.accessories.map(product => (
                        <button
                          key={product.id}
                          onClick={() => addToCart(product)}
                          className="w-full text-right p-2 hover:bg-gray-50 rounded border text-sm"
                        >
                          <div className="font-medium">{product.nameAr || product.name}</div>
                          <div className="text-green-600">${parseFloat(product.unitPrice || 0).toFixed(2)}</div>
                          <div className="text-xs text-gray-500">المخزون: {product.currentStock || 0}</div>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Services */}
                  <div className="border border-gray-200 rounded-lg p-3">
                    <h3 className="font-medium text-gray-900 mb-3">خدمات</h3>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {organizedProducts.services.map(product => (
                        <button
                          key={product.id}
                          onClick={() => addToCart(product)}
                          className="w-full text-right p-2 hover:bg-gray-50 rounded border text-sm"
                        >
                          <div className="font-medium">{product.nameAr || product.name}</div>
                          <div className="text-green-600">${parseFloat(product.unitPrice || 0).toFixed(2)}</div>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel - Cart */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  <ShoppingCartIcon className="h-5 w-5 ml-2 text-blue-600" />
                  السلة ({totals.itemCount})
                </h2>
                {cart.length > 0 && (
                  <button
                    onClick={clearCart}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    مسح الكل
                  </button>
                )}
              </div>
            </div>

            {/* Cart Items */}
            <div className="p-4 space-y-3 max-h-96 overflow-y-auto">
              {cart.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  <ShoppingCartIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                  <p>السلة فارغة</p>
                  <p className="text-sm">اضغط على المنتجات لإضافتها</p>
                </div>
              ) : (
                cart.map(item => (
                  <div key={item.id} className="border border-gray-200 rounded-lg p-3">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{item.productName}</h4>
                        <p className="text-xs text-gray-600">{item.productCode}</p>
                        {item.buildDetails && (
                          <div className="text-xs text-green-600 mt-1">
                            ✓ {item.buildDetails.buildType === 'DESKTOP' ? 'تجميعة كمبيوتر' : 'ترقية لابتوب'}
                          </div>
                        )}
                      </div>
                      <button
                        onClick={() => removeFromCart(item.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-2 text-sm">
                      <div>
                        <label className="text-xs text-gray-600">الكمية</label>
                        <div className="flex items-center">
                          <button
                            onClick={() => updateCartItem(item.id, 'quantity', Math.max(1, item.quantity - 1))}
                            className="w-6 h-6 bg-gray-200 rounded text-xs hover:bg-gray-300"
                          >
                            <MinusIcon className="h-3 w-3 mx-auto" />
                          </button>
                          <span className="mx-2 w-8 text-center">{item.quantity}</span>
                          <button
                            onClick={() => updateCartItem(item.id, 'quantity', item.quantity + 1)}
                            className="w-6 h-6 bg-gray-200 rounded text-xs hover:bg-gray-300"
                          >
                            <PlusIcon className="h-3 w-3 mx-auto" />
                          </button>
                        </div>
                      </div>
                      
                      <div>
                        <label className="text-xs text-gray-600">السعر</label>
                        <input
                          type="number"
                          value={item.unitPrice}
                          onChange={(e) => updateCartItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                          className="form-input text-sm h-8"
                          step="0.01"
                        />
                      </div>
                      
                      <div>
                        <label className="text-xs text-gray-600">خصم %</label>
                        <input
                          type="number"
                          value={item.discount || 0}
                          onChange={(e) => updateCartItem(item.id, 'discount', parseFloat(e.target.value) || 0)}
                          className="form-input text-sm h-8"
                          min="0"
                          max="100"
                        />
                      </div>
                    </div>
                    
                    <div className="mt-2 flex justify-between items-center">
                      <label className="flex items-center text-xs">
                        <input
                          type="checkbox"
                          checked={item.hasTax || false}
                          onChange={(e) => updateCartItem(item.id, 'hasTax', e.target.checked)}
                          className="ml-1"
                        />
                        ضريبة {item.taxRate || 14}%
                      </label>
                      <span className="font-bold text-green-600">
                        ${item.total.toFixed(2)}
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Cart Summary */}
            {cart.length > 0 && (
              <div className="p-4 border-t border-gray-200">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>المجموع الفرعي:</span>
                    <span>${totals.subtotal.toFixed(2)}</span>
                  </div>
                  {totals.totalDiscount > 0 && (
                    <div className="flex justify-between text-red-600">
                      <span>الخصم:</span>
                      <span>-${totals.totalDiscount.toFixed(2)}</span>
                    </div>
                  )}
                  {totals.totalTax > 0 && (
                    <div className="flex justify-between text-blue-600">
                      <span>الضريبة:</span>
                      <span>+${totals.totalTax.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="flex justify-between font-bold text-lg border-t pt-2">
                    <span>الإجمالي:</span>
                    <span className="text-green-600">${totals.total.toFixed(2)}</span>
                  </div>
                </div>

                {/* Payment Button */}
                <button
                  onClick={onShowPayment}
                  className={`w-full mt-4 py-3 rounded-lg font-medium transition-colors ${
                    saleType === 'DIRECT' 
                      ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                      : saleType === 'CUSTOM_BUILD'
                      ? 'bg-green-600 hover:bg-green-700 text-white'
                      : saleType === 'CUSTOM_ORDER'
                      ? 'bg-purple-600 hover:bg-purple-700 text-white'
                      : 'bg-yellow-600 hover:bg-yellow-700 text-white'
                  }`}
                  disabled={cart.length === 0}
                >
                  {saleType === 'DIRECT' ? '💳 الدفع والإنهاء' : 
                   saleType === 'CUSTOM_BUILD' ? '🔧 حفظ التجميعة' :
                   saleType === 'CUSTOM_ORDER' ? '📋 إنشاء الطلب' : 
                   '📄 إنشاء عرض السعر'}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
