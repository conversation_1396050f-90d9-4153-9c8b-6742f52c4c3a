"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "./components/sales/QuoteModal.js":
/*!****************************************!*\
  !*** ./components/sales/QuoteModal.js ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ QuoteModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CogIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=CogIcon,PlusIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _ProductCustomizerAdvanced__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ProductCustomizerAdvanced */ \"./components/sales/ProductCustomizerAdvanced.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction QuoteModal(param) {\n    let { isOpen, onClose, onSave, quote = null } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: \"\",\n        validUntil: \"\",\n        notes: \"\",\n        items: []\n    });\n    // Customization states\n    const [showCustomizer, setShowCustomizer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load customers and products\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            loadCustomers();\n            loadProducts();\n            if (quote) {\n                setFormData({\n                    customerId: quote.customerId || \"\",\n                    validUntil: quote.validUntil ? quote.validUntil.split(\"T\")[0] : \"\",\n                    notes: quote.notes || \"\",\n                    items: quote.items || []\n                });\n            } else {\n                // Set default valid until date (7 days from now)\n                const validUntil = new Date();\n                validUntil.setDate(validUntil.getDate() + 7);\n                setFormData((prev)=>({\n                        ...prev,\n                        validUntil: validUntil.toISOString().split(\"T\")[0]\n                    }));\n            }\n        }\n    }, [\n        isOpen,\n        quote\n    ]);\n    const loadCustomers = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/customers\"));\n            setCustomers(response.data.customers || []);\n        } catch (error) {\n            console.error(\"Error loading customers:\", error);\n        }\n    };\n    const loadProducts = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/products\"));\n            setProducts(response.data.products || []);\n        } catch (error) {\n            console.error(\"Error loading products:\", error);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const addItem = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: [\n                    ...prev.items,\n                    {\n                        productId: \"\",\n                        productName: \"\",\n                        quantity: 1,\n                        unitPrice: 0,\n                        discount: 0,\n                        taxRate: 14,\n                        hasTax: true,\n                        total: 0,\n                        isCustomized: false,\n                        customizations: null,\n                        customizationDetails: []\n                    }\n                ]\n            }));\n    };\n    const removeItem = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: prev.items.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateItem = (index, field, value)=>{\n        setFormData((prev)=>{\n            const newItems = [\n                ...prev.items\n            ];\n            newItems[index] = {\n                ...newItems[index],\n                [field]: value\n            };\n            if (field === \"productId\") {\n                const product = products.find((p)=>p.id === value);\n                if (product) {\n                    newItems[index].unitPrice = parseFloat(product.unitPrice);\n                    newItems[index].productName = product.nameAr || product.name;\n                    // Check if product is customizable\n                    if (product.isCustomizable) {\n                        setSelectedProduct(product);\n                        setCurrentItemIndex(index);\n                        setShowCustomizer(true);\n                        return prev; // Don't update yet, wait for customization\n                    }\n                }\n            }\n            // Recalculate totals for any change\n            if ([\n                \"quantity\",\n                \"unitPrice\",\n                \"discount\",\n                \"taxRate\",\n                \"hasTax\"\n            ].includes(field)) {\n                const item = newItems[index];\n                const quantity = parseFloat(item.quantity) || 0;\n                const unitPrice = parseFloat(item.unitPrice) || 0;\n                const discountPercent = parseFloat(item.discount) || 0;\n                const taxRate = parseFloat(item.taxRate) || 0;\n                // Calculate subtotal\n                const subtotal = quantity * unitPrice;\n                // Apply discount\n                const discountAmount = subtotal * (discountPercent / 100);\n                const afterDiscount = subtotal - discountAmount;\n                // Apply tax if enabled\n                const taxAmount = item.hasTax ? afterDiscount * (taxRate / 100) : 0;\n                const total = afterDiscount + taxAmount;\n                newItems[index].total = total;\n                newItems[index].subtotal = subtotal;\n                newItems[index].discountAmount = discountAmount;\n                newItems[index].taxAmount = taxAmount;\n            }\n            return {\n                ...prev,\n                items: newItems\n            };\n        });\n    };\n    // Handle customization save\n    const handleCustomizationSave = (customizationData)=>{\n        if (currentItemIndex !== null) {\n            setFormData((prev)=>{\n                const newItems = [\n                    ...prev.items\n                ];\n                newItems[currentItemIndex] = {\n                    ...newItems[currentItemIndex],\n                    unitPrice: customizationData.totalPrice,\n                    isCustomized: true,\n                    customizations: customizationData.customizations,\n                    customizationDetails: customizationData.customizationDetails\n                };\n                // Recalculate total\n                const item = newItems[currentItemIndex];\n                const quantity = parseFloat(item.quantity) || 0;\n                const unitPrice = parseFloat(item.unitPrice) || 0;\n                const discountPercent = parseFloat(item.discount) || 0;\n                const taxRate = parseFloat(item.taxRate) || 0;\n                const subtotal = quantity * unitPrice;\n                const discountAmount = subtotal * (discountPercent / 100);\n                const afterDiscount = subtotal - discountAmount;\n                const taxAmount = item.hasTax ? afterDiscount * (taxRate / 100) : 0;\n                const total = afterDiscount + taxAmount;\n                newItems[currentItemIndex].total = total;\n                newItems[currentItemIndex].subtotal = subtotal;\n                newItems[currentItemIndex].discountAmount = discountAmount;\n                newItems[currentItemIndex].taxAmount = taxAmount;\n                return {\n                    ...prev,\n                    items: newItems\n                };\n            });\n        }\n        setShowCustomizer(false);\n        setSelectedProduct(null);\n        setCurrentItemIndex(null);\n    };\n    const calculateTotals = ()=>{\n        const subtotal = formData.items.reduce((sum, item)=>sum + (parseFloat(item.total) || 0), 0);\n        const taxAmount = subtotal * 0.14; // 14% tax\n        const total = subtotal + taxAmount;\n        return {\n            subtotal,\n            taxAmount,\n            total\n        };\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.customerId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى اختيار العميل\");\n            return;\n        }\n        if (formData.items.length === 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى إضافة عنصر واحد على الأقل\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const { subtotal, taxAmount, total } = calculateTotals();\n            const quoteData = {\n                ...formData,\n                subtotal,\n                taxAmount,\n                total,\n                status: \"DRAFT\"\n            };\n            const response = quote ? await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].put(\"\".concat(\"http://localhost:3070\", \"/api/quotes/\").concat(quote.id), quoteData) : await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"\".concat(\"http://localhost:3070\", \"/api/quotes\"), quoteData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.data.message || (quote ? \"تم تحديث عرض السعر\" : \"تم إنشاء عرض السعر\"));\n            onSave(response.data.quote);\n            onClose();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"حدث خطأ\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    const { subtotal, taxAmount, total } = calculateTotals();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: quote ? \"تعديل عرض السعر\" : \"عرض سعر جديد\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.XMarkIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"العميل *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            name: \"customerId\",\n                                            value: formData.customerId,\n                                            onChange: handleChange,\n                                            className: \"form-input\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"اختر العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: customer.id,\n                                                        children: customer.name\n                                                    }, customer.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"صالح حتى *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            name: \"validUntil\",\n                                            value: formData.validUntil,\n                                            onChange: handleChange,\n                                            className: \"form-input\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"العناصر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addItem,\n                                            className: \"btn-primary flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PlusIcon, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"إضافة عنصر\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: formData.items.map((item, index)=>{\n                                        var _products_find;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-12 gap-4 items-end p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"المنتج\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: item.productId,\n                                                                    onChange: (e)=>updateItem(index, \"productId\", e.target.value),\n                                                                    className: \"form-input flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"اختر المنتج\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                            lineNumber: 319,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: product.id,\n                                                                                children: product.nameAr || product.name\n                                                                            }, product.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                                lineNumber: 321,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                item.productId && ((_products_find = products.find((p)=>p.id === item.productId)) === null || _products_find === void 0 ? void 0 : _products_find.isCustomizable) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>{\n                                                                        const product = products.find((p)=>p.id === item.productId);\n                                                                        setSelectedProduct(product);\n                                                                        setCurrentItemIndex(index);\n                                                                        setShowCustomizer(true);\n                                                                    },\n                                                                    className: \"btn-secondary flex items-center px-3\",\n                                                                    title: \"تخصيص المنتج\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.CogIcon, {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                        lineNumber: 338,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        item.isCustomized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 text-xs text-green-600\",\n                                                            children: [\n                                                                \"✓ تم تخصيص المنتج\",\n                                                                item.customizationDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-1\",\n                                                                    children: item.customizationDetails.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: [\n                                                                                \"• \",\n                                                                                detail.optionName,\n                                                                                \": \",\n                                                                                detail.selectedName,\n                                                                                detail.quantity > 1 && \" (\".concat(detail.quantity, \")\")\n                                                                            ]\n                                                                        }, idx, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                            lineNumber: 348,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"الكمية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.quantity,\n                                                            onChange: (e)=>updateItem(index, \"quantity\", e.target.value),\n                                                            className: \"form-input\",\n                                                            min: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"السعر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.unitPrice,\n                                                            onChange: (e)=>updateItem(index, \"unitPrice\", e.target.value),\n                                                            className: \"form-input\",\n                                                            step: \"0.01\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"خصم %\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.discount,\n                                                            onChange: (e)=>updateItem(index, \"discount\", e.target.value),\n                                                            className: \"form-input\",\n                                                            min: \"0\",\n                                                            max: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"الإجمالي\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900 py-2\",\n                                                            children: [\n                                                                \"$\",\n                                                                (item.total || 0).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>removeItem(index),\n                                                        className: \"text-red-600 hover:text-red-800\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.TrashIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"المجموع الفرعي:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    subtotal.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"الضريبة (14%):\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    taxAmount.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between font-bold text-lg border-t pt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"الإجمالي:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"form-label\",\n                                    children: \"ملاحظات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 434,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    name: \"notes\",\n                                    value: formData.notes,\n                                    onChange: handleChange,\n                                    className: \"form-input\",\n                                    rows: \"3\",\n                                    placeholder: \"ملاحظات إضافية...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 433,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"btn-secondary\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"btn-primary\",\n                                    children: loading ? \"جاري الحفظ...\" : quote ? \"تحديث\" : \"إنشاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 454,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n            lineNumber: 247,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n        lineNumber: 246,\n        columnNumber: 5\n    }, this);\n}\n_s(QuoteModal, \"rwn8c/8tmO85V7gLP0UzTJ2M44c=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = QuoteModal;\nvar _c;\n$RefreshReg$(_c, \"QuoteModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/sales/QuoteModal.js\n"));

/***/ })

});