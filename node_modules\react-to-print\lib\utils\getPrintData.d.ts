import type { UseReactToPrintHookContent } from "../types/UseReactToPrintHookContent";
import type { UseReactToPrintOptions } from "../types/UseReactToPrintOptions";
import type { HandlePrintWindowOnLoadData } from "./handlePrintWindowOnLoad";
export declare function getPrintData(optionalContent: UseReactToPrintHookContent | undefined, options: UseReactToPrintOptions): HandlePrintWindowOnLoadData | undefined;
