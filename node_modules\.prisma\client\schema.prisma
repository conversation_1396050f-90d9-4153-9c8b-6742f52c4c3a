// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String    @id @default(cuid())
  email            String    @unique
  username         String    @unique
  password         String
  firstName        String
  lastName         String
  role             UserRole  @default(USER)
  branchId         String? // User's assigned branch
  isActive         Boolean   @default(true)
  twoFactorEnabled Boolean   @default(false)
  lastLogin        DateTime?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  // Relations
  branch                    Branch?               @relation("BranchUsers", fields: [branchId], references: [id])
  managedBranch             Branch?               @relation("BranchManager")
  salesOrders               SalesOrder[]
  purchaseOrders            PurchaseOrder[]
  maintenanceOrders         MaintenanceOrder[]
  inventoryAdjustments      InventoryAdjustment[]
  cashTransactions          CashTransaction[]
  treasuryTransfersCreated  TreasuryTransfer[]    @relation("TreasuryCreator")
  treasuryTransfersApproved TreasuryTransfer[]    @relation("TreasuryApprover")
  branchTransfersRequested  BranchTransfer[]      @relation("TransferRequester")
  branchTransfersApproved   BranchTransfer[]      @relation("TransferApprover")
  payments                  Payment[]

  @@map("users")
}

model Company {
  id        String   @id @default(cuid())
  name      String
  nameAr    String
  address   String
  addressAr String
  phone     String
  email     String
  website   String?
  logo      String?
  currency  String   @default("USD")
  language  String   @default("ar")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  branches Branch[]

  @@map("companies")
}

model Branch {
  id           String   @id @default(cuid())
  companyId    String
  code         String   @unique
  name         String
  nameAr       String
  address      String
  addressAr    String
  phone        String
  email        String?
  managerId    String?  @unique
  isActive     Boolean  @default(true)
  isMainBranch Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  company             Company            @relation(fields: [companyId], references: [id])
  manager             User?              @relation("BranchManager", fields: [managerId], references: [id])
  users               User[]             @relation("BranchUsers")
  inventory           BranchInventory[]
  cashBox             CashBox?
  salesOrders         SalesOrder[]
  purchaseOrders      PurchaseOrder[]
  maintenanceOrders   MaintenanceOrder[]
  branchTransfersFrom BranchTransfer[]   @relation("TransferFrom")
  branchTransfersTo   BranchTransfer[]   @relation("TransferTo")

  @@map("branches")
}

model CashBox {
  id               String    @id @default(cuid())
  branchId         String    @unique
  currentBalance   Decimal   @default(0) @db.Decimal(10, 2)
  openingBalance   Decimal   @default(0) @db.Decimal(10, 2)
  lastTransferDate DateTime?
  isActive         Boolean   @default(true)
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  // Relations
  branch       Branch             @relation(fields: [branchId], references: [id])
  transactions CashTransaction[]
  transfers    TreasuryTransfer[]

  @@map("cash_boxes")
}

model CashTransaction {
  id            String          @id @default(cuid())
  cashBoxId     String
  type          TransactionType
  amount        Decimal         @db.Decimal(10, 2)
  description   String
  descriptionAr String
  referenceId   String? // Link to sales order, purchase order, etc.
  referenceType String? // 'SALES', 'PURCHASE', 'MAINTENANCE', 'TRANSFER'
  userId        String
  createdAt     DateTime        @default(now())

  // Relations
  cashBox CashBox @relation(fields: [cashBoxId], references: [id])
  user    User    @relation(fields: [userId], references: [id])

  @@map("cash_transactions")
}

model TreasuryTransfer {
  id           String         @id @default(cuid())
  cashBoxId    String
  amount       Decimal        @db.Decimal(10, 2)
  transferDate DateTime       @default(now())
  approvedBy   String?
  status       TransferStatus @default(PENDING)
  notes        String?
  createdBy    String
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt

  // Relations
  cashBox  CashBox @relation(fields: [cashBoxId], references: [id])
  approver User?   @relation("TreasuryApprover", fields: [approvedBy], references: [id])
  creator  User    @relation("TreasuryCreator", fields: [createdBy], references: [id])

  @@map("treasury_transfers")
}

model BranchInventory {
  id          String   @id @default(cuid())
  branchId    String
  productId   String
  quantity    Int      @default(0)
  reservedQty Int      @default(0) // Reserved for pending orders
  minStock    Int      @default(0)
  maxStock    Int      @default(0)
  lastUpdated DateTime @default(now())

  // Relations
  branch  Branch  @relation(fields: [branchId], references: [id])
  product Product @relation(fields: [productId], references: [id])

  @@unique([branchId, productId])
  @@map("branch_inventory")
}

model BranchTransfer {
  id             String         @id @default(cuid())
  transferNumber String         @unique
  fromBranchId   String
  toBranchId     String
  status         TransferStatus @default(PENDING)
  requestedBy    String
  approvedBy     String?
  transferDate   DateTime?
  notes          String?
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // Relations
  fromBranch Branch               @relation("TransferFrom", fields: [fromBranchId], references: [id])
  toBranch   Branch               @relation("TransferTo", fields: [toBranchId], references: [id])
  requester  User                 @relation("TransferRequester", fields: [requestedBy], references: [id])
  approver   User?                @relation("TransferApprover", fields: [approvedBy], references: [id])
  items      BranchTransferItem[]

  @@map("branch_transfers")
}

model BranchTransferItem {
  id         String  @id @default(cuid())
  transferId String
  productId  String
  quantity   Int
  notes      String?

  // Relations
  transfer BranchTransfer @relation(fields: [transferId], references: [id], onDelete: Cascade)
  product  Product        @relation(fields: [productId], references: [id])

  @@map("branch_transfer_items")
}

enum TransactionType {
  INCOME
  EXPENSE
  TRANSFER_IN
  TRANSFER_OUT
}

enum TransferStatus {
  PENDING
  APPROVED
  COMPLETED
  CANCELLED
}

enum PaymentMethod {
  CASH
  INSTAPAY
  VODAFONE_CASH
  VISA
  INSTALLMENTS
}

model Payment {
  id                 String        @id @default(cuid())
  salesOrderId       String?
  purchaseOrderId    String?
  maintenanceOrderId String?
  method             PaymentMethod
  amount             Decimal       @db.Decimal(10, 2)
  reference          String? // Transaction reference for digital payments
  installmentPlan    String? // JSON for installment details
  status             PaymentStatus @default(PENDING)
  paidAt             DateTime?
  createdBy          String
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt

  // Relations
  salesOrder       SalesOrder?       @relation(fields: [salesOrderId], references: [id])
  purchaseOrder    PurchaseOrder?    @relation(fields: [purchaseOrderId], references: [id])
  maintenanceOrder MaintenanceOrder? @relation(fields: [maintenanceOrderId], references: [id])
  creator          User              @relation(fields: [createdBy], references: [id])

  @@map("payments")
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

model Category {
  id            String   @id @default(cuid())
  name          String
  nameAr        String
  description   String?
  descriptionAr String?
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  products Product[]

  @@map("categories")
}

model Product {
  id            String   @id @default(cuid())
  code          String   @unique
  name          String
  nameAr        String
  description   String?
  descriptionAr String?
  categoryId    String
  unitPrice     Decimal  @db.Decimal(10, 2)
  costPrice     Decimal  @db.Decimal(10, 2)
  minStock      Int      @default(0)
  currentStock  Int      @default(0) // Global stock (sum of all branches)
  unit          String
  unitAr        String
  barcode       String?
  image         String?
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  category             Category              @relation(fields: [categoryId], references: [id])
  salesOrderItems      SalesOrderItem[]
  purchaseOrderItems   PurchaseOrderItem[]
  inventoryAdjustments InventoryAdjustment[]
  branchInventory      BranchInventory[]
  branchTransferItems  BranchTransferItem[]

  @@map("products")
}

model Customer {
  id          String       @id @default(cuid())
  code        String       @unique
  name        String
  nameAr      String
  type        CustomerType @default(CUSTOMER)
  email       String?
  phone       String
  address     String?
  addressAr   String?
  balance     Decimal      @default(0) @db.Decimal(10, 2)
  creditLimit Decimal      @default(0) @db.Decimal(10, 2)
  isActive    Boolean      @default(true)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Relations
  salesOrders       SalesOrder[]
  purchaseOrders    PurchaseOrder[]
  maintenanceOrders MaintenanceOrder[]

  @@map("customers")
}

enum UserRole {
  ADMIN
  MANAGER
  SALES
  INVENTORY
  ACCOUNTANT
  USER
}

enum CustomerType {
  CUSTOMER
  SUPPLIER
  BOTH
}

enum OrderStatus {
  DRAFT
  PENDING
  CONFIRMED
  SHIPPED
  DELIVERED
  CANCELLED
  RETURNED
}

enum MaintenanceStatus {
  RECEIVED
  IN_PROGRESS
  WAITING_PARTS
  COMPLETED
  DELIVERED
  CANCELLED
}

model SalesOrder {
  id              String        @id @default(cuid())
  orderNumber     String        @unique
  customerId      String
  userId          String
  branchId        String
  orderDate       DateTime      @default(now())
  dueDate         DateTime?
  status          OrderStatus   @default(DRAFT)
  subtotal        Decimal       @db.Decimal(10, 2)
  taxAmount       Decimal       @default(0) @db.Decimal(10, 2)
  discount        Decimal       @default(0) @db.Decimal(10, 2)
  total           Decimal       @db.Decimal(10, 2)
  paidAmount      Decimal       @default(0) @db.Decimal(10, 2)
  remainingAmount Decimal       @default(0) @db.Decimal(10, 2)
  paymentStatus   PaymentStatus @default(PENDING)
  notes           String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  customer Customer         @relation(fields: [customerId], references: [id])
  user     User             @relation(fields: [userId], references: [id])
  branch   Branch           @relation(fields: [branchId], references: [id])
  items    SalesOrderItem[]
  payments Payment[]

  @@map("sales_orders")
}

model SalesOrderItem {
  id           String  @id @default(cuid())
  salesOrderId String
  productId    String
  quantity     Int
  unitPrice    Decimal @db.Decimal(10, 2)
  discount     Decimal @default(0) @db.Decimal(10, 2)
  total        Decimal @db.Decimal(10, 2)

  // Relations
  salesOrder SalesOrder @relation(fields: [salesOrderId], references: [id], onDelete: Cascade)
  product    Product    @relation(fields: [productId], references: [id])

  @@map("sales_order_items")
}

model PurchaseOrder {
  id              String        @id @default(cuid())
  orderNumber     String        @unique
  supplierId      String
  userId          String
  branchId        String
  orderDate       DateTime      @default(now())
  expectedDate    DateTime?
  receivedDate    DateTime?
  status          OrderStatus   @default(DRAFT)
  subtotal        Decimal       @db.Decimal(10, 2)
  taxAmount       Decimal       @default(0) @db.Decimal(10, 2)
  discount        Decimal       @default(0) @db.Decimal(10, 2)
  total           Decimal       @db.Decimal(10, 2)
  paidAmount      Decimal       @default(0) @db.Decimal(10, 2)
  remainingAmount Decimal       @default(0) @db.Decimal(10, 2)
  paymentStatus   PaymentStatus @default(PENDING)
  notes           String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  supplier Customer            @relation(fields: [supplierId], references: [id])
  user     User                @relation(fields: [userId], references: [id])
  branch   Branch              @relation(fields: [branchId], references: [id])
  items    PurchaseOrderItem[]
  payments Payment[]

  @@map("purchase_orders")
}

model PurchaseOrderItem {
  id              String  @id @default(cuid())
  purchaseOrderId String
  productId       String
  quantity        Int
  unitPrice       Decimal @db.Decimal(10, 2)
  discount        Decimal @default(0) @db.Decimal(10, 2)
  total           Decimal @db.Decimal(10, 2)

  // Relations
  purchaseOrder PurchaseOrder @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  product       Product       @relation(fields: [productId], references: [id])

  @@map("purchase_order_items")
}

model InventoryAdjustment {
  id        String   @id @default(cuid())
  productId String
  userId    String
  type      String // 'IN' or 'OUT'
  quantity  Int
  reason    String
  notes     String?
  createdAt DateTime @default(now())

  // Relations
  product Product @relation(fields: [productId], references: [id])
  user    User    @relation(fields: [userId], references: [id])

  @@map("inventory_adjustments")
}

model MaintenanceOrder {
  id              String            @id @default(cuid())
  orderNumber     String            @unique
  customerId      String
  userId          String
  branchId        String
  deviceType      String
  deviceModel     String
  serialNumber    String?
  problem         String
  problemAr       String
  diagnosis       String?
  diagnosisAr     String?
  status          MaintenanceStatus @default(RECEIVED)
  estimatedCost   Decimal?          @db.Decimal(10, 2)
  actualCost      Decimal?          @db.Decimal(10, 2)
  paidAmount      Decimal           @default(0) @db.Decimal(10, 2)
  remainingAmount Decimal           @default(0) @db.Decimal(10, 2)
  paymentStatus   PaymentStatus     @default(PENDING)
  receivedDate    DateTime          @default(now())
  completedDate   DateTime?
  deliveredDate   DateTime?
  notes           String?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  customer Customer  @relation(fields: [customerId], references: [id])
  user     User      @relation(fields: [userId], references: [id])
  branch   Branch    @relation(fields: [branchId], references: [id])
  payments Payment[]

  @@map("maintenance_orders")
}
