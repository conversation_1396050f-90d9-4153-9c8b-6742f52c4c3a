const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create default users
  const hashedPassword = await bcrypt.hash('admin123', 12);
  
  const admin = await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'System',
      lastName: 'Administrator',
      role: 'ADMIN',
      isActive: true
    }
  });

  const manager = await prisma.user.upsert({
    where: { username: 'manager' },
    update: {},
    create: {
      username: 'manager',
      email: '<EMAIL>',
      password: await bcrypt.hash('manager123', 12),
      firstName: 'Business',
      lastName: 'Manager',
      role: 'MANAGER',
      isActive: true
    }
  });

  const sales = await prisma.user.upsert({
    where: { username: 'sales' },
    update: {},
    create: {
      username: 'sales',
      email: '<EMAIL>',
      password: await bcrypt.hash('sales123', 12),
      firstName: 'Sales',
      lastName: 'Representative',
      role: 'SALES',
      isActive: true
    }
  });

  console.log('✅ Users created');

  // Create default company settings
  const company = await prisma.company.upsert({
    where: { id: 'default' },
    update: {},
    create: {
      id: 'default',
      name: 'Business Management System',
      nameAr: 'نظام إدارة الأعمال',
      address: '123 Business Street, City, Country',
      addressAr: '123 شارع الأعمال، المدينة، البلد',
      phone: '+1234567890',
      email: '<EMAIL>',
      website: 'https://bms.com',
      currency: 'USD',
      language: 'ar'
    }
  });

  console.log('✅ Company settings created');

  // Create branches
  const branches = [
    {
      id: 'main-branch',
      code: 'MAIN',
      name: 'Main Branch',
      nameAr: 'الفرع الرئيسي',
      address: '123 Main Street, Downtown',
      addressAr: '123 الشارع الرئيسي، وسط المدينة',
      phone: '+1234567891',
      email: '<EMAIL>',
      managerId: manager.id,
      isMainBranch: true,
      openingBalance: 10000
    },
    {
      id: 'branch-north',
      code: 'NORTH',
      name: 'North Branch',
      nameAr: 'الفرع الشمالي',
      address: '456 North Avenue, North District',
      addressAr: '456 شارع الشمال، المنطقة الشمالية',
      phone: '+1234567892',
      email: '<EMAIL>',
      managerId: sales.id,
      isMainBranch: false,
      openingBalance: 5000
    },
    {
      id: 'branch-south',
      code: 'SOUTH',
      name: 'South Branch',
      nameAr: 'الفرع الجنوبي',
      address: '789 South Road, South District',
      addressAr: '789 طريق الجنوب، المنطقة الجنوبية',
      phone: '+1234567893',
      email: '<EMAIL>',
      isMainBranch: false,
      openingBalance: 3000
    }
  ];

  for (const branchData of branches) {
    const { openingBalance, ...branchInfo } = branchData;

    const branch = await prisma.branch.upsert({
      where: { id: branchData.id },
      update: {},
      create: {
        ...branchInfo,
        companyId: company.id
      }
    });

    // Create cash box for each branch
    await prisma.cashBox.upsert({
      where: { branchId: branch.id },
      update: {},
      create: {
        branchId: branch.id,
        currentBalance: openingBalance,
        openingBalance: openingBalance
      }
    });

    // Create opening balance transaction
    const cashBox = await prisma.cashBox.findUnique({
      where: { branchId: branch.id }
    });

    if (openingBalance > 0) {
      // Check if opening balance transaction already exists
      const existingTransaction = await prisma.cashTransaction.findFirst({
        where: {
          cashBoxId: cashBox.id,
          description: 'Opening balance'
        }
      });

      if (!existingTransaction) {
        await prisma.cashTransaction.create({
          data: {
            cashBoxId: cashBox.id,
            branchId: branch.id,
            type: 'INCOME',
            amount: openingBalance,
            description: 'Opening balance',
            descriptionAr: 'الرصيد الافتتاحي',
            userId: admin.id
          }
        });
      }
    }
  }

  // Assign users to branches
  await prisma.user.update({
    where: { id: admin.id },
    data: { branchId: 'main-branch' }
  });

  await prisma.user.update({
    where: { id: manager.id },
    data: { branchId: 'main-branch' }
  });

  await prisma.user.update({
    where: { id: sales.id },
    data: { branchId: 'branch-north' }
  });

  console.log('✅ Branches and cash boxes created');

  // Create default categories
  const categories = [
    {
      id: 'electronics',
      name: 'Electronics',
      nameAr: 'الإلكترونيات',
      description: 'Electronic devices and components',
      descriptionAr: 'الأجهزة والمكونات الإلكترونية'
    },
    {
      id: 'computers',
      name: 'Computers',
      nameAr: 'أجهزة الكمبيوتر',
      description: 'Computer hardware and accessories',
      descriptionAr: 'أجهزة الكمبيوتر والملحقات'
    },
    {
      id: 'mobile',
      name: 'Mobile Devices',
      nameAr: 'الأجهزة المحمولة',
      description: 'Smartphones and tablets',
      descriptionAr: 'الهواتف الذكية والأجهزة اللوحية'
    }
  ];

  for (const category of categories) {
    await prisma.category.upsert({
      where: { id: category.id },
      update: {},
      create: category
    });
  }

  console.log('✅ Categories created');

  // Create sample products
  const products = [
    {
      code: 'LAPTOP001',
      name: 'Business Laptop',
      nameAr: 'لابتوب للأعمال',
      description: 'High-performance laptop for business use',
      descriptionAr: 'لابتوب عالي الأداء للاستخدام التجاري',
      categoryId: 'computers',
      unitPrice: 999.99,
      costPrice: 750.00,
      minStock: 5,
      currentStock: 15,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890123'
    },
    {
      code: 'PHONE001',
      name: 'Smartphone Pro',
      nameAr: 'هاتف ذكي برو',
      description: 'Latest smartphone with advanced features',
      descriptionAr: 'أحدث هاتف ذكي بميزات متقدمة',
      categoryId: 'mobile',
      unitPrice: 799.99,
      costPrice: 600.00,
      minStock: 10,
      currentStock: 25,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890124'
    },
    {
      code: 'TABLET001',
      name: 'Business Tablet',
      nameAr: 'تابلت للأعمال',
      description: 'Professional tablet for presentations',
      descriptionAr: 'تابلت احترافي للعروض التقديمية',
      categoryId: 'mobile',
      unitPrice: 499.99,
      costPrice: 350.00,
      minStock: 8,
      currentStock: 12,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890125'
    },
    {
      code: 'MOUSE001',
      name: 'Wireless Mouse',
      nameAr: 'فأرة لاسلكية',
      description: 'Ergonomic wireless mouse',
      descriptionAr: 'فأرة لاسلكية مريحة',
      categoryId: 'computers',
      unitPrice: 29.99,
      costPrice: 15.00,
      minStock: 20,
      currentStock: 50,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890126'
    },
    {
      code: 'KEYBOARD001',
      name: 'Mechanical Keyboard',
      nameAr: 'لوحة مفاتيح ميكانيكية',
      description: 'Professional mechanical keyboard',
      descriptionAr: 'لوحة مفاتيح ميكانيكية احترافية',
      categoryId: 'computers',
      unitPrice: 89.99,
      costPrice: 60.00,
      minStock: 15,
      currentStock: 30,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890127'
    },
    // CPU Components
    {
      code: 'CPU001',
      name: 'Intel Core i7-13700K',
      nameAr: 'معالج إنتل كور i7-13700K',
      description: 'High-performance CPU for gaming and productivity',
      descriptionAr: 'معالج عالي الأداء للألعاب والإنتاجية',
      categoryId: 'electronics',
      unitPrice: 399.99,
      costPrice: 320.00,
      minStock: 5,
      currentStock: 15,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890128'
    },
    {
      code: 'CPU002',
      name: 'AMD Ryzen 7 7700X',
      nameAr: 'معالج AMD رايزن 7 7700X',
      description: 'Advanced AMD processor',
      descriptionAr: 'معالج AMD متقدم',
      categoryId: 'electronics',
      unitPrice: 349.99,
      costPrice: 280.00,
      minStock: 5,
      currentStock: 12,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890129'
    },
    // RAM Components
    {
      code: 'RAM001',
      name: 'Corsair Vengeance 16GB DDR4',
      nameAr: 'ذاكرة كورسير فينجانس 16 جيجا DDR4',
      description: '16GB DDR4 RAM module',
      descriptionAr: 'وحدة ذاكرة 16 جيجا DDR4',
      categoryId: 'electronics',
      unitPrice: 79.99,
      costPrice: 60.00,
      minStock: 10,
      currentStock: 25,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890130'
    },
    {
      code: 'RAM002',
      name: 'G.Skill Trident Z 32GB DDR4',
      nameAr: 'ذاكرة جي سكيل ترايدنت Z 32 جيجا DDR4',
      description: '32GB DDR4 RAM kit',
      descriptionAr: 'طقم ذاكرة 32 جيجا DDR4',
      categoryId: 'electronics',
      unitPrice: 149.99,
      costPrice: 120.00,
      minStock: 8,
      currentStock: 18,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890131'
    },
    // Storage Components
    {
      code: 'SSD001',
      name: 'Samsung 980 PRO 1TB NVMe SSD',
      nameAr: 'قرص سامسونج 980 برو 1 تيرا NVMe SSD',
      description: 'High-speed NVMe SSD',
      descriptionAr: 'قرص SSD عالي السرعة NVMe',
      categoryId: 'electronics',
      unitPrice: 129.99,
      costPrice: 100.00,
      minStock: 10,
      currentStock: 20,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890132'
    },
    {
      code: 'HDD001',
      name: 'Seagate Barracuda 2TB HDD',
      nameAr: 'قرص سيجيت باراكودا 2 تيرا HDD',
      description: 'Traditional hard disk drive',
      descriptionAr: 'قرص صلب تقليدي',
      categoryId: 'electronics',
      unitPrice: 59.99,
      costPrice: 45.00,
      minStock: 8,
      currentStock: 15,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890133'
    },
    // GPU Components
    {
      code: 'GPU001',
      name: 'NVIDIA RTX 4070',
      nameAr: 'كرت رسوميات NVIDIA RTX 4070',
      description: 'High-end graphics card',
      descriptionAr: 'كرت رسوميات عالي الجودة',
      categoryId: 'electronics',
      unitPrice: 599.99,
      costPrice: 480.00,
      minStock: 3,
      currentStock: 8,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890134'
    },
    {
      code: 'GPU002',
      name: 'AMD Radeon RX 7600',
      nameAr: 'كرت رسوميات AMD راديون RX 7600',
      description: 'Mid-range graphics card',
      descriptionAr: 'كرت رسوميات متوسط المدى',
      categoryId: 'electronics',
      unitPrice: 299.99,
      costPrice: 240.00,
      minStock: 5,
      currentStock: 12,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890135'
    }
  ];

  for (const product of products) {
    await prisma.product.upsert({
      where: { code: product.code },
      update: {},
      create: product
    });
  }

  console.log('✅ Products created');

  // Create sample customers
  const customers = [
    {
      code: 'CUST001',
      name: 'ABC Corporation',
      nameAr: 'شركة أي بي سي',
      type: 'CUSTOMER',
      email: '<EMAIL>',
      phone: '+1234567891',
      address: '456 Corporate Ave, Business District',
      addressAr: '456 شارع الشركات، المنطقة التجارية',
      balance: 0,
      creditLimit: 10000
    },
    {
      code: 'CUST002',
      name: 'XYZ Trading',
      nameAr: 'شركة إكس واي زد للتجارة',
      type: 'CUSTOMER',
      email: '<EMAIL>',
      phone: '+1234567892',
      address: '789 Trade Street, Commercial Zone',
      addressAr: '789 شارع التجارة، المنطقة التجارية',
      balance: 0,
      creditLimit: 5000
    },
    {
      code: 'SUPP001',
      name: 'Tech Supplier Inc',
      nameAr: 'شركة المورد التقني',
      type: 'SUPPLIER',
      email: '<EMAIL>',
      phone: '+1234567893',
      address: '321 Supply Chain Blvd, Industrial Area',
      addressAr: '321 شارع سلسلة التوريد، المنطقة الصناعية',
      balance: 0,
      creditLimit: 0
    },
    {
      code: 'BOTH001',
      name: 'Global Electronics',
      nameAr: 'الإلكترونيات العالمية',
      type: 'BOTH',
      email: '<EMAIL>',
      phone: '+1234567894',
      address: '654 Electronics Plaza, Tech Hub',
      addressAr: '654 ساحة الإلكترونيات، مركز التقنية',
      balance: 0,
      creditLimit: 15000
    }
  ];

  for (const customer of customers) {
    await prisma.customer.upsert({
      where: { code: customer.code },
      update: {},
      create: customer
    });
  }

  console.log('✅ Customers created');

  // Create branch inventory for products
  const allBranches = await prisma.branch.findMany();
  const allProducts = await prisma.product.findMany();

  for (const branch of allBranches) {
    for (const product of allProducts) {
      // Distribute inventory across branches
      let quantity = 0;
      if (branch.isMainBranch) {
        quantity = Math.floor(product.currentStock * 0.5); // 50% to main branch
      } else {
        quantity = Math.floor(product.currentStock * 0.25); // 25% to each other branch
      }

      if (quantity > 0) {
        await prisma.branchInventory.upsert({
          where: {
            branchId_productId: {
              branchId: branch.id,
              productId: product.id
            }
          },
          update: {},
          create: {
            branchId: branch.id,
            productId: product.id,
            quantity: quantity,
            minStock: Math.floor(product.minStock / allBranches.length),
            maxStock: quantity * 2
          }
        });
      }
    }
  }

  console.log('✅ Branch inventory created');

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Demo Credentials:');
  console.log('Admin: admin / admin123');
  console.log('Manager: manager / manager123');
  console.log('Sales: sales / sales123');
  console.log('\n🏢 Branches:');
  console.log('Main Branch (MAIN) - Manager: Business Manager');
  console.log('North Branch (NORTH) - Manager: Sales Representative');
  console.log('South Branch (SOUTH) - No assigned manager');
  console.log('\n💰 Cash Boxes:');
  console.log('Main Branch: $10,000');
  console.log('North Branch: $5,000');
  console.log('South Branch: $3,000');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
