const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create default users
  const hashedPassword = await bcrypt.hash('admin123', 12);
  
  const admin = await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'System',
      lastName: 'Administrator',
      role: 'ADMIN',
      isActive: true
    }
  });

  const manager = await prisma.user.upsert({
    where: { username: 'manager' },
    update: {},
    create: {
      username: 'manager',
      email: '<EMAIL>',
      password: await bcrypt.hash('manager123', 12),
      firstName: 'Business',
      lastName: 'Manager',
      role: 'MANAGER',
      isActive: true
    }
  });

  const sales = await prisma.user.upsert({
    where: { username: 'sales' },
    update: {},
    create: {
      username: 'sales',
      email: '<EMAIL>',
      password: await bcrypt.hash('sales123', 12),
      firstName: 'Sales',
      lastName: 'Representative',
      role: 'SALES',
      isActive: true
    }
  });

  console.log('✅ Users created');

  // Create default company settings
  const company = await prisma.company.upsert({
    where: { id: 'default' },
    update: {},
    create: {
      id: 'default',
      name: 'Business Management System',
      nameAr: 'نظام إدارة الأعمال',
      address: '123 Business Street, City, Country',
      addressAr: '123 شارع الأعمال، المدينة، البلد',
      phone: '+1234567890',
      email: '<EMAIL>',
      website: 'https://bms.com',
      currency: 'USD',
      language: 'ar'
    }
  });

  console.log('✅ Company settings created');

  // Create default categories
  const categories = [
    {
      id: 'electronics',
      name: 'Electronics',
      nameAr: 'الإلكترونيات',
      description: 'Electronic devices and components',
      descriptionAr: 'الأجهزة والمكونات الإلكترونية'
    },
    {
      id: 'computers',
      name: 'Computers',
      nameAr: 'أجهزة الكمبيوتر',
      description: 'Computer hardware and accessories',
      descriptionAr: 'أجهزة الكمبيوتر والملحقات'
    },
    {
      id: 'mobile',
      name: 'Mobile Devices',
      nameAr: 'الأجهزة المحمولة',
      description: 'Smartphones and tablets',
      descriptionAr: 'الهواتف الذكية والأجهزة اللوحية'
    }
  ];

  for (const category of categories) {
    await prisma.category.upsert({
      where: { id: category.id },
      update: {},
      create: category
    });
  }

  console.log('✅ Categories created');

  // Create sample products
  const products = [
    {
      code: 'LAPTOP001',
      name: 'Business Laptop',
      nameAr: 'لابتوب للأعمال',
      description: 'High-performance laptop for business use',
      descriptionAr: 'لابتوب عالي الأداء للاستخدام التجاري',
      categoryId: 'computers',
      unitPrice: 999.99,
      costPrice: 750.00,
      minStock: 5,
      currentStock: 15,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890123'
    },
    {
      code: 'PHONE001',
      name: 'Smartphone Pro',
      nameAr: 'هاتف ذكي برو',
      description: 'Latest smartphone with advanced features',
      descriptionAr: 'أحدث هاتف ذكي بميزات متقدمة',
      categoryId: 'mobile',
      unitPrice: 799.99,
      costPrice: 600.00,
      minStock: 10,
      currentStock: 25,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890124'
    },
    {
      code: 'TABLET001',
      name: 'Business Tablet',
      nameAr: 'تابلت للأعمال',
      description: 'Professional tablet for presentations',
      descriptionAr: 'تابلت احترافي للعروض التقديمية',
      categoryId: 'mobile',
      unitPrice: 499.99,
      costPrice: 350.00,
      minStock: 8,
      currentStock: 12,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890125'
    },
    {
      code: 'MOUSE001',
      name: 'Wireless Mouse',
      nameAr: 'فأرة لاسلكية',
      description: 'Ergonomic wireless mouse',
      descriptionAr: 'فأرة لاسلكية مريحة',
      categoryId: 'computers',
      unitPrice: 29.99,
      costPrice: 15.00,
      minStock: 20,
      currentStock: 50,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890126'
    },
    {
      code: 'KEYBOARD001',
      name: 'Mechanical Keyboard',
      nameAr: 'لوحة مفاتيح ميكانيكية',
      description: 'Professional mechanical keyboard',
      descriptionAr: 'لوحة مفاتيح ميكانيكية احترافية',
      categoryId: 'computers',
      unitPrice: 89.99,
      costPrice: 60.00,
      minStock: 15,
      currentStock: 30,
      unit: 'piece',
      unitAr: 'قطعة',
      barcode: '1234567890127'
    }
  ];

  for (const product of products) {
    await prisma.product.upsert({
      where: { code: product.code },
      update: {},
      create: product
    });
  }

  console.log('✅ Products created');

  // Create sample customers
  const customers = [
    {
      code: 'CUST001',
      name: 'ABC Corporation',
      nameAr: 'شركة أي بي سي',
      type: 'CUSTOMER',
      email: '<EMAIL>',
      phone: '+1234567891',
      address: '456 Corporate Ave, Business District',
      addressAr: '456 شارع الشركات، المنطقة التجارية',
      balance: 0,
      creditLimit: 10000
    },
    {
      code: 'CUST002',
      name: 'XYZ Trading',
      nameAr: 'شركة إكس واي زد للتجارة',
      type: 'CUSTOMER',
      email: '<EMAIL>',
      phone: '+1234567892',
      address: '789 Trade Street, Commercial Zone',
      addressAr: '789 شارع التجارة، المنطقة التجارية',
      balance: 0,
      creditLimit: 5000
    },
    {
      code: 'SUPP001',
      name: 'Tech Supplier Inc',
      nameAr: 'شركة المورد التقني',
      type: 'SUPPLIER',
      email: '<EMAIL>',
      phone: '+1234567893',
      address: '321 Supply Chain Blvd, Industrial Area',
      addressAr: '321 شارع سلسلة التوريد، المنطقة الصناعية',
      balance: 0,
      creditLimit: 0
    },
    {
      code: 'BOTH001',
      name: 'Global Electronics',
      nameAr: 'الإلكترونيات العالمية',
      type: 'BOTH',
      email: '<EMAIL>',
      phone: '+1234567894',
      address: '654 Electronics Plaza, Tech Hub',
      addressAr: '654 ساحة الإلكترونيات، مركز التقنية',
      balance: 0,
      creditLimit: 15000
    }
  ];

  for (const customer of customers) {
    await prisma.customer.upsert({
      where: { code: customer.code },
      update: {},
      create: customer
    });
  }

  console.log('✅ Customers created');

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Demo Credentials:');
  console.log('Admin: admin / admin123');
  console.log('Manager: manager / manager123');
  console.log('Sales: sales / sales123');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
