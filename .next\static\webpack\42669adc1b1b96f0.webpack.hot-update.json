{"c": ["webpack"], "r": ["pages/products", "pages/sales", "pages/purchases", "pages/inventory"], "m": ["./components/ProductModals.js", "./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js", "./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js", "./node_modules/@heroicons/react/24/outline/esm/TagIcon.js", "./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Cproducts.js&page=%2Fproducts!", "./pages/products.js", "__barrel_optimize__?names=ArchiveBoxIcon,CubeIcon,CurrencyDollarIcon,TagIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./components/SalesModals.js", "./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js", "./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js", "./node_modules/@heroicons/react/24/outline/esm/UserIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Csales.js&page=%2Fsales!", "./pages/sales.js", "__barrel_optimize__?names=CalendarIcon,CurrencyDollarIcon,DocumentTextIcon,ShoppingCartIcon,UserIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "__barrel_optimize__?names=CurrencyDollarIcon,EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,ShoppingCartIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Cpurchases.js&page=%2Fpurchases!", "./pages/purchases.js", "__barrel_optimize__?names=CurrencyDollarIcon,EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,ShoppingBagIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js", "./node_modules/@heroicons/react/24/outline/esm/BuildingStorefrontIcon.js", "./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Cinventory.js&page=%2Finventory!", "./pages/inventory.js", "__barrel_optimize__?names=ArrowsRightLeftIcon,BuildingStorefrontIcon,CubeIcon,ExclamationTriangleIcon,MagnifyingGlassIcon,PlusIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js"]}