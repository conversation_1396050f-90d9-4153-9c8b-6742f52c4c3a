// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String   @id @default(cuid())
  email             String   @unique
  username          String   @unique
  password          String
  firstName         String
  lastName          String
  role              UserRole @default(USER)
  isActive          Boolean  @default(true)
  twoFactorEnabled  Boolean  @default(false)
  lastLogin         DateTime?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  salesOrders       SalesOrder[]
  purchaseOrders    PurchaseOrder[]
  maintenanceOrders MaintenanceOrder[]
  inventoryAdjustments InventoryAdjustment[]

  @@map("users")
}

model Company {
  id          String @id @default(cuid())
  name        String
  nameAr      String
  address     String
  addressAr   String
  phone       String
  email       String
  website     String?
  logo        String?
  currency    String @default("USD")
  language    String @default("ar")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("companies")
}

model Category {
  id        String    @id @default(cuid())
  name      String
  nameAr    String
  description String?
  descriptionAr String?
  isActive  Boolean   @default(true)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  // Relations
  products  Product[]

  @@map("categories")
}

model Product {
  id            String  @id @default(cuid())
  code          String  @unique
  name          String
  nameAr        String
  description   String?
  descriptionAr String?
  categoryId    String
  unitPrice     Decimal @db.Decimal(10, 2)
  costPrice     Decimal @db.Decimal(10, 2)
  minStock      Int     @default(0)
  currentStock  Int     @default(0)
  unit          String
  unitAr        String
  barcode       String?
  image         String?
  isActive      Boolean @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  category      Category @relation(fields: [categoryId], references: [id])
  salesOrderItems SalesOrderItem[]
  purchaseOrderItems PurchaseOrderItem[]
  inventoryAdjustments InventoryAdjustment[]

  @@map("products")
}

model Customer {
  id            String      @id @default(cuid())
  code          String      @unique
  name          String
  nameAr        String
  type          CustomerType @default(CUSTOMER)
  email         String?
  phone         String
  address       String?
  addressAr     String?
  balance       Decimal     @db.Decimal(10, 2) @default(0)
  creditLimit   Decimal     @db.Decimal(10, 2) @default(0)
  isActive      Boolean     @default(true)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // Relations
  salesOrders   SalesOrder[]
  purchaseOrders PurchaseOrder[]
  maintenanceOrders MaintenanceOrder[]

  @@map("customers")
}

enum UserRole {
  ADMIN
  MANAGER
  SALES
  INVENTORY
  ACCOUNTANT
  USER
}

enum CustomerType {
  CUSTOMER
  SUPPLIER
  BOTH
}

enum OrderStatus {
  DRAFT
  PENDING
  CONFIRMED
  SHIPPED
  DELIVERED
  CANCELLED
  RETURNED
}

enum MaintenanceStatus {
  RECEIVED
  IN_PROGRESS
  WAITING_PARTS
  COMPLETED
  DELIVERED
  CANCELLED
}

model SalesOrder {
  id          String      @id @default(cuid())
  orderNumber String      @unique
  customerId  String
  userId      String
  orderDate   DateTime    @default(now())
  dueDate     DateTime?
  status      OrderStatus @default(DRAFT)
  subtotal    Decimal     @db.Decimal(10, 2)
  taxAmount   Decimal     @db.Decimal(10, 2) @default(0)
  discount    Decimal     @db.Decimal(10, 2) @default(0)
  total       Decimal     @db.Decimal(10, 2)
  notes       String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  customer    Customer @relation(fields: [customerId], references: [id])
  user        User     @relation(fields: [userId], references: [id])
  items       SalesOrderItem[]

  @@map("sales_orders")
}

model SalesOrderItem {
  id            String  @id @default(cuid())
  salesOrderId  String
  productId     String
  quantity      Int
  unitPrice     Decimal @db.Decimal(10, 2)
  discount      Decimal @db.Decimal(10, 2) @default(0)
  total         Decimal @db.Decimal(10, 2)

  // Relations
  salesOrder    SalesOrder @relation(fields: [salesOrderId], references: [id], onDelete: Cascade)
  product       Product    @relation(fields: [productId], references: [id])

  @@map("sales_order_items")
}

model PurchaseOrder {
  id          String      @id @default(cuid())
  orderNumber String      @unique
  supplierId  String
  userId      String
  orderDate   DateTime    @default(now())
  expectedDate DateTime?
  status      OrderStatus @default(DRAFT)
  subtotal    Decimal     @db.Decimal(10, 2)
  taxAmount   Decimal     @db.Decimal(10, 2) @default(0)
  discount    Decimal     @db.Decimal(10, 2) @default(0)
  total       Decimal     @db.Decimal(10, 2)
  notes       String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  supplier    Customer @relation(fields: [supplierId], references: [id])
  user        User     @relation(fields: [userId], references: [id])
  items       PurchaseOrderItem[]

  @@map("purchase_orders")
}

model PurchaseOrderItem {
  id              String  @id @default(cuid())
  purchaseOrderId String
  productId       String
  quantity        Int
  unitPrice       Decimal @db.Decimal(10, 2)
  discount        Decimal @db.Decimal(10, 2) @default(0)
  total           Decimal @db.Decimal(10, 2)

  // Relations
  purchaseOrder   PurchaseOrder @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  product         Product       @relation(fields: [productId], references: [id])

  @@map("purchase_order_items")
}

model InventoryAdjustment {
  id          String    @id @default(cuid())
  productId   String
  userId      String
  type        String    // 'IN' or 'OUT'
  quantity    Int
  reason      String
  notes       String?
  createdAt   DateTime  @default(now())

  // Relations
  product     Product @relation(fields: [productId], references: [id])
  user        User    @relation(fields: [userId], references: [id])

  @@map("inventory_adjustments")
}

model MaintenanceOrder {
  id            String            @id @default(cuid())
  orderNumber   String            @unique
  customerId    String
  userId        String
  deviceType    String
  deviceModel   String
  serialNumber  String?
  problem       String
  problemAr     String
  status        MaintenanceStatus @default(RECEIVED)
  estimatedCost Decimal?          @db.Decimal(10, 2)
  actualCost    Decimal?          @db.Decimal(10, 2)
  receivedDate  DateTime          @default(now())
  completedDate DateTime?
  deliveredDate DateTime?
  notes         String?
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt

  // Relations
  customer      Customer @relation(fields: [customerId], references: [id])
  user          User     @relation(fields: [userId], references: [id])

  @@map("maintenance_orders")
}
