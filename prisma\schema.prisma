// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String   @id @default(cuid())
  email             String   @unique
  username          String   @unique
  password          String
  firstName         String
  lastName          String
  role              UserRole @default(USER)
  branchId          String?  // User's assigned branch
  isActive          Boolean  @default(true)
  twoFactorEnabled  Boolean  @default(false)
  lastLogin         DateTime?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  branch            Branch?  @relation("BranchUsers", fields: [branchId], references: [id])
  managedBranch     Branch?  @relation("BranchManager")
  salesOrders       SalesOrder[]
  purchaseOrders    PurchaseOrder[]
  maintenanceOrders MaintenanceOrder[]
  inventoryAdjustments InventoryAdjustment[]
  cashTransactions  CashTransaction[]
  treasuryTransfersCreated TreasuryTransfer[] @relation("TreasuryCreator")
  treasuryTransfersApproved TreasuryTransfer[] @relation("TreasuryApprover")
  branchTransfersRequested BranchTransfer[] @relation("TransferRequester")
  branchTransfersApproved BranchTransfer[] @relation("TransferApprover")
  payments          Payment[]
  customConfigurations CustomProductConfiguration[]
  quotes            Quote[]
  invoices          Invoice[]
  invoicePayments   InvoicePayment[]

  @@map("users")
}

model Company {
  id          String @id @default(cuid())
  name        String
  nameAr      String
  address     String
  addressAr   String
  phone       String
  email       String
  website     String?
  logo        String?
  currency    String @default("USD")
  language    String @default("ar")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  branches    Branch[]

  @@map("companies")
}

model Branch {
  id          String @id @default(cuid())
  companyId   String
  code        String @unique
  name        String
  nameAr      String
  address     String
  addressAr   String
  phone       String
  email       String?
  managerId   String? @unique
  isActive    Boolean @default(true)
  isMainBranch Boolean @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  company     Company @relation(fields: [companyId], references: [id])
  manager     User?   @relation("BranchManager", fields: [managerId], references: [id])
  users       User[]  @relation("BranchUsers")
  inventory   BranchInventory[]
  cashBox     CashBox?
  salesOrders SalesOrder[]
  purchaseOrders PurchaseOrder[]
  maintenanceOrders MaintenanceOrder[]
  branchTransfersFrom BranchTransfer[] @relation("TransferFrom")
  branchTransfersTo   BranchTransfer[] @relation("TransferTo")
  customConfigurations CustomProductConfiguration[]
  cashTransactions  CashTransaction[]
  quotes            Quote[]
  invoices          Invoice[]

  @@map("branches")
}

model CashBox {
  id          String @id @default(cuid())
  branchId    String @unique
  currentBalance Decimal @db.Decimal(10, 2) @default(0)
  openingBalance Decimal @db.Decimal(10, 2) @default(0)
  lastTransferDate DateTime?
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  branch      Branch @relation(fields: [branchId], references: [id])
  transactions CashTransaction[]
  transfers   TreasuryTransfer[]

  @@map("cash_boxes")
}

model CashTransaction {
  id          String @id @default(cuid())
  cashBoxId   String
  branchId    String
  type        TransactionType
  amount      Decimal @db.Decimal(10, 2)
  description String
  descriptionAr String
  referenceId String? // Link to sales order, purchase order, etc.
  referenceType String? // 'SALES', 'PURCHASE', 'MAINTENANCE', 'TRANSFER'
  userId      String
  createdAt   DateTime @default(now())

  // Relations
  cashBox     CashBox @relation(fields: [cashBoxId], references: [id])
  branch      Branch  @relation(fields: [branchId], references: [id])
  user        User    @relation(fields: [userId], references: [id])

  @@map("cash_transactions")
}

model TreasuryTransfer {
  id          String @id @default(cuid())
  cashBoxId   String
  amount      Decimal @db.Decimal(10, 2)
  transferDate DateTime @default(now())
  approvedBy  String?
  status      TransferStatus @default(PENDING)
  notes       String?
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  cashBox     CashBox @relation(fields: [cashBoxId], references: [id])
  approver    User?   @relation("TreasuryApprover", fields: [approvedBy], references: [id])
  creator     User    @relation("TreasuryCreator", fields: [createdBy], references: [id])

  @@map("treasury_transfers")
}

model BranchInventory {
  id          String @id @default(cuid())
  branchId    String
  productId   String
  quantity    Int @default(0)
  reservedQty Int @default(0) // Reserved for pending orders
  minStock    Int @default(0)
  maxStock    Int @default(0)
  lastUpdated DateTime @default(now())

  // Relations
  branch      Branch  @relation(fields: [branchId], references: [id])
  product     Product @relation(fields: [productId], references: [id])

  @@unique([branchId, productId])
  @@map("branch_inventory")
}

model BranchTransfer {
  id          String @id @default(cuid())
  transferNumber String @unique
  fromBranchId String
  toBranchId  String
  status      TransferStatus @default(PENDING)
  requestedBy String
  approvedBy  String?
  transferDate DateTime?
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  fromBranch  Branch @relation("TransferFrom", fields: [fromBranchId], references: [id])
  toBranch    Branch @relation("TransferTo", fields: [toBranchId], references: [id])
  requester   User   @relation("TransferRequester", fields: [requestedBy], references: [id])
  approver    User?  @relation("TransferApprover", fields: [approvedBy], references: [id])
  items       BranchTransferItem[]

  @@map("branch_transfers")
}

model BranchTransferItem {
  id          String @id @default(cuid())
  transferId  String
  productId   String
  quantity    Int
  notes       String?

  // Relations
  transfer    BranchTransfer @relation(fields: [transferId], references: [id], onDelete: Cascade)
  product     Product        @relation(fields: [productId], references: [id])

  @@map("branch_transfer_items")
}

enum TransactionType {
  INCOME
  EXPENSE
  TRANSFER_IN
  TRANSFER_OUT
}

enum TransferStatus {
  PENDING
  APPROVED
  COMPLETED
  CANCELLED
}

enum PaymentMethod {
  CASH
  INSTAPAY
  VODAFONE_CASH
  VISA
  INSTALLMENTS
}

model Payment {
  id              String @id @default(cuid())
  salesOrderId    String?
  purchaseOrderId String?
  maintenanceOrderId String?
  method          PaymentMethod
  amount          Decimal @db.Decimal(10, 2)
  reference       String? // Transaction reference for digital payments
  installmentPlan String? // JSON for installment details
  status          PaymentStatus @default(PENDING)
  paidAt          DateTime?
  createdBy       String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  salesOrder      SalesOrder? @relation(fields: [salesOrderId], references: [id])
  purchaseOrder   PurchaseOrder? @relation(fields: [purchaseOrderId], references: [id])
  maintenanceOrder MaintenanceOrder? @relation(fields: [maintenanceOrderId], references: [id])
  creator         User @relation(fields: [createdBy], references: [id])

  @@map("payments")
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

model Category {
  id        String    @id @default(cuid())
  name      String
  nameAr    String
  description String?
  descriptionAr String?
  isActive  Boolean   @default(true)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  // Relations
  products  Product[]

  @@map("categories")
}

model Product {
  id            String  @id @default(cuid())
  code          String  @unique
  name          String
  nameAr        String
  description   String?
  descriptionAr String?
  categoryId    String
  productType   ProductType @default(COMPONENT) // CUSTOMIZABLE, COMPONENT, SERVICE
  unitPrice     Decimal @db.Decimal(10, 2)
  costPrice     Decimal @db.Decimal(10, 2)
  minStock      Int     @default(0)
  currentStock  Int     @default(0) // Global stock (sum of all branches)
  unit          String
  unitAr        String
  barcode       String?
  image         String?
  isActive      Boolean @default(true)
  hasInventory  Boolean @default(true) // false for services
  customizationOptions Json? // For customizable products - defines available options
  compatibleComponents String[] // Array of component product IDs for customizable products
  assemblyInstructions String? // Instructions for assembling customizable products
  assemblyInstructionsAr String? // Arabic assembly instructions
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  category      Category @relation(fields: [categoryId], references: [id])
  salesOrderItems SalesOrderItem[]
  purchaseOrderItems PurchaseOrderItem[]
  inventoryAdjustments InventoryAdjustment[]
  branchInventory BranchInventory[]
  branchTransferItems BranchTransferItem[]
  customConfigurations CustomProductConfiguration[]
  componentUsage SalesOrderCustomComponent[] @relation("ComponentUsage")
  quoteItems      QuoteItem[]
  invoiceItems    InvoiceItem[]

  @@map("products")
}

model Customer {
  id            String      @id @default(cuid())
  code          String      @unique
  name          String
  nameAr        String
  type          CustomerType @default(CUSTOMER)
  email         String?
  phone         String
  address       String?
  addressAr     String?
  balance       Decimal     @db.Decimal(10, 2) @default(0)
  creditLimit   Decimal     @db.Decimal(10, 2) @default(0)
  isActive      Boolean     @default(true)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // Relations
  salesOrders   SalesOrder[]
  purchaseOrders PurchaseOrder[]
  maintenanceOrders MaintenanceOrder[]
  customConfigurations CustomProductConfiguration[]
  quotes        Quote[]
  invoices      Invoice[]

  @@map("customers")
}

enum UserRole {
  ADMIN
  MANAGER
  SALES
  INVENTORY
  ACCOUNTANT
  USER
}

enum CustomerType {
  CUSTOMER
  SUPPLIER
  BOTH
}

enum ProductType {
  CUSTOMIZABLE  // Base products that can be configured (e.g., Laptops, Desktop PCs)
  COMPONENT     // Individual parts with inventory (e.g., RAM, SSD, CPU, GPU)
  SERVICE       // Services without inventory (e.g., Maintenance, Software Installation)
}

enum QuoteStatus {
  DRAFT
  PENDING
  APPROVED
  REJECTED
  EXPIRED
  CONVERTED_TO_ORDER
}

enum OrderStatus {
  DRAFT
  PENDING
  CONFIRMED
  SHIPPED
  DELIVERED
  CANCELLED
  RETURNED
  CONVERTED_TO_INVOICE
}

enum InvoiceStatus {
  DRAFT
  PENDING
  PAID
  PARTIALLY_PAID
  OVERDUE
  CANCELLED
}

enum MaintenanceStatus {
  RECEIVED
  IN_PROGRESS
  WAITING_PARTS
  COMPLETED
  DELIVERED
  CANCELLED
}

model SalesOrder {
  id          String      @id @default(cuid())
  orderNumber String      @unique
  customerId  String
  userId      String
  branchId    String
  orderDate   DateTime    @default(now())
  dueDate     DateTime?
  status      OrderStatus @default(DRAFT)
  subtotal    Decimal     @db.Decimal(10, 2)
  taxAmount   Decimal     @db.Decimal(10, 2) @default(0)
  discount    Decimal     @db.Decimal(10, 2) @default(0)
  total       Decimal     @db.Decimal(10, 2)
  paidAmount  Decimal     @db.Decimal(10, 2) @default(0)
  remainingAmount Decimal @db.Decimal(10, 2) @default(0)
  paymentStatus PaymentStatus @default(PENDING)
  notes       String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  customer    Customer @relation(fields: [customerId], references: [id])
  user        User     @relation(fields: [userId], references: [id])
  branch      Branch   @relation(fields: [branchId], references: [id])
  items       SalesOrderItem[]
  payments    Payment[]
  invoices    Invoice[]

  @@map("sales_orders")
}

model SalesOrderItem {
  id            String  @id @default(cuid())
  salesOrderId  String
  productId     String
  quantity      Int
  unitPrice     Decimal @db.Decimal(10, 2)
  discount      Decimal @db.Decimal(10, 2) @default(0)
  total         Decimal @db.Decimal(10, 2)
  isCustomizable Boolean @default(false) // true if this is a customizable product
  customConfiguration Json? // Configuration details for customizable products
  assemblyFee   Decimal @db.Decimal(10, 2) @default(0)
  serviceFee    Decimal @db.Decimal(10, 2) @default(0)

  // Relations
  salesOrder    SalesOrder @relation(fields: [salesOrderId], references: [id], onDelete: Cascade)
  product       Product    @relation(fields: [productId], references: [id])
  customComponents SalesOrderCustomComponent[] // Components used in customizable products

  @@map("sales_order_items")
}

model SalesOrderCustomComponent {
  id              String  @id @default(cuid())
  salesOrderItemId String
  componentId     String  // Product ID of the component
  quantity        Int
  unitPrice       Decimal @db.Decimal(10, 2)
  total           Decimal @db.Decimal(10, 2)

  // Relations
  salesOrderItem  SalesOrderItem @relation(fields: [salesOrderItemId], references: [id], onDelete: Cascade)
  component       Product        @relation("ComponentUsage", fields: [componentId], references: [id])

  @@map("sales_order_custom_components")
}

model PurchaseOrder {
  id          String      @id @default(cuid())
  orderNumber String      @unique
  supplierId  String
  userId      String
  branchId    String
  orderDate   DateTime    @default(now())
  expectedDate DateTime?
  receivedDate DateTime?
  status      OrderStatus @default(DRAFT)
  subtotal    Decimal     @db.Decimal(10, 2)
  taxAmount   Decimal     @db.Decimal(10, 2) @default(0)
  discount    Decimal     @db.Decimal(10, 2) @default(0)
  total       Decimal     @db.Decimal(10, 2)
  paidAmount  Decimal     @db.Decimal(10, 2) @default(0)
  remainingAmount Decimal @db.Decimal(10, 2) @default(0)
  paymentStatus PaymentStatus @default(PENDING)
  notes       String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  supplier    Customer @relation(fields: [supplierId], references: [id])
  user        User     @relation(fields: [userId], references: [id])
  branch      Branch   @relation(fields: [branchId], references: [id])
  items       PurchaseOrderItem[]
  payments    Payment[]

  @@map("purchase_orders")
}

model PurchaseOrderItem {
  id              String  @id @default(cuid())
  purchaseOrderId String
  productId       String
  quantity        Int
  unitPrice       Decimal @db.Decimal(10, 2)
  discount        Decimal @db.Decimal(10, 2) @default(0)
  total           Decimal @db.Decimal(10, 2)

  // Relations
  purchaseOrder   PurchaseOrder @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  product         Product       @relation(fields: [productId], references: [id])

  @@map("purchase_order_items")
}

model InventoryAdjustment {
  id          String    @id @default(cuid())
  productId   String
  userId      String
  type        String    // 'IN' or 'OUT'
  quantity    Int
  reason      String
  notes       String?
  createdAt   DateTime  @default(now())

  // Relations
  product     Product @relation(fields: [productId], references: [id])
  user        User    @relation(fields: [userId], references: [id])

  @@map("inventory_adjustments")
}

model MaintenanceOrder {
  id            String            @id @default(cuid())
  orderNumber   String            @unique
  customerId    String
  userId        String
  branchId      String
  deviceType    String
  deviceModel   String
  serialNumber  String?
  problem       String
  problemAr     String
  diagnosis     String?
  diagnosisAr   String?
  status        MaintenanceStatus @default(RECEIVED)
  estimatedCost Decimal?          @db.Decimal(10, 2)
  actualCost    Decimal?          @db.Decimal(10, 2)
  paidAmount    Decimal           @db.Decimal(10, 2) @default(0)
  remainingAmount Decimal         @db.Decimal(10, 2) @default(0)
  paymentStatus PaymentStatus     @default(PENDING)
  receivedDate  DateTime          @default(now())
  completedDate DateTime?
  deliveredDate DateTime?
  notes         String?
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt

  // Relations
  customer      Customer @relation(fields: [customerId], references: [id])
  user          User     @relation(fields: [userId], references: [id])
  branch        Branch   @relation(fields: [branchId], references: [id])
  payments      Payment[]

  @@map("maintenance_orders")
}

model CustomProductConfiguration {
  id            String   @id @default(cuid())
  baseProductId String?
  baseProduct   Product? @relation(fields: [baseProductId], references: [id])
  customerId    String?
  customer      Customer? @relation(fields: [customerId], references: [id])
  branchId      String
  branch        Branch   @relation(fields: [branchId], references: [id])
  components    String   // JSON string of selected components
  quantities    String   // JSON string of component quantities
  assemblyFee   Decimal  @db.Decimal(10, 2) @default(0)
  serviceFee    Decimal  @db.Decimal(10, 2) @default(0)
  totalPrice    Decimal  @db.Decimal(10, 2)
  mode          String   @default("sales") // "sales" or "purchase"
  status        String   @default("DRAFT") // "DRAFT", "CONFIRMED", "COMPLETED"
  createdBy     String
  createdUser   User     @relation(fields: [createdBy], references: [id])
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("custom_product_configurations")
}

// عروض الأسعار - لا تؤثر على المخزون
model Quote {
  id          String      @id @default(cuid())
  quoteNumber String      @unique
  customerId  String
  userId      String
  branchId    String
  quoteDate   DateTime    @default(now())
  validUntil  DateTime    // تاريخ انتهاء صلاحية العرض
  status      QuoteStatus @default(DRAFT)
  subtotal    Decimal     @db.Decimal(10, 2)
  taxAmount   Decimal     @db.Decimal(10, 2) @default(0)
  discount    Decimal     @db.Decimal(10, 2) @default(0)
  total       Decimal     @db.Decimal(10, 2)
  notes       String?
  convertedToOrderId String? // ID of sales order if converted
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  customer    Customer @relation(fields: [customerId], references: [id])
  user        User     @relation(fields: [userId], references: [id])
  branch      Branch   @relation(fields: [branchId], references: [id])
  items       QuoteItem[]

  @@map("quotes")
}

model QuoteItem {
  id            String  @id @default(cuid())
  quoteId       String
  productId     String
  quantity      Int
  unitPrice     Decimal @db.Decimal(10, 2)
  discount      Decimal @db.Decimal(10, 2) @default(0)
  total         Decimal @db.Decimal(10, 2)
  isCustomizable Boolean @default(false)
  customConfiguration Json?
  assemblyFee   Decimal @db.Decimal(10, 2) @default(0)
  serviceFee    Decimal @db.Decimal(10, 2) @default(0)

  // Relations
  quote         Quote   @relation(fields: [quoteId], references: [id], onDelete: Cascade)
  product       Product @relation(fields: [productId], references: [id])

  @@map("quote_items")
}

// الفواتير - تخصم من المخزون نهائياً
model Invoice {
  id          String        @id @default(cuid())
  invoiceNumber String      @unique
  customerId  String
  userId      String
  branchId    String
  salesOrderId String?     // ID of sales order if converted from order
  invoiceDate DateTime     @default(now())
  dueDate     DateTime?
  status      InvoiceStatus @default(DRAFT)
  subtotal    Decimal      @db.Decimal(10, 2)
  taxAmount   Decimal      @db.Decimal(10, 2) @default(0)
  discount    Decimal      @db.Decimal(10, 2) @default(0)
  total       Decimal      @db.Decimal(10, 2)
  paidAmount  Decimal      @db.Decimal(10, 2) @default(0)
  remainingAmount Decimal  @db.Decimal(10, 2) @default(0)
  paymentStatus PaymentStatus @default(PENDING)
  notes       String?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Relations
  customer    Customer @relation(fields: [customerId], references: [id])
  user        User     @relation(fields: [userId], references: [id])
  branch      Branch   @relation(fields: [branchId], references: [id])
  salesOrder  SalesOrder? @relation(fields: [salesOrderId], references: [id])
  items       InvoiceItem[]
  payments    InvoicePayment[]

  @@map("invoices")
}

model InvoiceItem {
  id            String  @id @default(cuid())
  invoiceId     String
  productId     String
  quantity      Int
  unitPrice     Decimal @db.Decimal(10, 2)
  discount      Decimal @db.Decimal(10, 2) @default(0)
  total         Decimal @db.Decimal(10, 2)
  isCustomizable Boolean @default(false)
  customConfiguration Json?
  assemblyFee   Decimal @db.Decimal(10, 2) @default(0)
  serviceFee    Decimal @db.Decimal(10, 2) @default(0)

  // Relations
  invoice       Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  product       Product @relation(fields: [productId], references: [id])

  @@map("invoice_items")
}

model InvoicePayment {
  id              String @id @default(cuid())
  invoiceId       String
  method          PaymentMethod
  amount          Decimal @db.Decimal(10, 2)
  reference       String?
  installmentPlan String?
  status          PaymentStatus @default(PENDING)
  paidAt          DateTime?
  createdBy       String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  invoice         Invoice @relation(fields: [invoiceId], references: [id])
  creator         User @relation(fields: [createdBy], references: [id])

  @@map("invoice_payments")
}
