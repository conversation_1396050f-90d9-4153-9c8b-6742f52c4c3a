import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useQuery } from 'react-query';
import axios from 'axios';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  ArrowsRightLeftIcon,
  ExclamationTriangleIcon,
  CubeIcon,
  BuildingStorefrontIcon,
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/LoadingSpinner';

export default function Inventory() {
  const { t } = useTranslation('common');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBranch, setSelectedBranch] = useState('all');
  const [showLowStock, setShowLowStock] = useState(false);

  // Fetch inventory data
  const { data: inventoryData, isLoading, error, refetch } = useQuery(
    ['inventory', searchTerm, selectedBranch, showLowStock],
    async () => {
      const params = new URLSearchParams({
        search: searchTerm,
        branchId: selectedBranch,
        lowStock: showLowStock.toString(),
      });

      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/reports/inventory?${params}`);
      return response.data;
    },
    {
      keepPreviousData: true,
    }
  );

  // Fetch branches for filter
  const { data: branchesData } = useQuery('branches', async () => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/branches`);
    return response.data;
  });

  const inventory = inventoryData?.inventory || [];
  const metrics = inventoryData?.metrics || {};
  const branches = branchesData?.branches || [];

  const handleSearch = (e) => {
    e.preventDefault();
    refetch();
  };

  const getStockStatusColor = (item) => {
    if (item.quantity === 0) {
      return 'bg-red-100 text-red-800';
    } else if (item.quantity <= item.minStock) {
      return 'bg-yellow-100 text-yellow-800';
    } else if (item.quantity > item.maxStock) {
      return 'bg-blue-100 text-blue-800';
    }
    return 'bg-green-100 text-green-800';
  };

  const getStockStatus = (item) => {
    if (item.quantity === 0) {
      return 'Out of Stock';
    } else if (item.quantity <= item.minStock) {
      return 'Low Stock';
    } else if (item.quantity > item.maxStock) {
      return 'Overstock';
    }
    return 'In Stock';
  };

  if (isLoading && !inventory.length) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h3 className="mt-2 text-sm font-medium text-gray-900">{t('common.error')}</h3>
        <p className="mt-1 text-sm text-gray-500">Failed to load inventory data</p>
        <button
          onClick={() => refetch()}
          className="mt-4 btn-primary"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{t('inventory.title')}</h1>
          <p className="mt-1 text-sm text-gray-600">
            مراقبة مستويات المخزون عبر جميع الفروع
          </p>
        </div>
        <div className="flex space-x-3 rtl:space-x-reverse">
          <button className="btn-secondary">
            <ArrowsRightLeftIcon className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2" />
            {t('inventory.transfer')}
          </button>
          <button className="btn-primary">
            <PlusIcon className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2" />
            {t('inventory.adjustment')}
          </button>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CubeIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {t('inventory.totalProducts')}
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {metrics.totalProducts || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {t('inventory.lowStock')}
                  </dt>
                  <dd className="text-lg font-medium text-yellow-600">
                    {metrics.lowStockItems || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-400" />
              </div>
              <div className="ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {t('inventory.outOfStock')}
                  </dt>
                  <dd className="text-lg font-medium text-red-600">
                    {metrics.outOfStockItems || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BuildingStorefrontIcon className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {t('inventory.totalValue')}
                  </dt>
                  <dd className="text-lg font-medium text-green-600">
                    ${(metrics.totalValue || 0).toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder={t('common.search')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input pl-10 rtl:pl-3 rtl:pr-10"
              />
            </div>
          </div>
          <select
            value={selectedBranch}
            onChange={(e) => setSelectedBranch(e.target.value)}
            className="form-input"
          >
            <option value="all">{t('inventory.allBranches')}</option>
            {branches.map((branch) => (
              <option key={branch.id} value={branch.id}>
                {branch.name}
              </option>
            ))}
          </select>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={showLowStock}
              onChange={(e) => setShowLowStock(e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <span className="ml-2 rtl:ml-0 rtl:mr-2 text-sm text-gray-700">
              {t('inventory.lowStockAlert')}
            </span>
          </label>
          <button type="submit" className="btn-primary">
            {t('common.search')}
          </button>
        </form>
      </div>

      {/* Inventory Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="table">
            <thead>
              <tr>
                <th>{t('inventory.product')}</th>
                <th>{t('inventory.branch')}</th>
                <th>{t('inventory.currentStock')}</th>
                <th>{t('inventory.minStock')}</th>
                <th>{t('inventory.maxStock')}</th>
                <th>{t('inventory.stockValue')}</th>
                <th>{t('common.status')}</th>
                <th>{t('common.actions')}</th>
              </tr>
            </thead>
            <tbody>
              {inventory.map((item) => (
                <tr key={`${item.branchId}-${item.productId}`}>
                  <td>
                    <div>
                      <div className="font-medium text-gray-900">{item.product.name}</div>
                      <div className="text-sm text-gray-500">{item.product.nameAr}</div>
                      <div className="text-xs text-gray-400">{item.product.code}</div>
                    </div>
                  </td>
                  <td>
                    <div>
                      <div className="font-medium text-gray-900">{item.branch.name}</div>
                      <div className="text-sm text-gray-500">{item.branch.nameAr}</div>
                    </div>
                  </td>
                  <td>
                    <span className="font-medium">
                      {item.quantity} {item.product.unit}
                    </span>
                  </td>
                  <td>{item.minStock}</td>
                  <td>{item.maxStock}</td>
                  <td>
                    <span className="font-medium">
                      ${(item.quantity * item.product.costPrice).toFixed(2)}
                    </span>
                  </td>
                  <td>
                    <span className={`badge ${getStockStatusColor(item)}`}>
                      {getStockStatus(item)}
                    </span>
                  </td>
                  <td>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <button
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title="Adjust Stock"
                      >
                        <PlusIcon className="h-4 w-4" />
                      </button>
                      <button
                        className="p-1 text-gray-400 hover:text-green-600"
                        title="Transfer"
                      >
                        <ArrowsRightLeftIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Empty State */}
        {inventory.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">لا يوجد مخزون</h3>
            <p className="mt-1 text-sm text-gray-500">
              لا توجد منتجات تطابق الفلاتر المحددة
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  };
}
