"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/store-pos",{

/***/ "./pages/store-pos.js":
/*!****************************!*\
  !*** ./pages/store-pos.js ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StorePOS; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _components_sales_OrganizedStorePOS__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/sales/OrganizedStorePOS */ \"./components/sales/OrganizedStorePOS.js\");\n/* harmony import */ var _components_sales_PaymentManagerAdvanced__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/sales/PaymentManagerAdvanced */ \"./components/sales/PaymentManagerAdvanced.js\");\n/* harmony import */ var _components_sales_QuickCustomerModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/sales/QuickCustomerModal */ \"./components/sales/QuickCustomerModal.js\");\n/* harmony import */ var _components_sales_ComputerBuilderAdvanced__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/sales/ComputerBuilderAdvanced */ \"./components/sales/ComputerBuilderAdvanced.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction StorePOS() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Main POS states\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customer, setCustomer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Modal states\n    const [showPayment, setShowPayment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCustomerModal, setShowCustomerModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showComputerBuilder, setShowComputerBuilder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [builderType, setBuilderType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"DESKTOP\"); // DESKTOP or LAPTOP\n    // Sale type\n    const [saleType, setSaleType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"DIRECT\"); // DIRECT, CUSTOM_ORDER, QUOTE\n    // Customer search\n    const [customerSearch, setCustomerSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Product search and filters\n    const [productSearch, setProductSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    // Daily summary\n    const [dailySummary, setDailySummary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadProducts();\n        loadCustomers();\n        loadCategories();\n        loadDailySummary();\n    }, []);\n    const loadProducts = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].get(\"/api/products\");\n            setProducts(response.data.products || response.data);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(\"خطأ في تحميل المنتجات\");\n        }\n    };\n    const loadCustomers = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].get(\"/api/customers\");\n            setCustomers(response.data.customers || response.data);\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(\"خطأ في تحميل العملاء\");\n        }\n    };\n    const loadCategories = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].get(\"/api/categories\");\n            setCategories(response.data.categories || response.data || []);\n        } catch (error) {\n            console.error(\"Error loading categories:\", error);\n        }\n    };\n    const loadDailySummary = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].get(\"/api/store-sales/daily-summary\");\n            setDailySummary(response.data);\n        } catch (error) {\n            console.error(\"Error loading daily summary:\", error);\n        }\n    };\n    // Customer functions\n    const searchCustomers = (phone)=>{\n        if (phone.length < 3) return [];\n        return customers.filter((c)=>c.phone.includes(phone) || c.name.toLowerCase().includes(phone.toLowerCase()) || c.nameAr && c.nameAr.includes(phone));\n    };\n    const selectCustomer = (selectedCustomer)=>{\n        setCustomer(selectedCustomer);\n        setCustomerSearch(selectedCustomer.phone);\n    };\n    const clearCustomer = ()=>{\n        setCustomer(null);\n        setCustomerSearch(\"\");\n    };\n    const handleCustomerCreated = (newCustomer)=>{\n        setCustomers([\n            ...customers,\n            newCustomer\n        ]);\n        selectCustomer(newCustomer);\n        setShowCustomerModal(false);\n    };\n    // Cart functions\n    const addToCart = function(product) {\n        let quantity = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, buildDetails = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : null;\n        const existingIndex = cart.findIndex((item)=>item.productId === product.id && JSON.stringify(item.buildDetails) === JSON.stringify(buildDetails));\n        if (existingIndex >= 0) {\n            const newCart = [\n                ...cart\n            ];\n            newCart[existingIndex].quantity += quantity;\n            newCart[existingIndex].total = newCart[existingIndex].quantity * newCart[existingIndex].unitPrice;\n            setCart(newCart);\n        } else {\n            const unitPrice = buildDetails ? buildDetails.totalPrice : parseFloat(product.unitPrice || product.basePrice || 0);\n            const newItem = {\n                id: Date.now() + Math.random(),\n                productId: product.id,\n                productName: product.nameAr || product.name,\n                productCode: product.code,\n                quantity,\n                unitPrice,\n                total: quantity * unitPrice,\n                buildDetails,\n                hasTax: false,\n                taxRate: 14,\n                discount: 0\n            };\n            setCart([\n                ...cart,\n                newItem\n            ]);\n        }\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success(\"تم إضافة المنتج للسلة\");\n    };\n    const updateCartItem = (itemId, field, value)=>{\n        setCart(cart.map((item)=>{\n            if (item.id === itemId) {\n                const updatedItem = {\n                    ...item,\n                    [field]: value\n                };\n                // Recalculate total\n                const quantity = parseFloat(updatedItem.quantity) || 0;\n                const unitPrice = parseFloat(updatedItem.unitPrice) || 0;\n                const discount = parseFloat(updatedItem.discount) || 0;\n                const taxRate = parseFloat(updatedItem.taxRate) || 0;\n                const subtotal = quantity * unitPrice;\n                const discountAmount = subtotal * (discount / 100);\n                const afterDiscount = subtotal - discountAmount;\n                const taxAmount = updatedItem.hasTax ? afterDiscount * (taxRate / 100) : 0;\n                updatedItem.total = afterDiscount + taxAmount;\n                updatedItem.subtotal = subtotal;\n                updatedItem.discountAmount = discountAmount;\n                updatedItem.taxAmount = taxAmount;\n                return updatedItem;\n            }\n            return item;\n        }));\n    };\n    const removeFromCart = (itemId)=>{\n        setCart(cart.filter((item)=>item.id !== itemId));\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success(\"تم حذف المنتج من السلة\");\n    };\n    const clearCart = ()=>{\n        setCart([]);\n        setCustomer(null);\n        setCustomerSearch(\"\");\n    };\n    // Handle customization\n    const handleCustomizationSave = (customizationData)=>{\n        if (currentItemIndex !== null) {\n            // Update existing item\n            updateCartItem(cart[currentItemIndex].id, \"unitPrice\", customizationData.totalPrice);\n            updateCartItem(cart[currentItemIndex].id, \"customization\", customizationData);\n        } else {\n            // Add new item\n            addToCart(selectedProduct, 1, customizationData);\n        }\n        setShowCustomizer(false);\n        setSelectedProduct(null);\n        setCurrentItemIndex(null);\n    };\n    // Calculate totals\n    const calculateTotals = ()=>{\n        const subtotal = cart.reduce((sum, item)=>sum + (item.subtotal || item.total), 0);\n        const totalDiscount = cart.reduce((sum, item)=>sum + (item.discountAmount || 0), 0);\n        const totalTax = cart.reduce((sum, item)=>sum + (item.taxAmount || 0), 0);\n        const total = cart.reduce((sum, item)=>sum + item.total, 0);\n        return {\n            subtotal,\n            totalDiscount,\n            totalTax,\n            total,\n            itemCount: cart.reduce((sum, item)=>sum + item.quantity, 0)\n        };\n    };\n    // Handle payment completion\n    const handlePaymentComplete = async (paymentData)=>{\n        try {\n            const totals = calculateTotals();\n            const saleData = {\n                customerId: (customer === null || customer === void 0 ? void 0 : customer.id) || null,\n                items: cart,\n                payments: paymentData.payments,\n                notes: \"\",\n                subtotal: totals.subtotal,\n                total: totals.total\n            };\n            let response;\n            let successMessage;\n            switch(saleType){\n                case \"DIRECT\":\n                    response = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].post(\"/api/store-sales/direct-sale\", saleData);\n                    successMessage = \"تم إتمام البيع بنجاح\";\n                    break;\n                case \"CUSTOM_ORDER\":\n                    if (!customer) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(\"العميل مطلوب للطلبات المخصصة\");\n                        return;\n                    }\n                    response = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].post(\"/api/store-sales/custom-order\", {\n                        ...saleData,\n                        expectedDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now\n                    });\n                    successMessage = \"تم إنشاء الطلب المخصص بنجاح\";\n                    break;\n                case \"QUOTE\":\n                    response = await axios__WEBPACK_IMPORTED_MODULE_10__[\"default\"].post(\"/api/store-sales/quick-quote\", {\n                        ...saleData,\n                        validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now\n                    });\n                    successMessage = \"تم إنشاء عرض السعر بنجاح\";\n                    break;\n                default:\n                    throw new Error(\"نوع البيع غير صحيح\");\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success(successMessage);\n            // Clear cart and reset\n            clearCart();\n            setShowPayment(false);\n            // Reload daily summary\n            loadDailySummary();\n            // Optionally print receipt or redirect\n            console.log(\"Sale completed:\", response.data);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Payment completion error:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"خطأ في إتمام العملية\");\n        }\n    };\n    // Filter products\n    const filteredProducts = products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(productSearch.toLowerCase()) || product.nameAr && product.nameAr.includes(productSearch) || product.code.toLowerCase().includes(productSearch.toLowerCase());\n        const matchesCategory = selectedCategory === \"all\" || product.categoryId === selectedCategory;\n        return matchesSearch && matchesCategory && product.isActive !== false;\n    });\n    const totals = calculateTotals();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"نقطة البيع - متجر الكمبيوتر\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\store-pos.js\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\store-pos.js\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComputerStorePOS, {\n                        cart: cart,\n                        customer: customer,\n                        products: filteredProducts,\n                        customers: customers,\n                        categories: categories,\n                        dailySummary: dailySummary,\n                        saleType: saleType,\n                        setSaleType: setSaleType,\n                        customerSearch: customerSearch,\n                        setCustomerSearch: setCustomerSearch,\n                        productSearch: productSearch,\n                        setProductSearch: setProductSearch,\n                        selectedCategory: selectedCategory,\n                        setSelectedCategory: setSelectedCategory,\n                        searchCustomers: searchCustomers,\n                        selectCustomer: selectCustomer,\n                        clearCustomer: clearCustomer,\n                        addToCart: addToCart,\n                        updateCartItem: updateCartItem,\n                        removeFromCart: removeFromCart,\n                        clearCart: clearCart,\n                        calculateTotals: calculateTotals,\n                        onShowPayment: ()=>setShowPayment(true),\n                        onShowCustomerModal: ()=>setShowCustomerModal(true),\n                        onShowCustomizer: function(product) {\n                            let itemIndex = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n                            setSelectedProduct(product);\n                            setCurrentItemIndex(itemIndex);\n                            setShowCustomizer(true);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\store-pos.js\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_PaymentManagerAdvanced__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        isOpen: showPayment,\n                        onClose: ()=>setShowPayment(false),\n                        totalAmount: totals.total,\n                        customer: customer,\n                        onPaymentComplete: handlePaymentComplete,\n                        saleType: saleType\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\store-pos.js\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_QuickCustomerModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        isOpen: showCustomerModal,\n                        onClose: ()=>setShowCustomerModal(false),\n                        onCustomerCreated: handleCustomerCreated,\n                        initialPhone: customerSearch\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\store-pos.js\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCustomizerAdvanced, {\n                        isOpen: showCustomizer,\n                        onClose: ()=>{\n                            setShowCustomizer(false);\n                            setSelectedProduct(null);\n                            setCurrentItemIndex(null);\n                        },\n                        product: selectedProduct,\n                        onSave: handleCustomizationSave\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\store-pos.js\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\store-pos.js\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(StorePOS, \"lFiKdS2c3Gh4biAYPe06KP7+OKs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = StorePOS;\nvar _c;\n$RefreshReg$(_c, \"StorePOS\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/store-pos.js\n"));

/***/ })

});