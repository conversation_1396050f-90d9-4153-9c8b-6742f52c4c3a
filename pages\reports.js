import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useQuery } from 'react-query';
import axios from 'axios';
import {
  ChartBarIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  ShoppingCartIcon,
  ShoppingBagIcon,
  WrenchScrewdriverIcon,
  UsersIcon,
  CubeIcon,
  CalendarIcon,
  ArrowDownTrayIcon,
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/LoadingSpinner';

export default function Reports() {
  const { t } = useTranslation('common');
  const [selectedReport, setSelectedReport] = useState('sales');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [selectedBranch, setSelectedBranch] = useState('all');

  // Fetch branches for filter
  const { data: branchesData } = useQuery('branches', async () => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/branches`);
    return response.data;
  });

  // Fetch report data
  const { data: reportData, isLoading, error, refetch } = useQuery(
    ['reports', selectedReport, dateFrom, dateTo, selectedBranch],
    async () => {
      if (!dateFrom || !dateTo) return null;
      
      const params = new URLSearchParams({
        dateFrom,
        dateTo,
        branchId: selectedBranch,
      });

      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/reports/${selectedReport}?${params}`);
      return response.data;
    },
    {
      enabled: !!(dateFrom && dateTo),
      keepPreviousData: true,
    }
  );

  const branches = branchesData?.branches || [];

  const reportTypes = [
    {
      id: 'sales',
      name: t('reports.salesReport'),
      nameAr: 'تقرير المبيعات',
      icon: ShoppingCartIcon,
      color: 'bg-blue-500',
      description: 'Sales performance and revenue analysis'
    },
    {
      id: 'purchases',
      name: t('reports.purchaseReport'),
      nameAr: 'تقرير المشتريات',
      icon: ShoppingBagIcon,
      color: 'bg-green-500',
      description: 'Purchase orders and supplier analysis'
    },
    {
      id: 'inventory',
      name: t('reports.inventoryReport'),
      nameAr: 'تقرير المخزون',
      icon: CubeIcon,
      color: 'bg-yellow-500',
      description: 'Stock levels and inventory valuation'
    },
    {
      id: 'maintenance',
      name: t('reports.maintenanceReport'),
      nameAr: 'تقرير الصيانة',
      icon: WrenchScrewdriverIcon,
      color: 'bg-purple-500',
      description: 'Maintenance services and performance'
    },
    {
      id: 'financial',
      name: t('reports.financialReport'),
      nameAr: 'التقرير المالي',
      icon: CurrencyDollarIcon,
      color: 'bg-indigo-500',
      description: 'Cash flow and financial analysis'
    },
    {
      id: 'customers',
      name: t('reports.customerReport'),
      nameAr: 'تقرير العملاء',
      icon: UsersIcon,
      color: 'bg-pink-500',
      description: 'Customer behavior and revenue analysis'
    }
  ];

  const handleGenerateReport = () => {
    if (dateFrom && dateTo) {
      refetch();
    }
  };

  const handleExportReport = (format) => {
    // TODO: Implement export functionality
    console.log(`Exporting ${selectedReport} report as ${format}`);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{t('reports.title')}</h1>
          <p className="mt-1 text-sm text-gray-600">
            إنشاء تقارير أعمال شاملة وتحليلات
          </p>
        </div>
      </div>

      {/* Report Type Selection */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-lg font-medium text-gray-900 mb-4">{t('reports.reportType')}</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {reportTypes.map((report) => (
            <div
              key={report.id}
              className={`relative rounded-lg border-2 cursor-pointer transition-all ${
                selectedReport === report.id
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setSelectedReport(report.id)}
            >
              <div className="p-4">
                <div className="flex items-center">
                  <div className={`flex-shrink-0 p-2 rounded-lg ${report.color}`}>
                    <report.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-4 rtl:ml-0 rtl:mr-4">
                    <h3 className="text-sm font-medium text-gray-900">{report.name}</h3>
                    <p className="text-xs text-gray-500">{report.description}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Report Filters */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-lg font-medium text-gray-900 mb-4">فلاتر التقرير</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <CalendarIcon className="h-4 w-4 inline mr-1 rtl:mr-0 rtl:ml-1" />
              {t('reports.from')}
            </label>
            <input
              type="date"
              value={dateFrom}
              onChange={(e) => setDateFrom(e.target.value)}
              className="form-input"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <CalendarIcon className="h-4 w-4 inline mr-1 rtl:mr-0 rtl:ml-1" />
              {t('reports.to')}
            </label>
            <input
              type="date"
              value={dateTo}
              onChange={(e) => setDateTo(e.target.value)}
              className="form-input"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Branch
            </label>
            <select
              value={selectedBranch}
              onChange={(e) => setSelectedBranch(e.target.value)}
              className="form-input"
            >
              <option value="all">All Branches</option>
              {branches.map((branch) => (
                <option key={branch.id} value={branch.id}>
                  {branch.name}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-end">
            <button
              onClick={handleGenerateReport}
              disabled={!dateFrom || !dateTo}
              className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChartBarIcon className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2" />
              {t('reports.generate')}
            </button>
          </div>
        </div>
      </div>

      {/* Report Results */}
      {isLoading && (
        <div className="bg-white p-12 rounded-lg shadow text-center">
          <LoadingSpinner size="large" />
          <p className="mt-4 text-gray-600">Generating report...</p>
        </div>
      )}

      {error && (
        <div className="bg-white p-12 rounded-lg shadow text-center">
          <div className="text-red-600">
            <DocumentTextIcon className="h-12 w-12 mx-auto mb-4" />
            <h3 className="text-lg font-medium">Error generating report</h3>
            <p className="mt-2 text-sm">Please try again or contact support.</p>
          </div>
        </div>
      )}

      {reportData && (
        <div className="bg-white rounded-lg shadow">
          {/* Report Header */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-lg font-medium text-gray-900">
                  {reportTypes.find(r => r.id === selectedReport)?.name}
                </h2>
                <p className="text-sm text-gray-500">
                  {dateFrom} to {dateTo}
                  {selectedBranch !== 'all' && ` • ${branches.find(b => b.id === selectedBranch)?.name}`}
                </p>
              </div>
              <div className="flex space-x-2 rtl:space-x-reverse">
                <button
                  onClick={() => handleExportReport('pdf')}
                  className="btn-secondary btn-sm"
                >
                  <ArrowDownTrayIcon className="h-4 w-4 mr-1 rtl:mr-0 rtl:ml-1" />
                  PDF
                </button>
                <button
                  onClick={() => handleExportReport('excel')}
                  className="btn-secondary btn-sm"
                >
                  <ArrowDownTrayIcon className="h-4 w-4 mr-1 rtl:mr-0 rtl:ml-1" />
                  Excel
                </button>
              </div>
            </div>
          </div>

          {/* Report Content */}
          <div className="p-6">
            {/* Summary Metrics */}
            {reportData.metrics && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {Object.entries(reportData.metrics).map(([key, value]) => (
                  <div key={key} className="bg-gray-50 p-4 rounded-lg">
                    <dt className="text-sm font-medium text-gray-500 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </dt>
                    <dd className="mt-1 text-2xl font-semibold text-gray-900">
                      {typeof value === 'number' && key.toLowerCase().includes('revenue') || key.toLowerCase().includes('cost') || key.toLowerCase().includes('total') && key.toLowerCase().includes('value')
                        ? `$${value.toFixed(2)}`
                        : typeof value === 'number' && key.toLowerCase().includes('rate')
                        ? `${value.toFixed(1)}%`
                        : value}
                    </dd>
                  </div>
                ))}
              </div>
            )}

            {/* Data Table */}
            {reportData.orders && reportData.orders.length > 0 && (
              <div className="overflow-x-auto">
                <table className="table">
                  <thead>
                    <tr>
                      <th>Order Number</th>
                      <th>Date</th>
                      <th>Customer/Supplier</th>
                      <th>Total</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.orders.slice(0, 10).map((order) => (
                      <tr key={order.id}>
                        <td className="font-medium">{order.orderNumber}</td>
                        <td>{new Date(order.orderDate || order.receivedDate).toLocaleDateString()}</td>
                        <td>{order.customer?.name || order.supplier?.name}</td>
                        <td>${(order.total || order.actualCost || 0).toFixed(2)}</td>
                        <td>
                          <span className="badge badge-secondary">{order.status}</span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {reportData.orders.length > 10 && (
                  <div className="mt-4 text-center text-sm text-gray-500">
                    Showing 10 of {reportData.orders.length} records
                  </div>
                )}
              </div>
            )}

            {/* Inventory Data */}
            {reportData.inventory && reportData.inventory.length > 0 && (
              <div className="overflow-x-auto">
                <table className="table">
                  <thead>
                    <tr>
                      <th>Product</th>
                      <th>Branch</th>
                      <th>Current Stock</th>
                      <th>Value</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.inventory.slice(0, 10).map((item, index) => (
                      <tr key={index}>
                        <td>{item.product.name}</td>
                        <td>{item.branch.name}</td>
                        <td>{item.quantity} {item.product.unit}</td>
                        <td>${(item.quantity * item.product.costPrice).toFixed(2)}</td>
                        <td>
                          <span className={`badge ${
                            item.quantity === 0 ? 'badge-danger' :
                            item.quantity <= item.minStock ? 'badge-warning' : 'badge-success'
                          }`}>
                            {item.quantity === 0 ? 'Out of Stock' :
                             item.quantity <= item.minStock ? 'Low Stock' : 'In Stock'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {/* No Data Message */}
            {(!reportData.orders || reportData.orders.length === 0) && 
             (!reportData.inventory || reportData.inventory.length === 0) && (
              <div className="text-center py-12">
                <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No data found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  No data available for the selected date range and filters.
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Empty State */}
      {!dateFrom || !dateTo ? (
        <div className="bg-white p-12 rounded-lg shadow text-center">
          <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Select date range</h3>
          <p className="mt-1 text-sm text-gray-500">
            Choose a date range and report type to generate your report.
          </p>
        </div>
      ) : null}
    </div>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  };
}
