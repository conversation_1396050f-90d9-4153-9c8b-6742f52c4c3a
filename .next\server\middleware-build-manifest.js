self.__BUILD_MANIFEST = {
  "polyfillFiles": [
    "static/chunks/polyfills.js"
  ],
  "devFiles": [
    "static/chunks/react-refresh.js"
  ],
  "ampDevFiles": [],
  "lowPriorityFiles": [],
  "rootMainFiles": [],
  "pages": {
    "/_app": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/_app.js"
    ],
    "/_error": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/_error.js"
    ],
    "/accounting": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/accounting.js"
    ],
    "/customers": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/customers.js"
    ],
    "/inventory": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/inventory.js"
    ],
    "/maintenance": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/maintenance.js"
    ],
    "/purchases": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/purchases.js"
    ],
    "/sales": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/sales.js"
    ],
    "/settings": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/settings.js"
    ]
  },
  "ampFirstPages": []
};
self.__BUILD_MANIFEST.lowPriorityFiles = [
"/static/" + process.env.__NEXT_BUILD_ID + "/_buildManifest.js",
,"/static/" + process.env.__NEXT_BUILD_ID + "/_ssgManifest.js",

];