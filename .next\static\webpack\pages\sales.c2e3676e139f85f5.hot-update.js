"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "./components/sales/QuoteModal.js":
/*!****************************************!*\
  !*** ./components/sales/QuoteModal.js ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ QuoteModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=PlusIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction QuoteModal(param) {\n    let { isOpen, onClose, onSave, quote = null } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: \"\",\n        validUntil: \"\",\n        notes: \"\",\n        items: []\n    });\n    // Load customers and products\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            loadCustomers();\n            loadProducts();\n            if (quote) {\n                setFormData({\n                    customerId: quote.customerId || \"\",\n                    validUntil: quote.validUntil ? quote.validUntil.split(\"T\")[0] : \"\",\n                    notes: quote.notes || \"\",\n                    items: quote.items || []\n                });\n            } else {\n                // Set default valid until date (7 days from now)\n                const validUntil = new Date();\n                validUntil.setDate(validUntil.getDate() + 7);\n                setFormData((prev)=>({\n                        ...prev,\n                        validUntil: validUntil.toISOString().split(\"T\")[0]\n                    }));\n            }\n        }\n    }, [\n        isOpen,\n        quote\n    ]);\n    const loadCustomers = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/customers\"));\n            setCustomers(response.data.customers || []);\n        } catch (error) {\n            console.error(\"Error loading customers:\", error);\n        }\n    };\n    const loadProducts = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/products\"));\n            setProducts(response.data.products || []);\n        } catch (error) {\n            console.error(\"Error loading products:\", error);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const addItem = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: [\n                    ...prev.items,\n                    {\n                        productId: \"\",\n                        productName: \"\",\n                        quantity: 1,\n                        unitPrice: 0,\n                        discount: 0,\n                        taxRate: 14,\n                        hasTax: true,\n                        total: 0,\n                        isCustomized: false,\n                        customizations: null,\n                        customizationDetails: []\n                    }\n                ]\n            }));\n    };\n    const removeItem = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: prev.items.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateItem = (index, field, value)=>{\n        setFormData((prev)=>{\n            const newItems = [\n                ...prev.items\n            ];\n            newItems[index] = {\n                ...newItems[index],\n                [field]: value\n            };\n            // Auto-calculate total\n            if (field === \"productId\") {\n                const product = products.find((p)=>p.id === value);\n                if (product) {\n                    newItems[index].unitPrice = parseFloat(product.unitPrice);\n                }\n            }\n            if (field === \"quantity\" || field === \"unitPrice\" || field === \"discount\") {\n                const item = newItems[index];\n                const subtotal = (parseFloat(item.quantity) || 0) * (parseFloat(item.unitPrice) || 0);\n                const discountAmount = subtotal * ((parseFloat(item.discount) || 0) / 100);\n                newItems[index].total = subtotal - discountAmount;\n            }\n            return {\n                ...prev,\n                items: newItems\n            };\n        });\n    };\n    const calculateTotals = ()=>{\n        const subtotal = formData.items.reduce((sum, item)=>sum + (parseFloat(item.total) || 0), 0);\n        const taxAmount = subtotal * 0.14; // 14% tax\n        const total = subtotal + taxAmount;\n        return {\n            subtotal,\n            taxAmount,\n            total\n        };\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.customerId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى اختيار العميل\");\n            return;\n        }\n        if (formData.items.length === 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى إضافة عنصر واحد على الأقل\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const { subtotal, taxAmount, total } = calculateTotals();\n            const quoteData = {\n                ...formData,\n                subtotal,\n                taxAmount,\n                total,\n                status: \"DRAFT\"\n            };\n            const response = quote ? await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].put(\"\".concat(\"http://localhost:3070\", \"/api/quotes/\").concat(quote.id), quoteData) : await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(\"\".concat(\"http://localhost:3070\", \"/api/quotes\"), quoteData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.data.message || (quote ? \"تم تحديث عرض السعر\" : \"تم إنشاء عرض السعر\"));\n            onSave(response.data.quote);\n            onClose();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"حدث خطأ\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    const { subtotal, taxAmount, total } = calculateTotals();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: quote ? \"تعديل عرض السعر\" : \"عرض سعر جديد\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.XMarkIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"العميل *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            name: \"customerId\",\n                                            value: formData.customerId,\n                                            onChange: handleChange,\n                                            className: \"form-input\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"اختر العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: customer.id,\n                                                        children: customer.name\n                                                    }, customer.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"صالح حتى *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            name: \"validUntil\",\n                                            value: formData.validUntil,\n                                            onChange: handleChange,\n                                            className: \"form-input\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"العناصر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addItem,\n                                            className: \"btn-primary flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.PlusIcon, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"إضافة عنصر\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: formData.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-12 gap-4 items-end p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"المنتج\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: item.productId,\n                                                            onChange: (e)=>updateItem(index, \"productId\", e.target.value),\n                                                            className: \"form-input\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر المنتج\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                    lineNumber: 246,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: product.id,\n                                                                        children: product.name\n                                                                    }, product.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"الكمية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.quantity,\n                                                            onChange: (e)=>updateItem(index, \"quantity\", e.target.value),\n                                                            className: \"form-input\",\n                                                            min: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"السعر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.unitPrice,\n                                                            onChange: (e)=>updateItem(index, \"unitPrice\", e.target.value),\n                                                            className: \"form-input\",\n                                                            step: \"0.01\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"خصم %\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.discount,\n                                                            onChange: (e)=>updateItem(index, \"discount\", e.target.value),\n                                                            className: \"form-input\",\n                                                            min: \"0\",\n                                                            max: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"الإجمالي\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900 py-2\",\n                                                            children: [\n                                                                \"$\",\n                                                                (item.total || 0).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>removeItem(index),\n                                                        className: \"text-red-600 hover:text-red-800\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.TrashIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"المجموع الفرعي:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    subtotal.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"الضريبة (14%):\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    taxAmount.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between font-bold text-lg border-t pt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"الإجمالي:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"form-label\",\n                                    children: \"ملاحظات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    name: \"notes\",\n                                    value: formData.notes,\n                                    onChange: handleChange,\n                                    className: \"form-input\",\n                                    rows: \"3\",\n                                    placeholder: \"ملاحظات إضافية...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"btn-secondary\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"btn-primary\",\n                                    children: loading ? \"جاري الحفظ...\" : quote ? \"تحديث\" : \"إنشاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n_s(QuoteModal, \"SuIipWdoMEzVJgo0Mdy9Wkj1NxA=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = QuoteModal;\nvar _c;\n$RefreshReg$(_c, \"QuoteModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3NhbGVzL1F1b3RlTW9kYWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBNEM7QUFDRztBQUM4QjtBQUNuRDtBQUNVO0FBRXJCLFNBQVNRLFdBQVcsS0FBeUM7UUFBekMsRUFBRUMsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLE1BQU0sRUFBRUMsUUFBUSxJQUFJLEVBQUUsR0FBekM7O0lBQ2pDLE1BQU0sRUFBRUMsQ0FBQyxFQUFFLEdBQUdYLDZEQUFjQSxDQUFDO0lBQzdCLE1BQU0sQ0FBQ1ksU0FBU0MsV0FBVyxHQUFHZiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNnQixXQUFXQyxhQUFhLEdBQUdqQiwrQ0FBUUEsQ0FBQyxFQUFFO0lBQzdDLE1BQU0sQ0FBQ2tCLFVBQVVDLFlBQVksR0FBR25CLCtDQUFRQSxDQUFDLEVBQUU7SUFFM0MsTUFBTSxDQUFDb0IsVUFBVUMsWUFBWSxHQUFHckIsK0NBQVFBLENBQUM7UUFDdkNzQixZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsT0FBTztRQUNQQyxPQUFPLEVBQUU7SUFDWDtJQUVBLDhCQUE4QjtJQUM5QnhCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSVEsUUFBUTtZQUNWaUI7WUFDQUM7WUFFQSxJQUFJZixPQUFPO2dCQUNUUyxZQUFZO29CQUNWQyxZQUFZVixNQUFNVSxVQUFVLElBQUk7b0JBQ2hDQyxZQUFZWCxNQUFNVyxVQUFVLEdBQUdYLE1BQU1XLFVBQVUsQ0FBQ0ssS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEdBQUc7b0JBQ2hFSixPQUFPWixNQUFNWSxLQUFLLElBQUk7b0JBQ3RCQyxPQUFPYixNQUFNYSxLQUFLLElBQUksRUFBRTtnQkFDMUI7WUFDRixPQUFPO2dCQUNMLGlEQUFpRDtnQkFDakQsTUFBTUYsYUFBYSxJQUFJTTtnQkFDdkJOLFdBQVdPLE9BQU8sQ0FBQ1AsV0FBV1EsT0FBTyxLQUFLO2dCQUMxQ1YsWUFBWVcsQ0FBQUEsT0FBUzt3QkFDbkIsR0FBR0EsSUFBSTt3QkFDUFQsWUFBWUEsV0FBV1UsV0FBVyxHQUFHTCxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7b0JBQ3BEO1lBQ0Y7UUFDRjtJQUNGLEdBQUc7UUFBQ25CO1FBQVFHO0tBQU07SUFFbEIsTUFBTWMsZ0JBQWdCO1FBQ3BCLElBQUk7WUFDRixNQUFNUSxXQUFXLE1BQU01QixpREFBUyxDQUFDLEdBQW1DLE9BQWhDOEIsdUJBQStCLEVBQUM7WUFDcEVuQixhQUFhaUIsU0FBU0ssSUFBSSxDQUFDdkIsU0FBUyxJQUFJLEVBQUU7UUFDNUMsRUFBRSxPQUFPd0IsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtRQUM1QztJQUNGO0lBRUEsTUFBTWIsZUFBZTtRQUNuQixJQUFJO1lBQ0YsTUFBTU8sV0FBVyxNQUFNNUIsaURBQVMsQ0FBQyxHQUFtQyxPQUFoQzhCLHVCQUErQixFQUFDO1lBQ3BFakIsWUFBWWUsU0FBU0ssSUFBSSxDQUFDckIsUUFBUSxJQUFJLEVBQUU7UUFDMUMsRUFBRSxPQUFPc0IsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtRQUMzQztJQUNGO0lBRUEsTUFBTUUsZUFBZSxDQUFDQztRQUNwQixNQUFNLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUdGLEVBQUVHLE1BQU07UUFDaEN6QixZQUFZVyxDQUFBQSxPQUFTO2dCQUNuQixHQUFHQSxJQUFJO2dCQUNQLENBQUNZLEtBQUssRUFBRUM7WUFDVjtJQUNGO0lBRUEsTUFBTUUsVUFBVTtRQUNkMUIsWUFBWVcsQ0FBQUEsT0FBUztnQkFDbkIsR0FBR0EsSUFBSTtnQkFDUFAsT0FBTzt1QkFBSU8sS0FBS1AsS0FBSztvQkFBRTt3QkFDckJ1QixXQUFXO3dCQUNYQyxhQUFhO3dCQUNiQyxVQUFVO3dCQUNWQyxXQUFXO3dCQUNYQyxVQUFVO3dCQUNWQyxTQUFTO3dCQUNUQyxRQUFRO3dCQUNSQyxPQUFPO3dCQUNQQyxjQUFjO3dCQUNkQyxnQkFBZ0I7d0JBQ2hCQyxzQkFBc0IsRUFBRTtvQkFDMUI7aUJBQUU7WUFDSjtJQUNGO0lBRUEsTUFBTUMsYUFBYSxDQUFDQztRQUNsQnZDLFlBQVlXLENBQUFBLE9BQVM7Z0JBQ25CLEdBQUdBLElBQUk7Z0JBQ1BQLE9BQU9PLEtBQUtQLEtBQUssQ0FBQ29DLE1BQU0sQ0FBQyxDQUFDQyxHQUFHQyxJQUFNQSxNQUFNSDtZQUMzQztJQUNGO0lBRUEsTUFBTUksYUFBYSxDQUFDSixPQUFPSyxPQUFPcEI7UUFDaEN4QixZQUFZVyxDQUFBQTtZQUNWLE1BQU1rQyxXQUFXO21CQUFJbEMsS0FBS1AsS0FBSzthQUFDO1lBQ2hDeUMsUUFBUSxDQUFDTixNQUFNLEdBQUc7Z0JBQUUsR0FBR00sUUFBUSxDQUFDTixNQUFNO2dCQUFFLENBQUNLLE1BQU0sRUFBRXBCO1lBQU07WUFFdkQsdUJBQXVCO1lBQ3ZCLElBQUlvQixVQUFVLGFBQWE7Z0JBQ3pCLE1BQU1FLFVBQVVqRCxTQUFTa0QsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUt6QjtnQkFDNUMsSUFBSXNCLFNBQVM7b0JBQ1hELFFBQVEsQ0FBQ04sTUFBTSxDQUFDVCxTQUFTLEdBQUdvQixXQUFXSixRQUFRaEIsU0FBUztnQkFDMUQ7WUFDRjtZQUVBLElBQUljLFVBQVUsY0FBY0EsVUFBVSxlQUFlQSxVQUFVLFlBQVk7Z0JBQ3pFLE1BQU1PLE9BQU9OLFFBQVEsQ0FBQ04sTUFBTTtnQkFDNUIsTUFBTWEsV0FBVyxDQUFDRixXQUFXQyxLQUFLdEIsUUFBUSxLQUFLLEtBQU1xQixDQUFBQSxXQUFXQyxLQUFLckIsU0FBUyxLQUFLO2dCQUNuRixNQUFNdUIsaUJBQWlCRCxXQUFZLEVBQUNGLFdBQVdDLEtBQUtwQixRQUFRLEtBQUssS0FBSyxHQUFFO2dCQUN4RWMsUUFBUSxDQUFDTixNQUFNLENBQUNMLEtBQUssR0FBR2tCLFdBQVdDO1lBQ3JDO1lBRUEsT0FBTztnQkFBRSxHQUFHMUMsSUFBSTtnQkFBRVAsT0FBT3lDO1lBQVM7UUFDcEM7SUFDRjtJQUVBLE1BQU1TLGtCQUFrQjtRQUN0QixNQUFNRixXQUFXckQsU0FBU0ssS0FBSyxDQUFDbUQsTUFBTSxDQUFDLENBQUNDLEtBQUtMLE9BQVNLLE1BQU9OLENBQUFBLFdBQVdDLEtBQUtqQixLQUFLLEtBQUssSUFBSTtRQUMzRixNQUFNdUIsWUFBWUwsV0FBVyxNQUFNLFVBQVU7UUFDN0MsTUFBTWxCLFFBQVFrQixXQUFXSztRQUV6QixPQUFPO1lBQUVMO1lBQVVLO1lBQVd2QjtRQUFNO0lBQ3RDO0lBRUEsTUFBTXdCLGVBQWUsT0FBT3BDO1FBQzFCQSxFQUFFcUMsY0FBYztRQUVoQixJQUFJLENBQUM1RCxTQUFTRSxVQUFVLEVBQUU7WUFDeEJmLDZEQUFXLENBQUM7WUFDWjtRQUNGO1FBRUEsSUFBSWEsU0FBU0ssS0FBSyxDQUFDd0QsTUFBTSxLQUFLLEdBQUc7WUFDL0IxRSw2REFBVyxDQUFDO1lBQ1o7UUFDRjtRQUVBUSxXQUFXO1FBRVgsSUFBSTtZQUNGLE1BQU0sRUFBRTBELFFBQVEsRUFBRUssU0FBUyxFQUFFdkIsS0FBSyxFQUFFLEdBQUdvQjtZQUV2QyxNQUFNTyxZQUFZO2dCQUNoQixHQUFHOUQsUUFBUTtnQkFDWHFEO2dCQUNBSztnQkFDQXZCO2dCQUNBNEIsUUFBUTtZQUNWO1lBRUEsTUFBTWpELFdBQVd0QixRQUNiLE1BQU1OLGlEQUFTLENBQUMsR0FBaURNLE9BQTlDd0IsdUJBQStCLEVBQUMsZ0JBQXVCLE9BQVR4QixNQUFNMEQsRUFBRSxHQUFJWSxhQUM3RSxNQUFNNUUsa0RBQVUsQ0FBQyxHQUFtQyxPQUFoQzhCLHVCQUErQixFQUFDLGdCQUFjOEM7WUFFdEUzRSwrREFBYSxDQUFDMkIsU0FBU0ssSUFBSSxDQUFDZ0QsT0FBTyxJQUFLM0UsQ0FBQUEsUUFBUSx1QkFBdUIsb0JBQW1CO1lBQzFGRCxPQUFPdUIsU0FBU0ssSUFBSSxDQUFDM0IsS0FBSztZQUMxQkY7UUFDRixFQUFFLE9BQU84QixPQUFPO2dCQUNGQSxzQkFBQUE7WUFBWmpDLDZEQUFXLENBQUNpQyxFQUFBQSxrQkFBQUEsTUFBTU4sUUFBUSxjQUFkTSx1Q0FBQUEsdUJBQUFBLGdCQUFnQkQsSUFBSSxjQUFwQkMsMkNBQUFBLHFCQUFzQkEsS0FBSyxLQUFJO1FBQzdDLFNBQVU7WUFDUnpCLFdBQVc7UUFDYjtJQUNGO0lBRUEsSUFBSSxDQUFDTixRQUFRLE9BQU87SUFFcEIsTUFBTSxFQUFFZ0UsUUFBUSxFQUFFSyxTQUFTLEVBQUV2QixLQUFLLEVBQUUsR0FBR29CO0lBRXZDLHFCQUNFLDhEQUFDYTtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBR0QsV0FBVTtzQ0FDWDdFLFFBQVEsb0JBQW9COzs7Ozs7c0NBRS9CLDhEQUFDK0U7NEJBQ0NDLFNBQVNsRjs0QkFDVCtFLFdBQVU7c0NBRVYsNEVBQUN0RixxSEFBU0E7Z0NBQUNzRixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFJekIsOERBQUNJO29CQUFLQyxVQUFVZjtvQkFBY1UsV0FBVTs7c0NBRXRDLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEOztzREFDQyw4REFBQ087NENBQU1OLFdBQVU7c0RBQWE7Ozs7OztzREFDOUIsOERBQUNPOzRDQUNDcEQsTUFBSzs0Q0FDTEMsT0FBT3pCLFNBQVNFLFVBQVU7NENBQzFCMkUsVUFBVXZEOzRDQUNWK0MsV0FBVTs0Q0FDVlMsUUFBUTs7OERBRVIsOERBQUNDO29EQUFPdEQsT0FBTTs4REFBRzs7Ozs7O2dEQUNoQjdCLFVBQVVvRixHQUFHLENBQUNDLENBQUFBLHlCQUNiLDhEQUFDRjt3REFBeUJ0RCxPQUFPd0QsU0FBUy9CLEVBQUU7a0VBQ3pDK0IsU0FBU3pELElBQUk7dURBREh5RCxTQUFTL0IsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTzlCLDhEQUFDa0I7O3NEQUNDLDhEQUFDTzs0Q0FBTU4sV0FBVTtzREFBYTs7Ozs7O3NEQUM5Qiw4REFBQ2E7NENBQ0NDLE1BQUs7NENBQ0wzRCxNQUFLOzRDQUNMQyxPQUFPekIsU0FBU0csVUFBVTs0Q0FDMUIwRSxVQUFVdkQ7NENBQ1YrQyxXQUFVOzRDQUNWUyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTWQsOERBQUNWOzs4Q0FDQyw4REFBQ0E7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDZTs0Q0FBR2YsV0FBVTtzREFBb0M7Ozs7OztzREFDbEQsOERBQUNFOzRDQUNDWSxNQUFLOzRDQUNMWCxTQUFTN0M7NENBQ1QwQyxXQUFVOzs4REFFViw4REFBQ3JGLG9IQUFRQTtvREFBQ3FGLFdBQVU7Ozs7OztnREFBaUI7Ozs7Ozs7Ozs7Ozs7OENBS3pDLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWnJFLFNBQVNLLEtBQUssQ0FBQzJFLEdBQUcsQ0FBQyxDQUFDNUIsTUFBTVosc0JBQ3pCLDhEQUFDNEI7NENBQWdCQyxXQUFVOzs4REFDekIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ007NERBQU1OLFdBQVU7c0VBQWE7Ozs7OztzRUFDOUIsOERBQUNPOzREQUNDbkQsT0FBTzJCLEtBQUt4QixTQUFTOzREQUNyQmlELFVBQVUsQ0FBQ3RELElBQU1xQixXQUFXSixPQUFPLGFBQWFqQixFQUFFRyxNQUFNLENBQUNELEtBQUs7NERBQzlENEMsV0FBVTs7OEVBRVYsOERBQUNVO29FQUFPdEQsT0FBTTs4RUFBRzs7Ozs7O2dFQUNoQjNCLFNBQVNrRixHQUFHLENBQUNqQyxDQUFBQSx3QkFDWiw4REFBQ2dDO3dFQUF3QnRELE9BQU9zQixRQUFRRyxFQUFFO2tGQUN2Q0gsUUFBUXZCLElBQUk7dUVBREZ1QixRQUFRRyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs4REFPN0IsOERBQUNrQjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNNOzREQUFNTixXQUFVO3NFQUFhOzs7Ozs7c0VBQzlCLDhEQUFDYTs0REFDQ0MsTUFBSzs0REFDTDFELE9BQU8yQixLQUFLdEIsUUFBUTs0REFDcEIrQyxVQUFVLENBQUN0RCxJQUFNcUIsV0FBV0osT0FBTyxZQUFZakIsRUFBRUcsTUFBTSxDQUFDRCxLQUFLOzREQUM3RDRDLFdBQVU7NERBQ1ZnQixLQUFJOzs7Ozs7Ozs7Ozs7OERBSVIsOERBQUNqQjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNNOzREQUFNTixXQUFVO3NFQUFhOzs7Ozs7c0VBQzlCLDhEQUFDYTs0REFDQ0MsTUFBSzs0REFDTDFELE9BQU8yQixLQUFLckIsU0FBUzs0REFDckI4QyxVQUFVLENBQUN0RCxJQUFNcUIsV0FBV0osT0FBTyxhQUFhakIsRUFBRUcsTUFBTSxDQUFDRCxLQUFLOzREQUM5RDRDLFdBQVU7NERBQ1ZpQixNQUFLOzs7Ozs7Ozs7Ozs7OERBSVQsOERBQUNsQjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNNOzREQUFNTixXQUFVO3NFQUFhOzs7Ozs7c0VBQzlCLDhEQUFDYTs0REFDQ0MsTUFBSzs0REFDTDFELE9BQU8yQixLQUFLcEIsUUFBUTs0REFDcEI2QyxVQUFVLENBQUN0RCxJQUFNcUIsV0FBV0osT0FBTyxZQUFZakIsRUFBRUcsTUFBTSxDQUFDRCxLQUFLOzREQUM3RDRDLFdBQVU7NERBQ1ZnQixLQUFJOzREQUNKRSxLQUFJOzs7Ozs7Ozs7Ozs7OERBSVIsOERBQUNuQjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNNOzREQUFNTixXQUFVO3NFQUFhOzs7Ozs7c0VBQzlCLDhEQUFDRDs0REFBSUMsV0FBVTs7Z0VBQXlDO2dFQUNuRGpCLENBQUFBLEtBQUtqQixLQUFLLElBQUksR0FBR3FELE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7Ozs4REFJaEMsOERBQUNwQjtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0U7d0RBQ0NZLE1BQUs7d0RBQ0xYLFNBQVMsSUFBTWpDLFdBQVdDO3dEQUMxQjZCLFdBQVU7a0VBRVYsNEVBQUNwRixxSEFBU0E7NERBQUNvRixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzsyQ0FoRWpCN0I7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBeUVoQiw4REFBQzRCOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ29COzBEQUFLOzs7Ozs7MERBQ04sOERBQUNBOztvREFBSztvREFBRXBDLFNBQVNtQyxPQUFPLENBQUM7Ozs7Ozs7Ozs7Ozs7a0RBRTNCLDhEQUFDcEI7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDb0I7MERBQUs7Ozs7OzswREFDTiw4REFBQ0E7O29EQUFLO29EQUFFL0IsVUFBVThCLE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7OztrREFFNUIsOERBQUNwQjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNvQjswREFBSzs7Ozs7OzBEQUNOLDhEQUFDQTs7b0RBQUs7b0RBQUV0RCxNQUFNcUQsT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTTVCLDhEQUFDcEI7OzhDQUNDLDhEQUFDTztvQ0FBTU4sV0FBVTs4Q0FBYTs7Ozs7OzhDQUM5Qiw4REFBQ3FCO29DQUNDbEUsTUFBSztvQ0FDTEMsT0FBT3pCLFNBQVNJLEtBQUs7b0NBQ3JCeUUsVUFBVXZEO29DQUNWK0MsV0FBVTtvQ0FDVnNCLE1BQUs7b0NBQ0xDLGFBQVk7Ozs7Ozs7Ozs7OztzQ0FLaEIsOERBQUN4Qjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNFO29DQUNDWSxNQUFLO29DQUNMWCxTQUFTbEY7b0NBQ1QrRSxXQUFVOzhDQUNYOzs7Ozs7OENBR0QsOERBQUNFO29DQUNDWSxNQUFLO29DQUNMVSxVQUFVbkc7b0NBQ1YyRSxXQUFVOzhDQUVUM0UsVUFBVSxrQkFBbUJGLFFBQVEsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPOUQ7R0FuV3dCSjs7UUFDUk4seURBQWNBOzs7S0FETk0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9zYWxlcy9RdW90ZU1vZGFsLmpzP2M0ZTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAncmVhY3QtaTE4bmV4dCc7XG5pbXBvcnQgeyBYTWFya0ljb24sIFBsdXNJY29uLCBUcmFzaEljb24gfSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuaW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJztcbmltcG9ydCB0b2FzdCBmcm9tICdyZWFjdC1ob3QtdG9hc3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBRdW90ZU1vZGFsKHsgaXNPcGVuLCBvbkNsb3NlLCBvblNhdmUsIHF1b3RlID0gbnVsbCB9KSB7XG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oJ2NvbW1vbicpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtjdXN0b21lcnMsIHNldEN1c3RvbWVyc10gPSB1c2VTdGF0ZShbXSk7XG4gIGNvbnN0IFtwcm9kdWN0cywgc2V0UHJvZHVjdHNdID0gdXNlU3RhdGUoW10pO1xuICBcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XG4gICAgY3VzdG9tZXJJZDogJycsXG4gICAgdmFsaWRVbnRpbDogJycsXG4gICAgbm90ZXM6ICcnLFxuICAgIGl0ZW1zOiBbXVxuICB9KTtcblxuICAvLyBMb2FkIGN1c3RvbWVycyBhbmQgcHJvZHVjdHNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNPcGVuKSB7XG4gICAgICBsb2FkQ3VzdG9tZXJzKCk7XG4gICAgICBsb2FkUHJvZHVjdHMoKTtcbiAgICAgIFxuICAgICAgaWYgKHF1b3RlKSB7XG4gICAgICAgIHNldEZvcm1EYXRhKHtcbiAgICAgICAgICBjdXN0b21lcklkOiBxdW90ZS5jdXN0b21lcklkIHx8ICcnLFxuICAgICAgICAgIHZhbGlkVW50aWw6IHF1b3RlLnZhbGlkVW50aWwgPyBxdW90ZS52YWxpZFVudGlsLnNwbGl0KCdUJylbMF0gOiAnJyxcbiAgICAgICAgICBub3RlczogcXVvdGUubm90ZXMgfHwgJycsXG4gICAgICAgICAgaXRlbXM6IHF1b3RlLml0ZW1zIHx8IFtdXG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gU2V0IGRlZmF1bHQgdmFsaWQgdW50aWwgZGF0ZSAoNyBkYXlzIGZyb20gbm93KVxuICAgICAgICBjb25zdCB2YWxpZFVudGlsID0gbmV3IERhdGUoKTtcbiAgICAgICAgdmFsaWRVbnRpbC5zZXREYXRlKHZhbGlkVW50aWwuZ2V0RGF0ZSgpICsgNyk7XG4gICAgICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIHZhbGlkVW50aWw6IHZhbGlkVW50aWwudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdXG4gICAgICAgIH0pKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtpc09wZW4sIHF1b3RlXSk7XG5cbiAgY29uc3QgbG9hZEN1c3RvbWVycyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQoYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTH0vYXBpL2N1c3RvbWVyc2ApO1xuICAgICAgc2V0Q3VzdG9tZXJzKHJlc3BvbnNlLmRhdGEuY3VzdG9tZXJzIHx8IFtdKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBjdXN0b21lcnM6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBsb2FkUHJvZHVjdHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkx9L2FwaS9wcm9kdWN0c2ApO1xuICAgICAgc2V0UHJvZHVjdHMocmVzcG9uc2UuZGF0YS5wcm9kdWN0cyB8fCBbXSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgcHJvZHVjdHM6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDaGFuZ2UgPSAoZSkgPT4ge1xuICAgIGNvbnN0IHsgbmFtZSwgdmFsdWUgfSA9IGUudGFyZ2V0O1xuICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBbbmFtZV06IHZhbHVlXG4gICAgfSkpO1xuICB9O1xuXG4gIGNvbnN0IGFkZEl0ZW0gPSAoKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIGl0ZW1zOiBbLi4ucHJldi5pdGVtcywge1xuICAgICAgICBwcm9kdWN0SWQ6ICcnLFxuICAgICAgICBwcm9kdWN0TmFtZTogJycsXG4gICAgICAgIHF1YW50aXR5OiAxLFxuICAgICAgICB1bml0UHJpY2U6IDAsXG4gICAgICAgIGRpc2NvdW50OiAwLFxuICAgICAgICB0YXhSYXRlOiAxNCxcbiAgICAgICAgaGFzVGF4OiB0cnVlLFxuICAgICAgICB0b3RhbDogMCxcbiAgICAgICAgaXNDdXN0b21pemVkOiBmYWxzZSxcbiAgICAgICAgY3VzdG9taXphdGlvbnM6IG51bGwsXG4gICAgICAgIGN1c3RvbWl6YXRpb25EZXRhaWxzOiBbXVxuICAgICAgfV1cbiAgICB9KSk7XG4gIH07XG5cbiAgY29uc3QgcmVtb3ZlSXRlbSA9IChpbmRleCkgPT4ge1xuICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBpdGVtczogcHJldi5pdGVtcy5maWx0ZXIoKF8sIGkpID0+IGkgIT09IGluZGV4KVxuICAgIH0pKTtcbiAgfTtcblxuICBjb25zdCB1cGRhdGVJdGVtID0gKGluZGV4LCBmaWVsZCwgdmFsdWUpID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+IHtcbiAgICAgIGNvbnN0IG5ld0l0ZW1zID0gWy4uLnByZXYuaXRlbXNdO1xuICAgICAgbmV3SXRlbXNbaW5kZXhdID0geyAuLi5uZXdJdGVtc1tpbmRleF0sIFtmaWVsZF06IHZhbHVlIH07XG4gICAgICBcbiAgICAgIC8vIEF1dG8tY2FsY3VsYXRlIHRvdGFsXG4gICAgICBpZiAoZmllbGQgPT09ICdwcm9kdWN0SWQnKSB7XG4gICAgICAgIGNvbnN0IHByb2R1Y3QgPSBwcm9kdWN0cy5maW5kKHAgPT4gcC5pZCA9PT0gdmFsdWUpO1xuICAgICAgICBpZiAocHJvZHVjdCkge1xuICAgICAgICAgIG5ld0l0ZW1zW2luZGV4XS51bml0UHJpY2UgPSBwYXJzZUZsb2F0KHByb2R1Y3QudW5pdFByaWNlKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgXG4gICAgICBpZiAoZmllbGQgPT09ICdxdWFudGl0eScgfHwgZmllbGQgPT09ICd1bml0UHJpY2UnIHx8IGZpZWxkID09PSAnZGlzY291bnQnKSB7XG4gICAgICAgIGNvbnN0IGl0ZW0gPSBuZXdJdGVtc1tpbmRleF07XG4gICAgICAgIGNvbnN0IHN1YnRvdGFsID0gKHBhcnNlRmxvYXQoaXRlbS5xdWFudGl0eSkgfHwgMCkgKiAocGFyc2VGbG9hdChpdGVtLnVuaXRQcmljZSkgfHwgMCk7XG4gICAgICAgIGNvbnN0IGRpc2NvdW50QW1vdW50ID0gc3VidG90YWwgKiAoKHBhcnNlRmxvYXQoaXRlbS5kaXNjb3VudCkgfHwgMCkgLyAxMDApO1xuICAgICAgICBuZXdJdGVtc1tpbmRleF0udG90YWwgPSBzdWJ0b3RhbCAtIGRpc2NvdW50QW1vdW50O1xuICAgICAgfVxuICAgICAgXG4gICAgICByZXR1cm4geyAuLi5wcmV2LCBpdGVtczogbmV3SXRlbXMgfTtcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBjYWxjdWxhdGVUb3RhbHMgPSAoKSA9PiB7XG4gICAgY29uc3Qgc3VidG90YWwgPSBmb3JtRGF0YS5pdGVtcy5yZWR1Y2UoKHN1bSwgaXRlbSkgPT4gc3VtICsgKHBhcnNlRmxvYXQoaXRlbS50b3RhbCkgfHwgMCksIDApO1xuICAgIGNvbnN0IHRheEFtb3VudCA9IHN1YnRvdGFsICogMC4xNDsgLy8gMTQlIHRheFxuICAgIGNvbnN0IHRvdGFsID0gc3VidG90YWwgKyB0YXhBbW91bnQ7XG4gICAgXG4gICAgcmV0dXJuIHsgc3VidG90YWwsIHRheEFtb3VudCwgdG90YWwgfTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZSkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBcbiAgICBpZiAoIWZvcm1EYXRhLmN1c3RvbWVySWQpIHtcbiAgICAgIHRvYXN0LmVycm9yKCfZitix2KzZiSDYp9iu2KrZitin2LEg2KfZhNi52YXZitmEJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIFxuICAgIGlmIChmb3JtRGF0YS5pdGVtcy5sZW5ndGggPT09IDApIHtcbiAgICAgIHRvYXN0LmVycm9yKCfZitix2KzZiSDYpdi22KfZgdipINi52YbYtdixINmI2KfYrdivINi52YTZiSDYp9mE2KPZgtmEJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBzdWJ0b3RhbCwgdGF4QW1vdW50LCB0b3RhbCB9ID0gY2FsY3VsYXRlVG90YWxzKCk7XG4gICAgICBcbiAgICAgIGNvbnN0IHF1b3RlRGF0YSA9IHtcbiAgICAgICAgLi4uZm9ybURhdGEsXG4gICAgICAgIHN1YnRvdGFsLFxuICAgICAgICB0YXhBbW91bnQsXG4gICAgICAgIHRvdGFsLFxuICAgICAgICBzdGF0dXM6ICdEUkFGVCdcbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gcXVvdGUgXG4gICAgICAgID8gYXdhaXQgYXhpb3MucHV0KGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkx9L2FwaS9xdW90ZXMvJHtxdW90ZS5pZH1gLCBxdW90ZURhdGEpXG4gICAgICAgIDogYXdhaXQgYXhpb3MucG9zdChgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMfS9hcGkvcXVvdGVzYCwgcXVvdGVEYXRhKTtcblxuICAgICAgdG9hc3Quc3VjY2VzcyhyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgKHF1b3RlID8gJ9iq2YUg2KrYrdiv2YrYqyDYudix2LYg2KfZhNiz2LnYsScgOiAn2KrZhSDYpdmG2LTYp9ihINi52LHYtiDYp9mE2LPYudixJykpO1xuICAgICAgb25TYXZlKHJlc3BvbnNlLmRhdGEucXVvdGUpO1xuICAgICAgb25DbG9zZSgpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0b2FzdC5lcnJvcihlcnJvci5yZXNwb25zZT8uZGF0YT8uZXJyb3IgfHwgJ9it2K/YqyDYrti32KMnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGlmICghaXNPcGVuKSByZXR1cm4gbnVsbDtcblxuICBjb25zdCB7IHN1YnRvdGFsLCB0YXhBbW91bnQsIHRvdGFsIH0gPSBjYWxjdWxhdGVUb3RhbHMoKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cteGwgdy1mdWxsIG1heC13LTR4bCBtYXgtaC1bOTB2aF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNiBib3JkZXItYlwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAge3F1b3RlID8gJ9iq2LnYr9mK2YQg2LnYsdi2INin2YTYs9i52LEnIDogJ9i52LHYtiDYs9i52LEg2KzYr9mK2K8nfVxuICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFhNYXJrSWNvbiBjbGFzc05hbWU9XCJoLTYgdy02XCIgLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwicC02IHNwYWNlLXktNlwiPlxuICAgICAgICAgIHsvKiBDdXN0b21lciBhbmQgRGF0ZSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmb3JtLWxhYmVsXCI+2KfZhNi52YXZitmEICo8L2xhYmVsPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgbmFtZT1cImN1c3RvbWVySWRcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jdXN0b21lcklkfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dFwiXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj7Yp9iu2KrYsSDYp9mE2LnZhdmK2YQ8L29wdGlvbj5cbiAgICAgICAgICAgICAgICB7Y3VzdG9tZXJzLm1hcChjdXN0b21lciA9PiAoXG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17Y3VzdG9tZXIuaWR9IHZhbHVlPXtjdXN0b21lci5pZH0+XG4gICAgICAgICAgICAgICAgICAgIHtjdXN0b21lci5uYW1lfVxuICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmb3JtLWxhYmVsXCI+2LXYp9mE2K0g2K3YqtmJICo8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICAgICAgbmFtZT1cInZhbGlkVW50aWxcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS52YWxpZFVudGlsfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS1pbnB1dFwiXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEl0ZW1zICovfVxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj7Yp9mE2LnZhtin2LXYsTwvaDM+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXthZGRJdGVtfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5IGZsZXggaXRlbXMtY2VudGVyXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxQbHVzSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgINil2LbYp9mB2Kkg2LnZhti12LFcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAge2Zvcm1EYXRhLml0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEyIGdhcC00IGl0ZW1zLWVuZCBwLTQgYm9yZGVyIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tNFwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZm9ybS1sYWJlbFwiPtin2YTZhdmG2KrYrDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aXRlbS5wcm9kdWN0SWR9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVJdGVtKGluZGV4LCAncHJvZHVjdElkJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvcm0taW5wdXRcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPtin2K7YqtixINin2YTZhdmG2KrYrDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIHtwcm9kdWN0cy5tYXAocHJvZHVjdCA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17cHJvZHVjdC5pZH0gdmFsdWU9e3Byb2R1Y3QuaWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMlwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZm9ybS1sYWJlbFwiPtin2YTZg9mF2YrYqTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtpdGVtLnF1YW50aXR5fVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlSXRlbShpbmRleCwgJ3F1YW50aXR5JywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvcm0taW5wdXRcIlxuICAgICAgICAgICAgICAgICAgICAgIG1pbj1cIjFcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMlwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZm9ybS1sYWJlbFwiPtin2YTYs9i52LE8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aXRlbS51bml0UHJpY2V9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVJdGVtKGluZGV4LCAndW5pdFByaWNlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvcm0taW5wdXRcIlxuICAgICAgICAgICAgICAgICAgICAgIHN0ZXA9XCIwLjAxXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZvcm0tbGFiZWxcIj7Yrti12YUgJTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtpdGVtLmRpc2NvdW50fVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlSXRlbShpbmRleCwgJ2Rpc2NvdW50JywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvcm0taW5wdXRcIlxuICAgICAgICAgICAgICAgICAgICAgIG1pbj1cIjBcIlxuICAgICAgICAgICAgICAgICAgICAgIG1heD1cIjEwMFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmb3JtLWxhYmVsXCI+2KfZhNil2KzZhdin2YTZijwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIHB5LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAkeyhpdGVtLnRvdGFsIHx8IDApLnRvRml4ZWQoMil9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMVwiPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcmVtb3ZlSXRlbShpbmRleCl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIGhvdmVyOnRleHQtcmVkLTgwMFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8VHJhc2hJY29uIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFRvdGFscyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcC00IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICA8c3Bhbj7Yp9mE2YXYrNmF2YjYuSDYp9mE2YHYsdi52Yo6PC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuPiR7c3VidG90YWwudG9GaXhlZCgyKX08L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4+2KfZhNi22LHZitio2KkgKDE0JSk6PC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuPiR7dGF4QW1vdW50LnRvRml4ZWQoMil9PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBmb250LWJvbGQgdGV4dC1sZyBib3JkZXItdCBwdC0yXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4+2KfZhNil2KzZhdin2YTZijo8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4+JHt0b3RhbC50b0ZpeGVkKDIpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBOb3RlcyAqL31cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZvcm0tbGFiZWxcIj7ZhdmE2KfYrdi42KfYqjwvbGFiZWw+XG4gICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgbmFtZT1cIm5vdGVzXCJcbiAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLm5vdGVzfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hhbmdlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmb3JtLWlucHV0XCJcbiAgICAgICAgICAgICAgcm93cz1cIjNcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItmF2YTYp9it2LjYp9iqINil2LbYp9mB2YrYqS4uLlwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEFjdGlvbnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXNlY29uZGFyeVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgINil2YTYutin2KFcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2xvYWRpbmcgPyAn2KzYp9ix2Yog2KfZhNit2YHYuC4uLicgOiAocXVvdGUgPyAn2KrYrdiv2YrYqycgOiAn2KXZhti02KfYoScpfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZm9ybT5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlVHJhbnNsYXRpb24iLCJYTWFya0ljb24iLCJQbHVzSWNvbiIsIlRyYXNoSWNvbiIsImF4aW9zIiwidG9hc3QiLCJRdW90ZU1vZGFsIiwiaXNPcGVuIiwib25DbG9zZSIsIm9uU2F2ZSIsInF1b3RlIiwidCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiY3VzdG9tZXJzIiwic2V0Q3VzdG9tZXJzIiwicHJvZHVjdHMiLCJzZXRQcm9kdWN0cyIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJjdXN0b21lcklkIiwidmFsaWRVbnRpbCIsIm5vdGVzIiwiaXRlbXMiLCJsb2FkQ3VzdG9tZXJzIiwibG9hZFByb2R1Y3RzIiwic3BsaXQiLCJEYXRlIiwic2V0RGF0ZSIsImdldERhdGUiLCJwcmV2IiwidG9JU09TdHJpbmciLCJyZXNwb25zZSIsImdldCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwiZGF0YSIsImVycm9yIiwiY29uc29sZSIsImhhbmRsZUNoYW5nZSIsImUiLCJuYW1lIiwidmFsdWUiLCJ0YXJnZXQiLCJhZGRJdGVtIiwicHJvZHVjdElkIiwicHJvZHVjdE5hbWUiLCJxdWFudGl0eSIsInVuaXRQcmljZSIsImRpc2NvdW50IiwidGF4UmF0ZSIsImhhc1RheCIsInRvdGFsIiwiaXNDdXN0b21pemVkIiwiY3VzdG9taXphdGlvbnMiLCJjdXN0b21pemF0aW9uRGV0YWlscyIsInJlbW92ZUl0ZW0iLCJpbmRleCIsImZpbHRlciIsIl8iLCJpIiwidXBkYXRlSXRlbSIsImZpZWxkIiwibmV3SXRlbXMiLCJwcm9kdWN0IiwiZmluZCIsInAiLCJpZCIsInBhcnNlRmxvYXQiLCJpdGVtIiwic3VidG90YWwiLCJkaXNjb3VudEFtb3VudCIsImNhbGN1bGF0ZVRvdGFscyIsInJlZHVjZSIsInN1bSIsInRheEFtb3VudCIsImhhbmRsZVN1Ym1pdCIsInByZXZlbnREZWZhdWx0IiwibGVuZ3RoIiwicXVvdGVEYXRhIiwic3RhdHVzIiwicHV0IiwicG9zdCIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJidXR0b24iLCJvbkNsaWNrIiwiZm9ybSIsIm9uU3VibWl0IiwibGFiZWwiLCJzZWxlY3QiLCJvbkNoYW5nZSIsInJlcXVpcmVkIiwib3B0aW9uIiwibWFwIiwiY3VzdG9tZXIiLCJpbnB1dCIsInR5cGUiLCJoMyIsIm1pbiIsInN0ZXAiLCJtYXgiLCJ0b0ZpeGVkIiwic3BhbiIsInRleHRhcmVhIiwicm93cyIsInBsYWNlaG9sZGVyIiwiZGlzYWJsZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/sales/QuoteModal.js\n"));

/***/ })

});