"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "./components/sales/InvoiceModal.js":
/*!******************************************!*\
  !*** ./components/sales/InvoiceModal.js ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InvoiceModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CogIcon,CreditCardIcon,PlusIcon,PrinterIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=CogIcon,CreditCardIcon,PlusIcon,PrinterIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-to-print */ \"./node_modules/react-to-print/lib/index.js\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_to_print__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _ProductCustomizer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ProductCustomizer */ \"./components/sales/ProductCustomizer.js\");\n/* harmony import */ var _PaymentManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./PaymentManager */ \"./components/sales/PaymentManager.js\");\n/* harmony import */ var _InvoicePrintView__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./InvoicePrintView */ \"./components/sales/InvoicePrintView.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction InvoiceModal(param) {\n    let { isOpen, onClose, onSave, invoice = null, fromSalesOrder = null } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const printRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Modal states\n    const [showCustomizer, setShowCustomizer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPaymentManager, setShowPaymentManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: \"\",\n        dueDate: \"\",\n        notes: \"\",\n        items: [],\n        payments: [],\n        // إضافة خصم عام\n        generalDiscount: 0,\n        generalDiscountType: \"PERCENTAGE\" // PERCENTAGE or AMOUNT\n    });\n    const [showPaymentModal, setShowPaymentModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentData, setPaymentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        method: \"CASH\",\n        amount: 0,\n        reference: \"\",\n        installmentPlan: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            loadCustomers();\n            loadProducts();\n            if (invoice) {\n                setFormData({\n                    customerId: invoice.customerId || \"\",\n                    dueDate: invoice.dueDate ? invoice.dueDate.split(\"T\")[0] : \"\",\n                    notes: invoice.notes || \"\",\n                    items: invoice.items || [],\n                    payments: invoice.payments || []\n                });\n            } else if (fromSalesOrder) {\n                // Convert sales order to invoice\n                const dueDate = new Date();\n                dueDate.setDate(dueDate.getDate() + 30); // 30 days payment terms\n                setFormData({\n                    customerId: fromSalesOrder.customerId || \"\",\n                    dueDate: dueDate.toISOString().split(\"T\")[0],\n                    notes: \"تم التحويل من أمر البيع: \".concat(fromSalesOrder.orderNumber),\n                    items: fromSalesOrder.items || [],\n                    payments: []\n                });\n            } else {\n                // New invoice\n                const dueDate = new Date();\n                dueDate.setDate(dueDate.getDate() + 30);\n                setFormData((prev)=>({\n                        ...prev,\n                        dueDate: dueDate.toISOString().split(\"T\")[0]\n                    }));\n            }\n        }\n    }, [\n        isOpen,\n        invoice,\n        fromSalesOrder\n    ]);\n    const loadCustomers = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/customers\"));\n            setCustomers(response.data.customers || []);\n        } catch (error) {\n            console.error(\"Error loading customers:\", error);\n        }\n    };\n    const loadProducts = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/products\"));\n            setProducts(response.data.products || []);\n        } catch (error) {\n            console.error(\"Error loading products:\", error);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const addItem = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: [\n                    ...prev.items,\n                    {\n                        productId: \"\",\n                        productName: \"\",\n                        quantity: 1,\n                        unitPrice: 0,\n                        discount: 0,\n                        taxRate: 14,\n                        hasTax: true,\n                        total: 0,\n                        isCustomized: false,\n                        customizations: null,\n                        customizationDetails: []\n                    }\n                ]\n            }));\n    };\n    const removeItem = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: prev.items.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateItem = (index, field, value)=>{\n        setFormData((prev)=>{\n            const newItems = [\n                ...prev.items\n            ];\n            newItems[index] = {\n                ...newItems[index],\n                [field]: value\n            };\n            if (field === \"productId\") {\n                const product = products.find((p)=>p.id === value);\n                if (product) {\n                    newItems[index].unitPrice = parseFloat(product.unitPrice);\n                    newItems[index].productName = product.nameAr || product.name;\n                    // Check if product is customizable\n                    if (product.isCustomizable) {\n                        setSelectedProduct(product);\n                        setCurrentItemIndex(index);\n                        setShowCustomizer(true);\n                        return prev; // Don't update yet, wait for customization\n                    }\n                }\n            }\n            // Recalculate totals for any change\n            if ([\n                \"quantity\",\n                \"unitPrice\",\n                \"discount\",\n                \"taxRate\",\n                \"hasTax\"\n            ].includes(field)) {\n                const item = newItems[index];\n                const quantity = parseFloat(item.quantity) || 0;\n                const unitPrice = parseFloat(item.unitPrice) || 0;\n                const discountPercent = parseFloat(item.discount) || 0;\n                const taxRate = parseFloat(item.taxRate) || 0;\n                // Calculate subtotal\n                const subtotal = quantity * unitPrice;\n                // Apply discount\n                const discountAmount = subtotal * (discountPercent / 100);\n                const afterDiscount = subtotal - discountAmount;\n                // Apply tax if enabled\n                const taxAmount = item.hasTax ? afterDiscount * (taxRate / 100) : 0;\n                const total = afterDiscount + taxAmount;\n                newItems[index].total = total;\n                newItems[index].subtotal = subtotal;\n                newItems[index].discountAmount = discountAmount;\n                newItems[index].taxAmount = taxAmount;\n            }\n            return {\n                ...prev,\n                items: newItems\n            };\n        });\n    };\n    // Handle customized product\n    const handleCustomizedProduct = (customizedProduct)=>{\n        setFormData((prev)=>{\n            const newItems = [\n                ...prev.items\n            ];\n            newItems[currentItemIndex] = {\n                ...newItems[currentItemIndex],\n                productId: customizedProduct.id,\n                productName: customizedProduct.nameAr || customizedProduct.name,\n                unitPrice: customizedProduct.finalPrice,\n                customizations: customizedProduct.customizations,\n                customizationDetails: customizedProduct.customizationDetails,\n                isCustomized: true\n            };\n            // Recalculate total\n            const item = newItems[currentItemIndex];\n            const subtotal = (parseFloat(item.quantity) || 0) * (parseFloat(item.unitPrice) || 0);\n            const discountAmount = subtotal * ((parseFloat(item.discount) || 0) / 100);\n            newItems[currentItemIndex].total = subtotal - discountAmount;\n            return {\n                ...prev,\n                items: newItems\n            };\n        });\n    };\n    // Print functionality\n    const handlePrint = (0,react_to_print__WEBPACK_IMPORTED_MODULE_4__.useReactToPrint)({\n        content: ()=>printRef.current,\n        documentTitle: \"فاتورة-\".concat(formData.invoiceNumber || \"جديدة\")\n    });\n    const calculateTotals = ()=>{\n        // حساب المجاميع من العناصر\n        const itemsSubtotal = formData.items.reduce((sum, item)=>sum + (parseFloat(item.subtotal) || 0), 0);\n        const itemsDiscountAmount = formData.items.reduce((sum, item)=>sum + (parseFloat(item.discountAmount) || 0), 0);\n        const itemsTaxAmount = formData.items.reduce((sum, item)=>sum + (parseFloat(item.taxAmount) || 0), 0);\n        const itemsTotal = formData.items.reduce((sum, item)=>sum + (parseFloat(item.total) || 0), 0);\n        // حساب الخصم العام\n        let generalDiscountAmount = 0;\n        if (formData.generalDiscount > 0) {\n            if (formData.generalDiscountType === \"PERCENTAGE\") {\n                generalDiscountAmount = itemsTotal * (parseFloat(formData.generalDiscount) / 100);\n            } else {\n                generalDiscountAmount = parseFloat(formData.generalDiscount);\n            }\n        }\n        // الإجمالي النهائي بعد الخصم العام\n        const finalTotal = itemsTotal - generalDiscountAmount;\n        // المدفوعات\n        const paidAmount = formData.payments.reduce((sum, payment)=>sum + (parseFloat(payment.amount) || 0), 0);\n        const remainingAmount1 = finalTotal - paidAmount;\n        return {\n            itemsSubtotal,\n            itemsDiscountAmount,\n            itemsTaxAmount,\n            itemsTotal,\n            generalDiscountAmount,\n            finalTotal,\n            paidAmount,\n            remainingAmount: remainingAmount1\n        };\n    };\n    const addPayment = ()=>{\n        const { finalTotal, paidAmount } = calculateTotals();\n        const maxAmount = finalTotal - paidAmount;\n        if (maxAmount <= 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"تم دفع المبلغ بالكامل\");\n            return;\n        }\n        setShowPaymentManager(true);\n    };\n    const handlePaymentAdd = (payment)=>{\n        setFormData((prev)=>({\n                ...prev,\n                payments: [\n                    ...prev.payments,\n                    {\n                        ...payment,\n                        id: Date.now().toString()\n                    }\n                ]\n            }));\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"تم إضافة الدفعة بنجاح\");\n    };\n    const savePayment = ()=>{\n        if (!paymentData.amount || paymentData.amount <= 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى إدخال مبلغ صحيح\");\n            return;\n        }\n        const { total, paidAmount } = calculateTotals();\n        if (paidAmount + parseFloat(paymentData.amount) > total) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"المبلغ المدفوع أكبر من المبلغ المطلوب\");\n            return;\n        }\n        setFormData((prev)=>({\n                ...prev,\n                payments: [\n                    ...prev.payments,\n                    {\n                        ...paymentData,\n                        id: Date.now().toString(),\n                        paidAt: new Date().toISOString()\n                    }\n                ]\n            }));\n        setShowPaymentModal(false);\n        setPaymentData({\n            method: \"CASH\",\n            amount: 0,\n            reference: \"\",\n            installmentPlan: \"\"\n        });\n    };\n    const removePayment = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                payments: prev.payments.filter((_, i)=>i !== index)\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.customerId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى اختيار العميل\");\n            return;\n        }\n        if (formData.items.length === 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى إضافة عنصر واحد على الأقل\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const { subtotal, taxAmount, total, paidAmount, remainingAmount: remainingAmount1 } = calculateTotals();\n            const invoiceData = {\n                ...formData,\n                subtotal,\n                taxAmount,\n                total,\n                paidAmount,\n                remainingAmount: remainingAmount1,\n                status: paidAmount >= total ? \"PAID\" : paidAmount > 0 ? \"PARTIALLY_PAID\" : \"PENDING\",\n                salesOrderId: (fromSalesOrder === null || fromSalesOrder === void 0 ? void 0 : fromSalesOrder.id) || null\n            };\n            const response = invoice ? await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].put(\"\".concat(\"http://localhost:3070\", \"/api/invoices/\").concat(invoice.id), invoiceData) : await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].post(\"\".concat(\"http://localhost:3070\", \"/api/invoices\"), invoiceData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.data.message || (invoice ? \"تم تحديث الفاتورة\" : \"تم إنشاء الفاتورة\"));\n            onSave(response.data.invoice);\n            onClose();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"حدث خطأ\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: [\n                                        invoice ? \"تعديل الفاتورة\" : \"فاتورة جديدة\",\n                                        fromSalesOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-green-600 block\",\n                                            children: [\n                                                \"تحويل من أمر البيع: \",\n                                                fromSalesOrder.orderNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 361,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.XMarkIcon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"p-6 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"form-label\",\n                                                    children: \"العميل *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    name: \"customerId\",\n                                                    value: formData.customerId,\n                                                    onChange: handleChange,\n                                                    className: \"form-input\",\n                                                    required: true,\n                                                    disabled: fromSalesOrder,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"اختر العميل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: customer.id,\n                                                                children: customer.name\n                                                            }, customer.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"form-label\",\n                                                    children: \"تاريخ الاستحقاق\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"date\",\n                                                    name: \"dueDate\",\n                                                    value: formData.dueDate,\n                                                    onChange: handleChange,\n                                                    className: \"form-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"العناصر\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: addItem,\n                                                    className: \"btn-primary flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.PlusIcon, {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"إضافة عنصر\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: formData.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-12 gap-2 items-end p-4 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"المنتج\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: item.productId,\n                                                                            onChange: (e)=>updateItem(index, \"productId\", e.target.value),\n                                                                            className: \"form-input flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"\",\n                                                                                    children: \"اختر المنتج\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                                    lineNumber: 433,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: product.id,\n                                                                                        children: [\n                                                                                            product.nameAr || product.name,\n                                                                                            product.isCustomizable && \" (قابل للتخصيص)\"\n                                                                                        ]\n                                                                                    }, product.id, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                                        lineNumber: 435,\n                                                                                        columnNumber: 29\n                                                                                    }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 428,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        item.isCustomized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>{\n                                                                                const product = products.find((p)=>p.id === item.productId);\n                                                                                if (product) {\n                                                                                    setSelectedProduct(product);\n                                                                                    setCurrentItemIndex(index);\n                                                                                    setShowCustomizer(true);\n                                                                                }\n                                                                            },\n                                                                            className: \"mr-2 px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200\",\n                                                                            title: \"تعديل التخصيص\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.CogIcon, {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                                lineNumber: 455,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 442,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                item.customizationDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 text-xs text-gray-600 bg-blue-50 p-2 rounded\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"التخصيصات:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 461,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        item.customizationDetails.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    \"• \",\n                                                                                    detail.optionName,\n                                                                                    \": \",\n                                                                                    detail.selectedName\n                                                                                ]\n                                                                            }, idx, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                                lineNumber: 463,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 460,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"الكمية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: item.quantity,\n                                                                    onChange: (e)=>updateItem(index, \"quantity\", e.target.value),\n                                                                    className: \"form-input\",\n                                                                    min: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"السعر\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: item.unitPrice,\n                                                                    onChange: (e)=>updateItem(index, \"unitPrice\", e.target.value),\n                                                                    className: \"form-input\",\n                                                                    step: \"0.01\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"خصم %\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: item.discount,\n                                                                    onChange: (e)=>updateItem(index, \"discount\", e.target.value),\n                                                                    className: \"form-input\",\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"ضريبة\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: item.hasTax,\n                                                                            onChange: (e)=>updateItem(index, \"hasTax\", e.target.checked),\n                                                                            className: \"rounded\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            value: item.taxRate,\n                                                                            onChange: (e)=>updateItem(index, \"taxRate\", e.target.value),\n                                                                            className: \"form-input w-16\",\n                                                                            min: \"0\",\n                                                                            max: \"100\",\n                                                                            disabled: !item.hasTax\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 512,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs\",\n                                                                            children: \"%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 521,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"الإجمالي\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900 py-2\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        (item.total || 0).toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>removeItem(index),\n                                                                className: \"text-red-600 hover:text-red-800\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.TrashIcon, {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 422,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"المدفوعات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: addPayment,\n                                                    className: \"btn-secondary flex items-center\",\n                                                    disabled: remainingAmount <= 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.CreditCardIcon, {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"إضافة دفعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 548,\n                                            columnNumber: 15\n                                        }, this),\n                                        formData.payments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: formData.payments.map((payment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: payment.method\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"$\",\n                                                                        parseFloat(payment.amount).toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                payment.reference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"المرجع: \",\n                                                                        payment.reference\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>removePayment(index),\n                                                            className: \"text-red-600 hover:text-red-800\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.TrashIcon, {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 562,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5 text-red-400\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 588,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-red-800\",\n                                                        children: \"تنبيه خصم المخزون\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-sm text-red-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"عند حفظ الفاتورة، سيتم خصم الكميات من المخزون نهائياً وتسجيل المبيعات. هذا الإجراء لا يمكن التراجع عنه.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 598,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 593,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                        lineNumber: 587,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 586,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-3\",\n                                            children: \"خصم عام\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"نوع الخصم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: formData.generalDiscountType,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        generalDiscountType: e.target.value\n                                                                    })),\n                                                            className: \"form-input\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"PERCENTAGE\",\n                                                                    children: \"نسبة مئوية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 618,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"AMOUNT\",\n                                                                    children: \"مبلغ ثابت\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: [\n                                                                \"قيمة الخصم \",\n                                                                formData.generalDiscountType === \"PERCENTAGE\" ? \"(%)\" : \"($)\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: formData.generalDiscount,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        generalDiscount: e.target.value\n                                                                    })),\n                                                            className: \"form-input\",\n                                                            min: \"0\",\n                                                            step: \"0.01\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"مبلغ الخصم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-semibold text-red-600 py-2\",\n                                                            children: [\n                                                                \"$\",\n                                                                calculateTotals().generalDiscountAmount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 610,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"المجموع الفرعي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateTotals().itemsSubtotal.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-red-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"خصم العناصر:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"-$\",\n                                                            calculateTotals().itemsDiscountAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 651,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-blue-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"الضرائب:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateTotals().itemsTaxAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 655,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between font-medium border-t pt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"إجمالي العناصر:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateTotals().itemsTotal.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 659,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-red-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"الخصم العام:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"-$\",\n                                                            calculateTotals().generalDiscountAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 663,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between font-bold text-lg border-t pt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"الإجمالي النهائي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateTotals().finalTotal.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 667,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-green-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"المدفوع:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateTotals().paidAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 671,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-red-600 font-bold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"المتبقي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            calculateTotals().remainingAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 675,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                        lineNumber: 646,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 645,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"ملاحظات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 684,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            name: \"notes\",\n                                            value: formData.notes,\n                                            onChange: handleChange,\n                                            className: \"form-input\",\n                                            rows: \"3\",\n                                            placeholder: \"ملاحظات إضافية...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 685,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 683,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: invoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: handlePrint,\n                                                className: \"btn-secondary flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.PrinterIcon, {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"طباعة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 699,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 697,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: onClose,\n                                                    className: \"btn-secondary\",\n                                                    children: \"إلغاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: loading,\n                                                    className: \"btn-primary\",\n                                                    children: loading ? \"جاري الحفظ...\" : invoice ? \"تحديث\" : \"إنشاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 710,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 696,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                    lineNumber: 356,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InvoicePrintView__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    ref: printRef,\n                    invoice: {\n                        ...formData,\n                        invoiceNumber: (invoice === null || invoice === void 0 ? void 0 : invoice.invoiceNumber) || \"جديدة\",\n                        ...calculateTotals()\n                    },\n                    customer: customers.find((c)=>c.id === formData.customerId)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                    lineNumber: 733,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                lineNumber: 732,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCustomizer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showCustomizer,\n                onClose: ()=>setShowCustomizer(false),\n                product: selectedProduct,\n                onSave: handleCustomizedProduct\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                lineNumber: 745,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PaymentManager__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: showPaymentManager,\n                onClose: ()=>setShowPaymentManager(false),\n                totalAmount: calculateTotals().total,\n                paidAmount: calculateTotals().paidAmount,\n                onPaymentAdd: handlePaymentAdd,\n                existingPayments: formData.payments\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                lineNumber: 753,\n                columnNumber: 7\n            }, this),\n             false && /*#__PURE__*/ 0\n        ]\n    }, void 0, true);\n}\n_s(InvoiceModal, \"DGiUdf9m2Q22iXvk0MtiEmsClKs=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        react_to_print__WEBPACK_IMPORTED_MODULE_4__.useReactToPrint\n    ];\n});\n_c = InvoiceModal;\nvar _c;\n$RefreshReg$(_c, \"InvoiceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/sales/InvoiceModal.js\n"));

/***/ })

});