const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Get all customers with pagination and search
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '', 
      type = 'all',
      status = 'all',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Build where clause
    const where = {
      AND: [
        search ? {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { nameAr: { contains: search, mode: 'insensitive' } },
            { code: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } },
            { phone: { contains: search, mode: 'insensitive' } }
          ]
        } : {},
        type !== 'all' ? { type: type.toUpperCase() } : {},
        status !== 'all' ? { isActive: status === 'active' } : {}
      ]
    };

    // Get customers with pagination
    const [customers, total] = await Promise.all([
      prisma.customer.findMany({
        where,
        orderBy: { [sortBy]: sortOrder },
        skip,
        take
      }),
      prisma.customer.count({ where })
    ]);

    res.json({
      customers,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Get customers error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get single customer
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const customer = await prisma.customer.findUnique({
      where: { id: req.params.id },
      include: {
        salesOrders: {
          take: 10,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            orderNumber: true,
            orderDate: true,
            status: true,
            total: true
          }
        },
        purchaseOrders: {
          take: 10,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            orderNumber: true,
            orderDate: true,
            status: true,
            total: true
          }
        },
        maintenanceOrders: {
          take: 10,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            orderNumber: true,
            receivedDate: true,
            status: true,
            deviceType: true
          }
        }
      }
    });

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    res.json({ customer });

  } catch (error) {
    console.error('Get customer error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create new customer
router.post('/', authenticateToken, authorizeRoles('ADMIN', 'MANAGER', 'SALES'), async (req, res) => {
  try {
    const {
      code,
      name,
      nameAr,
      type = 'CUSTOMER',
      email,
      phone,
      address,
      addressAr,
      balance = 0,
      creditLimit = 0
    } = req.body;

    // Validation
    if (!code || !name || !nameAr || !phone) {
      return res.status(400).json({ error: 'Required fields are missing' });
    }

    // Check if code already exists
    const existingCustomer = await prisma.customer.findUnique({
      where: { code }
    });

    if (existingCustomer) {
      return res.status(400).json({ error: 'Customer code already exists' });
    }

    const customer = await prisma.customer.create({
      data: {
        code,
        name,
        nameAr,
        type: type.toUpperCase(),
        email,
        phone,
        address,
        addressAr,
        balance: parseFloat(balance),
        creditLimit: parseFloat(creditLimit)
      }
    });

    res.status(201).json({
      customer,
      message: 'Customer created successfully'
    });

  } catch (error) {
    console.error('Create customer error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update customer
router.put('/:id', authenticateToken, authorizeRoles('ADMIN', 'MANAGER', 'SALES'), async (req, res) => {
  try {
    const {
      code,
      name,
      nameAr,
      type,
      email,
      phone,
      address,
      addressAr,
      balance,
      creditLimit,
      isActive
    } = req.body;

    // Check if customer exists
    const existingCustomer = await prisma.customer.findUnique({
      where: { id: req.params.id }
    });

    if (!existingCustomer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    // Check if code is being changed and already exists
    if (code && code !== existingCustomer.code) {
      const codeExists = await prisma.customer.findUnique({
        where: { code }
      });

      if (codeExists) {
        return res.status(400).json({ error: 'Customer code already exists' });
      }
    }

    const customer = await prisma.customer.update({
      where: { id: req.params.id },
      data: {
        ...(code && { code }),
        ...(name && { name }),
        ...(nameAr && { nameAr }),
        ...(type && { type: type.toUpperCase() }),
        ...(email !== undefined && { email }),
        ...(phone && { phone }),
        ...(address !== undefined && { address }),
        ...(addressAr !== undefined && { addressAr }),
        ...(balance !== undefined && { balance: parseFloat(balance) }),
        ...(creditLimit !== undefined && { creditLimit: parseFloat(creditLimit) }),
        ...(isActive !== undefined && { isActive })
      }
    });

    res.json({
      customer,
      message: 'Customer updated successfully'
    });

  } catch (error) {
    console.error('Update customer error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete customer
router.delete('/:id', authenticateToken, authorizeRoles('ADMIN', 'MANAGER'), async (req, res) => {
  try {
    // Check if customer exists
    const customer = await prisma.customer.findUnique({
      where: { id: req.params.id }
    });

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    // Check if customer has associated orders
    const [salesOrders, purchaseOrders, maintenanceOrders] = await Promise.all([
      prisma.salesOrder.findFirst({ where: { customerId: req.params.id } }),
      prisma.purchaseOrder.findFirst({ where: { supplierId: req.params.id } }),
      prisma.maintenanceOrder.findFirst({ where: { customerId: req.params.id } })
    ]);

    if (salesOrders || purchaseOrders || maintenanceOrders) {
      return res.status(400).json({ 
        error: 'Cannot delete customer with associated orders. Deactivate instead.' 
      });
    }

    await prisma.customer.delete({
      where: { id: req.params.id }
    });

    res.json({ message: 'Customer deleted successfully' });

  } catch (error) {
    console.error('Delete customer error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get customer balance and transactions
router.get('/:id/balance', authenticateToken, async (req, res) => {
  try {
    const customer = await prisma.customer.findUnique({
      where: { id: req.params.id },
      select: {
        id: true,
        name: true,
        nameAr: true,
        balance: true,
        creditLimit: true
      }
    });

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    // Get recent transactions (sales orders)
    const transactions = await prisma.salesOrder.findMany({
      where: { 
        customerId: req.params.id,
        status: { not: 'CANCELLED' }
      },
      select: {
        id: true,
        orderNumber: true,
        orderDate: true,
        total: true,
        status: true
      },
      orderBy: { orderDate: 'desc' },
      take: 20
    });

    res.json({
      customer,
      transactions
    });

  } catch (error) {
    console.error('Get customer balance error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get customers for dropdown (simplified data)
router.get('/dropdown/list', authenticateToken, async (req, res) => {
  try {
    const { type = 'all' } = req.query;

    const where = {
      isActive: true,
      ...(type !== 'all' ? { type: type.toUpperCase() } : {})
    };

    const customers = await prisma.customer.findMany({
      where,
      select: {
        id: true,
        code: true,
        name: true,
        nameAr: true,
        type: true
      },
      orderBy: { name: 'asc' }
    });

    res.json({ customers });

  } catch (error) {
    console.error('Get customers dropdown error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
