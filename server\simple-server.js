const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3070;

// Middleware
app.use(cors({
  origin: "http://localhost:3071",
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    port: PORT 
  });
});

// Simple auth endpoint for testing
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;
  
  // Simple test credentials
  if (username === 'admin' && password === 'admin123') {
    res.json({
      token: 'test-token-123',
      user: {
        id: '1',
        username: 'admin',
        firstName: 'Admin',
        lastName: 'User',
        role: 'ADMIN'
      },
      message: 'Login successful'
    });
  } else {
    res.status(401).json({ error: 'Invalid credentials' });
  }
});

// Simple products endpoint
app.get('/api/products', (req, res) => {
  res.json({
    products: [
      {
        id: '1',
        code: 'LAPTOP001',
        name: 'Custom Business Laptop',
        nameAr: 'لابتوب أعمال مخصص',
        productType: 'CUSTOMIZABLE',
        unitPrice: 999.99,
        currentStock: 15
      },
      {
        id: '2',
        code: 'CPU001',
        name: 'Intel Core i7-13700K',
        nameAr: 'معالج إنتل كور i7-13700K',
        productType: 'COMPONENT',
        unitPrice: 399.99,
        currentStock: 15
      }
    ]
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

app.listen(PORT, () => {
  console.log(`🚀 Simple Server running on port ${PORT}`);
  console.log(`📡 Health check: http://localhost:${PORT}/api/health`);
});

module.exports = app;
