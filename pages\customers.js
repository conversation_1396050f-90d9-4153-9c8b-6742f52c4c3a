import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import axios from 'axios';
import toast from 'react-hot-toast';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  UserIcon,
  TruckIcon,
  UsersIcon,
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/LoadingSpinner';
import { ViewCustomerModal, CustomerFormModal } from '../components/CustomerModals';

export default function Customers() {
  const { t } = useTranslation('common');
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedType, setSelectedType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  // Fetch customers
  const { data: customersData, isLoading, error, refetch } = useQuery(
    ['customers', currentPage, searchTerm, selectedType, selectedStatus],
    async () => {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        search: searchTerm,
        type: selectedType,
        status: selectedStatus,
      });

      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/customers?${params}`);
      return response.data;
    },
    {
      keepPreviousData: true,
    }
  );

  const customers = customersData?.customers || [];
  const pagination = customersData?.pagination || {};

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    refetch();
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Delete customer mutation
  const deleteMutation = useMutation(
    async (customerId) => {
      const response = await axios.delete(`${process.env.NEXT_PUBLIC_API_URL}/api/customers/${customerId}`);
      return response.data;
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['customers']);
        toast.success(t('customers.deleteSuccess') || 'Customer deleted successfully');
      },
      onError: (error) => {
        toast.error(error.response?.data?.error || 'Failed to delete customer');
      }
    }
  );

  // Handle delete customer
  const handleDelete = async (customer) => {
    if (window.confirm(t('customers.confirmDelete') || `Are you sure you want to delete ${customer.name}?`)) {
      deleteMutation.mutate(customer.id);
    }
  };

  // Handle view customer
  const handleView = (customer) => {
    setSelectedCustomer(customer);
    setShowViewModal(true);
  };

  // Handle edit customer
  const handleEdit = (customer) => {
    setSelectedCustomer(customer);
    setShowEditModal(true);
  };

  // Handle create customer
  const handleCreate = () => {
    setSelectedCustomer(null);
    setShowCreateModal(true);
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'CUSTOMER':
        return <UserIcon className="h-4 w-4" />;
      case 'SUPPLIER':
        return <TruckIcon className="h-4 w-4" />;
      case 'BOTH':
        return <UsersIcon className="h-4 w-4" />;
      default:
        return <UserIcon className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'CUSTOMER':
        return 'bg-blue-100 text-blue-800';
      case 'SUPPLIER':
        return 'bg-green-100 text-green-800';
      case 'BOTH':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading && !customers.length) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h3 className="mt-2 text-sm font-medium text-gray-900">{t('common.error')}</h3>
        <p className="mt-1 text-sm text-gray-500">Failed to load customers</p>
        <button
          onClick={() => refetch()}
          className="mt-4 btn-primary"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{t('customers.title')}</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage your customers and suppliers
          </p>
        </div>
        <button onClick={handleCreate} className="btn-primary">
          <PlusIcon className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2" />
          {t('customers.addCustomer')}
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder={t('common.search')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input pl-10 rtl:pl-3 rtl:pr-10"
              />
            </div>
          </div>
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="form-input"
          >
            <option value="all">All Types</option>
            <option value="customer">{t('customers.customer')}</option>
            <option value="supplier">{t('customers.supplier')}</option>
            <option value="both">{t('customers.both')}</option>
          </select>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="form-input"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
          <button type="submit" className="btn-primary">
            {t('common.search')}
          </button>
        </form>
      </div>

      {/* Customers Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="table">
            <thead>
              <tr>
                <th>{t('customers.customerCode')}</th>
                <th>{t('customers.customerName')}</th>
                <th>{t('customers.customerType')}</th>
                <th>{t('customers.phone')}</th>
                <th>{t('customers.balance')}</th>
                <th>Status</th>
                <th>{t('common.actions')}</th>
              </tr>
            </thead>
            <tbody>
              {customers.map((customer) => (
                <tr key={customer.id}>
                  <td className="font-medium">{customer.code}</td>
                  <td>
                    <div>
                      <div className="font-medium text-gray-900">{customer.name}</div>
                      <div className="text-sm text-gray-500">{customer.nameAr}</div>
                    </div>
                  </td>
                  <td>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(customer.type)}`}>
                      {getTypeIcon(customer.type)}
                      <span className="ml-1 rtl:ml-0 rtl:mr-1">
                        {t(`customers.${customer.type.toLowerCase()}`)}
                      </span>
                    </span>
                  </td>
                  <td>{customer.phone}</td>
                  <td>
                    <span className={`font-medium ${(customer.balance || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      ${(parseFloat(customer.balance) || 0).toFixed(2)}
                    </span>
                  </td>
                  <td>
                    <span className={`badge ${customer.isActive ? 'badge-success' : 'badge-secondary'}`}>
                      {customer.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <button
                        onClick={() => handleView(customer)}
                        className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        title={t('common.view')}
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleEdit(customer)}
                        className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                        title={t('common.edit')}
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(customer)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title={t('common.delete')}
                        disabled={deleteMutation.isLoading}
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="px-6 py-3 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                {pagination.total} results
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                  const page = i + Math.max(1, pagination.page - 2);
                  if (page > pagination.pages) return null;
                  return (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`btn-sm ${
                        page === pagination.page ? 'btn-primary' : 'btn-secondary'
                      }`}
                    >
                      {page}
                    </button>
                  );
                })}
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.pages}
                  className="btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {customers.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <UsersIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No customers found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating your first customer.
            </p>
            <div className="mt-6">
              <button onClick={handleCreate} className="btn-primary">
                <PlusIcon className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2" />
                {t('customers.addCustomer')}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <ViewCustomerModal
        customer={selectedCustomer}
        isOpen={showViewModal}
        onClose={() => setShowViewModal(false)}
      />

      <CustomerFormModal
        customer={selectedCustomer}
        isOpen={showCreateModal || showEditModal}
        onClose={() => {
          setShowCreateModal(false);
          setShowEditModal(false);
          setSelectedCustomer(null);
        }}
        onSuccess={() => {
          setShowCreateModal(false);
          setShowEditModal(false);
          setSelectedCustomer(null);
        }}
      />
    </div>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  };
}
