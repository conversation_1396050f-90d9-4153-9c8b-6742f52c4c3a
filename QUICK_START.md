# 🚀 Quick Start Guide - Complete ERP System

## ⚡ **Fastest Way to Get Started**

### **1. Prerequisites Check**
```bash
# Check Node.js version (need 18+)
node --version

# Check PostgreSQL (need 12+)
psql --version
```

### **2. Database Setup**
```sql
-- Connect to PostgreSQL as superuser
CREATE DATABASE business_management_db;
CREATE USER bms_user WITH PASSWORD 'bms123';
GRANT ALL PRIVILEGES ON DATABASE business_management_db TO bms_user;
```

### **3. Environment Configuration**
```bash
# Copy environment file
cp .env.example .env
```

**Edit `.env` file:**
```env
DATABASE_URL="postgresql://bms_user:bms123@localhost:5432/business_management_db"
JWT_SECRET="your-super-secret-jwt-key-make-it-very-long-and-random-for-security"
PORT=3001
NEXT_PUBLIC_API_URL="http://localhost:3001"
NEXT_PUBLIC_SOCKET_URL="http://localhost:3001"
NEXT_PUBLIC_COMPANY_NAME="Your Business Name"
```

### **4. One-Command Setup**
```bash
# Install dependencies and setup database
npm run setup
```

### **5. Start the System**
```bash
# Start both frontend and backend
npm run dev:all
```

**OR start separately:**
```bash
# Terminal 1 - Backend
npm run server:dev

# Terminal 2 - Frontend  
npm run dev
```

### **6. Access Your System**
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **Database Studio**: `npm run db:studio`

## 🔑 **Login Credentials**

| Role | Username | Password | Access Level |
|------|----------|----------|--------------|
| **Admin** | admin | admin123 | Full system access |
| **Manager** | manager | manager123 | Business operations |
| **Sales** | sales | sales123 | Sales & customers |

## 🏢 **Pre-configured Branches**

| Branch | Code | Manager | Cash Box |
|--------|------|---------|----------|
| **Main Branch** | MAIN | Business Manager | $10,000 |
| **North Branch** | NORTH | Sales Representative | $5,000 |
| **South Branch** | SOUTH | (Unassigned) | $3,000 |

## 📦 **Sample Data Included**

### **Products:**
- Business Laptop ($999.99)
- Smartphone Pro ($799.99)
- Business Tablet ($499.99)
- Wireless Mouse ($29.99)
- Mechanical Keyboard ($89.99)

### **Customers:**
- ABC Corporation (Customer)
- XYZ Trading (Customer)
- Tech Supplier Inc (Supplier)
- Global Electronics (Both)

## 🎯 **What You Can Do Immediately**

### **Sales Operations:**
1. **Create Sales Orders** with multiple products
2. **Process Payments** using any of 5 payment methods
3. **Split Payments** across multiple methods
4. **Track Inventory** automatically updated per branch
5. **Generate Reports** for sales performance

### **Purchase Operations:**
1. **Create Purchase Orders** from suppliers
2. **Receive Goods** with automatic inventory updates
3. **Update Cost Prices** during goods receipt
4. **Track Payments** to suppliers

### **Maintenance Services:**
1. **Receive Devices** for repair
2. **Add Diagnosis** and cost estimates
3. **Track Status** through repair stages
4. **Process Payments** when completed
5. **Generate Service Reports**

### **Multi-Branch Management:**
1. **Transfer Inventory** between branches
2. **Manage Cash Boxes** per branch
3. **Transfer to Treasury** at end of day
4. **View Branch Performance** reports

### **Business Intelligence:**
1. **Real-time Dashboard** with live metrics
2. **Comprehensive Reports** for all operations
3. **Financial Analysis** with cash flow tracking
4. **Customer Analytics** and performance

## 🔧 **Troubleshooting**

### **Database Connection Issues:**
```bash
# Test database connection
psql "postgresql://bms_user:bms123@localhost:5432/business_management_db"

# Reset database if needed
npm run db:reset
npm run db:seed
```

### **Port Conflicts:**
```bash
# Use different ports
npm run dev -- -p 3002  # Frontend
# Edit .env PORT=3002     # Backend
```

### **Permission Issues:**
```bash
# Clear cache and reinstall
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

## 📱 **Mobile Access**

The system is fully responsive and works on:
- ✅ Desktop computers
- ✅ Tablets
- ✅ Mobile phones
- ✅ Both portrait and landscape modes

## 🌐 **Language Support**

- **Arabic (العربية)** - Full RTL support
- **English** - Default language
- **Switch anytime** using the language button in header

## 🔐 **Security Features**

- ✅ **JWT Authentication** with secure tokens
- ✅ **Role-based Access Control** 
- ✅ **Branch-level Security** 
- ✅ **Password Hashing** with bcrypt
- ✅ **Input Validation** on all forms
- ✅ **CORS Protection** for API

## 📊 **Business Metrics Available**

### **Dashboard Shows:**
- Total products, customers, suppliers
- Low stock alerts
- Pending orders (sales, purchase, maintenance)
- Monthly/yearly revenue
- Recent activities
- Sales trends charts
- Top selling products

### **Reports Available:**
- Sales performance by date/branch/customer
- Purchase analysis by supplier/branch
- Inventory levels and valuation
- Maintenance service performance
- Financial cash flow analysis
- Customer behavior and revenue
- Product performance and profitability

## 🎉 **You're Ready!**

Your complete ERP system is now running with:
- ✅ **Multi-branch operations**
- ✅ **Complete sales cycle**
- ✅ **Full maintenance workflow**
- ✅ **Purchase management with goods receipt**
- ✅ **All payment methods**
- ✅ **Treasury management**
- ✅ **Comprehensive reporting**
- ✅ **Real-time updates**
- ✅ **Bilingual interface**

**Start exploring and managing your computer/laptop sales and maintenance business!** 🚀
