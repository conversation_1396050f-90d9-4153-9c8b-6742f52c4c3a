import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useQuery } from 'react-query';
import axios from 'axios';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  EyeIcon,
  ShoppingBagIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/LoadingSpinner';

export default function Purchases() {
  const { t } = useTranslation('common');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedStatus, setSelectedStatus] = useState('all');

  // Fetch purchase orders
  const { data: purchasesData, isLoading, error, refetch } = useQuery(
    ['purchases', currentPage, searchTerm, selectedStatus],
    async () => {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        search: searchTerm,
        status: selectedStatus,
      });

      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/purchases?${params}`);
      return response.data;
    },
    {
      keepPreviousData: true,
    }
  );

  const orders = purchasesData?.orders || [];
  const pagination = purchasesData?.pagination || {};

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    refetch();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'CONFIRMED':
        return 'bg-blue-100 text-blue-800';
      case 'SHIPPED':
        return 'bg-indigo-100 text-indigo-800';
      case 'DELIVERED':
        return 'bg-green-100 text-green-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading && !orders.length) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h3 className="mt-2 text-sm font-medium text-gray-900">{t('common.error')}</h3>
        <p className="mt-1 text-sm text-gray-500">Failed to load purchase orders</p>
        <button
          onClick={() => refetch()}
          className="mt-4 btn-primary"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{t('purchases.title')}</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage your purchase orders and supplier relationships
          </p>
        </div>
        <button className="btn-primary">
          <PlusIcon className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2" />
          {t('purchases.newOrder')}
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder={t('common.search')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input pl-10 rtl:pl-3 rtl:pr-10"
              />
            </div>
          </div>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="form-input"
          >
            <option value="all">All Status</option>
            <option value="draft">{t('purchases.draft')}</option>
            <option value="pending">{t('purchases.pending')}</option>
            <option value="confirmed">{t('purchases.confirmed')}</option>
            <option value="shipped">{t('purchases.shipped')}</option>
            <option value="delivered">{t('purchases.delivered')}</option>
            <option value="cancelled">{t('purchases.cancelled')}</option>
          </select>
          <button type="submit" className="btn-primary">
            {t('common.search')}
          </button>
        </form>
      </div>

      {/* Purchase Orders Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="table">
            <thead>
              <tr>
                <th>{t('purchases.orderNumber')}</th>
                <th>{t('purchases.supplier')}</th>
                <th>{t('purchases.orderDate')}</th>
                <th>{t('purchases.expectedDate')}</th>
                <th>{t('purchases.total')}</th>
                <th>{t('purchases.status')}</th>
                <th>{t('common.actions')}</th>
              </tr>
            </thead>
            <tbody>
              {orders.map((order) => (
                <tr key={order.id}>
                  <td className="font-medium">{order.orderNumber}</td>
                  <td>
                    <div>
                      <div className="font-medium text-gray-900">{order.supplier?.name}</div>
                      <div className="text-sm text-gray-500">{order.supplier?.nameAr}</div>
                    </div>
                  </td>
                  <td>
                    {new Date(order.orderDate).toLocaleDateString()}
                  </td>
                  <td>
                    {order.expectedDate ? new Date(order.expectedDate).toLocaleDateString() : '-'}
                  </td>
                  <td>
                    <div className="flex items-center">
                      <CurrencyDollarIcon className="h-4 w-4 text-gray-400 mr-1" />
                      <span className="font-medium">${order.total.toFixed(2)}</span>
                    </div>
                  </td>
                  <td>
                    <span className={`badge ${getStatusColor(order.status)}`}>
                      {order.status}
                    </span>
                  </td>
                  <td>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <button
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title={t('common.view')}
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        className="p-1 text-gray-400 hover:text-green-600"
                        title={t('common.edit')}
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Empty State */}
        {orders.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <ShoppingBagIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No purchase orders found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating your first purchase order.
            </p>
            <div className="mt-6">
              <button className="btn-primary">
                <PlusIcon className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2" />
                {t('purchases.newOrder')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  };
}
