"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "__barrel_optimize__?names=MagnifyingGlassIcon,PlusIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!********************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=MagnifyingGlassIcon,PlusIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \********************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MagnifyingGlassIcon: function() { return /* reexport safe */ _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   PlusIcon: function() { return /* reexport safe */ _PlusIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   TrashIcon: function() { return /* reexport safe */ _TrashIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   XMarkIcon: function() { return /* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./MagnifyingGlassIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _PlusIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./PlusIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _TrashIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TrashIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1NYWduaWZ5aW5nR2xhc3NJY29uLFBsdXNJY29uLFRyYXNoSWNvbixYTWFya0ljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUN5RTtBQUN0QjtBQUNFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzPzE5MjMiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1hZ25pZnlpbmdHbGFzc0ljb24gfSBmcm9tIFwiLi9NYWduaWZ5aW5nR2xhc3NJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGx1c0ljb24gfSBmcm9tIFwiLi9QbHVzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRyYXNoSWNvbiB9IGZyb20gXCIuL1RyYXNoSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFhNYXJrSWNvbiB9IGZyb20gXCIuL1hNYXJrSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=MagnifyingGlassIcon,PlusIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "./components/sales/SalesReturnModal.js":
/*!**********************************************!*\
  !*** ./components/sales/SalesReturnModal.js ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SalesReturnModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_MagnifyingGlassIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MagnifyingGlassIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=MagnifyingGlassIcon,PlusIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SalesReturnModal(param) {\n    let { isOpen, onClose, onSave, salesReturn = null } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedInvoice, setSelectedInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchInvoice, setSearchInvoice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: \"\",\n        invoiceId: \"\",\n        invoiceNumber: \"\",\n        customerName: \"\",\n        returnDate: new Date().toISOString().split(\"T\")[0],\n        reason: \"\",\n        notes: \"\",\n        items: [],\n        refundMethod: \"CASH\",\n        refundAmount: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            loadCustomers();\n            if (salesReturn) {\n                setFormData({\n                    customerId: salesReturn.customerId || \"\",\n                    invoiceId: salesReturn.invoiceId || \"\",\n                    invoiceNumber: salesReturn.invoiceNumber || \"\",\n                    customerName: salesReturn.customerName || \"\",\n                    returnDate: salesReturn.returnDate ? salesReturn.returnDate.split(\"T\")[0] : new Date().toISOString().split(\"T\")[0],\n                    reason: salesReturn.reason || \"\",\n                    notes: salesReturn.notes || \"\",\n                    items: salesReturn.items || [],\n                    refundMethod: salesReturn.refundMethod || \"CASH\",\n                    refundAmount: salesReturn.refundAmount || 0\n                });\n            }\n        }\n    }, [\n        isOpen,\n        salesReturn\n    ]);\n    const loadCustomers = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/customers\"));\n            setCustomers(response.data.customers || []);\n        } catch (error) {\n            console.error(\"Error loading customers:\", error);\n        }\n    };\n    const searchInvoices = async (query)=>{\n        if (!query || query.length < 3) {\n            setInvoices([]);\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/invoices/search?q=\").concat(query));\n            setInvoices(response.data.invoices || []);\n        } catch (error) {\n            console.error(\"Error searching invoices:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"خطأ في البحث عن الفواتير\");\n        }\n    };\n    const handleInvoiceSearch = (value)=>{\n        setSearchInvoice(value);\n        searchInvoices(value);\n    };\n    const selectInvoice = (invoice)=>{\n        setSelectedInvoice(invoice);\n        setFormData((prev)=>({\n                ...prev,\n                customerId: invoice.customerId,\n                invoiceId: invoice.id,\n                invoiceNumber: invoice.invoiceNumber,\n                customerName: invoice.customerName,\n                items: invoice.items.map((item)=>({\n                        ...item,\n                        returnQuantity: 0,\n                        returnReason: \"\",\n                        canReturn: true,\n                        maxReturnQuantity: item.quantity\n                    }))\n            }));\n        setInvoices([]);\n        setSearchInvoice(invoice.invoiceNumber);\n    };\n    const updateReturnItem = (index, field, value)=>{\n        setFormData((prev)=>{\n            const newItems = [\n                ...prev.items\n            ];\n            newItems[index] = {\n                ...newItems[index],\n                [field]: value\n            };\n            // Validate return quantity\n            if (field === \"returnQuantity\") {\n                const maxQty = newItems[index].maxReturnQuantity;\n                if (parseFloat(value) > maxQty) {\n                    newItems[index].returnQuantity = maxQty;\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].warning(\"الحد الأقصى للإرجاع: \".concat(maxQty));\n                }\n            }\n            return {\n                ...prev,\n                items: newItems\n            };\n        });\n    };\n    const calculateRefundAmount = ()=>{\n        return formData.items.reduce((total, item)=>{\n            const returnQty = parseFloat(item.returnQuantity) || 0;\n            const unitPrice = parseFloat(item.unitPrice) || 0;\n            const discount = parseFloat(item.discount) || 0;\n            const itemTotal = returnQty * unitPrice;\n            const discountAmount = itemTotal * (discount / 100);\n            const afterDiscount = itemTotal - discountAmount;\n            // Add tax if applicable\n            const taxAmount = item.hasTax ? afterDiscount * (parseFloat(item.taxRate) || 0) / 100 : 0;\n            return total + afterDiscount + taxAmount;\n        }, 0);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.invoiceId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى اختيار الفاتورة\");\n            return;\n        }\n        const returningItems = formData.items.filter((item)=>parseFloat(item.returnQuantity) > 0);\n        if (returningItems.length === 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى تحديد العناصر المراد إرجاعها\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const refundAmount = calculateRefundAmount();\n            const returnData = {\n                ...formData,\n                items: returningItems,\n                refundAmount,\n                status: \"PENDING\"\n            };\n            const response = salesReturn ? await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].put(\"\".concat(\"http://localhost:3070\", \"/api/sales-returns/\").concat(salesReturn.id), returnData) : await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(\"\".concat(\"http://localhost:3070\", \"/api/sales-returns\"), returnData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.data.message || (salesReturn ? \"تم تحديث المرتجع\" : \"تم إنشاء المرتجع\"));\n            onSave(response.data.salesReturn);\n            onClose();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"حدث خطأ\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    const refundAmount = calculateRefundAmount();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: salesReturn ? \"تعديل مرتجع المبيعات\" : \"مرتجع مبيعات جديد\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.XMarkIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-blue-900 mb-3\",\n                                    children: \"البحث عن الفاتورة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MagnifyingGlassIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.MagnifyingGlassIcon, {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: searchInvoice,\n                                            onChange: (e)=>handleInvoiceSearch(e.target.value),\n                                            className: \"form-input pl-10\",\n                                            placeholder: \"ابحث برقم الفاتورة أو اسم العميل...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this),\n                                        invoices.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto\",\n                                            children: invoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    onClick: ()=>selectInvoice(invoice),\n                                                    className: \"p-3 hover:bg-gray-50 cursor-pointer border-b\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: invoice.invoiceNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                        lineNumber: 216,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: invoice.customerName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                        lineNumber: 217,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            parseFloat(invoice.finalTotal || 0).toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: new Date(invoice.createdAt).toLocaleDateString(\"ar-EG\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, invoice.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        selectedInvoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-green-900 mb-3\",\n                                    children: \"معلومات الفاتورة المختارة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"رقم الفاتورة:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" \",\n                                                        selectedInvoice.invoiceNumber\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"العميل:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" \",\n                                                        selectedInvoice.customerName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"التاريخ:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" \",\n                                                        new Date(selectedInvoice.createdAt).toLocaleDateString(\"ar-EG\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"الإجمالي:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 22\n                                                        }, this),\n                                                        \" $\",\n                                                        parseFloat(selectedInvoice.finalTotal || 0).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"تاريخ الإرجاع\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: formData.returnDate,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        returnDate: e.target.value\n                                                    })),\n                                            className: \"form-input\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"طريقة الاسترداد\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.refundMethod,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        refundMethod: e.target.value\n                                                    })),\n                                            className: \"form-input\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"CASH\",\n                                                    children: \"نقدي\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"CREDIT\",\n                                                    children: \"رصيد للعميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"EXCHANGE\",\n                                                    children: \"استبدال\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        formData.items.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                    children: \"عناصر الإرجاع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: formData.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-12 gap-4 items-end p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"المنتج\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900 py-2\",\n                                                            children: [\n                                                                item.productName,\n                                                                item.customizationDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600 mt-1\",\n                                                                    children: item.customizationDetails.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                \"• \",\n                                                                                detail.optionName,\n                                                                                \": \",\n                                                                                detail.selectedName\n                                                                            ]\n                                                                        }, idx, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"الكمية الأصلية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900 py-2\",\n                                                            children: item.quantity\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"كمية الإرجاع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.returnQuantity,\n                                                            onChange: (e)=>updateReturnItem(index, \"returnQuantity\", e.target.value),\n                                                            className: \"form-input\",\n                                                            min: \"0\",\n                                                            max: item.maxReturnQuantity\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"السعر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900 py-2\",\n                                                            children: [\n                                                                \"$\",\n                                                                parseFloat(item.unitPrice).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"سبب الإرجاع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: item.returnReason,\n                                                            onChange: (e)=>updateReturnItem(index, \"returnReason\", e.target.value),\n                                                            className: \"form-input\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر السبب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"DEFECTIVE\",\n                                                                    children: \"عيب في المنتج\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"WRONG_ITEM\",\n                                                                    children: \"منتج خاطئ\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"CUSTOMER_CHANGE\",\n                                                                    children: \"تغيير رأي العميل\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"DAMAGED\",\n                                                                    children: \"تلف أثناء الشحن\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"OTHER\",\n                                                                    children: \"أخرى\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 281,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this),\n                        refundAmount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-3\",\n                                    children: \"ملخص الاسترداد\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"إجمالي المبلغ المسترد:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-green-600\",\n                                            children: [\n                                                \"$\",\n                                                refundAmount.toFixed(2)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 345,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"سبب الإرجاع العام\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: formData.reason,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        reason: e.target.value\n                                                    })),\n                                            className: \"form-input\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"اختر السبب\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"DEFECTIVE\",\n                                                    children: \"عيب في المنتج\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"WRONG_ITEM\",\n                                                    children: \"منتج خاطئ\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"CUSTOMER_CHANGE\",\n                                                    children: \"تغيير رأي العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"DAMAGED\",\n                                                    children: \"تلف أثناء الشحن\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"WARRANTY\",\n                                                    children: \"مطالبة ضمان\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"OTHER\",\n                                                    children: \"أخرى\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"ملاحظات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: formData.notes,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        notes: e.target.value\n                                                    })),\n                                            className: \"form-input\",\n                                            rows: \"3\",\n                                            placeholder: \"ملاحظات إضافية...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"btn-secondary\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading || !selectedInvoice,\n                                    className: \"btn-primary\",\n                                    children: loading ? \"جاري الحفظ...\" : salesReturn ? \"تحديث\" : \"إنشاء المرتجع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                            lineNumber: 387,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\SalesReturnModal.js\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n_s(SalesReturnModal, \"jTQxBLV2Y2W2/T1v0aBuwUCeuBA=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = SalesReturnModal;\nvar _c;\n$RefreshReg$(_c, \"SalesReturnModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/sales/SalesReturnModal.js\n"));

/***/ }),

/***/ "./pages/sales/index.js":
/*!******************************!*\
  !*** ./pages/sales/index.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SalesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _components_sales_QuoteModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/sales/QuoteModal */ \"./components/sales/QuoteModal.js\");\n/* harmony import */ var _components_sales_SalesOrderModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/sales/SalesOrderModal */ \"./components/sales/SalesOrderModal.js\");\n/* harmony import */ var _components_sales_InvoiceModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../components/sales/InvoiceModal */ \"./components/sales/InvoiceModal.js\");\n/* harmony import */ var _components_sales_SalesReturnModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../components/sales/SalesReturnModal */ \"./components/sales/SalesReturnModal.js\");\n/* harmony import */ var _components_sales_ProductCustomizer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../components/sales/ProductCustomizer */ \"./components/sales/ProductCustomizer.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,ReceiptPercentIcon,ShoppingCartIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,ReceiptPercentIcon,ShoppingCartIcon,XCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SalesPage() {\n    _s();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"quotes\");\n    // Modal states\n    const [quoteModal, setQuoteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        quote: null\n    });\n    const [salesOrderModal, setSalesOrderModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        salesOrder: null,\n        fromQuote: null\n    });\n    const [invoiceModal, setInvoiceModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        invoice: null,\n        fromSalesOrder: null\n    });\n    // Data states\n    const [quotes, setQuotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [salesOrders, setSalesOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // التحقق من تسجيل الدخول\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && !user) {\n            router.push(\"/login\");\n        }\n    }, [\n        user,\n        isLoading,\n        router\n    ]);\n    // عرض شاشة التحميل أثناء التحقق من المصادقة\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 54,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this);\n    }\n    // إذا لم يكن المستخدم مسجل دخول، لا تعرض شيء (سيتم التوجيه)\n    if (!user) {\n        return null;\n    }\n    // Load data when tab changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            loadData();\n        }\n    }, [\n        activeTab,\n        user\n    ]);\n    const loadData = async ()=>{\n        setLoading(true);\n        try {\n            if (activeTab === \"quotes\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/quotes\"));\n                setQuotes(response.data.quotes || []);\n            } else if (activeTab === \"orders\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/sales-orders\"));\n                setSalesOrders(response.data.salesOrders || []);\n            } else if (activeTab === \"invoices\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/invoices\"));\n                setInvoices(response.data.invoices || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"خطأ في تحميل البيانات\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Modal handlers\n    const handleQuoteCreate = ()=>{\n        setQuoteModal({\n            isOpen: true,\n            quote: null\n        });\n    };\n    const handleQuoteEdit = (quote)=>{\n        setQuoteModal({\n            isOpen: true,\n            quote\n        });\n    };\n    const handleQuoteConvert = (quote)=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder: null,\n            fromQuote: quote\n        });\n    };\n    const handleSalesOrderCreate = ()=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder: null,\n            fromQuote: null\n        });\n    };\n    const handleSalesOrderEdit = (salesOrder)=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder,\n            fromQuote: null\n        });\n    };\n    const handleSalesOrderConvert = (salesOrder)=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice: null,\n            fromSalesOrder: salesOrder\n        });\n    };\n    const handleInvoiceCreate = ()=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice: null,\n            fromSalesOrder: null\n        });\n    };\n    const handleInvoiceEdit = (invoice)=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice,\n            fromSalesOrder: null\n        });\n    };\n    // Save handlers\n    const handleQuoteSave = (quote)=>{\n        loadData();\n    };\n    const handleSalesOrderSave = (salesOrder)=>{\n        loadData();\n    };\n    const handleInvoiceSave = (invoice)=>{\n        loadData();\n    };\n    const tabs = [\n        {\n            id: \"quotes\",\n            name: \"عروض الأسعار\",\n            nameEn: \"Quotes\",\n            icon: _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.DocumentTextIcon,\n            color: \"blue\",\n            description: \"لا تؤثر على المخزون - صالحة لمدة محددة\"\n        },\n        {\n            id: \"orders\",\n            name: \"أوامر البيع\",\n            nameEn: \"Sales Orders\",\n            icon: _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ShoppingCartIcon,\n            color: \"orange\",\n            description: \"تحجز من المخزون - لها مدة صلاحية\"\n        },\n        {\n            id: \"invoices\",\n            name: \"الفواتير\",\n            nameEn: \"Invoices\",\n            icon: _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ReceiptPercentIcon,\n            color: \"green\",\n            description: \"تخصم من المخزون نهائياً - تحسب في المبيعات\"\n        }\n    ];\n    const getStatusColor = (status)=>{\n        const colors = {\n            \"DRAFT\": \"gray\",\n            \"PENDING\": \"yellow\",\n            \"APPROVED\": \"green\",\n            \"REJECTED\": \"red\",\n            \"EXPIRED\": \"red\",\n            \"CONFIRMED\": \"blue\",\n            \"SHIPPED\": \"purple\",\n            \"DELIVERED\": \"green\",\n            \"CANCELLED\": \"red\",\n            \"PAID\": \"green\",\n            \"PARTIALLY_PAID\": \"yellow\",\n            \"OVERDUE\": \"red\"\n        };\n        return colors[status] || \"gray\";\n    };\n    const getStatusText = (status)=>{\n        const statusTexts = {\n            \"DRAFT\": \"مسودة\",\n            \"PENDING\": \"في الانتظار\",\n            \"APPROVED\": \"موافق عليه\",\n            \"REJECTED\": \"مرفوض\",\n            \"EXPIRED\": \"منتهي الصلاحية\",\n            \"CONFIRMED\": \"مؤكد\",\n            \"SHIPPED\": \"تم الشحن\",\n            \"DELIVERED\": \"تم التسليم\",\n            \"CANCELLED\": \"ملغي\",\n            \"PAID\": \"مدفوع\",\n            \"PARTIALLY_PAID\": \"مدفوع جزئياً\",\n            \"OVERDUE\": \"متأخر\"\n        };\n        return statusTexts[status] || status;\n    };\n    const renderQuotes = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"عروض الأسعار\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 207,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleQuoteCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 212,\n                                    columnNumber: 11\n                                }, this),\n                                \"عرض سعر جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 208,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 206,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم العرض\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"صالح حتى\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: quotes.map((quote)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: quote.quoteNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: quote.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(quote.status), \"-100 text-\").concat(getStatusColor(quote.status), \"-800\"),\n                                                    children: getStatusText(quote.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    quote.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: quote.validUntil\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuoteEdit(quote),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    quote.status === \"APPROVED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuoteConvert(quote),\n                                                        className: \"text-green-600 hover:text-green-900 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                                className: \"h-5 w-5 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"تحويل لأمر\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, quote.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 217,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 205,\n            columnNumber: 5\n        }, this);\n    const renderOrders = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"أوامر البيع\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 289,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSalesOrderCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 294,\n                                    columnNumber: 11\n                                }, this),\n                                \"أمر بيع جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 290,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 288,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم الأمر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: salesOrders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: order.orderNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: order.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(order.status), \"-100 text-\").concat(getStatusColor(order.status), \"-800\"),\n                                                    children: getStatusText(order.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    order.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: order.reservedStock ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ClockIcon, {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"محجوز\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                                    children: \"غير محجوز\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSalesOrderEdit(order),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    order.status === \"CONFIRMED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSalesOrderConvert(order),\n                                                        className: \"text-green-600 hover:text-green-900 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                                className: \"h-5 w-5 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"تحويل لفاتورة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, order.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 299,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 287,\n            columnNumber: 5\n        }, this);\n    const renderInvoices = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"الفواتير\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 380,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleInvoiceCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 385,\n                                    columnNumber: 11\n                                }, this),\n                                \"فاتورة جديدة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 381,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 379,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم الفاتورة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ الإجمالي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المدفوع\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 406,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 392,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: invoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: invoice.invoiceNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 417,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: invoice.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(invoice.status), \"-100 text-\").concat(getStatusColor(invoice.status), \"-800\"),\n                                                    children: getStatusText(invoice.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    invoice.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    invoice.paidAmount.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleInvoiceEdit(invoice),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-green-600 hover:text-green-900\",\n                                                        children: \"طباعة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, invoice.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 416,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 414,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 391,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 390,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 378,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: \"إدارة المبيعات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 458,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"نظام المبيعات بثلاث مراحل: عروض الأسعار → أوامر البيع → الفواتير\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 461,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-8 mb-6\",\n                                children: tabs.map((tab, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center p-4 rounded-lg border-2 \".concat(activeTab === tab.id ? \"border-\".concat(tab.color, \"-500 bg-\").concat(tab.color, \"-50\") : \"border-gray-200 bg-gray-50\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                        className: \"h-8 w-8 mb-2 \".concat(activeTab === tab.id ? \"text-\".concat(tab.color, \"-600\") : \"text-gray-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium \".concat(activeTab === tab.id ? \"text-\".concat(tab.color, \"-900\") : \"text-gray-600\"),\n                                                        children: tab.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 text-center mt-1\",\n                                                        children: tab.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this),\n                                            index < tabs.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                className: \"h-6 w-6 text-gray-400 mx-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 487,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8 px-6\",\n                                    children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-\".concat(tab.color, \"-500 text-\").concat(tab.color, \"-600\") : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                    className: \"h-5 w-5 inline-block ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 19\n                                                }, this),\n                                                tab.name\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 496,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    activeTab === \"quotes\" && renderQuotes(),\n                                    activeTab === \"orders\" && renderOrders(),\n                                    activeTab === \"invoices\" && renderInvoices()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 515,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 495,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 455,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_QuoteModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: quoteModal.isOpen,\n                onClose: ()=>setQuoteModal({\n                        isOpen: false,\n                        quote: null\n                    }),\n                onSave: handleQuoteSave,\n                quote: quoteModal.quote\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 524,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_SalesOrderModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: salesOrderModal.isOpen,\n                onClose: ()=>setSalesOrderModal({\n                        isOpen: false,\n                        salesOrder: null,\n                        fromQuote: null\n                    }),\n                onSave: handleSalesOrderSave,\n                salesOrder: salesOrderModal.salesOrder,\n                fromQuote: salesOrderModal.fromQuote\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 531,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_InvoiceModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: invoiceModal.isOpen,\n                onClose: ()=>setInvoiceModal({\n                        isOpen: false,\n                        invoice: null,\n                        fromSalesOrder: null\n                    }),\n                onSave: handleInvoiceSave,\n                invoice: invoiceModal.invoice,\n                fromSalesOrder: invoiceModal.fromSalesOrder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 539,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n        lineNumber: 454,\n        columnNumber: 5\n    }, this);\n}\n_s(SalesPage, \"yUseFTpiU5OSB6IobWg+i9H4Ruc=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = SalesPage;\nvar _c;\n$RefreshReg$(_c, \"SalesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/sales/index.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js ***!
  \*****************************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction MagnifyingGlassIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n    }));\n}\n_c = MagnifyingGlassIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(MagnifyingGlassIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"MagnifyingGlassIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\n"));

/***/ })

});