import { useState } from 'react';
import { XMarkIcon, UserPlusIcon, PhoneIcon } from '@heroicons/react/24/outline';
import axios from 'axios';
import toast from 'react-hot-toast';

export default function QuickCustomerModal({ isOpen, onClose, onCustomerCreated, initialPhone = '' }) {
  const [formData, setFormData] = useState({
    name: '',
    nameAr: '',
    phone: initialPhone,
    email: '',
    address: '',
    addressAr: '',
    type: 'CUSTOMER',
    creditLimit: 0
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const generateCustomerCode = () => {
    const timestamp = Date.now().toString().slice(-6);
    return `CUST${timestamp}`;
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.phone.trim()) {
      newErrors.phone = 'رقم الهاتف مطلوب';
    } else if (!/^[0-9+\-\s()]+$/.test(formData.phone)) {
      newErrors.phone = 'رقم هاتف غير صحيح';
    }

    if (!formData.name.trim() && !formData.nameAr.trim()) {
      newErrors.name = 'يجب إدخال اسم العميل بالعربية أو الإنجليزية';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    
    try {
      // Generate customer code
      const customerCode = generateCustomerCode();
      
      // Prepare customer data
      const customerData = {
        ...formData,
        code: customerCode,
        name: formData.name || formData.nameAr,
        nameAr: formData.nameAr || formData.name,
        balance: 0,
        isActive: true
      };

      const response = await axios.post('/api/customers', customerData);
      
      toast.success('تم إضافة العميل بنجاح');
      
      // Call the callback with the new customer
      if (onCustomerCreated) {
        onCustomerCreated(response.data);
      }
      
      // Reset form
      setFormData({
        name: '',
        nameAr: '',
        phone: '',
        email: '',
        address: '',
        addressAr: '',
        type: 'CUSTOMER',
        creditLimit: 0
      });
      
      onClose();
      
    } catch (error) {
      console.error('Error creating customer:', error);
      
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error('خطأ في إضافة العميل');
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <UserPlusIcon className="h-6 w-6 ml-2 text-blue-600" />
            إضافة عميل جديد
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Phone Number - Most Important */}
          <div>
            <label className="form-label flex items-center">
              <PhoneIcon className="h-4 w-4 ml-1 text-blue-600" />
              رقم الهاتف *
            </label>
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              className={`form-input ${errors.phone ? 'border-red-500' : ''}`}
              placeholder="01xxxxxxxxx"
              required
              autoFocus
            />
            {errors.phone && (
              <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
            )}
          </div>

          {/* Customer Names */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="form-label">الاسم بالعربية</label>
              <input
                type="text"
                name="nameAr"
                value={formData.nameAr}
                onChange={handleChange}
                className={`form-input ${errors.name ? 'border-red-500' : ''}`}
                placeholder="أحمد محمد"
              />
            </div>
            <div>
              <label className="form-label">الاسم بالإنجليزية</label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className={`form-input ${errors.name ? 'border-red-500' : ''}`}
                placeholder="Ahmed Mohamed"
              />
            </div>
          </div>
          {errors.name && (
            <p className="text-red-500 text-sm">{errors.name}</p>
          )}

          {/* Email */}
          <div>
            <label className="form-label">البريد الإلكتروني</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className="form-input"
              placeholder="<EMAIL>"
            />
          </div>

          {/* Customer Type */}
          <div>
            <label className="form-label">نوع العميل</label>
            <select
              name="type"
              value={formData.type}
              onChange={handleChange}
              className="form-input"
            >
              <option value="CUSTOMER">عميل عادي</option>
              <option value="VIP">عميل مميز</option>
              <option value="WHOLESALE">عميل جملة</option>
              <option value="CORPORATE">شركة</option>
            </select>
          </div>

          {/* Address */}
          <div>
            <label className="form-label">العنوان</label>
            <textarea
              name="addressAr"
              value={formData.addressAr}
              onChange={handleChange}
              className="form-input"
              rows="2"
              placeholder="العنوان بالتفصيل..."
            />
          </div>

          {/* Credit Limit */}
          <div>
            <label className="form-label">حد الائتمان</label>
            <input
              type="number"
              name="creditLimit"
              value={formData.creditLimit}
              onChange={handleChange}
              className="form-input"
              min="0"
              step="0.01"
              placeholder="0.00"
            />
            <p className="text-sm text-gray-600 mt-1">
              الحد الأقصى للمبلغ المسموح به كدين على العميل
            </p>
          </div>

          {/* Quick Tips */}
          <div className="bg-blue-50 p-3 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 mb-2">نصائح سريعة:</h4>
            <ul className="text-xs text-blue-700 space-y-1">
              <li>• رقم الهاتف مطلوب ويجب أن يكون فريد</li>
              <li>• يمكن إدخال الاسم بالعربية أو الإنجليزية أو كليهما</li>
              <li>• حد الائتمان يحدد المبلغ المسموح كدين</li>
              <li>• يمكن تعديل هذه المعلومات لاحقاً</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
              disabled={loading}
            >
              إلغاء
            </button>
            <button
              type="submit"
              className="btn-primary"
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  جاري الحفظ...
                </div>
              ) : (
                'حفظ العميل'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
