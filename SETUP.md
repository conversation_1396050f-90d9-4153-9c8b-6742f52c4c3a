# Business Management System - Setup Guide

This guide will help you set up the Business Management System on your local machine.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **PostgreSQL 12+** - [Download here](https://www.postgresql.org/download/)
- **Git** - [Download here](https://git-scm.com/)

## Quick Setup

### 1. <PERSON><PERSON> and Install

```bash
# Clone the repository (if from git)
git clone <repository-url>
cd business-management-system

# Or if you have the files locally, navigate to the directory
cd business-management-system

# Install all dependencies
npm install
```

### 2. Database Setup

Create a PostgreSQL database:

```sql
-- Connect to PostgreSQL as superuser
CREATE DATABASE business_management_db;
CREATE USER bms_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE business_management_db TO bms_user;
```

### 3. Environment Configuration

```bash
# Copy the environment template
cp .env.example .env

# Edit the .env file with your settings
```

Update the `.env` file with your database credentials:

```env
DATABASE_URL="postgresql://bms_user:your_password@localhost:5432/business_management_db"
JWT_SECRET="your-super-secret-jwt-key-here-make-it-long-and-random"
PORT=3001
NEXT_PUBLIC_API_URL="http://localhost:3001"
NEXT_PUBLIC_SOCKET_URL="http://localhost:3001"
```

### 4. Database Initialization

```bash
# Generate Prisma client
npm run db:generate

# Create database tables
npm run db:push

# Seed with initial data
npm run db:seed
```

### 5. Start the Application

Option A - Start both servers with one command:
```bash
npm run dev:all
```

Option B - Start servers separately:

Terminal 1 (Backend):
```bash
npm run server:dev
```

Terminal 2 (Frontend):
```bash
npm run dev
```

### 6. Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **Database Studio**: `npm run db:studio` (opens at http://localhost:5555)

## Demo Credentials

The system comes with pre-configured demo users:

| Role | Username | Password | Permissions |
|------|----------|----------|-------------|
| Admin | admin | admin123 | Full system access |
| Manager | manager | manager123 | Business operations |
| Sales | sales | sales123 | Sales and customers |

## Troubleshooting

### Database Connection Issues

1. **Check PostgreSQL is running**:
   ```bash
   # On Windows
   net start postgresql-x64-14
   
   # On macOS
   brew services start postgresql
   
   # On Linux
   sudo systemctl start postgresql
   ```

2. **Verify database exists**:
   ```bash
   psql -U postgres -l
   ```

3. **Test connection**:
   ```bash
   psql "postgresql://bms_user:your_password@localhost:5432/business_management_db"
   ```

### Port Conflicts

If ports 3000 or 3001 are in use:

1. **Change frontend port**:
   ```bash
   npm run dev -- -p 3002
   ```

2. **Change backend port**:
   Update `PORT=3002` in `.env` file

### Permission Issues

If you get permission errors:

```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### Database Reset

If you need to reset the database:

```bash
# Reset and reseed
npm run db:reset
npm run db:seed
```

## Development Commands

| Command | Description |
|---------|-------------|
| `npm run dev` | Start frontend development server |
| `npm run server:dev` | Start backend development server |
| `npm run dev:all` | Start both servers concurrently |
| `npm run build` | Build for production |
| `npm run start` | Start production server |
| `npm run db:studio` | Open database management UI |
| `npm run db:generate` | Generate Prisma client |
| `npm run db:push` | Push schema to database |
| `npm run db:migrate` | Create and run migrations |
| `npm run db:seed` | Seed database with demo data |
| `npm run db:reset` | Reset database completely |

## Production Deployment

### Environment Variables

Set these environment variables for production:

```env
NODE_ENV=production
DATABASE_URL=<production-database-url>
JWT_SECRET=<secure-random-string>
NEXT_PUBLIC_API_URL=<production-api-url>
NEXT_PUBLIC_SOCKET_URL=<production-socket-url>
```

### Build and Deploy

```bash
# Build the application
npm run build

# Start production server
npm start
```

## Features Overview

### ✅ Implemented
- User authentication and authorization
- Dashboard with real-time statistics
- Product management with categories
- Customer/supplier management
- Real-time notifications
- Bilingual support (Arabic/English)
- Responsive design
- Database seeding

### 🚧 In Progress
- Sales order management
- Purchase order management
- Inventory tracking
- Maintenance workflow
- Comprehensive reporting

### 📋 Planned
- Advanced reporting
- Email notifications
- WhatsApp integration
- Barcode scanning
- Mobile app

## Support

If you encounter any issues:

1. Check this troubleshooting guide
2. Review the main README.md
3. Check the console for error messages
4. Ensure all prerequisites are installed correctly

## Next Steps

After successful setup:

1. Log in with demo credentials
2. Explore the dashboard
3. Add your own products and customers
4. Configure company settings
5. Set up additional users

The system is now ready for use! 🎉
