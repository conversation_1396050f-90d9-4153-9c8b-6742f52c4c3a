"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "./pages/sales/index.js":
/*!******************************!*\
  !*** ./pages/sales/index.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SalesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _components_sales_QuoteModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/sales/QuoteModal */ \"./components/sales/QuoteModal.js\");\n/* harmony import */ var _components_sales_SalesOrderModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/sales/SalesOrderModal */ \"./components/sales/SalesOrderModal.js\");\n/* harmony import */ var _components_sales_InvoiceModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../components/sales/InvoiceModal */ \"./components/sales/InvoiceModal.js\");\n/* harmony import */ var _components_sales_SalesReturnModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../components/sales/SalesReturnModal */ \"./components/sales/SalesReturnModal.js\");\n/* harmony import */ var _components_sales_ProductCustomizer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../components/sales/ProductCustomizer */ \"./components/sales/ProductCustomizer.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,ReceiptPercentIcon,ShoppingCartIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,ReceiptPercentIcon,ShoppingCartIcon,XCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SalesPage() {\n    _s();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"quotes\");\n    // Modal states\n    const [quoteModal, setQuoteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        quote: null\n    });\n    const [salesOrderModal, setSalesOrderModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        salesOrder: null,\n        fromQuote: null\n    });\n    const [invoiceModal, setInvoiceModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        invoice: null,\n        fromSalesOrder: null\n    });\n    const [returnModal, setReturnModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        salesReturn: null\n    });\n    // Data states\n    const [quotes, setQuotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [salesOrders, setSalesOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [salesReturns, setSalesReturns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // التحقق من تسجيل الدخول\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && !user) {\n            router.push(\"/login\");\n        }\n    }, [\n        user,\n        isLoading,\n        router\n    ]);\n    // عرض شاشة التحميل أثناء التحقق من المصادقة\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 56,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    // إذا لم يكن المستخدم مسجل دخول، لا تعرض شيء (سيتم التوجيه)\n    if (!user) {\n        return null;\n    }\n    // Load data when tab changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            loadData();\n        }\n    }, [\n        activeTab,\n        user\n    ]);\n    const loadData = async ()=>{\n        setLoading(true);\n        try {\n            if (activeTab === \"quotes\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/quotes\"));\n                setQuotes(response.data.quotes || []);\n            } else if (activeTab === \"orders\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/sales-orders\"));\n                setSalesOrders(response.data.salesOrders || []);\n            } else if (activeTab === \"invoices\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/invoices\"));\n                setInvoices(response.data.invoices || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"خطأ في تحميل البيانات\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Modal handlers\n    const handleQuoteCreate = ()=>{\n        setQuoteModal({\n            isOpen: true,\n            quote: null\n        });\n    };\n    const handleQuoteEdit = (quote)=>{\n        setQuoteModal({\n            isOpen: true,\n            quote\n        });\n    };\n    const handleQuoteConvert = (quote)=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder: null,\n            fromQuote: quote\n        });\n    };\n    const handleSalesOrderCreate = ()=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder: null,\n            fromQuote: null\n        });\n    };\n    const handleSalesOrderEdit = (salesOrder)=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder,\n            fromQuote: null\n        });\n    };\n    const handleSalesOrderConvert = (salesOrder)=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice: null,\n            fromSalesOrder: salesOrder\n        });\n    };\n    const handleInvoiceCreate = ()=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice: null,\n            fromSalesOrder: null\n        });\n    };\n    const handleInvoiceEdit = (invoice)=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice,\n            fromSalesOrder: null\n        });\n    };\n    // Save handlers\n    const handleQuoteSave = (quote)=>{\n        loadData();\n    };\n    const handleSalesOrderSave = (salesOrder)=>{\n        loadData();\n    };\n    const handleInvoiceSave = (invoice)=>{\n        loadData();\n    };\n    const tabs = [\n        {\n            id: \"quotes\",\n            name: \"عروض الأسعار\",\n            nameEn: \"Quotes\",\n            icon: _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.DocumentTextIcon,\n            color: \"blue\",\n            description: \"لا تؤثر على المخزون - صالحة لمدة محددة\"\n        },\n        {\n            id: \"orders\",\n            name: \"أوامر البيع\",\n            nameEn: \"Sales Orders\",\n            icon: _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ShoppingCartIcon,\n            color: \"orange\",\n            description: \"تحجز من المخزون - لها مدة صلاحية\"\n        },\n        {\n            id: \"invoices\",\n            name: \"الفواتير\",\n            nameEn: \"Invoices\",\n            icon: _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ReceiptPercentIcon,\n            color: \"green\",\n            description: \"تخصم من المخزون نهائياً - تحسب في المبيعات\"\n        }\n    ];\n    const getStatusColor = (status)=>{\n        const colors = {\n            \"DRAFT\": \"gray\",\n            \"PENDING\": \"yellow\",\n            \"APPROVED\": \"green\",\n            \"REJECTED\": \"red\",\n            \"EXPIRED\": \"red\",\n            \"CONFIRMED\": \"blue\",\n            \"SHIPPED\": \"purple\",\n            \"DELIVERED\": \"green\",\n            \"CANCELLED\": \"red\",\n            \"PAID\": \"green\",\n            \"PARTIALLY_PAID\": \"yellow\",\n            \"OVERDUE\": \"red\"\n        };\n        return colors[status] || \"gray\";\n    };\n    const getStatusText = (status)=>{\n        const statusTexts = {\n            \"DRAFT\": \"مسودة\",\n            \"PENDING\": \"في الانتظار\",\n            \"APPROVED\": \"موافق عليه\",\n            \"REJECTED\": \"مرفوض\",\n            \"EXPIRED\": \"منتهي الصلاحية\",\n            \"CONFIRMED\": \"مؤكد\",\n            \"SHIPPED\": \"تم الشحن\",\n            \"DELIVERED\": \"تم التسليم\",\n            \"CANCELLED\": \"ملغي\",\n            \"PAID\": \"مدفوع\",\n            \"PARTIALLY_PAID\": \"مدفوع جزئياً\",\n            \"OVERDUE\": \"متأخر\"\n        };\n        return statusTexts[status] || status;\n    };\n    const renderQuotes = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"عروض الأسعار\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 209,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleQuoteCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 214,\n                                    columnNumber: 11\n                                }, this),\n                                \"عرض سعر جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 210,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 208,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم العرض\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"صالح حتى\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: quotes.map((quote)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: quote.quoteNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: quote.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(quote.status), \"-100 text-\").concat(getStatusColor(quote.status), \"-800\"),\n                                                    children: getStatusText(quote.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    quote.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: quote.validUntil\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuoteEdit(quote),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    quote.status === \"APPROVED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuoteConvert(quote),\n                                                        className: \"text-green-600 hover:text-green-900 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                                className: \"h-5 w-5 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"تحويل لأمر\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, quote.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 219,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 207,\n            columnNumber: 5\n        }, this);\n    const renderOrders = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"أوامر البيع\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 291,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSalesOrderCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 296,\n                                    columnNumber: 11\n                                }, this),\n                                \"أمر بيع جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 292,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 290,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم الأمر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: salesOrders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: order.orderNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: order.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(order.status), \"-100 text-\").concat(getStatusColor(order.status), \"-800\"),\n                                                    children: getStatusText(order.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    order.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: order.reservedStock ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ClockIcon, {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"محجوز\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                                    children: \"غير محجوز\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSalesOrderEdit(order),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    order.status === \"CONFIRMED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSalesOrderConvert(order),\n                                                        className: \"text-green-600 hover:text-green-900 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                                className: \"h-5 w-5 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"تحويل لفاتورة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, order.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 301,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 289,\n            columnNumber: 5\n        }, this);\n    const renderInvoices = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"الفواتير\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 382,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleInvoiceCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 387,\n                                    columnNumber: 11\n                                }, this),\n                                \"فاتورة جديدة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 383,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 381,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم الفاتورة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ الإجمالي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المدفوع\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 411,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: invoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: invoice.invoiceNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: invoice.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(invoice.status), \"-100 text-\").concat(getStatusColor(invoice.status), \"-800\"),\n                                                    children: getStatusText(invoice.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    invoice.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    invoice.paidAmount.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleInvoiceEdit(invoice),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-green-600 hover:text-green-900\",\n                                                        children: \"طباعة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, invoice.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 416,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 392,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 380,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: \"إدارة المبيعات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"نظام المبيعات بثلاث مراحل: عروض الأسعار → أوامر البيع → الفواتير\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 463,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-8 mb-6\",\n                                children: tabs.map((tab, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center p-4 rounded-lg border-2 \".concat(activeTab === tab.id ? \"border-\".concat(tab.color, \"-500 bg-\").concat(tab.color, \"-50\") : \"border-gray-200 bg-gray-50\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                        className: \"h-8 w-8 mb-2 \".concat(activeTab === tab.id ? \"text-\".concat(tab.color, \"-600\") : \"text-gray-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium \".concat(activeTab === tab.id ? \"text-\".concat(tab.color, \"-900\") : \"text-gray-600\"),\n                                                        children: tab.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 text-center mt-1\",\n                                                        children: tab.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, this),\n                                            index < tabs.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                className: \"h-6 w-6 text-gray-400 mx-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 489,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 470,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 468,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 459,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8 px-6\",\n                                    children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-\".concat(tab.color, \"-500 text-\").concat(tab.color, \"-600\") : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                    className: \"h-5 w-5 inline-block ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this),\n                                                tab.name\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 501,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 499,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 498,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    activeTab === \"quotes\" && renderQuotes(),\n                                    activeTab === \"orders\" && renderOrders(),\n                                    activeTab === \"invoices\" && renderInvoices()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 517,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 497,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 457,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_QuoteModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: quoteModal.isOpen,\n                onClose: ()=>setQuoteModal({\n                        isOpen: false,\n                        quote: null\n                    }),\n                onSave: handleQuoteSave,\n                quote: quoteModal.quote\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 526,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_SalesOrderModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: salesOrderModal.isOpen,\n                onClose: ()=>setSalesOrderModal({\n                        isOpen: false,\n                        salesOrder: null,\n                        fromQuote: null\n                    }),\n                onSave: handleSalesOrderSave,\n                salesOrder: salesOrderModal.salesOrder,\n                fromQuote: salesOrderModal.fromQuote\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 533,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_InvoiceModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: invoiceModal.isOpen,\n                onClose: ()=>setInvoiceModal({\n                        isOpen: false,\n                        invoice: null,\n                        fromSalesOrder: null\n                    }),\n                onSave: handleInvoiceSave,\n                invoice: invoiceModal.invoice,\n                fromSalesOrder: invoiceModal.fromSalesOrder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 541,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n        lineNumber: 456,\n        columnNumber: 5\n    }, this);\n}\n_s(SalesPage, \"RRWhhBSYkrjK1fRa3K47H2b2dW4=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = SalesPage;\nvar _c;\n$RefreshReg$(_c, \"SalesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/sales/index.js\n"));

/***/ })

});