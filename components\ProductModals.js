import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { useMutation, useQueryClient } from 'react-query';
import axios from 'axios';
import toast from 'react-hot-toast';
import {
  XMarkIcon,
  CubeIcon,
  CurrencyDollarIcon,
  TagIcon,
  ArchiveBoxIcon,
} from '@heroicons/react/24/outline';

// View Product Modal
export function ViewProductModal({ product, isOpen, onClose }) {
  const { t } = useTranslation('common');

  if (!isOpen || !product) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                {t('products.viewProduct')}
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <CubeIcon className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{product.name}</p>
                  <p className="text-sm text-gray-500">{product.nameAr}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <TagIcon className="h-5 w-5 text-gray-400" />
                <p className="text-sm text-gray-900">{product.code}</p>
              </div>

              <div className="flex items-center space-x-3">
                <div className="w-5 h-5 flex items-center justify-center">
                  <span className="text-xs font-medium text-gray-500">CAT</span>
                </div>
                <p className="text-sm text-gray-900">{product.category}</p>
              </div>

              <div className="flex items-center space-x-3">
                <CurrencyDollarIcon className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Unit Price: ${(parseFloat(product.unitPrice) || 0).toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-500">
                    Cost Price: ${(parseFloat(product.costPrice) || 0).toFixed(2)}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <ArchiveBoxIcon className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Current Stock: {product.currentStock || 0} {product.unit}
                  </p>
                  <p className="text-sm text-gray-500">
                    Min Stock: {product.minStock || 0} {product.unit}
                  </p>
                </div>
              </div>

              {product.barcode && (
                <div className="flex items-center space-x-3">
                  <div className="w-5 h-5 flex items-center justify-center">
                    <span className="text-xs font-medium text-gray-500">BAR</span>
                  </div>
                  <p className="text-sm text-gray-900">{product.barcode}</p>
                </div>
              )}

              {product.description && (
                <div className="flex items-start space-x-3">
                  <div className="w-5 h-5 flex items-center justify-center mt-0.5">
                    <span className="text-xs font-medium text-gray-500">DESC</span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-900">{product.description}</p>
                    {product.descriptionAr && (
                      <p className="text-sm text-gray-500">{product.descriptionAr}</p>
                    )}
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-3">
                <div className="w-5 h-5 flex items-center justify-center">
                  <span className="text-xs font-medium text-gray-500">STATUS</span>
                </div>
                <span className={`badge ${product.isActive ? 'badge-success' : 'badge-secondary'}`}>
                  {product.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              onClick={onClose}
              className="btn-secondary w-full sm:w-auto sm:ml-3"
            >
              {t('common.close')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Create/Edit Product Modal
export function ProductFormModal({ product, isOpen, onClose, onSuccess }) {
  const { t } = useTranslation('common');
  const queryClient = useQueryClient();
  const isEdit = !!product;

  const [formData, setFormData] = useState({
    code: product?.code || '',
    name: product?.name || '',
    nameAr: product?.nameAr || '',
    category: product?.category || '',
    unitPrice: product?.unitPrice || '',
    costPrice: product?.costPrice || '',
    unit: product?.unit || 'piece',
    barcode: product?.barcode || '',
    description: product?.description || '',
    descriptionAr: product?.descriptionAr || '',
    minStock: product?.minStock || 0,
    isActive: product?.isActive ?? true,
  });

  const [errors, setErrors] = useState({});

  const mutation = useMutation(
    async (data) => {
      if (isEdit) {
        const response = await axios.put(`${process.env.NEXT_PUBLIC_API_URL}/api/products/${product.id}`, data);
        return response.data;
      } else {
        const response = await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/api/products`, data);
        return response.data;
      }
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['products']);
        toast.success(isEdit ? 'Product updated successfully' : 'Product created successfully');
        onSuccess?.();
        onClose();
      },
      onError: (error) => {
        const errorMessage = error.response?.data?.error || 'Operation failed';
        toast.error(errorMessage);
        if (error.response?.data?.errors) {
          setErrors(error.response.data.errors);
        }
      }
    }
  );

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrors({});
    
    // Basic validation
    const newErrors = {};
    if (!formData.code) newErrors.code = 'Code is required';
    if (!formData.name) newErrors.name = 'Name is required';
    if (!formData.nameAr) newErrors.nameAr = 'Arabic name is required';
    if (!formData.category) newErrors.category = 'Category is required';
    if (!formData.unitPrice || parseFloat(formData.unitPrice) <= 0) {
      newErrors.unitPrice = 'Unit price is required and must be greater than 0';
    }
    if (!formData.costPrice || parseFloat(formData.costPrice) <= 0) {
      newErrors.costPrice = 'Cost price is required and must be greater than 0';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    mutation.mutate({
      ...formData,
      unitPrice: parseFloat(formData.unitPrice),
      costPrice: parseFloat(formData.costPrice),
      minStock: parseInt(formData.minStock) || 0,
    });
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {isEdit ? t('products.editProduct') : t('products.addProduct')}
                </h3>
                <button
                  type="button"
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="form-label">
                    {t('products.productCode')} *
                  </label>
                  <input
                    type="text"
                    name="code"
                    value={formData.code}
                    onChange={handleChange}
                    className={`form-input ${errors.code ? 'border-red-500' : ''}`}
                    placeholder="Enter product code"
                  />
                  {errors.code && <p className="form-error">{errors.code}</p>}
                </div>

                <div>
                  <label className="form-label">
                    {t('products.category')} *
                  </label>
                  <input
                    type="text"
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    className={`form-input ${errors.category ? 'border-red-500' : ''}`}
                    placeholder="Enter category"
                  />
                  {errors.category && <p className="form-error">{errors.category}</p>}
                </div>

                <div>
                  <label className="form-label">
                    Name (English) *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className={`form-input ${errors.name ? 'border-red-500' : ''}`}
                    placeholder="Enter name in English"
                  />
                  {errors.name && <p className="form-error">{errors.name}</p>}
                </div>

                <div>
                  <label className="form-label">
                    Name (Arabic) *
                  </label>
                  <input
                    type="text"
                    name="nameAr"
                    value={formData.nameAr}
                    onChange={handleChange}
                    className={`form-input ${errors.nameAr ? 'border-red-500' : ''}`}
                    placeholder="أدخل الاسم بالعربية"
                    dir="rtl"
                  />
                  {errors.nameAr && <p className="form-error">{errors.nameAr}</p>}
                </div>

                <div>
                  <label className="form-label">
                    {t('products.unitPrice')} *
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    name="unitPrice"
                    value={formData.unitPrice}
                    onChange={handleChange}
                    className={`form-input ${errors.unitPrice ? 'border-red-500' : ''}`}
                    placeholder="0.00"
                  />
                  {errors.unitPrice && <p className="form-error">{errors.unitPrice}</p>}
                </div>

                <div>
                  <label className="form-label">
                    {t('products.costPrice')} *
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    name="costPrice"
                    value={formData.costPrice}
                    onChange={handleChange}
                    className={`form-input ${errors.costPrice ? 'border-red-500' : ''}`}
                    placeholder="0.00"
                  />
                  {errors.costPrice && <p className="form-error">{errors.costPrice}</p>}
                </div>

                <div>
                  <label className="form-label">
                    {t('products.unit')}
                  </label>
                  <select
                    name="unit"
                    value={formData.unit}
                    onChange={handleChange}
                    className="form-input"
                  >
                    <option value="piece">Piece</option>
                    <option value="kg">Kilogram</option>
                    <option value="liter">Liter</option>
                    <option value="meter">Meter</option>
                    <option value="box">Box</option>
                  </select>
                </div>

                <div>
                  <label className="form-label">
                    {t('products.minStock')}
                  </label>
                  <input
                    type="number"
                    name="minStock"
                    value={formData.minStock}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="0"
                  />
                </div>

                <div>
                  <label className="form-label">
                    {t('products.barcode')}
                  </label>
                  <input
                    type="text"
                    name="barcode"
                    value={formData.barcode}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="Enter barcode"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="form-label">
                    Description (English)
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    className="form-input"
                    rows="3"
                    placeholder="Enter description"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="form-label">
                    Description (Arabic)
                  </label>
                  <textarea
                    name="descriptionAr"
                    value={formData.descriptionAr}
                    onChange={handleChange}
                    className="form-input"
                    rows="3"
                    placeholder="أدخل الوصف"
                    dir="rtl"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleChange}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      {t('common.active')}
                    </span>
                  </label>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={mutation.isLoading}
                className="btn-primary w-full sm:w-auto sm:ml-3"
              >
                {mutation.isLoading ? 'Saving...' : (isEdit ? t('common.save') : t('common.add'))}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary w-full sm:w-auto mt-3 sm:mt-0"
              >
                {t('common.cancel')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
