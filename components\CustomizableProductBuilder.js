import { useState, useEffect } from 'react';
import { useTranslation } from 'next-i18next';
import { useQuery } from 'react-query';
import axios from 'axios';
import {
  CpuChipIcon,
  CircuitBoardIcon,
  ComputerDesktopIcon,
  DevicePhoneMobileIcon,
  CubeIcon,
  PlusIcon,
  MinusIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

// Customizable Product Builder Component
export default function CustomizableProductBuilder({ 
  isOpen, 
  onClose, 
  onSave, 
  baseProduct = null,
  mode = 'sales' // 'sales' or 'purchase'
}) {
  const { t } = useTranslation('common');
  
  const [selectedComponents, setSelectedComponents] = useState({
    cpu: null,
    ram: null,
    storage: null,
    gpu: null,
    motherboard: null,
    psu: null,
    case: null,
    os: null,
  });
  
  const [quantities, setQuantities] = useState({});
  const [assemblyFee, setAssemblyFee] = useState(50); // Default assembly fee
  const [serviceFee, setServiceFee] = useState(25); // Default service fee
  const [totalPrice, setTotalPrice] = useState(0);

  // Fetch component categories
  const { data: componentsData, isLoading } = useQuery('components', async () => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/products/components`);
    return response.data;
  });

  const components = componentsData?.components || {};

  // Calculate total price whenever components change
  useEffect(() => {
    let total = 0;
    
    Object.values(selectedComponents).forEach(component => {
      if (component) {
        const qty = quantities[component.id] || 1;
        total += parseFloat(component.unitPrice || 0) * qty;
      }
    });
    
    total += assemblyFee + serviceFee;
    setTotalPrice(total);
  }, [selectedComponents, quantities, assemblyFee, serviceFee]);

  const handleComponentSelect = (category, component) => {
    setSelectedComponents(prev => ({
      ...prev,
      [category]: component
    }));
    
    if (component && !quantities[component.id]) {
      setQuantities(prev => ({
        ...prev,
        [component.id]: 1
      }));
    }
  };

  const handleQuantityChange = (componentId, quantity) => {
    setQuantities(prev => ({
      ...prev,
      [componentId]: Math.max(1, quantity)
    }));
  };

  const handleSave = () => {
    const configuration = {
      baseProduct,
      components: selectedComponents,
      quantities,
      assemblyFee,
      serviceFee,
      totalPrice,
      mode
    };
    
    onSave(configuration);
    onClose();
  };

  const getCategoryIcon = (category) => {
    const icons = {
      cpu: CpuChipIcon,
      ram: CircuitBoardIcon,
      storage: CubeIcon,
      gpu: ComputerDesktopIcon,
      motherboard: CircuitBoardIcon,
      psu: CubeIcon,
      case: ComputerDesktopIcon,
      os: DevicePhoneMobileIcon,
    };
    return icons[category] || CubeIcon;
  };

  const getCategoryName = (category) => {
    const names = {
      cpu: 'المعالج (CPU)',
      ram: 'الذاكرة العشوائية (RAM)',
      storage: 'التخزين (Storage)',
      gpu: 'كرت الرسوميات (GPU)',
      motherboard: 'اللوحة الأم (Motherboard)',
      psu: 'مزود الطاقة (PSU)',
      case: 'الصندوق (Case)',
      os: 'نظام التشغيل (OS)',
    };
    return names[category] || category;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900">
                {mode === 'sales' ? 'بناء جهاز مخصص للعميل' : 'تكوين منتج للشراء'}
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {isLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Component Selection */}
                <div className="lg:col-span-2 space-y-6">
                  {Object.entries(components).map(([category, categoryComponents]) => {
                    const IconComponent = getCategoryIcon(category);
                    const selectedComponent = selectedComponents[category];
                    
                    return (
                      <div key={category} className="border rounded-lg p-4">
                        <div className="flex items-center mb-3">
                          <IconComponent className="h-5 w-5 text-gray-400 mr-2" />
                          <h4 className="font-medium text-gray-900">
                            {getCategoryName(category)}
                          </h4>
                          {selectedComponent && (
                            <span className="ml-auto text-sm text-green-600">
                              ✓ محدد
                            </span>
                          )}
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {categoryComponents.map((component) => (
                            <div
                              key={component.id}
                              onClick={() => handleComponentSelect(category, component)}
                              className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                                selectedComponent?.id === component.id
                                  ? 'border-primary-500 bg-primary-50'
                                  : 'border-gray-200 hover:border-gray-300'
                              }`}
                            >
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <h5 className="font-medium text-sm text-gray-900">
                                    {component.name}
                                  </h5>
                                  <p className="text-xs text-gray-500 mt-1">
                                    {component.nameAr}
                                  </p>
                                  <p className="text-sm font-medium text-gray-900 mt-2">
                                    ${parseFloat(component.unitPrice || 0).toFixed(2)}
                                  </p>
                                </div>
                                {selectedComponent?.id === component.id && (
                                  <div className="flex items-center space-x-2 mt-2">
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleQuantityChange(component.id, (quantities[component.id] || 1) - 1);
                                      }}
                                      className="p-1 text-gray-400 hover:text-gray-600"
                                    >
                                      <MinusIcon className="h-4 w-4" />
                                    </button>
                                    <span className="text-sm font-medium">
                                      {quantities[component.id] || 1}
                                    </span>
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleQuantityChange(component.id, (quantities[component.id] || 1) + 1);
                                      }}
                                      className="p-1 text-gray-400 hover:text-gray-600"
                                    >
                                      <PlusIcon className="h-4 w-4" />
                                    </button>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Configuration Summary */}
                <div className="space-y-6">
                  <div className="border rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-4">ملخص التكوين</h4>
                    
                    <div className="space-y-3">
                      {Object.entries(selectedComponents).map(([category, component]) => {
                        if (!component) return null;
                        const qty = quantities[component.id] || 1;
                        const price = parseFloat(component.unitPrice || 0) * qty;
                        
                        return (
                          <div key={category} className="flex justify-between text-sm">
                            <div className="flex-1">
                              <p className="font-medium">{getCategoryName(category)}</p>
                              <p className="text-gray-500">{component.name}</p>
                              {qty > 1 && <p className="text-xs text-gray-400">الكمية: {qty}</p>}
                            </div>
                            <p className="font-medium">${price.toFixed(2)}</p>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Fees */}
                  <div className="border rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-4">الرسوم الإضافية</h4>
                    
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          رسوم التجميع
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          value={assemblyFee}
                          onChange={(e) => setAssemblyFee(parseFloat(e.target.value) || 0)}
                          className="form-input w-full"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          رسوم الخدمة
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          value={serviceFee}
                          onChange={(e) => setServiceFee(parseFloat(e.target.value) || 0)}
                          className="form-input w-full"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Total Price */}
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between items-center">
                      <h4 className="text-lg font-bold text-gray-900">الإجمالي</h4>
                      <p className="text-2xl font-bold text-primary-600">
                        ${totalPrice.toFixed(2)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              onClick={handleSave}
              disabled={Object.values(selectedComponents).every(c => !c)}
              className="btn-primary w-full sm:w-auto sm:ml-3"
            >
              {mode === 'sales' ? 'إضافة للطلب' : 'حفظ التكوين'}
            </button>
            <button
              onClick={onClose}
              className="btn-secondary w-full sm:w-auto mt-3 sm:mt-0"
            >
              إلغاء
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
