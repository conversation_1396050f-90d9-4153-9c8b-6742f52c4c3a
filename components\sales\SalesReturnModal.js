import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { XMarkIcon, PlusIcon, TrashIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import axios from 'axios';
import toast from 'react-hot-toast';

export default function SalesReturnModal({ isOpen, onClose, onSave, salesReturn = null }) {
  const { t } = useTranslation('common');
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [invoices, setInvoices] = useState([]);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [searchInvoice, setSearchInvoice] = useState('');
  
  const [formData, setFormData] = useState({
    customerId: '',
    invoiceId: '',
    invoiceNumber: '',
    customerName: '',
    returnDate: new Date().toISOString().split('T')[0],
    reason: '',
    notes: '',
    items: [],
    refundMethod: 'CASH', // CASH, CREDIT, EXCHANGE
    refundAmount: 0
  });

  useEffect(() => {
    if (isOpen) {
      loadCustomers();
      
      if (salesReturn) {
        setFormData({
          customerId: salesReturn.customerId || '',
          invoiceId: salesReturn.invoiceId || '',
          invoiceNumber: salesReturn.invoiceNumber || '',
          customerName: salesReturn.customerName || '',
          returnDate: salesReturn.returnDate ? salesReturn.returnDate.split('T')[0] : new Date().toISOString().split('T')[0],
          reason: salesReturn.reason || '',
          notes: salesReturn.notes || '',
          items: salesReturn.items || [],
          refundMethod: salesReturn.refundMethod || 'CASH',
          refundAmount: salesReturn.refundAmount || 0
        });
      }
    }
  }, [isOpen, salesReturn]);

  const loadCustomers = async () => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/customers`);
      setCustomers(response.data.customers || []);
    } catch (error) {
      console.error('Error loading customers:', error);
    }
  };

  const searchInvoices = async (query) => {
    if (!query || query.length < 3) {
      setInvoices([]);
      return;
    }

    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/invoices/search?q=${query}`);
      setInvoices(response.data.invoices || []);
    } catch (error) {
      console.error('Error searching invoices:', error);
      toast.error('خطأ في البحث عن الفواتير');
    }
  };

  const handleInvoiceSearch = (value) => {
    setSearchInvoice(value);
    searchInvoices(value);
  };

  const selectInvoice = (invoice) => {
    setSelectedInvoice(invoice);
    setFormData(prev => ({
      ...prev,
      customerId: invoice.customerId,
      invoiceId: invoice.id,
      invoiceNumber: invoice.invoiceNumber,
      customerName: invoice.customerName,
      items: invoice.items.map(item => ({
        ...item,
        returnQuantity: 0,
        returnReason: '',
        canReturn: true,
        maxReturnQuantity: item.quantity
      }))
    }));
    setInvoices([]);
    setSearchInvoice(invoice.invoiceNumber);
  };

  const updateReturnItem = (index, field, value) => {
    setFormData(prev => {
      const newItems = [...prev.items];
      newItems[index] = { ...newItems[index], [field]: value };
      
      // Validate return quantity
      if (field === 'returnQuantity') {
        const maxQty = newItems[index].maxReturnQuantity;
        if (parseFloat(value) > maxQty) {
          newItems[index].returnQuantity = maxQty;
          toast.warning(`الحد الأقصى للإرجاع: ${maxQty}`);
        }
      }
      
      return { ...prev, items: newItems };
    });
  };

  const calculateRefundAmount = () => {
    return formData.items.reduce((total, item) => {
      const returnQty = parseFloat(item.returnQuantity) || 0;
      if (returnQty <= 0) return total;

      const unitPrice = parseFloat(item.unitPrice) || 0;
      const discount = parseFloat(item.discount) || 0;
      const taxRate = parseFloat(item.taxRate) || 0;

      // Calculate based on original item calculation
      const itemSubtotal = returnQty * unitPrice;
      const discountAmount = itemSubtotal * (discount / 100);
      const afterDiscount = itemSubtotal - discountAmount;

      // Add tax if applicable
      const taxAmount = item.hasTax ? (afterDiscount * (taxRate / 100)) : 0;

      return total + afterDiscount + taxAmount;
    }, 0);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.invoiceId) {
      toast.error('يرجى اختيار الفاتورة');
      return;
    }
    
    const returningItems = formData.items.filter(item => parseFloat(item.returnQuantity) > 0);
    if (returningItems.length === 0) {
      toast.error('يرجى تحديد العناصر المراد إرجاعها');
      return;
    }

    setLoading(true);
    
    try {
      const refundAmount = calculateRefundAmount();
      
      const returnData = {
        ...formData,
        items: returningItems,
        refundAmount,
        status: 'PENDING'
      };

      const response = salesReturn 
        ? await axios.put(`${process.env.NEXT_PUBLIC_API_URL}/api/sales-returns/${salesReturn.id}`, returnData)
        : await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/api/sales-returns`, returnData);

      toast.success(response.data.message || (salesReturn ? 'تم تحديث المرتجع' : 'تم إنشاء المرتجع'));
      onSave(response.data.salesReturn);
      onClose();
    } catch (error) {
      toast.error(error.response?.data?.error || 'حدث خطأ');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const refundAmount = calculateRefundAmount();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {salesReturn ? 'تعديل مرتجع المبيعات' : 'مرتجع مبيعات جديد'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Invoice Search */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium text-blue-900 mb-3">البحث عن الفاتورة</h3>
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                value={searchInvoice}
                onChange={(e) => handleInvoiceSearch(e.target.value)}
                className="form-input pl-10"
                placeholder="ابحث برقم الفاتورة أو اسم العميل..."
              />
              
              {invoices.length > 0 && (
                <div className="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                  {invoices.map(invoice => (
                    <div
                      key={invoice.id}
                      onClick={() => selectInvoice(invoice)}
                      className="p-3 hover:bg-gray-50 cursor-pointer border-b"
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">{invoice.invoiceNumber}</p>
                          <p className="text-sm text-gray-600">{invoice.customerName}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">${parseFloat(invoice.finalTotal || 0).toFixed(2)}</p>
                          <p className="text-xs text-gray-500">{new Date(invoice.createdAt).toLocaleDateString('ar-EG')}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Selected Invoice Info */}
          {selectedInvoice && (
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-green-900 mb-3">معلومات الفاتورة المختارة</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p><strong>رقم الفاتورة:</strong> {selectedInvoice.invoiceNumber}</p>
                  <p><strong>العميل:</strong> {selectedInvoice.customerName}</p>
                </div>
                <div>
                  <p><strong>التاريخ:</strong> {new Date(selectedInvoice.createdAt).toLocaleDateString('ar-EG')}</p>
                  <p><strong>الإجمالي:</strong> ${parseFloat(selectedInvoice.finalTotal || 0).toFixed(2)}</p>
                </div>
              </div>
            </div>
          )}

          {/* Return Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="form-label">تاريخ الإرجاع</label>
              <input
                type="date"
                value={formData.returnDate}
                onChange={(e) => setFormData(prev => ({ ...prev, returnDate: e.target.value }))}
                className="form-input"
                required
              />
            </div>
            
            <div>
              <label className="form-label">طريقة الاسترداد</label>
              <select
                value={formData.refundMethod}
                onChange={(e) => setFormData(prev => ({ ...prev, refundMethod: e.target.value }))}
                className="form-input"
              >
                <option value="CASH">نقدي</option>
                <option value="CREDIT">رصيد للعميل</option>
                <option value="EXCHANGE">استبدال</option>
              </select>
            </div>
          </div>

          {/* Return Items */}
          {formData.items.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">عناصر الإرجاع</h3>
              <div className="space-y-4">
                {formData.items.map((item, index) => (
                  <div key={index} className="grid grid-cols-12 gap-4 items-end p-4 border rounded-lg">
                    <div className="col-span-3">
                      <label className="form-label">المنتج</label>
                      <div className="text-sm font-medium text-gray-900 py-2">
                        {item.productName}
                        {item.customizationDetails && (
                          <div className="text-xs text-gray-600 mt-1">
                            {item.customizationDetails.map((detail, idx) => (
                              <div key={idx}>• {detail.optionName}: {detail.selectedName}</div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="col-span-2">
                      <label className="form-label">الكمية الأصلية</label>
                      <div className="text-sm font-medium text-gray-900 py-2">
                        {item.quantity}
                      </div>
                    </div>
                    
                    <div className="col-span-2">
                      <label className="form-label">كمية الإرجاع</label>
                      <input
                        type="number"
                        value={item.returnQuantity}
                        onChange={(e) => updateReturnItem(index, 'returnQuantity', e.target.value)}
                        className="form-input"
                        min="0"
                        max={item.maxReturnQuantity}
                      />
                    </div>
                    
                    <div className="col-span-2">
                      <label className="form-label">السعر</label>
                      <div className="text-sm font-medium text-gray-900 py-2">
                        ${parseFloat(item.unitPrice).toFixed(2)}
                      </div>
                    </div>
                    
                    <div className="col-span-3">
                      <label className="form-label">سبب الإرجاع</label>
                      <select
                        value={item.returnReason}
                        onChange={(e) => updateReturnItem(index, 'returnReason', e.target.value)}
                        className="form-input"
                      >
                        <option value="">اختر السبب</option>
                        <option value="DEFECTIVE">عيب في المنتج</option>
                        <option value="WRONG_ITEM">منتج خاطئ</option>
                        <option value="CUSTOMER_CHANGE">تغيير رأي العميل</option>
                        <option value="DAMAGED">تلف أثناء الشحن</option>
                        <option value="OTHER">أخرى</option>
                      </select>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Refund Summary */}
          {refundAmount > 0 && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-3">ملخص الاسترداد</h3>
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold">إجمالي المبلغ المسترد:</span>
                <span className="text-xl font-bold text-green-600">${refundAmount.toFixed(2)}</span>
              </div>
            </div>
          )}

          {/* Reason and Notes */}
          <div className="space-y-4">
            <div>
              <label className="form-label">سبب الإرجاع العام</label>
              <select
                value={formData.reason}
                onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                className="form-input"
                required
              >
                <option value="">اختر السبب</option>
                <option value="DEFECTIVE">عيب في المنتج</option>
                <option value="WRONG_ITEM">منتج خاطئ</option>
                <option value="CUSTOMER_CHANGE">تغيير رأي العميل</option>
                <option value="DAMAGED">تلف أثناء الشحن</option>
                <option value="WARRANTY">مطالبة ضمان</option>
                <option value="OTHER">أخرى</option>
              </select>
            </div>
            
            <div>
              <label className="form-label">ملاحظات</label>
              <textarea
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                className="form-input"
                rows="3"
                placeholder="ملاحظات إضافية..."
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={loading || !selectedInvoice}
              className="btn-primary"
            >
              {loading ? 'جاري الحفظ...' : (salesReturn ? 'تحديث' : 'إنشاء المرتجع')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
