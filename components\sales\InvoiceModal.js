import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { XMarkIcon, PlusIcon, TrashIcon, CreditCardIcon } from '@heroicons/react/24/outline';
import axios from 'axios';
import toast from 'react-hot-toast';

export default function InvoiceModal({ isOpen, onClose, onSave, invoice = null, fromSalesOrder = null }) {
  const { t } = useTranslation('common');
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  
  const [formData, setFormData] = useState({
    customerId: '',
    dueDate: '',
    notes: '',
    items: [],
    payments: []
  });

  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentData, setPaymentData] = useState({
    method: 'CASH',
    amount: 0,
    reference: '',
    installmentPlan: ''
  });

  useEffect(() => {
    if (isOpen) {
      loadCustomers();
      loadProducts();
      
      if (invoice) {
        setFormData({
          customerId: invoice.customerId || '',
          dueDate: invoice.dueDate ? invoice.dueDate.split('T')[0] : '',
          notes: invoice.notes || '',
          items: invoice.items || [],
          payments: invoice.payments || []
        });
      } else if (fromSalesOrder) {
        // Convert sales order to invoice
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 30); // 30 days payment terms
        
        setFormData({
          customerId: fromSalesOrder.customerId || '',
          dueDate: dueDate.toISOString().split('T')[0],
          notes: `تم التحويل من أمر البيع: ${fromSalesOrder.orderNumber}`,
          items: fromSalesOrder.items || [],
          payments: []
        });
      } else {
        // New invoice
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 30);
        setFormData(prev => ({
          ...prev,
          dueDate: dueDate.toISOString().split('T')[0]
        }));
      }
    }
  }, [isOpen, invoice, fromSalesOrder]);

  const loadCustomers = async () => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/customers`);
      setCustomers(response.data.customers || []);
    } catch (error) {
      console.error('Error loading customers:', error);
    }
  };

  const loadProducts = async () => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/products`);
      setProducts(response.data.products || []);
    } catch (error) {
      console.error('Error loading products:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, {
        productId: '',
        quantity: 1,
        unitPrice: 0,
        discount: 0,
        total: 0
      }]
    }));
  };

  const removeItem = (index) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  const updateItem = (index, field, value) => {
    setFormData(prev => {
      const newItems = [...prev.items];
      newItems[index] = { ...newItems[index], [field]: value };
      
      if (field === 'productId') {
        const product = products.find(p => p.id === value);
        if (product) {
          newItems[index].unitPrice = parseFloat(product.unitPrice);
        }
      }
      
      if (field === 'quantity' || field === 'unitPrice' || field === 'discount') {
        const item = newItems[index];
        const subtotal = (parseFloat(item.quantity) || 0) * (parseFloat(item.unitPrice) || 0);
        const discountAmount = subtotal * ((parseFloat(item.discount) || 0) / 100);
        newItems[index].total = subtotal - discountAmount;
      }
      
      return { ...prev, items: newItems };
    });
  };

  const calculateTotals = () => {
    const subtotal = formData.items.reduce((sum, item) => sum + (parseFloat(item.total) || 0), 0);
    const taxAmount = subtotal * 0.14; // 14% tax
    const total = subtotal + taxAmount;
    const paidAmount = formData.payments.reduce((sum, payment) => sum + (parseFloat(payment.amount) || 0), 0);
    const remainingAmount = total - paidAmount;
    
    return { subtotal, taxAmount, total, paidAmount, remainingAmount };
  };

  const addPayment = () => {
    const { total, paidAmount } = calculateTotals();
    const maxAmount = total - paidAmount;
    
    if (maxAmount <= 0) {
      toast.error('تم دفع المبلغ بالكامل');
      return;
    }
    
    setPaymentData({
      method: 'CASH',
      amount: maxAmount,
      reference: '',
      installmentPlan: ''
    });
    setShowPaymentModal(true);
  };

  const savePayment = () => {
    if (!paymentData.amount || paymentData.amount <= 0) {
      toast.error('يرجى إدخال مبلغ صحيح');
      return;
    }
    
    const { total, paidAmount } = calculateTotals();
    if (paidAmount + parseFloat(paymentData.amount) > total) {
      toast.error('المبلغ المدفوع أكبر من المبلغ المطلوب');
      return;
    }
    
    setFormData(prev => ({
      ...prev,
      payments: [...prev.payments, {
        ...paymentData,
        id: Date.now().toString(),
        paidAt: new Date().toISOString()
      }]
    }));
    
    setShowPaymentModal(false);
    setPaymentData({
      method: 'CASH',
      amount: 0,
      reference: '',
      installmentPlan: ''
    });
  };

  const removePayment = (index) => {
    setFormData(prev => ({
      ...prev,
      payments: prev.payments.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.customerId) {
      toast.error('يرجى اختيار العميل');
      return;
    }
    
    if (formData.items.length === 0) {
      toast.error('يرجى إضافة عنصر واحد على الأقل');
      return;
    }

    setLoading(true);
    
    try {
      const { subtotal, taxAmount, total, paidAmount, remainingAmount } = calculateTotals();
      
      const invoiceData = {
        ...formData,
        subtotal,
        taxAmount,
        total,
        paidAmount,
        remainingAmount,
        status: paidAmount >= total ? 'PAID' : paidAmount > 0 ? 'PARTIALLY_PAID' : 'PENDING',
        salesOrderId: fromSalesOrder?.id || null
      };

      const response = invoice 
        ? await axios.put(`${process.env.NEXT_PUBLIC_API_URL}/api/invoices/${invoice.id}`, invoiceData)
        : await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/api/invoices`, invoiceData);

      toast.success(response.data.message || (invoice ? 'تم تحديث الفاتورة' : 'تم إنشاء الفاتورة'));
      onSave(response.data.invoice);
      onClose();
    } catch (error) {
      toast.error(error.response?.data?.error || 'حدث خطأ');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const { subtotal, taxAmount, total, paidAmount, remainingAmount } = calculateTotals();

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">
              {invoice ? 'تعديل الفاتورة' : 'فاتورة جديدة'}
              {fromSalesOrder && (
                <span className="text-sm text-green-600 block">
                  تحويل من أمر البيع: {fromSalesOrder.orderNumber}
                </span>
              )}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Customer and Date */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="form-label">العميل *</label>
                <select
                  name="customerId"
                  value={formData.customerId}
                  onChange={handleChange}
                  className="form-input"
                  required
                  disabled={fromSalesOrder}
                >
                  <option value="">اختر العميل</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>
                      {customer.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="form-label">تاريخ الاستحقاق</label>
                <input
                  type="date"
                  name="dueDate"
                  value={formData.dueDate}
                  onChange={handleChange}
                  className="form-input"
                />
              </div>
            </div>

            {/* Items */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">العناصر</h3>
                <button
                  type="button"
                  onClick={addItem}
                  className="btn-primary flex items-center"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  إضافة عنصر
                </button>
              </div>

              <div className="space-y-4">
                {formData.items.map((item, index) => (
                  <div key={index} className="grid grid-cols-12 gap-4 items-end p-4 border rounded-lg">
                    <div className="col-span-4">
                      <label className="form-label">المنتج</label>
                      <select
                        value={item.productId}
                        onChange={(e) => updateItem(index, 'productId', e.target.value)}
                        className="form-input"
                      >
                        <option value="">اختر المنتج</option>
                        {products.map(product => (
                          <option key={product.id} value={product.id}>
                            {product.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div className="col-span-2">
                      <label className="form-label">الكمية</label>
                      <input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => updateItem(index, 'quantity', e.target.value)}
                        className="form-input"
                        min="1"
                      />
                    </div>
                    
                    <div className="col-span-2">
                      <label className="form-label">السعر</label>
                      <input
                        type="number"
                        value={item.unitPrice}
                        onChange={(e) => updateItem(index, 'unitPrice', e.target.value)}
                        className="form-input"
                        step="0.01"
                      />
                    </div>
                    
                    <div className="col-span-2">
                      <label className="form-label">خصم %</label>
                      <input
                        type="number"
                        value={item.discount}
                        onChange={(e) => updateItem(index, 'discount', e.target.value)}
                        className="form-input"
                        min="0"
                        max="100"
                      />
                    </div>
                    
                    <div className="col-span-1">
                      <label className="form-label">الإجمالي</label>
                      <div className="text-sm font-medium text-gray-900 py-2">
                        ${(item.total || 0).toFixed(2)}
                      </div>
                    </div>
                    
                    <div className="col-span-1">
                      <button
                        type="button"
                        onClick={() => removeItem(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Payments */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">المدفوعات</h3>
                <button
                  type="button"
                  onClick={addPayment}
                  className="btn-secondary flex items-center"
                  disabled={remainingAmount <= 0}
                >
                  <CreditCardIcon className="h-5 w-5 mr-2" />
                  إضافة دفعة
                </button>
              </div>

              {formData.payments.length > 0 && (
                <div className="space-y-2">
                  {formData.payments.map((payment, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <span className="font-medium">{payment.method}</span>
                        <span>${parseFloat(payment.amount).toFixed(2)}</span>
                        {payment.reference && (
                          <span className="text-sm text-gray-500">المرجع: {payment.reference}</span>
                        )}
                      </div>
                      <button
                        type="button"
                        onClick={() => removePayment(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Inventory Deduction Notice */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    تنبيه خصم المخزون
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>
                      عند حفظ الفاتورة، سيتم خصم الكميات من المخزون نهائياً وتسجيل المبيعات.
                      هذا الإجراء لا يمكن التراجع عنه.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Totals */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>المجموع الفرعي:</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>الضريبة (14%):</span>
                  <span>${taxAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-bold text-lg border-t pt-2">
                  <span>الإجمالي:</span>
                  <span>${total.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-green-600">
                  <span>المدفوع:</span>
                  <span>${paidAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-red-600 font-bold">
                  <span>المتبقي:</span>
                  <span>${remainingAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {/* Notes */}
            <div>
              <label className="form-label">ملاحظات</label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                className="form-input"
                rows="3"
                placeholder="ملاحظات إضافية..."
              />
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary"
              >
                إلغاء
              </button>
              <button
                type="submit"
                disabled={loading}
                className="btn-primary"
              >
                {loading ? 'جاري الحفظ...' : (invoice ? 'تحديث' : 'إنشاء')}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div className="flex items-center justify-between p-6 border-b">
              <h3 className="text-lg font-semibold text-gray-900">إضافة دفعة</h3>
              <button
                onClick={() => setShowPaymentModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            
            <div className="p-6 space-y-4">
              <div>
                <label className="form-label">طريقة الدفع</label>
                <select
                  value={paymentData.method}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, method: e.target.value }))}
                  className="form-input"
                >
                  <option value="CASH">نقدي</option>
                  <option value="INSTAPAY">إنستاباي</option>
                  <option value="VODAFONE_CASH">فودافون كاش</option>
                  <option value="VISA">فيزا</option>
                  <option value="BANK_TRANSFER">تحويل بنكي</option>
                  <option value="INSTALLMENT">أقساط</option>
                </select>
              </div>
              
              <div>
                <label className="form-label">المبلغ</label>
                <input
                  type="number"
                  value={paymentData.amount}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, amount: e.target.value }))}
                  className="form-input"
                  step="0.01"
                  max={remainingAmount}
                />
                <p className="text-xs text-gray-500 mt-1">
                  الحد الأقصى: ${remainingAmount.toFixed(2)}
                </p>
              </div>
              
              <div>
                <label className="form-label">المرجع (اختياري)</label>
                <input
                  type="text"
                  value={paymentData.reference}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, reference: e.target.value }))}
                  className="form-input"
                  placeholder="رقم المرجع أو الإيصال"
                />
              </div>
              
              {paymentData.method === 'INSTALLMENT' && (
                <div>
                  <label className="form-label">خطة الأقساط</label>
                  <input
                    type="text"
                    value={paymentData.installmentPlan}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, installmentPlan: e.target.value }))}
                    className="form-input"
                    placeholder="مثال: 3 أقساط شهرية"
                  />
                </div>
              )}
            </div>
            
            <div className="flex justify-end space-x-4 p-6 border-t">
              <button
                type="button"
                onClick={() => setShowPaymentModal(false)}
                className="btn-secondary"
              >
                إلغاء
              </button>
              <button
                type="button"
                onClick={savePayment}
                className="btn-primary"
              >
                إضافة
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
