import { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { XMarkIcon, PlusIcon, TrashIcon, CreditCardIcon, PrinterIcon, CogIcon } from '@heroicons/react/24/outline';
import axios from 'axios';
import toast from 'react-hot-toast';
import { useReactToPrint } from 'react-to-print';
import ProductCustomizer from './ProductCustomizer';
import PaymentManager from './PaymentManager';
import InvoicePrintView from './InvoicePrintView';

export default function InvoiceModal({ isOpen, onClose, onSave, invoice = null, fromSalesOrder = null }) {
  const { t } = useTranslation('common');
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  const printRef = useRef();

  // Modal states
  const [showCustomizer, setShowCustomizer] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [showPaymentManager, setShowPaymentManager] = useState(false);
  const [currentItemIndex, setCurrentItemIndex] = useState(null);
  
  const [formData, setFormData] = useState({
    customerId: '',
    dueDate: '',
    notes: '',
    items: [],
    payments: [],
    // إضافة خصم عام
    generalDiscount: 0,
    generalDiscountType: 'PERCENTAGE' // PERCENTAGE or AMOUNT
  });

  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentData, setPaymentData] = useState({
    method: 'CASH',
    amount: 0,
    reference: '',
    installmentPlan: ''
  });

  useEffect(() => {
    if (isOpen) {
      loadCustomers();
      loadProducts();
      
      if (invoice) {
        setFormData({
          customerId: invoice.customerId || '',
          dueDate: invoice.dueDate ? invoice.dueDate.split('T')[0] : '',
          notes: invoice.notes || '',
          items: invoice.items || [],
          payments: invoice.payments || []
        });
      } else if (fromSalesOrder) {
        // Convert sales order to invoice
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 30); // 30 days payment terms
        
        setFormData({
          customerId: fromSalesOrder.customerId || '',
          dueDate: dueDate.toISOString().split('T')[0],
          notes: `تم التحويل من أمر البيع: ${fromSalesOrder.orderNumber}`,
          items: fromSalesOrder.items || [],
          payments: []
        });
      } else {
        // New invoice
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 30);
        setFormData(prev => ({
          ...prev,
          dueDate: dueDate.toISOString().split('T')[0]
        }));
      }
    }
  }, [isOpen, invoice, fromSalesOrder]);

  const loadCustomers = async () => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/customers`);
      setCustomers(response.data.customers || []);
    } catch (error) {
      console.error('Error loading customers:', error);
    }
  };

  const loadProducts = async () => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/products`);
      setProducts(response.data.products || []);
    } catch (error) {
      console.error('Error loading products:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, {
        productId: '',
        productName: '',
        quantity: 1,
        unitPrice: 0,
        discount: 0,
        taxRate: 14, // Default tax rate
        hasTax: true,
        total: 0,
        isCustomized: false,
        customizations: null,
        customizationDetails: []
      }]
    }));
  };

  const removeItem = (index) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  const updateItem = (index, field, value) => {
    setFormData(prev => {
      const newItems = [...prev.items];
      newItems[index] = { ...newItems[index], [field]: value };

      if (field === 'productId') {
        const product = products.find(p => p.id === value);
        if (product) {
          newItems[index].unitPrice = parseFloat(product.unitPrice);
          newItems[index].productName = product.nameAr || product.name;

          // Check if product is customizable
          if (product.isCustomizable) {
            setSelectedProduct(product);
            setCurrentItemIndex(index);
            setShowCustomizer(true);
            return prev; // Don't update yet, wait for customization
          }
        }
      }

      // Recalculate totals for any change
      if (['quantity', 'unitPrice', 'discount', 'taxRate', 'hasTax'].includes(field)) {
        const item = newItems[index];
        const quantity = parseFloat(item.quantity) || 0;
        const unitPrice = parseFloat(item.unitPrice) || 0;
        const discountPercent = parseFloat(item.discount) || 0;
        const taxRate = parseFloat(item.taxRate) || 0;

        // Calculate subtotal
        const subtotal = quantity * unitPrice;

        // Apply discount
        const discountAmount = subtotal * (discountPercent / 100);
        const afterDiscount = subtotal - discountAmount;

        // Apply tax if enabled
        const taxAmount = item.hasTax ? (afterDiscount * (taxRate / 100)) : 0;
        const total = afterDiscount + taxAmount;

        newItems[index].total = total;
        newItems[index].subtotal = subtotal;
        newItems[index].discountAmount = discountAmount;
        newItems[index].taxAmount = taxAmount;
      }

      return { ...prev, items: newItems };
    });
  };

  // Handle customized product
  const handleCustomizedProduct = (customizedProduct) => {
    setFormData(prev => {
      const newItems = [...prev.items];
      newItems[currentItemIndex] = {
        ...newItems[currentItemIndex],
        productId: customizedProduct.id,
        productName: customizedProduct.nameAr || customizedProduct.name,
        unitPrice: customizedProduct.finalPrice,
        customizations: customizedProduct.customizations,
        customizationDetails: customizedProduct.customizationDetails,
        isCustomized: true
      };

      // Recalculate total
      const item = newItems[currentItemIndex];
      const subtotal = (parseFloat(item.quantity) || 0) * (parseFloat(item.unitPrice) || 0);
      const discountAmount = subtotal * ((parseFloat(item.discount) || 0) / 100);
      newItems[currentItemIndex].total = subtotal - discountAmount;

      return { ...prev, items: newItems };
    });
  };

  // Print functionality
  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: `فاتورة-${formData.invoiceNumber || 'جديدة'}`,
  });

  const calculateTotals = () => {
    // حساب المجاميع من العناصر
    const itemsSubtotal = formData.items.reduce((sum, item) => sum + (parseFloat(item.subtotal) || 0), 0);
    const itemsDiscountAmount = formData.items.reduce((sum, item) => sum + (parseFloat(item.discountAmount) || 0), 0);
    const itemsTaxAmount = formData.items.reduce((sum, item) => sum + (parseFloat(item.taxAmount) || 0), 0);
    const itemsTotal = formData.items.reduce((sum, item) => sum + (parseFloat(item.total) || 0), 0);

    // حساب الخصم العام
    let generalDiscountAmount = 0;
    if (formData.generalDiscount > 0) {
      if (formData.generalDiscountType === 'PERCENTAGE') {
        generalDiscountAmount = itemsTotal * (parseFloat(formData.generalDiscount) / 100);
      } else {
        generalDiscountAmount = parseFloat(formData.generalDiscount);
      }
    }

    // الإجمالي النهائي بعد الخصم العام
    const finalTotal = itemsTotal - generalDiscountAmount;

    // المدفوعات
    const paidAmount = formData.payments.reduce((sum, payment) => sum + (parseFloat(payment.amount) || 0), 0);
    const remainingAmount = finalTotal - paidAmount;

    return {
      itemsSubtotal,
      itemsDiscountAmount,
      itemsTaxAmount,
      itemsTotal,
      generalDiscountAmount,
      finalTotal,
      paidAmount,
      remainingAmount
    };
  };

  const addPayment = () => {
    const { finalTotal, paidAmount } = calculateTotals();
    const maxAmount = finalTotal - paidAmount;

    if (maxAmount <= 0) {
      toast.error('تم دفع المبلغ بالكامل');
      return;
    }

    setShowPaymentManager(true);
  };

  const handlePaymentAdd = (payment) => {
    setFormData(prev => ({
      ...prev,
      payments: [...prev.payments, {
        ...payment,
        id: Date.now().toString()
      }]
    }));
    toast.success('تم إضافة الدفعة بنجاح');
  };

  const savePayment = () => {
    if (!paymentData.amount || paymentData.amount <= 0) {
      toast.error('يرجى إدخال مبلغ صحيح');
      return;
    }
    
    const { finalTotal, paidAmount } = calculateTotals();
    if (paidAmount + parseFloat(paymentData.amount) > finalTotal) {
      toast.error('المبلغ المدفوع أكبر من المبلغ المطلوب');
      return;
    }
    
    setFormData(prev => ({
      ...prev,
      payments: [...prev.payments, {
        ...paymentData,
        id: Date.now().toString(),
        paidAt: new Date().toISOString()
      }]
    }));
    
    setShowPaymentModal(false);
    setPaymentData({
      method: 'CASH',
      amount: 0,
      reference: '',
      installmentPlan: ''
    });
  };

  const removePayment = (index) => {
    setFormData(prev => ({
      ...prev,
      payments: prev.payments.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.customerId) {
      toast.error('يرجى اختيار العميل');
      return;
    }
    
    if (formData.items.length === 0) {
      toast.error('يرجى إضافة عنصر واحد على الأقل');
      return;
    }

    setLoading(true);
    
    try {
      const {
        itemsSubtotal,
        itemsDiscountAmount,
        itemsTaxAmount,
        itemsTotal,
        generalDiscountAmount,
        finalTotal,
        paidAmount,
        remainingAmount
      } = calculateTotals();

      const invoiceData = {
        ...formData,
        itemsSubtotal,
        itemsDiscountAmount,
        itemsTaxAmount,
        itemsTotal,
        generalDiscountAmount,
        finalTotal,
        paidAmount,
        remainingAmount,
        status: paidAmount >= finalTotal ? 'PAID' : paidAmount > 0 ? 'PARTIALLY_PAID' : 'PENDING',
        salesOrderId: fromSalesOrder?.id || null
      };

      const response = invoice 
        ? await axios.put(`${process.env.NEXT_PUBLIC_API_URL}/api/invoices/${invoice.id}`, invoiceData)
        : await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/api/invoices`, invoiceData);

      toast.success(response.data.message || (invoice ? 'تم تحديث الفاتورة' : 'تم إنشاء الفاتورة'));
      onSave(response.data.invoice);
      onClose();
    } catch (error) {
      toast.error(error.response?.data?.error || 'حدث خطأ');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">
              {invoice ? 'تعديل الفاتورة' : 'فاتورة جديدة'}
              {fromSalesOrder && (
                <span className="text-sm text-green-600 block">
                  تحويل من أمر البيع: {fromSalesOrder.orderNumber}
                </span>
              )}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Customer and Date */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="form-label">العميل *</label>
                <select
                  name="customerId"
                  value={formData.customerId}
                  onChange={handleChange}
                  className="form-input"
                  required
                  disabled={fromSalesOrder}
                >
                  <option value="">اختر العميل</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>
                      {customer.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="form-label">تاريخ الاستحقاق</label>
                <input
                  type="date"
                  name="dueDate"
                  value={formData.dueDate}
                  onChange={handleChange}
                  className="form-input"
                />
              </div>
            </div>

            {/* Items */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">العناصر</h3>
                <button
                  type="button"
                  onClick={addItem}
                  className="btn-primary flex items-center"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  إضافة عنصر
                </button>
              </div>

              <div className="space-y-4">
                {formData.items.map((item, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 items-end p-4 border rounded-lg">
                    <div className="col-span-4">
                      <label className="form-label">المنتج</label>
                      <div className="flex">
                        <select
                          value={item.productId}
                          onChange={(e) => updateItem(index, 'productId', e.target.value)}
                          className="form-input flex-1"
                        >
                          <option value="">اختر المنتج</option>
                          {products.map(product => (
                            <option key={product.id} value={product.id}>
                              {product.nameAr || product.name}
                              {product.isCustomizable && ' (قابل للتخصيص)'}
                            </option>
                          ))}
                        </select>
                        {item.isCustomized && (
                          <button
                            type="button"
                            onClick={() => {
                              const product = products.find(p => p.id === item.productId);
                              if (product) {
                                setSelectedProduct(product);
                                setCurrentItemIndex(index);
                                setShowCustomizer(true);
                              }
                            }}
                            className="mr-2 px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200"
                            title="تعديل التخصيص"
                          >
                            <CogIcon className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                      {item.customizationDetails && (
                        <div className="mt-2 text-xs text-gray-600 bg-blue-50 p-2 rounded">
                          <strong>التخصيصات:</strong>
                          {item.customizationDetails.map((detail, idx) => (
                            <div key={idx}>• {detail.optionName}: {detail.selectedName}</div>
                          ))}
                        </div>
                      )}
                    </div>
                    
                    <div className="col-span-2">
                      <label className="form-label">الكمية</label>
                      <input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => updateItem(index, 'quantity', e.target.value)}
                        className="form-input"
                        min="1"
                      />
                    </div>
                    
                    <div className="col-span-2">
                      <label className="form-label">السعر</label>
                      <input
                        type="number"
                        value={item.unitPrice}
                        onChange={(e) => updateItem(index, 'unitPrice', e.target.value)}
                        className="form-input"
                        step="0.01"
                      />
                    </div>
                    
                    <div className="col-span-1">
                      <label className="form-label">خصم %</label>
                      <input
                        type="number"
                        value={item.discount}
                        onChange={(e) => updateItem(index, 'discount', e.target.value)}
                        className="form-input"
                        min="0"
                        max="100"
                      />
                    </div>

                    <div className="col-span-1">
                      <label className="form-label">ضريبة</label>
                      <div className="flex items-center space-x-1">
                        <input
                          type="checkbox"
                          checked={item.hasTax}
                          onChange={(e) => updateItem(index, 'hasTax', e.target.checked)}
                          className="rounded"
                        />
                        <input
                          type="number"
                          value={item.taxRate}
                          onChange={(e) => updateItem(index, 'taxRate', e.target.value)}
                          className="form-input w-16"
                          min="0"
                          max="100"
                          disabled={!item.hasTax}
                        />
                        <span className="text-xs">%</span>
                      </div>
                    </div>

                    <div className="col-span-1">
                      <label className="form-label">الإجمالي</label>
                      <div className="text-sm font-medium text-gray-900 py-2">
                        ${(item.total || 0).toFixed(2)}
                      </div>
                    </div>

                    <div className="col-span-1">
                      <button
                        type="button"
                        onClick={() => removeItem(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Payments */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">المدفوعات</h3>
                <button
                  type="button"
                  onClick={addPayment}
                  className="btn-secondary flex items-center"
                  disabled={calculateTotals().remainingAmount <= 0}
                >
                  <CreditCardIcon className="h-5 w-5 mr-2" />
                  إضافة دفعة
                </button>
              </div>

              {formData.payments.length > 0 && (
                <div className="space-y-2">
                  {formData.payments.map((payment, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <span className="font-medium">{payment.method}</span>
                        <span>${parseFloat(payment.amount).toFixed(2)}</span>
                        {payment.reference && (
                          <span className="text-sm text-gray-500">المرجع: {payment.reference}</span>
                        )}
                      </div>
                      <button
                        type="button"
                        onClick={() => removePayment(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Inventory Deduction Notice */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    تنبيه خصم المخزون
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>
                      عند حفظ الفاتورة، سيتم خصم الكميات من المخزون نهائياً وتسجيل المبيعات.
                      هذا الإجراء لا يمكن التراجع عنه.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* General Discount */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-3">خصم عام</h3>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="form-label">نوع الخصم</label>
                  <select
                    value={formData.generalDiscountType}
                    onChange={(e) => setFormData(prev => ({ ...prev, generalDiscountType: e.target.value }))}
                    className="form-input"
                  >
                    <option value="PERCENTAGE">نسبة مئوية</option>
                    <option value="AMOUNT">مبلغ ثابت</option>
                  </select>
                </div>
                <div>
                  <label className="form-label">
                    قيمة الخصم {formData.generalDiscountType === 'PERCENTAGE' ? '(%)' : '($)'}
                  </label>
                  <input
                    type="number"
                    value={formData.generalDiscount}
                    onChange={(e) => setFormData(prev => ({ ...prev, generalDiscount: e.target.value }))}
                    className="form-input"
                    min="0"
                    step="0.01"
                  />
                </div>
                <div>
                  <label className="form-label">مبلغ الخصم</label>
                  <div className="text-lg font-semibold text-red-600 py-2">
                    ${calculateTotals().generalDiscountAmount.toFixed(2)}
                  </div>
                </div>
              </div>
            </div>

            {/* Totals */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>المجموع الفرعي:</span>
                  <span>${calculateTotals().itemsSubtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-red-600">
                  <span>خصم العناصر:</span>
                  <span>-${calculateTotals().itemsDiscountAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-blue-600">
                  <span>الضرائب:</span>
                  <span>${calculateTotals().itemsTaxAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-medium border-t pt-2">
                  <span>إجمالي العناصر:</span>
                  <span>${calculateTotals().itemsTotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-red-600">
                  <span>الخصم العام:</span>
                  <span>-${calculateTotals().generalDiscountAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-bold text-lg border-t pt-2">
                  <span>الإجمالي النهائي:</span>
                  <span>${calculateTotals().finalTotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-green-600">
                  <span>المدفوع:</span>
                  <span>${calculateTotals().paidAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-red-600 font-bold">
                  <span>المتبقي:</span>
                  <span>${calculateTotals().remainingAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {/* Notes */}
            <div>
              <label className="form-label">ملاحظات</label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                className="form-input"
                rows="3"
                placeholder="ملاحظات إضافية..."
              />
            </div>

            {/* Actions */}
            <div className="flex justify-between">
              <div className="flex space-x-4">
                {invoice && (
                  <button
                    type="button"
                    onClick={handlePrint}
                    className="btn-secondary flex items-center"
                  >
                    <PrinterIcon className="h-5 w-5 mr-2" />
                    طباعة
                  </button>
                )}
              </div>

              <div className="flex space-x-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="btn-secondary"
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="btn-primary"
                >
                  {loading ? 'جاري الحفظ...' : (invoice ? 'تحديث' : 'إنشاء')}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Hidden Print View */}
      <div style={{ display: 'none' }}>
        <InvoicePrintView
          ref={printRef}
          invoice={{
            ...formData,
            invoiceNumber: invoice?.invoiceNumber || 'جديدة',
            ...calculateTotals()
          }}
          customer={customers.find(c => c.id === formData.customerId)}
        />
      </div>

      {/* Product Customizer */}
      <ProductCustomizer
        isOpen={showCustomizer}
        onClose={() => setShowCustomizer(false)}
        product={selectedProduct}
        onSave={handleCustomizedProduct}
      />

      {/* Payment Manager */}
      <PaymentManager
        isOpen={showPaymentManager}
        onClose={() => setShowPaymentManager(false)}
        totalAmount={calculateTotals().finalTotal}
        paidAmount={calculateTotals().paidAmount}
        onPaymentAdd={handlePaymentAdd}
        existingPayments={formData.payments}
      />

      {/* Old Payment Modal - Remove this */}
      {false && showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div className="flex items-center justify-between p-6 border-b">
              <h3 className="text-lg font-semibold text-gray-900">إضافة دفعة</h3>
              <button
                onClick={() => setShowPaymentModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            
            <div className="p-6 space-y-4">
              <div>
                <label className="form-label">طريقة الدفع</label>
                <select
                  value={paymentData.method}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, method: e.target.value }))}
                  className="form-input"
                >
                  <option value="CASH">نقدي</option>
                  <option value="INSTAPAY">إنستاباي</option>
                  <option value="VODAFONE_CASH">فودافون كاش</option>
                  <option value="VISA">فيزا</option>
                  <option value="BANK_TRANSFER">تحويل بنكي</option>
                  <option value="INSTALLMENT">أقساط</option>
                </select>
              </div>
              
              <div>
                <label className="form-label">المبلغ</label>
                <input
                  type="number"
                  value={paymentData.amount}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, amount: e.target.value }))}
                  className="form-input"
                  step="0.01"
                  max={remainingAmount}
                />
                <p className="text-xs text-gray-500 mt-1">
                  الحد الأقصى: ${remainingAmount.toFixed(2)}
                </p>
              </div>
              
              <div>
                <label className="form-label">المرجع (اختياري)</label>
                <input
                  type="text"
                  value={paymentData.reference}
                  onChange={(e) => setPaymentData(prev => ({ ...prev, reference: e.target.value }))}
                  className="form-input"
                  placeholder="رقم المرجع أو الإيصال"
                />
              </div>
              
              {paymentData.method === 'INSTALLMENT' && (
                <div>
                  <label className="form-label">خطة الأقساط</label>
                  <input
                    type="text"
                    value={paymentData.installmentPlan}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, installmentPlan: e.target.value }))}
                    className="form-input"
                    placeholder="مثال: 3 أقساط شهرية"
                  />
                </div>
              )}
            </div>
            
            <div className="flex justify-end space-x-4 p-6 border-t">
              <button
                type="button"
                onClick={() => setShowPaymentModal(false)}
                className="btn-secondary"
              >
                إلغاء
              </button>
              <button
                type="button"
                onClick={savePayment}
                className="btn-primary"
              >
                إضافة
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
