{"navigation": {"dashboard": "Dashboard", "products": "Product Management", "customers": "Customer Management", "sales": "Sales Management", "purchases": "Purchase Management", "inventory": "Inventory Management", "accounting": "Accounting", "maintenance": "Technical Maintenance", "reports": "Reports", "settings": "Settings", "logout": "Logout"}, "auth": {"login": "<PERSON><PERSON>", "username": "Username", "password": "Password", "loginButton": "Sign In", "forgotPassword": "Forgot Password?", "invalidCredentials": "Invalid credentials", "loginSuccess": "Login successful", "logout": "Logout", "profile": "Profile"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome", "totalProducts": "Total Products", "totalCustomers": "Total Customers", "totalSuppliers": "Total Suppliers", "lowStock": "Low Stock", "pendingSales": "Pending Sales", "pendingPurchases": "Pending Purchases", "pendingMaintenance": "Pending Maintenance", "monthlySales": "Monthly Sales", "monthlyPurchases": "Monthly Purchases", "yearlySales": "Yearly Sales", "recentOrders": "Recent Orders", "salesChart": "Sales Chart", "topProducts": "Top Selling Products", "notifications": "Notifications"}, "products": {"title": "Product Management", "addProduct": "Add Product", "editProduct": "Edit Product", "productCode": "Product Code", "productName": "Product Name", "productNameAr": "Product Name (Arabic)", "category": "Category", "unitPrice": "Unit Price", "costPrice": "Cost Price", "currentStock": "Current Stock", "minStock": "Minimum Stock", "unit": "Unit", "unitAr": "Unit (Arabic)", "barcode": "Barcode", "description": "Description", "descriptionAr": "Description (Arabic)", "image": "Image", "status": "Status", "active": "Active", "inactive": "Inactive", "save": "Save", "cancel": "Cancel", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this product?"}, "customers": {"title": "Customer Management", "addCustomer": "Add Customer", "editCustomer": "Edit Customer", "customerCode": "Customer Code", "customerName": "Customer Name", "customerNameAr": "Customer Name (Arabic)", "customerType": "Customer Type", "customer": "Customer", "supplier": "Supplier", "both": "Customer & Supplier", "email": "Email", "phone": "Phone", "address": "Address", "addressAr": "Address (Arabic)", "balance": "Balance", "creditLimit": "Credit Limit"}, "sales": {"title": "Sales Management", "newOrder": "New Order", "orderNumber": "Order Number", "customer": "Customer", "orderDate": "Order Date", "dueDate": "Due Date", "status": "Status", "draft": "Draft", "pending": "Pending", "confirmed": "Confirmed", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled", "returned": "Returned", "subtotal": "Subtotal", "tax": "Tax", "discount": "Discount", "total": "Total", "addItem": "Add Item", "product": "Product", "quantity": "Quantity", "unitPrice": "Unit Price", "itemTotal": "Item Total"}, "common": {"search": "Search", "filter": "Filter", "export": "Export", "print": "Print", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "actions": "Actions", "loading": "Loading...", "noData": "No data available", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "confirm": "Confirm", "yes": "Yes", "no": "No", "required": "Required", "optional": "Optional", "selectOption": "Select an option", "date": "Date", "time": "Time", "name": "Name", "code": "Code", "description": "Description", "notes": "Notes", "createdAt": "Created At", "updatedAt": "Updated At", "language": "Language", "arabic": "العربية", "english": "English"}, "validation": {"required": "This field is required", "email": "Please enter a valid email", "minLength": "Must be at least {{min}} characters", "maxLength": "Must be less than {{max}} characters", "number": "Must be a number", "positive": "Must be a positive number", "phone": "Please enter a valid phone number"}}