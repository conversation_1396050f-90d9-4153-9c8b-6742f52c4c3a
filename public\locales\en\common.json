{"navigation": {"dashboard": "Dashboard", "products": "Product Management", "customers": "Customer Management", "sales": "Sales Management", "purchases": "Purchase Management", "inventory": "Inventory Management", "accounting": "Accounting", "maintenance": "Technical Maintenance", "reports": "Reports", "settings": "Settings", "logout": "Logout"}, "auth": {"login": "<PERSON><PERSON>", "username": "Username", "password": "Password", "loginButton": "Sign In", "forgotPassword": "Forgot Password?", "invalidCredentials": "Invalid credentials", "loginSuccess": "Login successful", "logout": "Logout", "profile": "Profile"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome", "totalProducts": "Total Products", "totalCustomers": "Total Customers", "totalSuppliers": "Total Suppliers", "lowStock": "Low Stock", "pendingSales": "Pending Sales", "pendingPurchases": "Pending Purchases", "pendingMaintenance": "Pending Maintenance", "monthlySales": "Monthly Sales", "monthlyPurchases": "Monthly Purchases", "yearlySales": "Yearly Sales", "recentOrders": "Recent Orders", "salesChart": "Sales Chart", "topProducts": "Top Selling Products", "notifications": "Notifications"}, "products": {"title": "Product Management", "addProduct": "Add Product", "editProduct": "Edit Product", "productCode": "Product Code", "productName": "Product Name", "productNameAr": "Product Name (Arabic)", "category": "Category", "unitPrice": "Unit Price", "costPrice": "Cost Price", "currentStock": "Current Stock", "minStock": "Minimum Stock", "unit": "Unit", "unitAr": "Unit (Arabic)", "barcode": "Barcode", "description": "Description", "descriptionAr": "Description (Arabic)", "image": "Image", "status": "Status", "active": "Active", "inactive": "Inactive", "save": "Save", "cancel": "Cancel", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this product?"}, "customers": {"title": "Customer Management", "addCustomer": "Add Customer", "editCustomer": "Edit Customer", "customerCode": "Customer Code", "customerName": "Customer Name", "customerNameAr": "Customer Name (Arabic)", "customerType": "Customer Type", "customer": "Customer", "supplier": "Supplier", "both": "Customer & Supplier", "email": "Email", "phone": "Phone", "address": "Address", "addressAr": "Address (Arabic)", "balance": "Balance", "creditLimit": "Credit Limit"}, "sales": {"title": "Sales Management", "newOrder": "New Order", "orderNumber": "Order Number", "customer": "Customer", "orderDate": "Order Date", "dueDate": "Due Date", "status": "Status", "draft": "Draft", "pending": "Pending", "confirmed": "Confirmed", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled", "returned": "Returned", "subtotal": "Subtotal", "tax": "Tax", "discount": "Discount", "total": "Total", "addItem": "Add Item", "product": "Product", "quantity": "Quantity", "unitPrice": "Unit Price", "itemTotal": "Item Total"}, "purchases": {"title": "Purchase Management", "newOrder": "New Purchase Order", "orderNumber": "Order Number", "supplier": "Supplier", "orderDate": "Order Date", "expectedDate": "Expected Date", "receivedDate": "Received Date", "total": "Total", "status": "Status", "draft": "Draft", "pending": "Pending", "confirmed": "Confirmed", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled"}, "inventory": {"title": "Inventory Management", "currentStock": "Current Stock", "availableStock": "Available Stock", "reservedStock": "Reserved Stock", "lowStockAlert": "Low Stock Alert", "stockMovement": "Stock Movement", "adjustment": "Stock Adjustment", "transfer": "Stock Transfer"}, "maintenance": {"title": "Maintenance Services", "newOrder": "New Maintenance Order", "orderNumber": "Order Number", "customer": "Customer", "deviceType": "Device Type", "deviceModel": "Device Model", "serialNumber": "Serial Number", "problem": "Problem Description", "diagnosis": "Diagnosis", "estimatedCost": "Estimated Cost", "actualCost": "Actual Cost", "receivedDate": "Received Date", "completedDate": "Completed Date", "deliveredDate": "Delivered Date", "status": "Status", "received": "Received", "inProgress": "In Progress", "waitingParts": "Waiting for Parts", "completed": "Completed", "delivered": "Delivered"}, "reports": {"title": "Reports & Analytics", "salesReport": "Sales Report", "purchaseReport": "Purchase Report", "inventoryReport": "Inventory Report", "maintenanceReport": "Maintenance Report", "financialReport": "Financial Report", "customerReport": "Customer Report", "productReport": "Product Report", "dateRange": "Date Range", "from": "From", "to": "To", "generate": "Generate Report", "export": "Export", "print": "Print"}, "settings": {"title": "System Settings", "companyInfo": "Company Information", "userManagement": "User Management", "systemSettings": "System Settings", "backupRestore": "Backup & Restore", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "timezone": "Timezone"}, "payments": {"title": "Payments", "method": "Payment Method", "amount": "Amount", "cash": "Cash", "instaPay": "InstaPay", "vodafoneCash": "Vodafone Cash", "visa": "Visa", "installments": "Installments", "reference": "Reference", "paidAmount": "<PERSON><PERSON>", "remainingAmount": "Remaining Amount", "paymentStatus": "Payment Status", "pending": "Pending", "partial": "Partial", "completed": "Completed", "failed": "Failed", "refunded": "Refunded"}, "common": {"search": "Search", "filter": "Filter", "export": "Export", "print": "Print", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "add": "Add", "actions": "Actions", "loading": "Loading...", "noData": "No data available", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "confirm": "Confirm", "yes": "Yes", "no": "No", "required": "Required", "optional": "Optional", "selectOption": "Select an option", "date": "Date", "time": "Time", "name": "Name", "code": "Code", "description": "Description", "notes": "Notes", "createdAt": "Created At", "updatedAt": "Updated At", "language": "Language", "arabic": "العربية", "english": "English"}, "validation": {"required": "This field is required", "email": "Please enter a valid email", "minLength": "Must be at least {{min}} characters", "maxLength": "Must be less than {{max}} characters", "number": "Must be a number", "positive": "Must be a positive number", "phone": "Please enter a valid phone number"}}