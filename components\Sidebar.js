import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import Link from 'next/link';
import {
  HomeIcon,
  CubeIcon,
  UsersIcon,
  ShoppingCartIcon,
  ShoppingBagIcon,
  ArchiveBoxIcon,
  CalculatorIcon,
  WrenchScrewdriverIcon,
  DocumentChartBarIcon,
  Cog6ToothIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';

const navigation = [
  { name: 'dashboard', href: '/', icon: HomeIcon },
  { name: 'products', href: '/products', icon: CubeIcon },
  { name: 'customers', href: '/customers', icon: UsersIcon },
  { name: 'sales', href: '/sales', icon: ShoppingCartIcon },
  { name: 'store-pos', href: '/store-pos', icon: ShoppingCartIcon },
  { name: 'purchases', href: '/purchases', icon: ShoppingBagIcon },
  { name: 'inventory', href: '/inventory', icon: ArchiveBoxIcon },
  { name: 'accounting', href: '/accounting', icon: CalculatorIcon },
  { name: 'maintenance', href: '/maintenance', icon: WrenchScrewdriverIcon },
  { name: 'reports', href: '/reports', icon: DocumentChartBarIcon },
  { name: 'settings', href: '/settings', icon: Cog6ToothIcon },
];

export default function Sidebar({ open, setOpen }) {
  const router = useRouter();
  const { t } = useTranslation('common');

  return (
    <>
      {/* Desktop Sidebar */}
      <div className={`fixed inset-y-0 left-0 rtl:left-auto rtl:right-0 z-50 ${open ? 'w-64' : 'w-20'} bg-white shadow-lg transition-all duration-300 hidden lg:block`}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            {open && (
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm font-bold">BMS</span>
                </div>
                <span className="text-lg font-semibold text-gray-900">
                  {process.env.NEXT_PUBLIC_COMPANY_NAME || 'BMS'}
                </span>
              </div>
            )}
            <button
              onClick={() => setOpen(!open)}
              className="p-1.5 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            >
              {open ? (
                router.locale === 'ar' ? <ChevronRightIcon className="h-5 w-5" /> : <ChevronLeftIcon className="h-5 w-5" />
              ) : (
                router.locale === 'ar' ? <ChevronLeftIcon className="h-5 w-5" /> : <ChevronRightIcon className="h-5 w-5" />
              )}
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
            {navigation.map((item) => {
              const isActive = router.pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                    isActive
                      ? 'bg-primary-100 text-primary-900'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <item.icon
                    className={`${open ? 'mr-3 rtl:mr-0 rtl:ml-3' : 'mx-auto'} h-6 w-6 flex-shrink-0 ${
                      isActive ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'
                    }`}
                  />
                  {open && (
                    <span className="truncate">
                      {t(`navigation.${item.name}`)}
                    </span>
                  )}
                </Link>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <div className={`fixed inset-y-0 left-0 rtl:left-auto rtl:right-0 z-50 w-64 bg-white shadow-lg transform ${open ? 'translate-x-0 rtl:-translate-x-0' : '-translate-x-full rtl:translate-x-full'} transition-transform duration-300 lg:hidden`}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm font-bold">BMS</span>
              </div>
              <span className="text-lg font-semibold text-gray-900">
                {process.env.NEXT_PUBLIC_COMPANY_NAME || 'BMS'}
              </span>
            </div>
            <button
              onClick={() => setOpen(false)}
              className="p-1.5 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            >
              <ChevronLeftIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
            {navigation.map((item) => {
              const isActive = router.pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => setOpen(false)}
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                    isActive
                      ? 'bg-primary-100 text-primary-900'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <item.icon
                    className={`mr-3 rtl:mr-0 rtl:ml-3 h-6 w-6 flex-shrink-0 ${
                      isActive ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'
                    }`}
                  />
                  <span className="truncate">
                    {t(`navigation.${item.name}`)}
                  </span>
                </Link>
              );
            })}
          </nav>
        </div>
      </div>
    </>
  );
}
