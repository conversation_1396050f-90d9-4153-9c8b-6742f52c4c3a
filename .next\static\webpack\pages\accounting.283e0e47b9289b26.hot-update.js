"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/accounting",{

/***/ "__barrel_optimize__?names=ArrowsRightLeftIcon,CurrencyDollarIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!********************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowsRightLeftIcon,CurrencyDollarIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \********************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowsRightLeftIcon: function() { return /* reexport safe */ _ArrowsRightLeftIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   CurrencyDollarIcon: function() { return /* reexport safe */ _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   XMarkIcon: function() { return /* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _ArrowsRightLeftIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowsRightLeftIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArrowsRightLeftIcon.js\");\n/* harmony import */ var _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CurrencyDollarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd3NSaWdodExlZnRJY29uLEN1cnJlbmN5RG9sbGFySWNvbixYTWFya0ljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFDeUU7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz8wNWM5Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd3NSaWdodExlZnRJY29uIH0gZnJvbSBcIi4vQXJyb3dzUmlnaHRMZWZ0SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEN1cnJlbmN5RG9sbGFySWNvbiB9IGZyb20gXCIuL0N1cnJlbmN5RG9sbGFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFhNYXJrSWNvbiB9IGZyb20gXCIuL1hNYXJrSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowsRightLeftIcon,CurrencyDollarIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "./components/AccountingModals.js":
/*!****************************************!*\
  !*** ./components/AccountingModals.js ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddTransactionModal: function() { return /* binding */ AddTransactionModal; },\n/* harmony export */   TransferFundsModal: function() { return /* binding */ TransferFundsModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowsRightLeftIcon_CurrencyDollarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowsRightLeftIcon,CurrencyDollarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowsRightLeftIcon,CurrencyDollarIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n// Add Transaction Modal\nfunction AddTransactionModal(param) {\n    let { isOpen, onClose, onSubmit } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"INCOME\",\n        amount: \"\",\n        description: \"\",\n        descriptionAr: \"\",\n        paymentMethod: \"CASH\",\n        reference: \"\",\n        branchId: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch branches\n    const { data: branchesData } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)(\"branches\", async ()=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"\".concat(\"http://localhost:3001\", \"/api/branches\"));\n        return response.data;\n    });\n    const branches = (branchesData === null || branchesData === void 0 ? void 0 : branchesData.branches) || [];\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        setErrors({});\n        // Validation\n        const newErrors = {};\n        if (!formData.amount || parseFloat(formData.amount) <= 0) {\n            newErrors.amount = \"Amount is required and must be greater than 0\";\n        }\n        if (!formData.description) newErrors.description = \"Description is required\";\n        if (!formData.branchId) newErrors.branchId = \"Branch is required\";\n        if (Object.keys(newErrors).length > 0) {\n            setErrors(newErrors);\n            return;\n        }\n        onSubmit({\n            ...formData,\n            amount: parseFloat(formData.amount)\n        });\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const resetForm = ()=>{\n        setFormData({\n            type: \"INCOME\",\n            amount: \"\",\n            description: \"\",\n            descriptionAr: \"\",\n            paymentMethod: \"CASH\",\n            reference: \"\",\n            branchId: \"\"\n        });\n        setErrors({});\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: \"Add Transaction\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>{\n                                                    resetForm();\n                                                    onClose();\n                                                },\n                                                className: \"text-gray-400 hover:text-gray-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowsRightLeftIcon_CurrencyDollarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.XMarkIcon, {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Transaction Type *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"type\",\n                                                        value: formData.type,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"INCOME\",\n                                                                children: \"Income\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"EXPENSE\",\n                                                                children: \"Expense\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Amount *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        name: \"amount\",\n                                                        value: formData.amount,\n                                                        onChange: handleChange,\n                                                        className: \"form-input \".concat(errors.amount ? \"border-red-500\" : \"\"),\n                                                        placeholder: \"0.00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.amount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Branch *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"branchId\",\n                                                        value: formData.branchId,\n                                                        onChange: handleChange,\n                                                        className: \"form-input \".concat(errors.branchId ? \"border-red-500\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select Branch\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            branches.map((branch)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: branch.id,\n                                                                    children: branch.name\n                                                                }, branch.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                                    lineNumber: 139,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.branchId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.branchId\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 39\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Payment Method\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"paymentMethod\",\n                                                        value: formData.paymentMethod,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"CASH\",\n                                                                children: \"Cash\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"INSTAPAY\",\n                                                                children: \"InstaPay\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"VODAFONE_CASH\",\n                                                                children: \"Vodafone Cash\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"VISA\",\n                                                                children: \"Visa\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"BANK_TRANSFER\",\n                                                                children: \"Bank Transfer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Description (English) *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"description\",\n                                                        value: formData.description,\n                                                        onChange: handleChange,\n                                                        className: \"form-input \".concat(errors.description ? \"border-red-500\" : \"\"),\n                                                        placeholder: \"Enter description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 42\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Description (Arabic)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"descriptionAr\",\n                                                        value: formData.descriptionAr,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        placeholder: \"أدخل الوصف\",\n                                                        dir: \"rtl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Reference\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"reference\",\n                                                        value: formData.reference,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        placeholder: \"Reference number or note\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"btn-primary w-full sm:w-auto sm:ml-3\",\n                                        children: \"Add Transaction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            resetForm();\n                                            onClose();\n                                        },\n                                        className: \"btn-secondary w-full sm:w-auto mt-3 sm:mt-0\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(AddTransactionModal, \"jdqvwh0nRK9gMXQtUfgI6+J4EZ4=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery\n    ];\n});\n_c = AddTransactionModal;\n// Transfer Funds Modal\nfunction TransferFundsModal(param) {\n    let { isOpen, onClose, onSubmit } = param;\n    _s1();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        fromBranchId: \"\",\n        toBranchId: \"\",\n        amount: \"\",\n        description: \"\",\n        descriptionAr: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Fetch branches\n    const { data: branchesData } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)(\"branches\", async ()=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"\".concat(\"http://localhost:3001\", \"/api/branches\"));\n        return response.data;\n    });\n    const branches = (branchesData === null || branchesData === void 0 ? void 0 : branchesData.branches) || [];\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        setErrors({});\n        // Validation\n        const newErrors = {};\n        if (!formData.amount || parseFloat(formData.amount) <= 0) {\n            newErrors.amount = \"Amount is required and must be greater than 0\";\n        }\n        if (!formData.fromBranchId) newErrors.fromBranchId = \"From branch is required\";\n        if (!formData.toBranchId) newErrors.toBranchId = \"To branch is required\";\n        if (formData.fromBranchId === formData.toBranchId) {\n            newErrors.toBranchId = \"To branch must be different from from branch\";\n        }\n        if (!formData.description) newErrors.description = \"Description is required\";\n        if (Object.keys(newErrors).length > 0) {\n            setErrors(newErrors);\n            return;\n        }\n        onSubmit({\n            ...formData,\n            amount: parseFloat(formData.amount)\n        });\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const resetForm = ()=>{\n        setFormData({\n            fromBranchId: \"\",\n            toBranchId: \"\",\n            amount: \"\",\n            description: \"\",\n            descriptionAr: \"\"\n        });\n        setErrors({});\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: \"Transfer Funds\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>{\n                                                    resetForm();\n                                                    onClose();\n                                                },\n                                                className: \"text-gray-400 hover:text-gray-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowsRightLeftIcon_CurrencyDollarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.XMarkIcon, {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"From Branch *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"fromBranchId\",\n                                                        value: formData.fromBranchId,\n                                                        onChange: handleChange,\n                                                        className: \"form-input \".concat(errors.fromBranchId ? \"border-red-500\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select From Branch\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            branches.map((branch)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: branch.id,\n                                                                    children: branch.name\n                                                                }, branch.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.fromBranchId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.fromBranchId\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 43\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"To Branch *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"toBranchId\",\n                                                        value: formData.toBranchId,\n                                                        onChange: handleChange,\n                                                        className: \"form-input \".concat(errors.toBranchId ? \"border-red-500\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select To Branch\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            branches.filter((branch)=>branch.id !== formData.fromBranchId).map((branch)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: branch.id,\n                                                                    children: branch.name\n                                                                }, branch.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.toBranchId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.toBranchId\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Amount *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        name: \"amount\",\n                                                        value: formData.amount,\n                                                        onChange: handleChange,\n                                                        className: \"form-input \".concat(errors.amount ? \"border-red-500\" : \"\"),\n                                                        placeholder: \"0.00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.amount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Description (English) *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"description\",\n                                                        value: formData.description,\n                                                        onChange: handleChange,\n                                                        className: \"form-input \".concat(errors.description ? \"border-red-500\" : \"\"),\n                                                        placeholder: \"Enter transfer description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 42\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Description (Arabic)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"descriptionAr\",\n                                                        value: formData.descriptionAr,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        placeholder: \"أدخل وصف التحويل\",\n                                                        dir: \"rtl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"btn-primary w-full sm:w-auto sm:ml-3\",\n                                        children: \"Transfer Funds\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                        lineNumber: 398,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            resetForm();\n                                            onClose();\n                                        },\n                                        className: \"btn-secondary w-full sm:w-auto mt-3 sm:mt-0\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n            lineNumber: 296,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\AccountingModals.js\",\n        lineNumber: 295,\n        columnNumber: 5\n    }, this);\n}\n_s1(TransferFundsModal, \"+AYYsRzTVhl2NEEdNcFzJGC9yWo=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery\n    ];\n});\n_c1 = TransferFundsModal;\nvar _c, _c1;\n$RefreshReg$(_c, \"AddTransactionModal\");\n$RefreshReg$(_c1, \"TransferFundsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/AccountingModals.js\n"));

/***/ }),

/***/ "./pages/accounting.js":
/*!*****************************!*\
  !*** ./pages/accounting.js ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: function() { return /* binding */ __N_SSG; },\n/* harmony export */   \"default\": function() { return /* binding */ Accounting; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ArrowsRightLeftIcon,BanknotesIcon,BuildingLibraryIcon,CreditCardIcon,CurrencyDollarIcon,MagnifyingGlassIcon,PlusIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,ArrowsRightLeftIcon,BanknotesIcon,BuildingLibraryIcon,CreditCardIcon,CurrencyDollarIcon,MagnifyingGlassIcon,PlusIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/LoadingSpinner */ \"./components/LoadingSpinner.js\");\n/* harmony import */ var _components_AccountingModals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/AccountingModals */ \"./components/AccountingModals.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Accounting() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedBranch, setSelectedBranch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showAddTransactionModal, setShowAddTransactionModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTransferModal, setShowTransferModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dateFrom, setDateFrom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [dateTo, setDateTo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Set default date range (last 30 days)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const today = new Date();\n        const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);\n        setDateFrom(thirtyDaysAgo.toISOString().split(\"T\")[0]);\n        setDateTo(today.toISOString().split(\"T\")[0]);\n    }, []);\n    // Fetch cash transactions\n    const { data: transactionsData, isLoading, error, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        \"transactions\",\n        currentPage,\n        searchTerm,\n        selectedType,\n        selectedBranch,\n        dateFrom,\n        dateTo\n    ], async ()=>{\n        const params = new URLSearchParams({\n            page: currentPage.toString(),\n            limit: \"10\",\n            search: searchTerm,\n            type: selectedType,\n            branchId: selectedBranch\n        });\n        if (dateFrom) params.append(\"dateFrom\", dateFrom);\n        if (dateTo) params.append(\"dateTo\", dateTo);\n        const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"\".concat(\"http://localhost:3001\", \"/api/cash-transactions?\").concat(params));\n        return response.data;\n    }, {\n        keepPreviousData: true,\n        enabled: !!(dateFrom && dateTo)\n    });\n    // Fetch branches for filter\n    const { data: branchesData } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)(\"branches\", async ()=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"\".concat(\"http://localhost:3001\", \"/api/branches\"));\n        return response.data;\n    });\n    const transactions = (transactionsData === null || transactionsData === void 0 ? void 0 : transactionsData.transactions) || [];\n    const metrics = (transactionsData === null || transactionsData === void 0 ? void 0 : transactionsData.metrics) || {};\n    const branches = (branchesData === null || branchesData === void 0 ? void 0 : branchesData.branches) || [];\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        setCurrentPage(1);\n        refetch();\n    };\n    // Add transaction mutation\n    const addTransactionMutation = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)(async (transactionData)=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"\".concat(\"http://localhost:3001\", \"/api/cash-transactions\"), transactionData);\n        return response.data;\n    }, {\n        onSuccess: ()=>{\n            queryClient.invalidateQueries([\n                \"transactions\"\n            ]);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Transaction added successfully\");\n            setShowAddTransactionModal(false);\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"Failed to add transaction\");\n        }\n    });\n    // Transfer funds mutation\n    const transferMutation = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)(async (transferData)=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].post(\"\".concat(\"http://localhost:3001\", \"/api/cash-boxes/transfer\"), transferData);\n        return response.data;\n    }, {\n        onSuccess: ()=>{\n            queryClient.invalidateQueries([\n                \"transactions\"\n            ]);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Transfer completed successfully\");\n            setShowTransferModal(false);\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"Failed to transfer funds\");\n        }\n    });\n    const getTransactionIcon = (type)=>{\n        switch(type){\n            case \"INCOME\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.ArrowUpIcon, {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 120,\n                    columnNumber: 16\n                }, this);\n            case \"EXPENSE\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.ArrowDownIcon, {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 122,\n                    columnNumber: 16\n                }, this);\n            case \"TRANSFER_IN\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.ArrowsRightLeftIcon, {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 124,\n                    columnNumber: 16\n                }, this);\n            case \"TRANSFER_OUT\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.ArrowsRightLeftIcon, {\n                    className: \"h-4 w-4 text-orange-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 126,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.CurrencyDollarIcon, {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 128,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getTransactionColor = (type)=>{\n        switch(type){\n            case \"INCOME\":\n                return \"bg-green-100 text-green-800\";\n            case \"EXPENSE\":\n                return \"bg-red-100 text-red-800\";\n            case \"TRANSFER_IN\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"TRANSFER_OUT\":\n                return \"bg-orange-100 text-orange-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getPaymentMethodIcon = (method)=>{\n        switch(method){\n            case \"CASH\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.BanknotesIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 150,\n                    columnNumber: 16\n                }, this);\n            case \"VISA\":\n            case \"INSTAPAY\":\n            case \"VODAFONE_CASH\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.CreditCardIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 154,\n                    columnNumber: 16\n                }, this);\n            case \"BANK_TRANSFER\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.BuildingLibraryIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 156,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.CurrencyDollarIcon, {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 158,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (isLoading && !transactions.length) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                lineNumber: 165,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n            lineNumber: 164,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"mt-2 text-sm font-medium text-gray-900\",\n                    children: t(\"common.error\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: \"Failed to load accounting data\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>refetch(),\n                    className: \"mt-4 btn-primary\",\n                    children: \"Try Again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: t(\"navigation.accounting\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-600\",\n                                children: \"Manage cash flow, transactions, and financial records\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3 rtl:space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowTransferModal(true),\n                                className: \"btn-secondary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.ArrowsRightLeftIcon, {\n                                        className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Transfer Funds\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddTransactionModal(true),\n                                className: \"btn-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.PlusIcon, {\n                                        className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Add Transaction\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.ArrowUpIcon, {\n                                            className: \"h-6 w-6 text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: \"Total Income\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"text-lg font-medium text-green-600\",\n                                                    children: [\n                                                        \"$\",\n                                                        (metrics.totalIncome || 0).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.ArrowDownIcon, {\n                                            className: \"h-6 w-6 text-red-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: \"Total Expenses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"text-lg font-medium text-red-600\",\n                                                    children: [\n                                                        \"$\",\n                                                        (metrics.totalExpense || 0).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.CurrencyDollarIcon, {\n                                            className: \"h-6 w-6 \".concat((metrics.netCashFlow || 0) >= 0 ? \"text-green-400\" : \"text-red-400\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: \"Net Cash Flow\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"text-lg font-medium \".concat((metrics.netCashFlow || 0) >= 0 ? \"text-green-600\" : \"text-red-600\"),\n                                                    children: [\n                                                        \"$\",\n                                                        (metrics.netCashFlow || 0).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white overflow-hidden shadow rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.ArrowsRightLeftIcon, {\n                                            className: \"h-6 w-6 text-blue-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-5 rtl:ml-0 rtl:mr-5 w-0 flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                    className: \"text-sm font-medium text-gray-500 truncate\",\n                                                    children: \"Total Transactions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: metrics.totalTransactions || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-4 rounded-lg shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col sm:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.MagnifyingGlassIcon, {\n                                        className: \"absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t(\"common.search\"),\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"form-input pl-10 rtl:pl-3 rtl:pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: dateFrom,\n                                onChange: (e)=>setDateFrom(e.target.value),\n                                className: \"form-input\",\n                                placeholder: \"From Date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: dateTo,\n                                onChange: (e)=>setDateTo(e.target.value),\n                                className: \"form-input\",\n                                placeholder: \"To Date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedType,\n                            onChange: (e)=>setSelectedType(e.target.value),\n                            className: \"form-input\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"all\",\n                                    children: \"All Types\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"income\",\n                                    children: \"Income\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"expense\",\n                                    children: \"Expense\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"transfer_in\",\n                                    children: \"Transfer In\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"transfer_out\",\n                                    children: \"Transfer Out\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedBranch,\n                            onChange: (e)=>setSelectedBranch(e.target.value),\n                            className: \"form-input\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"all\",\n                                    children: \"All Branches\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                branches.map((branch)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: branch.id,\n                                        children: branch.name\n                                    }, branch.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary\",\n                            children: t(\"common.search\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"table\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Branch\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Payment Method\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: \"Reference\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"common.actions\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: transactions.map((transaction)=>{\n                                        var _transaction_cashBox_branch, _transaction_cashBox, _transaction_cashBox_branch1, _transaction_cashBox1, _transaction_paymentMethod;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: new Date(transaction.createdAt).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            getTransactionIcon(transaction.type),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 rtl:ml-0 rtl:mr-2 badge \".concat(getTransactionColor(transaction.type)),\n                                                                children: transaction.type.replace(\"_\", \" \")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-w-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900 truncate\",\n                                                                children: transaction.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 truncate\",\n                                                                children: transaction.descriptionAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: (_transaction_cashBox = transaction.cashBox) === null || _transaction_cashBox === void 0 ? void 0 : (_transaction_cashBox_branch = _transaction_cashBox.branch) === null || _transaction_cashBox_branch === void 0 ? void 0 : _transaction_cashBox_branch.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: (_transaction_cashBox1 = transaction.cashBox) === null || _transaction_cashBox1 === void 0 ? void 0 : (_transaction_cashBox_branch1 = _transaction_cashBox1.branch) === null || _transaction_cashBox_branch1 === void 0 ? void 0 : _transaction_cashBox_branch1.nameAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            getPaymentMethodIcon(transaction.paymentMethod),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 rtl:ml-0 rtl:mr-2 text-sm\",\n                                                                children: ((_transaction_paymentMethod = transaction.paymentMethod) === null || _transaction_paymentMethod === void 0 ? void 0 : _transaction_paymentMethod.replace(\"_\", \" \")) || \"CASH\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium \".concat(transaction.type === \"INCOME\" || transaction.type === \"TRANSFER_IN\" ? \"text-green-600\" : \"text-red-600\"),\n                                                        children: [\n                                                            transaction.type === \"INCOME\" || transaction.type === \"TRANSFER_IN\" ? \"+\" : \"-\",\n                                                            \"$\",\n                                                            (parseFloat(transaction.amount) || 0).toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: transaction.reference || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 text-gray-400 hover:text-blue-600\",\n                                                            title: t(\"common.view\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.MagnifyingGlassIcon, {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, transaction.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    transactions.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.CurrencyDollarIcon, {\n                                className: \"mx-auto h-12 w-12 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"mt-2 text-sm font-medium text-gray-900\",\n                                children: \"No transactions found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-500\",\n                                children: \"No transactions match your current filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_ArrowsRightLeftIcon_BanknotesIcon_BuildingLibraryIcon_CreditCardIcon_CurrencyDollarIcon_MagnifyingGlassIcon_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.PlusIcon, {\n                                            className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Add Transaction\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                                lineNumber: 447,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                        lineNumber: 441,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\accounting.js\",\n        lineNumber: 186,\n        columnNumber: 5\n    }, this);\n}\n_s(Accounting, \"KAyecOTTtjq6xS097ozOKFoHNfY=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation\n    ];\n});\n_c = Accounting;\nvar _c;\n$RefreshReg$(_c, \"Accounting\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/accounting.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js ***!
  \*******************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction XMarkIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M6 18 18 6M6 6l12 12\"\n    }));\n}\n_c = XMarkIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(XMarkIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"XMarkIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\n"));

/***/ })

});