# 🚀 **نظام المبيعات الجديد - ثلاث مراحل**

## 📋 **نظرة عامة**

تم تطوير نظام مبيعات متكامل بثلاث مراحل منفصلة:

### **1. 📄 عروض الأسعار (Quotes)**
- **الغرض**: تقديم أسعار للعملاء
- **تأثير المخزون**: ❌ **لا تؤثر على المخزون**
- **المدة**: لها تاريخ انتهاء صلاحية
- **التحويل**: يمكن تحويلها إلى أوامر بيع

### **2. 🛒 أوامر البيع (Sales Orders)**
- **الغرض**: تأكيد الطلب وحجز المنتجات
- **تأثير المخزون**: ⏳ **تحجز من المخزون**
- **المدة**: لها مدة صلاحية
- **التحويل**: يمكن تحويلها إلى فواتير

### **3. 🧾 الفواتير (Invoices)**
- **الغرض**: البيع النهائي والدفع
- **تأثير المخزون**: ✅ **تخصم من المخزون نهائياً**
- **المحاسبة**: تحسب في إيرادات المبيعات
- **الدفع**: تدعم طرق الدفع المتعددة

---

## 🔄 **سير العمل (Workflow)**

```
عرض السعر → أمر البيع → فاتورة
    ↓           ↓          ↓
لا تأثير    حجز المخزون   خصم نهائي
```

### **المسار الكامل:**
1. **إنشاء عرض سعر** للعميل (صالح لمدة محددة)
2. **موافقة العميل** → تحويل لأمر بيع
3. **تأكيد الأمر** → حجز المنتجات من المخزون
4. **تحويل لفاتورة** → خصم نهائي + تسجيل المبيعات
5. **الدفع** → إكمال العملية

---

## 🖥️ **كيفية التشغيل**

### **الطريقة الموحدة (خادم واحد):**
```bash
node server/unified-server.js
```

### **الوصول للنظام:**
- **الموقع**: http://localhost:3070
- **API**: http://localhost:3070/api
- **المبيعات**: http://localhost:3070/sales

### **بيانات الدخول:**
```
المدير: admin / admin123
المدير التنفيذي: manager / manager123
المبيعات: sales / sales123
```

---

## 📊 **الميزات الجديدة**

### **✅ عروض الأسعار:**
- إنشاء عروض أسعار مع تاريخ انتهاء
- لا تؤثر على المخزون
- تحويل العروض المعتمدة إلى أوامر بيع
- تتبع حالة العروض (مسودة، في الانتظار، معتمد، مرفوض، منتهي)

### **✅ أوامر البيع المحسنة:**
- حجز المنتجات من المخزون
- تتبع المخزون المحجوز
- تحويل الأوامر المؤكدة إلى فواتير
- إدارة دورة حياة الأمر

### **✅ الفواتير المتكاملة:**
- خصم نهائي من المخزون
- تسجيل في إيرادات المبيعات
- طرق دفع متعددة (نقدي، إنستاباي، فيزا، أقساط)
- طباعة وتصدير الفواتير

### **✅ إدارة المخزون الذكية:**
- تتبع المخزون المتاح
- تتبع المخزون المحجوز
- تنبيهات نفاد المخزون
- تحديث تلقائي عند التحويل بين المراحل

---

## 🎯 **حالات الاستخدام**

### **1. عميل يطلب عرض سعر:**
```
1. إنشاء عرض سعر جديد
2. إضافة المنتجات والكميات
3. تحديد تاريخ انتهاء الصلاحية
4. إرسال للعميل
5. انتظار الموافقة
```

### **2. العميل يوافق على العرض:**
```
1. تحويل عرض السعر إلى أمر بيع
2. حجز المنتجات من المخزون
3. تأكيد الأمر
4. تحضير المنتجات
```

### **3. تسليم المنتجات:**
```
1. تحويل أمر البيع إلى فاتورة
2. خصم المنتجات من المخزون نهائياً
3. تحصيل المبلغ
4. تسجيل في المبيعات
```

---

## 🔧 **التكوين التقني**

### **قاعدة البيانات:**
- جداول منفصلة: `quotes`, `sales_orders`, `invoices`
- تتبع العلاقات بين المراحل
- إدارة حالات المخزون

### **API Endpoints:**
```
GET  /api/quotes              - عرض جميع عروض الأسعار
POST /api/quotes              - إنشاء عرض سعر جديد
POST /api/quotes/:id/convert-to-order - تحويل لأمر بيع

GET  /api/sales-orders        - عرض جميع أوامر البيع
POST /api/sales-orders        - إنشاء أمر بيع جديد
POST /api/sales-orders/:id/convert-to-invoice - تحويل لفاتورة

GET  /api/invoices            - عرض جميع الفواتير
POST /api/invoices            - إنشاء فاتورة جديدة
```

### **حالات النظام:**
- **عروض الأسعار**: DRAFT, PENDING, APPROVED, REJECTED, EXPIRED
- **أوامر البيع**: DRAFT, PENDING, CONFIRMED, SHIPPED, DELIVERED, CANCELLED
- **الفواتير**: DRAFT, PENDING, PAID, PARTIALLY_PAID, OVERDUE, CANCELLED

---

## 📈 **الفوائد**

### **✅ للإدارة:**
- تتبع دقيق لدورة المبيعات
- تحكم أفضل في المخزون
- تقارير مفصلة لكل مرحلة
- تحليل معدلات التحويل

### **✅ للمبيعات:**
- سير عمل واضح ومنظم
- تجنب أخطاء المخزون
- متابعة أفضل للعملاء
- إدارة أسهل للعروض والأوامر

### **✅ للعملاء:**
- عروض أسعار واضحة
- ضمان توفر المنتجات
- مرونة في اتخاذ القرار
- شفافية في العملية

---

## 🎉 **النتيجة**

**تم تطوير نظام مبيعات متكامل وشامل يدعم:**
- ✅ ثلاث مراحل منفصلة ومترابطة
- ✅ إدارة ذكية للمخزون
- ✅ تتبع دقيق لدورة المبيعات
- ✅ واجهة سهلة الاستخدام
- ✅ تكامل كامل مع النظام الحالي

**النظام جاهز للاستخدام الفوري!** 🚀

---

## 🎯 **المكونات المكتملة**

### **✅ صفحة المبيعات الرئيسية:**
- **التبويبات الثلاث**: عروض الأسعار، أوامر البيع، الفواتير
- **مخطط سير العمل**: عرض بصري للمراحل الثلاث
- **جداول تفاعلية**: عرض البيانات مع إمكانية التحرير والتحويل
- **أزرار الإجراءات**: إنشاء، تعديل، تحويل بين المراحل

### **✅ مكونات الإنشاء والتحرير:**
- **QuoteModal**: إنشاء وتعديل عروض الأسعار
- **SalesOrderModal**: إنشاء وتعديل أوامر البيع مع تحقق المخزون
- **InvoiceModal**: إنشاء وتعديل الفواتير مع نظام الدفعات

### **✅ الميزات المتقدمة:**
- **تحقق المخزون**: تنبيهات عند نقص المخزون في أوامر البيع
- **نظام الدفعات**: دعم طرق دفع متعددة (نقدي، إنستاباي، فيزا، أقساط)
- **التحويل التلقائي**: تحويل سلس بين المراحل مع نقل البيانات
- **حساب الضرائب**: حساب تلقائي للضرائب والخصومات

### **✅ API المكتمل:**
- **CRUD Operations**: إنشاء، قراءة، تحديث لجميع المراحل
- **التحويل بين المراحل**: endpoints خاصة للتحويل
- **بيانات تجريبية**: عملاء ومنتجات جاهزة للاختبار

---

## 🚀 **كيفية الاستخدام**

### **1. تشغيل النظام:**
```bash
node server/unified-server.js
```

### **2. الوصول للنظام:**
- **الرابط**: http://localhost:3070
- **المبيعات**: http://localhost:3070/sales

### **3. تسجيل الدخول:**
```
admin / admin123
```

### **4. سير العمل:**

#### **أ) إنشاء عرض سعر:**
1. اذهب لتبويب "عروض الأسعار"
2. اضغط "عرض سعر جديد"
3. اختر العميل وحدد تاريخ انتهاء الصلاحية
4. أضف المنتجات والكميات
5. احفظ العرض

#### **ب) تحويل لأمر بيع:**
1. في جدول عروض الأسعار، اضغط "تحويل لأمر"
2. سيتم نقل جميع البيانات تلقائياً
3. تحقق من توفر المخزون
4. احفظ الأمر

#### **ج) تحويل لفاتورة:**
1. في جدول أوامر البيع، اضغط "تحويل لفاتورة"
2. أضف طرق الدفع المطلوبة
3. احفظ الفاتورة (سيتم خصم المخزون نهائياً)

---

## 🔧 **الميزات التقنية**

### **🎨 واجهة المستخدم:**
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **دعم RTL**: دعم كامل للعربية
- **تنبيهات ذكية**: تنبيهات للمخزون والحالات
- **ألوان مميزة**: لون مختلف لكل مرحلة

### **⚡ الأداء:**
- **تحميل سريع**: تحميل البيانات عند الحاجة
- **تحديث تلقائي**: تحديث الجداول بعد كل عملية
- **معالجة الأخطاء**: رسائل خطأ واضحة

### **🔒 الأمان:**
- **تحقق المصادقة**: حماية جميع الصفحات
- **التحقق من البيانات**: تحقق من صحة البيانات
- **حماية API**: endpoints محمية

---

## 📊 **البيانات التجريبية**

### **العملاء:**
- شركة ABC للتكنولوجيا
- مؤسسة XYZ التجارية
- شركة النيل للحاسوب

### **المنتجات:**
- أجهزة لابتوب (Dell, HP, Lenovo)
- مكونات (معالجات، ذاكرة، كروت شاشة)
- خدمات (صيانة، تركيب، دعم فني)

---

## 🎉 **النتيجة النهائية**

**تم إنجاز نظام مبيعات شامل ومتكامل يشمل:**

✅ **ثلاث مراحل منفصلة ومترابطة**
✅ **واجهة سهلة الاستخدام**
✅ **إدارة ذكية للمخزون**
✅ **نظام دفعات متقدم**
✅ **تحويل سلس بين المراحل**
✅ **دعم كامل للعربية**
✅ **API متكامل**
✅ **بيانات تجريبية جاهزة**

**النظام جاهز للاستخدام الفوري والتطوير المستقبلي!** 🚀✨

---

## 🎯 **التحسينات الجديدة المضافة**

### **✅ المنتجات القابلة للتخصيص:**
- **نظام تخصيص متقدم** مع خيارات متعددة
- **حساب السعر التلقائي** بناءً على الخيارات المختارة
- **واجهة تخصيص تفاعلية** سهلة الاستخدام
- **عرض تفاصيل التخصيص** في الفواتير

### **✅ نظام الدفع المتطور:**
- **6 طرق دفع مختلفة**: نقدي، إنستاباي، فودافون كاش، فيزا، تحويل بنكي، أقساط
- **نظام أقساط ذكي** مع حساب تلقائي للأقساط الشهرية
- **إدارة مدفوعات متعددة** لنفس الفاتورة
- **تتبع المراجع والإيصالات**

### **✅ فاتورة محسنة للطباعة:**
- **تصميم احترافي** مع هوية بصرية متكاملة
- **عرض تفاصيل التخصيص** للمنتجات المخصصة
- **جدول مدفوعات مفصل** مع التواريخ والمراجع
- **دعم الطباعة المباشرة** من المتصفح

### **✅ صفحة اختبار شاملة:**
- **اختبار جميع المكونات** بشكل منفصل
- **بيانات تجريبية واقعية** للاختبار
- **معاينة فورية** للنتائج

---

## 🔧 **كيفية الاستخدام المحسن**

### **1. تشغيل النظام:**
```bash
node server/unified-server.js
```

### **2. الوصول للصفحات:**
- **المبيعات الرئيسية**: http://localhost:3070/sales
- **صفحة الاختبار**: http://localhost:3070/test-sales
- **تسجيل الدخول**: http://localhost:3070/login

### **3. اختبار المنتجات القابلة للتخصيص:**
1. اذهب لصفحة الاختبار
2. اضغط "فتح مخصص المنتجات"
3. اختر الخيارات المطلوبة
4. شاهد السعر يتحدث تلقائياً
5. احفظ التخصيص

### **4. اختبار نظام الدفع:**
1. اضغط "فتح إدارة المدفوعات"
2. اختر طريقة الدفع
3. أدخل المبلغ
4. للأقساط: حدد عدد الأشهر
5. أضف المرجع إذا لزم الأمر

### **5. معاينة الفاتورة:**
1. اضغط "معاينة الفاتورة"
2. شاهد التصميم الاحترافي
3. لاحظ تفاصيل التخصيص
4. راجع جدول المدفوعات

---

## 🎨 **الميزات التقنية المتقدمة**

### **🔧 المنتجات القابلة للتخصيص:**
```javascript
// مثال على منتج قابل للتخصيص
{
  id: 'CUSTOM001',
  name: 'جهاز كمبيوتر ألعاب مخصص',
  basePrice: 800.00,
  isCustomizable: true,
  customizationOptions: [
    {
      id: 'cpu',
      name: 'المعالج',
      required: true,
      options: [
        { name: 'Intel i5-12400F', price: 200 },
        { name: 'Intel i7-12700F', price: 350 }
      ]
    }
  ]
}
```

### **💳 نظام الدفع المتقدم:**
```javascript
// طرق الدفع المدعومة
const paymentMethods = [
  'CASH',           // نقدي
  'INSTAPAY',       // إنستاباي
  'VODAFONE_CASH',  // فودافون كاش
  'VISA',           // فيزا
  'BANK_TRANSFER',  // تحويل بنكي
  'INSTALLMENT'     // أقساط
];
```

### **🖨️ نظام الطباعة:**
- **مكتبة react-to-print** للطباعة المباشرة
- **تصميم متجاوب** للطباعة
- **تنسيق احترافي** مع الألوان والخطوط

---

## 📊 **البيانات التجريبية المحسنة**

### **المنتجات القابلة للتخصيص:**
1. **جهاز كمبيوتر ألعاب مخصص**
   - خيارات المعالج (3 خيارات)
   - خيارات الذاكرة (3 خيارات)
   - خيارات كارت الشاشة (3 خيارات)

2. **لابتوب أعمال مخصص**
   - خيارات المعالج (2 خيارات)
   - خيارات الذاكرة (2 خيارات)
   - خيارات التخزين (3 خيارات)

### **طرق الدفع المتاحة:**
- ✅ **نقدي** - دفع فوري
- ✅ **إنستاباي** - تحويل فوري
- ✅ **فودافون كاش** - محفظة إلكترونية
- ✅ **فيزا** - بطاقة ائتمان/خصم
- ✅ **تحويل بنكي** - تحويل مصرفي
- ✅ **أقساط** - دفع على فترات (3، 6، 12، 24 شهر)

---

## 🎉 **النتيجة النهائية المحسنة**

**تم إنجاز نظام مبيعات شامل ومتطور يشمل:**

✅ **ثلاث مراحل مترابطة** (عروض أسعار → أوامر بيع → فواتير)
✅ **منتجات قابلة للتخصيص** مع حساب تلقائي للأسعار
✅ **نظام دفع متقدم** مع 6 طرق دفع مختلفة
✅ **فواتير احترافية** قابلة للطباعة
✅ **واجهة عربية كاملة** مع دعم RTL
✅ **صفحة اختبار شاملة** لجميع المكونات
✅ **إدارة ذكية للمخزون** مع التحقق التلقائي
✅ **API متكامل** مع جميع العمليات

**النظام الآن أكثر تطوراً واحترافية ومناسب للاستخدام التجاري الفعلي!** 🚀✨🎯

### **✅ المنتجات القابلة للتخصيص:**
- **نظام تخصيص متقدم** مع خيارات متعددة
- **حساب السعر التلقائي** بناءً على الخيارات المختارة
- **واجهة تخصيص تفاعلية** سهلة الاستخدام
- **عرض تفاصيل التخصيص** في الفواتير

### **✅ نظام الدفع المتطور:**
- **6 طرق دفع مختلفة**: نقدي، إنستاباي، فودافون كاش، فيزا، تحويل بنكي، أقساط
- **نظام أقساط ذكي** مع حساب تلقائي للأقساط الشهرية
- **إدارة مدفوعات متعددة** لنفس الفاتورة
- **تتبع المراجع والإيصالات**

### **✅ فاتورة محسنة للطباعة:**
- **تصميم احترافي** مع هوية بصرية متكاملة
- **عرض تفاصيل التخصيص** للمنتجات المخصصة
- **جدول مدفوعات مفصل** مع التواريخ والمراجع
- **دعم الطباعة المباشرة** من المتصفح

### **✅ صفحة اختبار شاملة:**
- **اختبار جميع المكونات** بشكل منفصل
- **بيانات تجريبية واقعية** للاختبار
- **معاينة فورية** للنتائج

---

## 🔧 **كيفية الاستخدام المحسن**

### **1. تشغيل النظام:**
```bash
node server/unified-server.js
```

### **2. الوصول للصفحات:**
- **المبيعات الرئيسية**: http://localhost:3070/sales
- **صفحة الاختبار**: http://localhost:3070/test-sales
- **تسجيل الدخول**: http://localhost:3070/login

### **3. اختبار المنتجات القابلة للتخصيص:**
1. اذهب لصفحة الاختبار
2. اضغط "فتح مخصص المنتجات"
3. اختر الخيارات المطلوبة
4. شاهد السعر يتحدث تلقائياً
5. احفظ التخصيص

### **4. اختبار نظام الدفع:**
1. اضغط "فتح إدارة المدفوعات"
2. اختر طريقة الدفع
3. أدخل المبلغ
4. للأقساط: حدد عدد الأشهر
5. أضف المرجع إذا لزم الأمر

### **5. معاينة الفاتورة:**
1. اضغط "معاينة الفاتورة"
2. شاهد التصميم الاحترافي
3. لاحظ تفاصيل التخصيص
4. راجع جدول المدفوعات

---

## 🎨 **الميزات التقنية المتقدمة**

### **🔧 المنتجات القابلة للتخصيص:**
```javascript
// مثال على منتج قابل للتخصيص
{
  id: 'CUSTOM001',
  name: 'جهاز كمبيوتر ألعاب مخصص',
  basePrice: 800.00,
  isCustomizable: true,
  customizationOptions: [
    {
      id: 'cpu',
      name: 'المعالج',
      required: true,
      options: [
        { name: 'Intel i5-12400F', price: 200 },
        { name: 'Intel i7-12700F', price: 350 }
      ]
    }
  ]
}
```

### **💳 نظام الدفع المتقدم:**
```javascript
// طرق الدفع المدعومة
const paymentMethods = [
  'CASH',           // نقدي
  'INSTAPAY',       // إنستاباي
  'VODAFONE_CASH',  // فودافون كاش
  'VISA',           // فيزا
  'BANK_TRANSFER',  // تحويل بنكي
  'INSTALLMENT'     // أقساط
];
```

### **🖨️ نظام الطباعة:**
- **مكتبة react-to-print** للطباعة المباشرة
- **تصميم متجاوب** للطباعة
- **تنسيق احترافي** مع الألوان والخطوط

---

## 📊 **البيانات التجريبية المحسنة**

### **المنتجات القابلة للتخصيص:**
1. **جهاز كمبيوتر ألعاب مخصص**
   - خيارات المعالج (3 خيارات)
   - خيارات الذاكرة (3 خيارات)
   - خيارات كارت الشاشة (3 خيارات)

2. **لابتوب أعمال مخصص**
   - خيارات المعالج (2 خيارات)
   - خيارات الذاكرة (2 خيارات)
   - خيارات التخزين (3 خيارات)

### **طرق الدفع المتاحة:**
- ✅ **نقدي** - دفع فوري
- ✅ **إنستاباي** - تحويل فوري
- ✅ **فودافون كاش** - محفظة إلكترونية
- ✅ **فيزا** - بطاقة ائتمان/خصم
- ✅ **تحويل بنكي** - تحويل مصرفي
- ✅ **أقساط** - دفع على فترات (3، 6، 12، 24 شهر)

---

## 🎉 **النتيجة النهائية المحسنة**

**تم إنجاز نظام مبيعات شامل ومتطور يشمل:**

✅ **ثلاث مراحل مترابطة** (عروض أسعار → أوامر بيع → فواتير)
✅ **منتجات قابلة للتخصيص** مع حساب تلقائي للأسعار
✅ **نظام دفع متقدم** مع 6 طرق دفع مختلفة
✅ **فواتير احترافية** قابلة للطباعة
✅ **واجهة عربية كاملة** مع دعم RTL
✅ **صفحة اختبار شاملة** لجميع المكونات
✅ **إدارة ذكية للمخزون** مع التحقق التلقائي
✅ **API متكامل** مع جميع العمليات

**النظام الآن أكثر تطوراً واحترافية ومناسب للاستخدام التجاري الفعلي!** 🚀✨🎯
