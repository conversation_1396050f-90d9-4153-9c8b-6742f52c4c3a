# 🚀 **نظام المبيعات الجديد - ثلاث مراحل**

## 📋 **نظرة عامة**

تم تطوير نظام مبيعات متكامل بثلاث مراحل منفصلة:

### **1. 📄 عروض الأسعار (Quotes)**
- **الغرض**: تقديم أسعار للعملاء
- **تأثير المخزون**: ❌ **لا تؤثر على المخزون**
- **المدة**: لها تاريخ انتهاء صلاحية
- **التحويل**: يمكن تحويلها إلى أوامر بيع

### **2. 🛒 أوامر البيع (Sales Orders)**
- **الغرض**: تأكيد الطلب وحجز المنتجات
- **تأثير المخزون**: ⏳ **تحجز من المخزون**
- **المدة**: لها مدة صلاحية
- **التحويل**: يمكن تحويلها إلى فواتير

### **3. 🧾 الفواتير (Invoices)**
- **الغرض**: البيع النهائي والدفع
- **تأثير المخزون**: ✅ **تخصم من المخزون نهائياً**
- **المحاسبة**: تحسب في إيرادات المبيعات
- **الدفع**: تدعم طرق الدفع المتعددة

---

## 🔄 **سير العمل (Workflow)**

```
عرض السعر → أمر البيع → فاتورة
    ↓           ↓          ↓
لا تأثير    حجز المخزون   خصم نهائي
```

### **المسار الكامل:**
1. **إنشاء عرض سعر** للعميل (صالح لمدة محددة)
2. **موافقة العميل** → تحويل لأمر بيع
3. **تأكيد الأمر** → حجز المنتجات من المخزون
4. **تحويل لفاتورة** → خصم نهائي + تسجيل المبيعات
5. **الدفع** → إكمال العملية

---

## 🖥️ **كيفية التشغيل**

### **الطريقة الموحدة (خادم واحد):**
```bash
node server/unified-server.js
```

### **الوصول للنظام:**
- **الموقع**: http://localhost:3070
- **API**: http://localhost:3070/api
- **المبيعات**: http://localhost:3070/sales

### **بيانات الدخول:**
```
المدير: admin / admin123
المدير التنفيذي: manager / manager123
المبيعات: sales / sales123
```

---

## 📊 **الميزات الجديدة**

### **✅ عروض الأسعار:**
- إنشاء عروض أسعار مع تاريخ انتهاء
- لا تؤثر على المخزون
- تحويل العروض المعتمدة إلى أوامر بيع
- تتبع حالة العروض (مسودة، في الانتظار، معتمد، مرفوض، منتهي)

### **✅ أوامر البيع المحسنة:**
- حجز المنتجات من المخزون
- تتبع المخزون المحجوز
- تحويل الأوامر المؤكدة إلى فواتير
- إدارة دورة حياة الأمر

### **✅ الفواتير المتكاملة:**
- خصم نهائي من المخزون
- تسجيل في إيرادات المبيعات
- طرق دفع متعددة (نقدي، إنستاباي، فيزا، أقساط)
- طباعة وتصدير الفواتير

### **✅ إدارة المخزون الذكية:**
- تتبع المخزون المتاح
- تتبع المخزون المحجوز
- تنبيهات نفاد المخزون
- تحديث تلقائي عند التحويل بين المراحل

---

## 🎯 **حالات الاستخدام**

### **1. عميل يطلب عرض سعر:**
```
1. إنشاء عرض سعر جديد
2. إضافة المنتجات والكميات
3. تحديد تاريخ انتهاء الصلاحية
4. إرسال للعميل
5. انتظار الموافقة
```

### **2. العميل يوافق على العرض:**
```
1. تحويل عرض السعر إلى أمر بيع
2. حجز المنتجات من المخزون
3. تأكيد الأمر
4. تحضير المنتجات
```

### **3. تسليم المنتجات:**
```
1. تحويل أمر البيع إلى فاتورة
2. خصم المنتجات من المخزون نهائياً
3. تحصيل المبلغ
4. تسجيل في المبيعات
```

---

## 🔧 **التكوين التقني**

### **قاعدة البيانات:**
- جداول منفصلة: `quotes`, `sales_orders`, `invoices`
- تتبع العلاقات بين المراحل
- إدارة حالات المخزون

### **API Endpoints:**
```
GET  /api/quotes              - عرض جميع عروض الأسعار
POST /api/quotes              - إنشاء عرض سعر جديد
POST /api/quotes/:id/convert-to-order - تحويل لأمر بيع

GET  /api/sales-orders        - عرض جميع أوامر البيع
POST /api/sales-orders        - إنشاء أمر بيع جديد
POST /api/sales-orders/:id/convert-to-invoice - تحويل لفاتورة

GET  /api/invoices            - عرض جميع الفواتير
POST /api/invoices            - إنشاء فاتورة جديدة
```

### **حالات النظام:**
- **عروض الأسعار**: DRAFT, PENDING, APPROVED, REJECTED, EXPIRED
- **أوامر البيع**: DRAFT, PENDING, CONFIRMED, SHIPPED, DELIVERED, CANCELLED
- **الفواتير**: DRAFT, PENDING, PAID, PARTIALLY_PAID, OVERDUE, CANCELLED

---

## 📈 **الفوائد**

### **✅ للإدارة:**
- تتبع دقيق لدورة المبيعات
- تحكم أفضل في المخزون
- تقارير مفصلة لكل مرحلة
- تحليل معدلات التحويل

### **✅ للمبيعات:**
- سير عمل واضح ومنظم
- تجنب أخطاء المخزون
- متابعة أفضل للعملاء
- إدارة أسهل للعروض والأوامر

### **✅ للعملاء:**
- عروض أسعار واضحة
- ضمان توفر المنتجات
- مرونة في اتخاذ القرار
- شفافية في العملية

---

## 🎉 **النتيجة**

**تم تطوير نظام مبيعات متكامل وشامل يدعم:**
- ✅ ثلاث مراحل منفصلة ومترابطة
- ✅ إدارة ذكية للمخزون
- ✅ تتبع دقيق لدورة المبيعات
- ✅ واجهة سهلة الاستخدام
- ✅ تكامل كامل مع النظام الحالي

**النظام جاهز للاستخدام الفوري!** 🚀
