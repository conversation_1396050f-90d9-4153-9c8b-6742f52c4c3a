"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "./components/sales/InvoiceModal.js":
/*!******************************************!*\
  !*** ./components/sales/InvoiceModal.js ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ InvoiceModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CogIcon,CreditCardIcon,PlusIcon,PrinterIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=CogIcon,CreditCardIcon,PlusIcon,PrinterIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-to-print */ \"./node_modules/react-to-print/lib/index.js\");\n/* harmony import */ var react_to_print__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_to_print__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _ProductCustomizer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ProductCustomizer */ \"./components/sales/ProductCustomizer.js\");\n/* harmony import */ var _PaymentManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./PaymentManager */ \"./components/sales/PaymentManager.js\");\n/* harmony import */ var _InvoicePrintView__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./InvoicePrintView */ \"./components/sales/InvoicePrintView.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction InvoiceModal(param) {\n    let { isOpen, onClose, onSave, invoice = null, fromSalesOrder = null } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const printRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    // Modal states\n    const [showCustomizer, setShowCustomizer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPaymentManager, setShowPaymentManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: \"\",\n        dueDate: \"\",\n        notes: \"\",\n        items: [],\n        payments: []\n    });\n    const [showPaymentModal, setShowPaymentModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentData, setPaymentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        method: \"CASH\",\n        amount: 0,\n        reference: \"\",\n        installmentPlan: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            loadCustomers();\n            loadProducts();\n            if (invoice) {\n                setFormData({\n                    customerId: invoice.customerId || \"\",\n                    dueDate: invoice.dueDate ? invoice.dueDate.split(\"T\")[0] : \"\",\n                    notes: invoice.notes || \"\",\n                    items: invoice.items || [],\n                    payments: invoice.payments || []\n                });\n            } else if (fromSalesOrder) {\n                // Convert sales order to invoice\n                const dueDate = new Date();\n                dueDate.setDate(dueDate.getDate() + 30); // 30 days payment terms\n                setFormData({\n                    customerId: fromSalesOrder.customerId || \"\",\n                    dueDate: dueDate.toISOString().split(\"T\")[0],\n                    notes: \"تم التحويل من أمر البيع: \".concat(fromSalesOrder.orderNumber),\n                    items: fromSalesOrder.items || [],\n                    payments: []\n                });\n            } else {\n                // New invoice\n                const dueDate = new Date();\n                dueDate.setDate(dueDate.getDate() + 30);\n                setFormData((prev)=>({\n                        ...prev,\n                        dueDate: dueDate.toISOString().split(\"T\")[0]\n                    }));\n            }\n        }\n    }, [\n        isOpen,\n        invoice,\n        fromSalesOrder\n    ]);\n    const loadCustomers = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/customers\"));\n            setCustomers(response.data.customers || []);\n        } catch (error) {\n            console.error(\"Error loading customers:\", error);\n        }\n    };\n    const loadProducts = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/products\"));\n            setProducts(response.data.products || []);\n        } catch (error) {\n            console.error(\"Error loading products:\", error);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const addItem = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: [\n                    ...prev.items,\n                    {\n                        productId: \"\",\n                        productName: \"\",\n                        quantity: 1,\n                        unitPrice: 0,\n                        discount: 0,\n                        taxRate: 14,\n                        hasTax: true,\n                        total: 0,\n                        isCustomized: false,\n                        customizations: null,\n                        customizationDetails: []\n                    }\n                ]\n            }));\n    };\n    const removeItem = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: prev.items.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateItem = (index, field, value)=>{\n        setFormData((prev)=>{\n            const newItems = [\n                ...prev.items\n            ];\n            newItems[index] = {\n                ...newItems[index],\n                [field]: value\n            };\n            if (field === \"productId\") {\n                const product = products.find((p)=>p.id === value);\n                if (product) {\n                    newItems[index].unitPrice = parseFloat(product.unitPrice);\n                    newItems[index].productName = product.nameAr || product.name;\n                    // Check if product is customizable\n                    if (product.isCustomizable) {\n                        setSelectedProduct(product);\n                        setCurrentItemIndex(index);\n                        setShowCustomizer(true);\n                        return prev; // Don't update yet, wait for customization\n                    }\n                }\n            }\n            if (field === \"quantity\" || field === \"unitPrice\" || field === \"discount\") {\n                const item = newItems[index];\n                const subtotal = (parseFloat(item.quantity) || 0) * (parseFloat(item.unitPrice) || 0);\n                const discountAmount = subtotal * ((parseFloat(item.discount) || 0) / 100);\n                newItems[index].total = subtotal - discountAmount;\n            }\n            return {\n                ...prev,\n                items: newItems\n            };\n        });\n    };\n    // Handle customized product\n    const handleCustomizedProduct = (customizedProduct)=>{\n        setFormData((prev)=>{\n            const newItems = [\n                ...prev.items\n            ];\n            newItems[currentItemIndex] = {\n                ...newItems[currentItemIndex],\n                productId: customizedProduct.id,\n                productName: customizedProduct.nameAr || customizedProduct.name,\n                unitPrice: customizedProduct.finalPrice,\n                customizations: customizedProduct.customizations,\n                customizationDetails: customizedProduct.customizationDetails,\n                isCustomized: true\n            };\n            // Recalculate total\n            const item = newItems[currentItemIndex];\n            const subtotal = (parseFloat(item.quantity) || 0) * (parseFloat(item.unitPrice) || 0);\n            const discountAmount = subtotal * ((parseFloat(item.discount) || 0) / 100);\n            newItems[currentItemIndex].total = subtotal - discountAmount;\n            return {\n                ...prev,\n                items: newItems\n            };\n        });\n    };\n    // Print functionality\n    const handlePrint = (0,react_to_print__WEBPACK_IMPORTED_MODULE_4__.useReactToPrint)({\n        content: ()=>printRef.current,\n        documentTitle: \"فاتورة-\".concat(formData.invoiceNumber || \"جديدة\")\n    });\n    const calculateTotals = ()=>{\n        const subtotal = formData.items.reduce((sum, item)=>sum + (parseFloat(item.total) || 0), 0);\n        const taxAmount = subtotal * 0.14; // 14% tax\n        const total = subtotal + taxAmount;\n        const paidAmount = formData.payments.reduce((sum, payment)=>sum + (parseFloat(payment.amount) || 0), 0);\n        const remainingAmount = total - paidAmount;\n        return {\n            subtotal,\n            taxAmount,\n            total,\n            paidAmount,\n            remainingAmount\n        };\n    };\n    const addPayment = ()=>{\n        const { total, paidAmount } = calculateTotals();\n        const maxAmount = total - paidAmount;\n        if (maxAmount <= 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"تم دفع المبلغ بالكامل\");\n            return;\n        }\n        setShowPaymentManager(true);\n    };\n    const handlePaymentAdd = (payment)=>{\n        setFormData((prev)=>({\n                ...prev,\n                payments: [\n                    ...prev.payments,\n                    {\n                        ...payment,\n                        id: Date.now().toString()\n                    }\n                ]\n            }));\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"تم إضافة الدفعة بنجاح\");\n    };\n    const savePayment = ()=>{\n        if (!paymentData.amount || paymentData.amount <= 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى إدخال مبلغ صحيح\");\n            return;\n        }\n        const { total, paidAmount } = calculateTotals();\n        if (paidAmount + parseFloat(paymentData.amount) > total) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"المبلغ المدفوع أكبر من المبلغ المطلوب\");\n            return;\n        }\n        setFormData((prev)=>({\n                ...prev,\n                payments: [\n                    ...prev.payments,\n                    {\n                        ...paymentData,\n                        id: Date.now().toString(),\n                        paidAt: new Date().toISOString()\n                    }\n                ]\n            }));\n        setShowPaymentModal(false);\n        setPaymentData({\n            method: \"CASH\",\n            amount: 0,\n            reference: \"\",\n            installmentPlan: \"\"\n        });\n    };\n    const removePayment = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                payments: prev.payments.filter((_, i)=>i !== index)\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.customerId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى اختيار العميل\");\n            return;\n        }\n        if (formData.items.length === 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى إضافة عنصر واحد على الأقل\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const { subtotal, taxAmount, total, paidAmount, remainingAmount } = calculateTotals();\n            const invoiceData = {\n                ...formData,\n                subtotal,\n                taxAmount,\n                total,\n                paidAmount,\n                remainingAmount,\n                status: paidAmount >= total ? \"PAID\" : paidAmount > 0 ? \"PARTIALLY_PAID\" : \"PENDING\",\n                salesOrderId: (fromSalesOrder === null || fromSalesOrder === void 0 ? void 0 : fromSalesOrder.id) || null\n            };\n            const response = invoice ? await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].put(\"\".concat(\"http://localhost:3070\", \"/api/invoices/\").concat(invoice.id), invoiceData) : await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].post(\"\".concat(\"http://localhost:3070\", \"/api/invoices\"), invoiceData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.data.message || (invoice ? \"تم تحديث الفاتورة\" : \"تم إنشاء الفاتورة\"));\n            onSave(response.data.invoice);\n            onClose();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"حدث خطأ\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    const { subtotal, taxAmount, total, paidAmount, remainingAmount } = calculateTotals();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: [\n                                        invoice ? \"تعديل الفاتورة\" : \"فاتورة جديدة\",\n                                        fromSalesOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-green-600 block\",\n                                            children: [\n                                                \"تحويل من أمر البيع: \",\n                                                fromSalesOrder.orderNumber\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.XMarkIcon, {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"p-6 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"form-label\",\n                                                    children: \"العميل *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    name: \"customerId\",\n                                                    value: formData.customerId,\n                                                    onChange: handleChange,\n                                                    className: \"form-input\",\n                                                    required: true,\n                                                    disabled: fromSalesOrder,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"اختر العميل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: customer.id,\n                                                                children: customer.name\n                                                            }, customer.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"form-label\",\n                                                    children: \"تاريخ الاستحقاق\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"date\",\n                                                    name: \"dueDate\",\n                                                    value: formData.dueDate,\n                                                    onChange: handleChange,\n                                                    className: \"form-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"العناصر\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: addItem,\n                                                    className: \"btn-primary flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.PlusIcon, {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"إضافة عنصر\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: formData.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-12 gap-4 items-end p-4 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"المنتج\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: item.productId,\n                                                                            onChange: (e)=>updateItem(index, \"productId\", e.target.value),\n                                                                            className: \"form-input flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"\",\n                                                                                    children: \"اختر المنتج\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                                    lineNumber: 388,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: product.id,\n                                                                                        children: [\n                                                                                            product.nameAr || product.name,\n                                                                                            product.isCustomizable && \" (قابل للتخصيص)\"\n                                                                                        ]\n                                                                                    }, product.id, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                                        lineNumber: 390,\n                                                                                        columnNumber: 29\n                                                                                    }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 383,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        item.isCustomized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>{\n                                                                                const product = products.find((p)=>p.id === item.productId);\n                                                                                if (product) {\n                                                                                    setSelectedProduct(product);\n                                                                                    setCurrentItemIndex(index);\n                                                                                    setShowCustomizer(true);\n                                                                                }\n                                                                            },\n                                                                            className: \"mr-2 px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200\",\n                                                                            title: \"تعديل التخصيص\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.CogIcon, {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                                lineNumber: 410,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                item.customizationDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 text-xs text-gray-600 bg-blue-50 p-2 rounded\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"التخصيصات:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        item.customizationDetails.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    \"• \",\n                                                                                    detail.optionName,\n                                                                                    \": \",\n                                                                                    detail.selectedName\n                                                                                ]\n                                                                            }, idx, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                                lineNumber: 418,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"الكمية\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: item.quantity,\n                                                                    onChange: (e)=>updateItem(index, \"quantity\", e.target.value),\n                                                                    className: \"form-input\",\n                                                                    min: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"السعر\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: item.unitPrice,\n                                                                    onChange: (e)=>updateItem(index, \"unitPrice\", e.target.value),\n                                                                    className: \"form-input\",\n                                                                    step: \"0.01\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"خصم %\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: item.discount,\n                                                                    onChange: (e)=>updateItem(index, \"discount\", e.target.value),\n                                                                    className: \"form-input\",\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"form-label\",\n                                                                    children: \"الإجمالي\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900 py-2\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        (item.total || 0).toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 460,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>removeItem(index),\n                                                                className: \"text-red-600 hover:text-red-800\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.TrashIcon, {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"المدفوعات\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: addPayment,\n                                                    className: \"btn-secondary flex items-center\",\n                                                    disabled: remainingAmount <= 0,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.CreditCardIcon, {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"إضافة دفعة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, this),\n                                        formData.payments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: formData.payments.map((payment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: payment.method\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 499,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"$\",\n                                                                        parseFloat(payment.amount).toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                payment.reference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"المرجع: \",\n                                                                        payment.reference\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>removePayment(index),\n                                                            className: \"text-red-600 hover:text-red-800\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.TrashIcon, {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 480,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5 text-red-400\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 521,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-medium text-red-800\",\n                                                        children: \"تنبيه خصم المخزون\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-sm text-red-700\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"عند حفظ الفاتورة، سيتم خصم الكميات من المخزون نهائياً وتسجيل المبيعات. هذا الإجراء لا يمكن التراجع عنه.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 526,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                        lineNumber: 520,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 519,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"المجموع الفرعي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            subtotal.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"الضريبة (14%):\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            taxAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between font-bold text-lg border-t pt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"الإجمالي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            total.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 551,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-green-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"المدفوع:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            paidAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-red-600 font-bold\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"المتبقي:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"$\",\n                                                            remainingAmount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 559,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 541,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"ملاحظات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 568,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            name: \"notes\",\n                                            value: formData.notes,\n                                            onChange: handleChange,\n                                            className: \"form-input\",\n                                            rows: \"3\",\n                                            placeholder: \"ملاحظات إضافية...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 569,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 567,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: invoice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: handlePrint,\n                                                className: \"btn-secondary flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_CreditCardIcon_PlusIcon_PrinterIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__.PrinterIcon, {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"طباعة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                lineNumber: 583,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 581,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: onClose,\n                                                    className: \"btn-secondary\",\n                                                    children: \"إلغاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: loading,\n                                                    className: \"btn-primary\",\n                                                    children: loading ? \"جاري الحفظ...\" : invoice ? \"تحديث\" : \"إنشاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                            lineNumber: 594,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                                    lineNumber: 580,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"none\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InvoicePrintView__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    ref: printRef,\n                    invoice: {\n                        ...formData,\n                        invoiceNumber: (invoice === null || invoice === void 0 ? void 0 : invoice.invoiceNumber) || \"جديدة\",\n                        ...calculateTotals()\n                    },\n                    customer: customers.find((c)=>c.id === formData.customerId)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                    lineNumber: 617,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                lineNumber: 616,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProductCustomizer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: showCustomizer,\n                onClose: ()=>setShowCustomizer(false),\n                product: selectedProduct,\n                onSave: handleCustomizedProduct\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                lineNumber: 629,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PaymentManager__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: showPaymentManager,\n                onClose: ()=>setShowPaymentManager(false),\n                totalAmount: calculateTotals().total,\n                paidAmount: calculateTotals().paidAmount,\n                onPaymentAdd: handlePaymentAdd,\n                existingPayments: formData.payments\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\InvoiceModal.js\",\n                lineNumber: 637,\n                columnNumber: 7\n            }, this),\n             false && /*#__PURE__*/ 0\n        ]\n    }, void 0, true);\n}\n_s(InvoiceModal, \"xMUley7+AGlhBXjAMuZUokHsfaY=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        react_to_print__WEBPACK_IMPORTED_MODULE_4__.useReactToPrint\n    ];\n});\n_c = InvoiceModal;\nvar _c;\n$RefreshReg$(_c, \"InvoiceModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/sales/InvoiceModal.js\n"));

/***/ })

});