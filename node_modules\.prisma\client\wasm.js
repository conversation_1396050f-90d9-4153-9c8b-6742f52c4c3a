
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  username: 'username',
  password: 'password',
  firstName: 'firstName',
  lastName: 'lastName',
  role: 'role',
  branchId: 'branchId',
  isActive: 'isActive',
  twoFactorEnabled: 'twoFactorEnabled',
  lastLogin: 'lastLogin',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompanyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  nameAr: 'nameAr',
  address: 'address',
  addressAr: 'addressAr',
  phone: 'phone',
  email: 'email',
  website: 'website',
  logo: 'logo',
  currency: 'currency',
  language: 'language',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BranchScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  code: 'code',
  name: 'name',
  nameAr: 'nameAr',
  address: 'address',
  addressAr: 'addressAr',
  phone: 'phone',
  email: 'email',
  managerId: 'managerId',
  isActive: 'isActive',
  isMainBranch: 'isMainBranch',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CashBoxScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  currentBalance: 'currentBalance',
  openingBalance: 'openingBalance',
  lastTransferDate: 'lastTransferDate',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CashTransactionScalarFieldEnum = {
  id: 'id',
  cashBoxId: 'cashBoxId',
  branchId: 'branchId',
  type: 'type',
  amount: 'amount',
  description: 'description',
  descriptionAr: 'descriptionAr',
  referenceId: 'referenceId',
  referenceType: 'referenceType',
  userId: 'userId',
  createdAt: 'createdAt'
};

exports.Prisma.TreasuryTransferScalarFieldEnum = {
  id: 'id',
  cashBoxId: 'cashBoxId',
  amount: 'amount',
  transferDate: 'transferDate',
  approvedBy: 'approvedBy',
  status: 'status',
  notes: 'notes',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BranchInventoryScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  productId: 'productId',
  quantity: 'quantity',
  reservedQty: 'reservedQty',
  minStock: 'minStock',
  maxStock: 'maxStock',
  lastUpdated: 'lastUpdated'
};

exports.Prisma.BranchTransferScalarFieldEnum = {
  id: 'id',
  transferNumber: 'transferNumber',
  fromBranchId: 'fromBranchId',
  toBranchId: 'toBranchId',
  status: 'status',
  requestedBy: 'requestedBy',
  approvedBy: 'approvedBy',
  transferDate: 'transferDate',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BranchTransferItemScalarFieldEnum = {
  id: 'id',
  transferId: 'transferId',
  productId: 'productId',
  quantity: 'quantity',
  notes: 'notes'
};

exports.Prisma.PaymentScalarFieldEnum = {
  id: 'id',
  salesOrderId: 'salesOrderId',
  purchaseOrderId: 'purchaseOrderId',
  maintenanceOrderId: 'maintenanceOrderId',
  method: 'method',
  amount: 'amount',
  reference: 'reference',
  installmentPlan: 'installmentPlan',
  status: 'status',
  paidAt: 'paidAt',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  nameAr: 'nameAr',
  description: 'description',
  descriptionAr: 'descriptionAr',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  code: 'code',
  name: 'name',
  nameAr: 'nameAr',
  description: 'description',
  descriptionAr: 'descriptionAr',
  categoryId: 'categoryId',
  productType: 'productType',
  unitPrice: 'unitPrice',
  costPrice: 'costPrice',
  minStock: 'minStock',
  currentStock: 'currentStock',
  unit: 'unit',
  unitAr: 'unitAr',
  barcode: 'barcode',
  image: 'image',
  isActive: 'isActive',
  hasInventory: 'hasInventory',
  customizationOptions: 'customizationOptions',
  compatibleComponents: 'compatibleComponents',
  assemblyInstructions: 'assemblyInstructions',
  assemblyInstructionsAr: 'assemblyInstructionsAr',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CustomerScalarFieldEnum = {
  id: 'id',
  code: 'code',
  name: 'name',
  nameAr: 'nameAr',
  type: 'type',
  email: 'email',
  phone: 'phone',
  address: 'address',
  addressAr: 'addressAr',
  balance: 'balance',
  creditLimit: 'creditLimit',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SalesOrderScalarFieldEnum = {
  id: 'id',
  orderNumber: 'orderNumber',
  customerId: 'customerId',
  userId: 'userId',
  branchId: 'branchId',
  orderDate: 'orderDate',
  dueDate: 'dueDate',
  status: 'status',
  subtotal: 'subtotal',
  taxAmount: 'taxAmount',
  discount: 'discount',
  total: 'total',
  paidAmount: 'paidAmount',
  remainingAmount: 'remainingAmount',
  paymentStatus: 'paymentStatus',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SalesOrderItemScalarFieldEnum = {
  id: 'id',
  salesOrderId: 'salesOrderId',
  productId: 'productId',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  discount: 'discount',
  total: 'total',
  isCustomizable: 'isCustomizable',
  customConfiguration: 'customConfiguration',
  assemblyFee: 'assemblyFee',
  serviceFee: 'serviceFee'
};

exports.Prisma.SalesOrderCustomComponentScalarFieldEnum = {
  id: 'id',
  salesOrderItemId: 'salesOrderItemId',
  componentId: 'componentId',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  total: 'total'
};

exports.Prisma.PurchaseOrderScalarFieldEnum = {
  id: 'id',
  orderNumber: 'orderNumber',
  supplierId: 'supplierId',
  userId: 'userId',
  branchId: 'branchId',
  orderDate: 'orderDate',
  expectedDate: 'expectedDate',
  receivedDate: 'receivedDate',
  status: 'status',
  subtotal: 'subtotal',
  taxAmount: 'taxAmount',
  discount: 'discount',
  total: 'total',
  paidAmount: 'paidAmount',
  remainingAmount: 'remainingAmount',
  paymentStatus: 'paymentStatus',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PurchaseOrderItemScalarFieldEnum = {
  id: 'id',
  purchaseOrderId: 'purchaseOrderId',
  productId: 'productId',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  discount: 'discount',
  total: 'total'
};

exports.Prisma.InventoryAdjustmentScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  userId: 'userId',
  type: 'type',
  quantity: 'quantity',
  reason: 'reason',
  notes: 'notes',
  createdAt: 'createdAt'
};

exports.Prisma.MaintenanceOrderScalarFieldEnum = {
  id: 'id',
  orderNumber: 'orderNumber',
  customerId: 'customerId',
  userId: 'userId',
  branchId: 'branchId',
  deviceType: 'deviceType',
  deviceModel: 'deviceModel',
  serialNumber: 'serialNumber',
  problem: 'problem',
  problemAr: 'problemAr',
  diagnosis: 'diagnosis',
  diagnosisAr: 'diagnosisAr',
  status: 'status',
  estimatedCost: 'estimatedCost',
  actualCost: 'actualCost',
  paidAmount: 'paidAmount',
  remainingAmount: 'remainingAmount',
  paymentStatus: 'paymentStatus',
  receivedDate: 'receivedDate',
  completedDate: 'completedDate',
  deliveredDate: 'deliveredDate',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CustomProductConfigurationScalarFieldEnum = {
  id: 'id',
  baseProductId: 'baseProductId',
  customerId: 'customerId',
  branchId: 'branchId',
  components: 'components',
  quantities: 'quantities',
  assemblyFee: 'assemblyFee',
  serviceFee: 'serviceFee',
  totalPrice: 'totalPrice',
  mode: 'mode',
  status: 'status',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.QuoteScalarFieldEnum = {
  id: 'id',
  quoteNumber: 'quoteNumber',
  customerId: 'customerId',
  userId: 'userId',
  branchId: 'branchId',
  quoteDate: 'quoteDate',
  validUntil: 'validUntil',
  status: 'status',
  subtotal: 'subtotal',
  taxAmount: 'taxAmount',
  discount: 'discount',
  total: 'total',
  notes: 'notes',
  convertedToOrderId: 'convertedToOrderId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.QuoteItemScalarFieldEnum = {
  id: 'id',
  quoteId: 'quoteId',
  productId: 'productId',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  discount: 'discount',
  total: 'total',
  isCustomizable: 'isCustomizable',
  customConfiguration: 'customConfiguration',
  assemblyFee: 'assemblyFee',
  serviceFee: 'serviceFee'
};

exports.Prisma.InvoiceScalarFieldEnum = {
  id: 'id',
  invoiceNumber: 'invoiceNumber',
  customerId: 'customerId',
  userId: 'userId',
  branchId: 'branchId',
  salesOrderId: 'salesOrderId',
  invoiceDate: 'invoiceDate',
  dueDate: 'dueDate',
  status: 'status',
  subtotal: 'subtotal',
  taxAmount: 'taxAmount',
  discount: 'discount',
  total: 'total',
  paidAmount: 'paidAmount',
  remainingAmount: 'remainingAmount',
  paymentStatus: 'paymentStatus',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InvoiceItemScalarFieldEnum = {
  id: 'id',
  invoiceId: 'invoiceId',
  productId: 'productId',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  discount: 'discount',
  total: 'total',
  isCustomizable: 'isCustomizable',
  customConfiguration: 'customConfiguration',
  assemblyFee: 'assemblyFee',
  serviceFee: 'serviceFee'
};

exports.Prisma.InvoicePaymentScalarFieldEnum = {
  id: 'id',
  invoiceId: 'invoiceId',
  method: 'method',
  amount: 'amount',
  reference: 'reference',
  installmentPlan: 'installmentPlan',
  status: 'status',
  paidAt: 'paidAt',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  MANAGER: 'MANAGER',
  SALES: 'SALES',
  INVENTORY: 'INVENTORY',
  ACCOUNTANT: 'ACCOUNTANT',
  USER: 'USER'
};

exports.TransactionType = exports.$Enums.TransactionType = {
  INCOME: 'INCOME',
  EXPENSE: 'EXPENSE',
  TRANSFER_IN: 'TRANSFER_IN',
  TRANSFER_OUT: 'TRANSFER_OUT'
};

exports.TransferStatus = exports.$Enums.TransferStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.PaymentMethod = exports.$Enums.PaymentMethod = {
  CASH: 'CASH',
  INSTAPAY: 'INSTAPAY',
  VODAFONE_CASH: 'VODAFONE_CASH',
  VISA: 'VISA',
  INSTALLMENTS: 'INSTALLMENTS'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  REFUNDED: 'REFUNDED'
};

exports.ProductType = exports.$Enums.ProductType = {
  CUSTOMIZABLE: 'CUSTOMIZABLE',
  COMPONENT: 'COMPONENT',
  SERVICE: 'SERVICE'
};

exports.CustomerType = exports.$Enums.CustomerType = {
  CUSTOMER: 'CUSTOMER',
  SUPPLIER: 'SUPPLIER',
  BOTH: 'BOTH'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  DRAFT: 'DRAFT',
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  RETURNED: 'RETURNED',
  CONVERTED_TO_INVOICE: 'CONVERTED_TO_INVOICE'
};

exports.MaintenanceStatus = exports.$Enums.MaintenanceStatus = {
  RECEIVED: 'RECEIVED',
  IN_PROGRESS: 'IN_PROGRESS',
  WAITING_PARTS: 'WAITING_PARTS',
  COMPLETED: 'COMPLETED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED'
};

exports.QuoteStatus = exports.$Enums.QuoteStatus = {
  DRAFT: 'DRAFT',
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  EXPIRED: 'EXPIRED',
  CONVERTED_TO_ORDER: 'CONVERTED_TO_ORDER'
};

exports.InvoiceStatus = exports.$Enums.InvoiceStatus = {
  DRAFT: 'DRAFT',
  PENDING: 'PENDING',
  PAID: 'PAID',
  PARTIALLY_PAID: 'PARTIALLY_PAID',
  OVERDUE: 'OVERDUE',
  CANCELLED: 'CANCELLED'
};

exports.Prisma.ModelName = {
  User: 'User',
  Company: 'Company',
  Branch: 'Branch',
  CashBox: 'CashBox',
  CashTransaction: 'CashTransaction',
  TreasuryTransfer: 'TreasuryTransfer',
  BranchInventory: 'BranchInventory',
  BranchTransfer: 'BranchTransfer',
  BranchTransferItem: 'BranchTransferItem',
  Payment: 'Payment',
  Category: 'Category',
  Product: 'Product',
  Customer: 'Customer',
  SalesOrder: 'SalesOrder',
  SalesOrderItem: 'SalesOrderItem',
  SalesOrderCustomComponent: 'SalesOrderCustomComponent',
  PurchaseOrder: 'PurchaseOrder',
  PurchaseOrderItem: 'PurchaseOrderItem',
  InventoryAdjustment: 'InventoryAdjustment',
  MaintenanceOrder: 'MaintenanceOrder',
  CustomProductConfiguration: 'CustomProductConfiguration',
  Quote: 'Quote',
  QuoteItem: 'QuoteItem',
  Invoice: 'Invoice',
  InvoiceItem: 'InvoiceItem',
  InvoicePayment: 'InvoicePayment'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
