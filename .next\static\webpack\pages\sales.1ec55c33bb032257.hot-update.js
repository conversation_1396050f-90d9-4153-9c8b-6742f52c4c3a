"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "./components/sales/QuoteModal.js":
/*!****************************************!*\
  !*** ./components/sales/QuoteModal.js ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ QuoteModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CogIcon,PlusIcon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=CogIcon,PlusIcon,TrashIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _ProductCustomizerAdvanced__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ProductCustomizerAdvanced */ \"./components/sales/ProductCustomizerAdvanced.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction QuoteModal(param) {\n    let { isOpen, onClose, onSave, quote = null } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customers, setCustomers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        customerId: \"\",\n        validUntil: \"\",\n        notes: \"\",\n        items: []\n    });\n    // Customization states\n    const [showCustomizer, setShowCustomizer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load customers and products\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            loadCustomers();\n            loadProducts();\n            if (quote) {\n                setFormData({\n                    customerId: quote.customerId || \"\",\n                    validUntil: quote.validUntil ? quote.validUntil.split(\"T\")[0] : \"\",\n                    notes: quote.notes || \"\",\n                    items: quote.items || []\n                });\n            } else {\n                // Set default valid until date (7 days from now)\n                const validUntil = new Date();\n                validUntil.setDate(validUntil.getDate() + 7);\n                setFormData((prev)=>({\n                        ...prev,\n                        validUntil: validUntil.toISOString().split(\"T\")[0]\n                    }));\n            }\n        }\n    }, [\n        isOpen,\n        quote\n    ]);\n    const loadCustomers = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/customers\"));\n            setCustomers(response.data.customers || []);\n        } catch (error) {\n            console.error(\"Error loading customers:\", error);\n        }\n    };\n    const loadProducts = async ()=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/products\"));\n            setProducts(response.data.products || []);\n        } catch (error) {\n            console.error(\"Error loading products:\", error);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const addItem = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: [\n                    ...prev.items,\n                    {\n                        productId: \"\",\n                        productName: \"\",\n                        quantity: 1,\n                        unitPrice: 0,\n                        discount: 0,\n                        taxRate: 14,\n                        hasTax: true,\n                        total: 0,\n                        isCustomized: false,\n                        customizations: null,\n                        customizationDetails: []\n                    }\n                ]\n            }));\n    };\n    const removeItem = (index)=>{\n        setFormData((prev)=>({\n                ...prev,\n                items: prev.items.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateItem = (index, field, value)=>{\n        setFormData((prev)=>{\n            const newItems = [\n                ...prev.items\n            ];\n            newItems[index] = {\n                ...newItems[index],\n                [field]: value\n            };\n            if (field === \"productId\") {\n                const product = products.find((p)=>p.id === value);\n                if (product) {\n                    newItems[index].unitPrice = parseFloat(product.unitPrice);\n                    newItems[index].productName = product.nameAr || product.name;\n                    // Check if product is customizable\n                    if (product.isCustomizable) {\n                        setSelectedProduct(product);\n                        setCurrentItemIndex(index);\n                        setShowCustomizer(true);\n                        return prev; // Don't update yet, wait for customization\n                    }\n                }\n            }\n            // Recalculate totals for any change\n            if ([\n                \"quantity\",\n                \"unitPrice\",\n                \"discount\",\n                \"taxRate\",\n                \"hasTax\"\n            ].includes(field)) {\n                const item = newItems[index];\n                const quantity = parseFloat(item.quantity) || 0;\n                const unitPrice = parseFloat(item.unitPrice) || 0;\n                const discountPercent = parseFloat(item.discount) || 0;\n                const taxRate = parseFloat(item.taxRate) || 0;\n                // Calculate subtotal\n                const subtotal = quantity * unitPrice;\n                // Apply discount\n                const discountAmount = subtotal * (discountPercent / 100);\n                const afterDiscount = subtotal - discountAmount;\n                // Apply tax if enabled\n                const taxAmount = item.hasTax ? afterDiscount * (taxRate / 100) : 0;\n                const total = afterDiscount + taxAmount;\n                newItems[index].total = total;\n                newItems[index].subtotal = subtotal;\n                newItems[index].discountAmount = discountAmount;\n                newItems[index].taxAmount = taxAmount;\n            }\n            return {\n                ...prev,\n                items: newItems\n            };\n        });\n    };\n    // Handle customization save\n    const handleCustomizationSave = (customizationData)=>{\n        if (currentItemIndex !== null) {\n            setFormData((prev)=>{\n                const newItems = [\n                    ...prev.items\n                ];\n                newItems[currentItemIndex] = {\n                    ...newItems[currentItemIndex],\n                    unitPrice: customizationData.totalPrice,\n                    isCustomized: true,\n                    customizations: customizationData.customizations,\n                    customizationDetails: customizationData.customizationDetails\n                };\n                // Recalculate total\n                const item = newItems[currentItemIndex];\n                const quantity = parseFloat(item.quantity) || 0;\n                const unitPrice = parseFloat(item.unitPrice) || 0;\n                const discountPercent = parseFloat(item.discount) || 0;\n                const taxRate = parseFloat(item.taxRate) || 0;\n                const subtotal = quantity * unitPrice;\n                const discountAmount = subtotal * (discountPercent / 100);\n                const afterDiscount = subtotal - discountAmount;\n                const taxAmount = item.hasTax ? afterDiscount * (taxRate / 100) : 0;\n                const total = afterDiscount + taxAmount;\n                newItems[currentItemIndex].total = total;\n                newItems[currentItemIndex].subtotal = subtotal;\n                newItems[currentItemIndex].discountAmount = discountAmount;\n                newItems[currentItemIndex].taxAmount = taxAmount;\n                return {\n                    ...prev,\n                    items: newItems\n                };\n            });\n        }\n        setShowCustomizer(false);\n        setSelectedProduct(null);\n        setCurrentItemIndex(null);\n    };\n    const calculateTotals = ()=>{\n        const subtotal = formData.items.reduce((sum, item)=>sum + (parseFloat(item.total) || 0), 0);\n        const taxAmount = subtotal * 0.14; // 14% tax\n        const total = subtotal + taxAmount;\n        return {\n            subtotal,\n            taxAmount,\n            total\n        };\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.customerId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى اختيار العميل\");\n            return;\n        }\n        if (formData.items.length === 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(\"يرجى إضافة عنصر واحد على الأقل\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const { subtotal, taxAmount, total } = calculateTotals();\n            const quoteData = {\n                ...formData,\n                subtotal,\n                taxAmount,\n                total,\n                status: \"DRAFT\"\n            };\n            const response = quote ? await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].put(\"\".concat(\"http://localhost:3070\", \"/api/quotes/\").concat(quote.id), quoteData) : await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"\".concat(\"http://localhost:3070\", \"/api/quotes\"), quoteData);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.data.message || (quote ? \"تم تحديث عرض السعر\" : \"تم إنشاء عرض السعر\"));\n            onSave(response.data.quote);\n            onClose();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"حدث خطأ\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    const { subtotal, taxAmount, total } = calculateTotals();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: quote ? \"تعديل عرض السعر\" : \"عرض سعر جديد\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.XMarkIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                lineNumber: 256,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"العميل *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            name: \"customerId\",\n                                            value: formData.customerId,\n                                            onChange: handleChange,\n                                            className: \"form-input\",\n                                            required: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"اختر العميل\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customers.map((customer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: customer.id,\n                                                        children: customer.name\n                                                    }, customer.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"form-label\",\n                                            children: \"صالح حتى *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            name: \"validUntil\",\n                                            value: formData.validUntil,\n                                            onChange: handleChange,\n                                            className: \"form-input\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"العناصر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addItem,\n                                            className: \"btn-primary flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.PlusIcon, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"إضافة عنصر\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: formData.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-12 gap-4 items-end p-4 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"المنتج\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: item.productId,\n                                                            onChange: (e)=>updateItem(index, \"productId\", e.target.value),\n                                                            className: \"form-input\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"اختر المنتج\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: product.id,\n                                                                        children: product.name\n                                                                    }, product.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"الكمية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.quantity,\n                                                            onChange: (e)=>updateItem(index, \"quantity\", e.target.value),\n                                                            className: \"form-input\",\n                                                            min: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"السعر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.unitPrice,\n                                                            onChange: (e)=>updateItem(index, \"unitPrice\", e.target.value),\n                                                            className: \"form-input\",\n                                                            step: \"0.01\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"خصم %\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: item.discount,\n                                                            onChange: (e)=>updateItem(index, \"discount\", e.target.value),\n                                                            className: \"form-input\",\n                                                            min: \"0\",\n                                                            max: \"100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"form-label\",\n                                                            children: \"الإجمالي\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-gray-900 py-2\",\n                                                            children: [\n                                                                \"$\",\n                                                                (item.total || 0).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-span-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>removeItem(index),\n                                                        className: \"text-red-600 hover:text-red-800\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CogIcon_PlusIcon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.TrashIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"المجموع الفرعي:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    subtotal.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"الضريبة (14%):\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    taxAmount.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between font-bold text-lg border-t pt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"الإجمالي:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"$\",\n                                                    total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                                lineNumber: 395,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                lineNumber: 384,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"form-label\",\n                                    children: \"ملاحظات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    name: \"notes\",\n                                    value: formData.notes,\n                                    onChange: handleChange,\n                                    className: \"form-input\",\n                                    rows: \"3\",\n                                    placeholder: \"ملاحظات إضافية...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 403,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: \"btn-secondary\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"btn-primary\",\n                                    children: loading ? \"جاري الحفظ...\" : quote ? \"تحديث\" : \"إنشاء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n            lineNumber: 247,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\sales\\\\QuoteModal.js\",\n        lineNumber: 246,\n        columnNumber: 5\n    }, this);\n}\n_s(QuoteModal, \"rwn8c/8tmO85V7gLP0UzTJ2M44c=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = QuoteModal;\nvar _c;\n$RefreshReg$(_c, \"QuoteModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/sales/QuoteModal.js\n"));

/***/ })

});