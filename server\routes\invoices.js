const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');

const DATA_DIR = path.join(__dirname, '../data');
const INVOICES_FILE = path.join(DATA_DIR, 'invoices.json');

// Helper functions
const readInvoices = async () => {
  try {
    const data = await fs.readFile(INVOICES_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    return [];
  }
};

const writeInvoices = async (invoices) => {
  await fs.writeFile(INVOICES_FILE, JSON.stringify(invoices, null, 2));
};

// GET /api/invoices - Get all invoices
router.get('/', async (req, res) => {
  try {
    const invoices = await readInvoices();
    res.json({ invoices });
  } catch (error) {
    console.error('Error fetching invoices:', error);
    res.status(500).json({ error: 'خطأ في جلب الفواتير' });
  }
});

// GET /api/invoices/search - Search invoices
router.get('/search', async (req, res) => {
  try {
    const { q } = req.query;
    if (!q || q.length < 3) {
      return res.json({ invoices: [] });
    }
    
    const invoices = await readInvoices();
    const searchTerm = q.toLowerCase();
    
    const filteredInvoices = invoices.filter(invoice => 
      invoice.invoiceNumber.toLowerCase().includes(searchTerm) ||
      invoice.customerName.toLowerCase().includes(searchTerm) ||
      (invoice.customerId && invoice.customerId.toLowerCase().includes(searchTerm))
    );
    
    res.json({ invoices: filteredInvoices });
  } catch (error) {
    console.error('Error searching invoices:', error);
    res.status(500).json({ error: 'خطأ في البحث عن الفواتير' });
  }
});

// GET /api/invoices/:id - Get specific invoice
router.get('/:id', async (req, res) => {
  try {
    const invoices = await readInvoices();
    const invoice = invoices.find(inv => inv.id === req.params.id);
    
    if (!invoice) {
      return res.status(404).json({ error: 'الفاتورة غير موجودة' });
    }
    
    res.json({ invoice });
  } catch (error) {
    console.error('Error fetching invoice:', error);
    res.status(500).json({ error: 'خطأ في جلب الفاتورة' });
  }
});

// POST /api/invoices - Create new invoice
router.post('/', async (req, res) => {
  try {
    const invoices = await readInvoices();
    
    // Generate invoice number
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    const existingInvoices = invoices.filter(inv => 
      inv.invoiceNumber && inv.invoiceNumber.startsWith(`INV-${year}${month}`)
    );
    const nextNumber = existingInvoices.length + 1;
    const invoiceNumber = `INV-${year}${month}-${String(nextNumber).padStart(4, '0')}`;
    
    const newInvoice = {
      id: Date.now().toString(),
      invoiceNumber,
      customerId: req.body.customerId,
      customerName: req.body.customerName,
      dueDate: req.body.dueDate,
      notes: req.body.notes || '',
      items: req.body.items || [],
      payments: req.body.payments || [],
      generalDiscount: parseFloat(req.body.generalDiscount) || 0,
      generalDiscountType: req.body.generalDiscountType || 'PERCENTAGE',
      itemsSubtotal: parseFloat(req.body.itemsSubtotal) || 0,
      itemsDiscountAmount: parseFloat(req.body.itemsDiscountAmount) || 0,
      itemsTaxAmount: parseFloat(req.body.itemsTaxAmount) || 0,
      itemsTotal: parseFloat(req.body.itemsTotal) || 0,
      generalDiscountAmount: parseFloat(req.body.generalDiscountAmount) || 0,
      finalTotal: parseFloat(req.body.finalTotal) || 0,
      paidAmount: parseFloat(req.body.paidAmount) || 0,
      remainingAmount: parseFloat(req.body.remainingAmount) || 0,
      status: req.body.status || 'PENDING',
      salesOrderId: req.body.salesOrderId || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    invoices.push(newInvoice);
    await writeInvoices(invoices);
    
    res.status(201).json({ 
      message: 'تم إنشاء الفاتورة بنجاح',
      invoice: newInvoice 
    });
  } catch (error) {
    console.error('Error creating invoice:', error);
    res.status(500).json({ error: 'خطأ في إنشاء الفاتورة' });
  }
});

// PUT /api/invoices/:id - Update invoice
router.put('/:id', async (req, res) => {
  try {
    const invoices = await readInvoices();
    const index = invoices.findIndex(inv => inv.id === req.params.id);
    
    if (index === -1) {
      return res.status(404).json({ error: 'الفاتورة غير موجودة' });
    }
    
    const updatedInvoice = {
      ...invoices[index],
      ...req.body,
      updatedAt: new Date().toISOString()
    };
    
    invoices[index] = updatedInvoice;
    await writeInvoices(invoices);
    
    res.json({ 
      message: 'تم تحديث الفاتورة بنجاح',
      invoice: updatedInvoice 
    });
  } catch (error) {
    console.error('Error updating invoice:', error);
    res.status(500).json({ error: 'خطأ في تحديث الفاتورة' });
  }
});

// DELETE /api/invoices/:id - Delete invoice
router.delete('/:id', async (req, res) => {
  try {
    const invoices = await readInvoices();
    const index = invoices.findIndex(inv => inv.id === req.params.id);
    
    if (index === -1) {
      return res.status(404).json({ error: 'الفاتورة غير موجودة' });
    }
    
    invoices.splice(index, 1);
    await writeInvoices(invoices);
    
    res.json({ message: 'تم حذف الفاتورة بنجاح' });
  } catch (error) {
    console.error('Error deleting invoice:', error);
    res.status(500).json({ error: 'خطأ في حذف الفاتورة' });
  }
});

module.exports = router;
