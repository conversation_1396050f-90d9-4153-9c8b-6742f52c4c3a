"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: Inter, ui-sans-serif, system-ui, sans-serif; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n\\n[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  border-radius: 0px;\\n  padding-top: 0.5rem;\\n  padding-right: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  padding-left: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  border-color: #2563eb;\\n}\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\ninput::placeholder,textarea::placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\n::-webkit-datetime-edit-fields-wrapper {\\n  padding: 0;\\n}\\n\\n::-webkit-date-and-time-value {\\n  min-height: 1.5em;\\n  text-align: inherit;\\n}\\n\\n::-webkit-datetime-edit {\\n  display: inline-flex;\\n}\\n\\n::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n}\\n\\nselect {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\\\");\\n  background-position: right 0.5rem center;\\n  background-repeat: no-repeat;\\n  background-size: 1.5em 1.5em;\\n  padding-right: 2.5rem;\\n  -webkit-print-color-adjust: exact;\\n          print-color-adjust: exact;\\n}\\n\\n[multiple],[size]:where(select:not([size=\\\"1\\\"])) {\\n  background-image: initial;\\n  background-position: initial;\\n  background-repeat: unset;\\n  background-size: initial;\\n  padding-right: 0.75rem;\\n  -webkit-print-color-adjust: unset;\\n          print-color-adjust: unset;\\n}\\n\\n[type='checkbox'],[type='radio'] {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  padding: 0;\\n  -webkit-print-color-adjust: exact;\\n          print-color-adjust: exact;\\n  display: inline-block;\\n  vertical-align: middle;\\n  background-origin: border-box;\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n  flex-shrink: 0;\\n  height: 1rem;\\n  width: 1rem;\\n  color: #2563eb;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='checkbox'] {\\n  border-radius: 0px;\\n}\\n\\n[type='radio'] {\\n  border-radius: 100%;\\n}\\n\\n[type='checkbox']:focus,[type='radio']:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n}\\n\\n[type='checkbox']:checked,[type='radio']:checked {\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n[type='checkbox']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:checked {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='radio']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='radio']:checked {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='checkbox']:indeterminate {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e\\\");\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:indeterminate {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='file'] {\\n  background: unset;\\n  border-color: inherit;\\n  border-width: 0;\\n  border-radius: 0;\\n  padding: 0;\\n  font-size: unset;\\n  line-height: inherit;\\n}\\n\\n[type='file']:focus {\\n  outline: 1px solid ButtonText;\\n  outline: 1px auto -webkit-focus-ring-color;\\n}\\n.form-input,.form-textarea,.form-select,.form-multiselect {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  border-radius: 0px;\\n  padding-top: 0.5rem;\\n  padding-right: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  padding-left: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n  --tw-shadow: 0 0 #0000;\\n}\\n.form-input:focus, .form-textarea:focus, .form-select:focus, .form-multiselect:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  border-color: #2563eb;\\n}\\n.form-input::-moz-placeholder, .form-textarea::-moz-placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n.form-input::placeholder,.form-textarea::placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n.form-input::-webkit-datetime-edit-fields-wrapper {\\n  padding: 0;\\n}\\n.form-input::-webkit-date-and-time-value {\\n  min-height: 1.5em;\\n  text-align: inherit;\\n}\\n.form-input::-webkit-datetime-edit {\\n  display: inline-flex;\\n}\\n.form-input::-webkit-datetime-edit,.form-input::-webkit-datetime-edit-year-field,.form-input::-webkit-datetime-edit-month-field,.form-input::-webkit-datetime-edit-day-field,.form-input::-webkit-datetime-edit-hour-field,.form-input::-webkit-datetime-edit-minute-field,.form-input::-webkit-datetime-edit-second-field,.form-input::-webkit-datetime-edit-millisecond-field,.form-input::-webkit-datetime-edit-meridiem-field {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n}\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n.fixed {\\n  position: fixed;\\n}\\n.absolute {\\n  position: absolute;\\n}\\n.relative {\\n  position: relative;\\n}\\n.inset-0 {\\n  inset: 0px;\\n}\\n.inset-y-0 {\\n  top: 0px;\\n  bottom: 0px;\\n}\\n.-right-1 {\\n  right: -0.25rem;\\n}\\n.-top-1 {\\n  top: -0.25rem;\\n}\\n.left-0 {\\n  left: 0px;\\n}\\n.left-2 {\\n  left: 0.5rem;\\n}\\n.left-3 {\\n  left: 0.75rem;\\n}\\n.right-0 {\\n  right: 0px;\\n}\\n.top-1\\\\/2 {\\n  top: 50%;\\n}\\n.z-40 {\\n  z-index: 40;\\n}\\n.z-50 {\\n  z-index: 50;\\n}\\n.col-span-1 {\\n  grid-column: span 1 / span 1;\\n}\\n.col-span-2 {\\n  grid-column: span 2 / span 2;\\n}\\n.col-span-4 {\\n  grid-column: span 4 / span 4;\\n}\\n.mx-4 {\\n  margin-left: 1rem;\\n  margin-right: 1rem;\\n}\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.-mb-px {\\n  margin-bottom: -1px;\\n}\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\n.ml-1 {\\n  margin-left: 0.25rem;\\n}\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\n.ml-3 {\\n  margin-left: 0.75rem;\\n}\\n.ml-4 {\\n  margin-left: 1rem;\\n}\\n.ml-5 {\\n  margin-left: 1.25rem;\\n}\\n.ml-auto {\\n  margin-left: auto;\\n}\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\n.mr-3 {\\n  margin-right: 0.75rem;\\n}\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\n.mt-3 {\\n  margin-top: 0.75rem;\\n}\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\n.block {\\n  display: block;\\n}\\n.inline-block {\\n  display: inline-block;\\n}\\n.inline {\\n  display: inline;\\n}\\n.flex {\\n  display: flex;\\n}\\n.inline-flex {\\n  display: inline-flex;\\n}\\n.table {\\n  display: table;\\n}\\n.grid {\\n  display: grid;\\n}\\n.hidden {\\n  display: none;\\n}\\n.h-12 {\\n  height: 3rem;\\n}\\n.h-2 {\\n  height: 0.5rem;\\n}\\n.h-20 {\\n  height: 5rem;\\n}\\n.h-32 {\\n  height: 8rem;\\n}\\n.h-4 {\\n  height: 1rem;\\n}\\n.h-5 {\\n  height: 1.25rem;\\n}\\n.h-6 {\\n  height: 1.5rem;\\n}\\n.h-64 {\\n  height: 16rem;\\n}\\n.h-8 {\\n  height: 2rem;\\n}\\n.h-full {\\n  height: 100%;\\n}\\n.max-h-96 {\\n  max-height: 24rem;\\n}\\n.max-h-\\\\[90vh\\\\] {\\n  max-height: 90vh;\\n}\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\n.w-0 {\\n  width: 0px;\\n}\\n.w-11 {\\n  width: 2.75rem;\\n}\\n.w-12 {\\n  width: 3rem;\\n}\\n.w-2 {\\n  width: 0.5rem;\\n}\\n.w-20 {\\n  width: 5rem;\\n}\\n.w-32 {\\n  width: 8rem;\\n}\\n.w-4 {\\n  width: 1rem;\\n}\\n.w-48 {\\n  width: 12rem;\\n}\\n.w-5 {\\n  width: 1.25rem;\\n}\\n.w-6 {\\n  width: 1.5rem;\\n}\\n.w-64 {\\n  width: 16rem;\\n}\\n.w-8 {\\n  width: 2rem;\\n}\\n.w-80 {\\n  width: 20rem;\\n}\\n.w-full {\\n  width: 100%;\\n}\\n.min-w-0 {\\n  min-width: 0px;\\n}\\n.min-w-full {\\n  min-width: 100%;\\n}\\n.max-w-2xl {\\n  max-width: 42rem;\\n}\\n.max-w-4xl {\\n  max-width: 56rem;\\n}\\n.max-w-5xl {\\n  max-width: 64rem;\\n}\\n.max-w-md {\\n  max-width: 28rem;\\n}\\n.max-w-xs {\\n  max-width: 20rem;\\n}\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\n.border-collapse {\\n  border-collapse: collapse;\\n}\\n.-translate-x-full {\\n  --tw-translate-x: -100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.-translate-y-1\\\\/2 {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-0 {\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\n.list-inside {\\n  list-style-position: inside;\\n}\\n.list-disc {\\n  list-style-type: disc;\\n}\\n.appearance-none {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n}\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-12 {\\n  grid-template-columns: repeat(12, minmax(0, 1fr));\\n}\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.flex-col {\\n  flex-direction: column;\\n}\\n.items-start {\\n  align-items: flex-start;\\n}\\n.items-end {\\n  align-items: flex-end;\\n}\\n.items-center {\\n  align-items: center;\\n}\\n.justify-end {\\n  justify-content: flex-end;\\n}\\n.justify-center {\\n  justify-content: center;\\n}\\n.justify-between {\\n  justify-content: space-between;\\n}\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\n.gap-4 {\\n  gap: 1rem;\\n}\\n.gap-5 {\\n  gap: 1.25rem;\\n}\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\n.gap-8 {\\n  gap: 2rem;\\n}\\n.gap-2 {\\n  gap: 0.5rem;\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\n.divide-y > :not([hidden]) ~ :not([hidden]) {\\n  --tw-divide-y-reverse: 0;\\n  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\\n  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));\\n}\\n.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-divide-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));\\n}\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\n.overflow-x-auto {\\n  overflow-x: auto;\\n}\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\n.truncate {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.whitespace-nowrap {\\n  white-space: nowrap;\\n}\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\n.rounded-2xl {\\n  border-radius: 1rem;\\n}\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\n.rounded-md {\\n  border-radius: 0.375rem;\\n}\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\n.border {\\n  border-width: 1px;\\n}\\n.border-2 {\\n  border-width: 2px;\\n}\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\n.border-b-2 {\\n  border-bottom-width: 2px;\\n}\\n.border-t {\\n  border-top-width: 1px;\\n}\\n.border-t-2 {\\n  border-top-width: 2px;\\n}\\n.border-blue-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-100 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.border-neutral-100 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));\\n}\\n.border-neutral-150 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(240 240 240 / var(--tw-border-opacity, 1));\\n}\\n.border-orange-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));\\n}\\n.border-primary-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(125 211 252 / var(--tw-border-opacity, 1));\\n}\\n.border-primary-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\\n}\\n.border-primary-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(2 132 199 / var(--tw-border-opacity, 1));\\n}\\n.border-red-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n}\\n.border-red-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));\\n}\\n.border-red-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\\n}\\n.border-transparent {\\n  border-color: transparent;\\n}\\n.border-t-primary-600 {\\n  --tw-border-opacity: 1;\\n  border-top-color: rgb(2 132 199 / var(--tw-border-opacity, 1));\\n}\\n.bg-black {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-300 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));\\n}\\n.bg-neutral-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));\\n}\\n.bg-pink-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\n.bg-status-error {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\n.bg-surface-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\\n}\\n.bg-opacity-50 {\\n  --tw-bg-opacity: 0.5;\\n}\\n.bg-opacity-75 {\\n  --tw-bg-opacity: 0.75;\\n}\\n.p-1 {\\n  padding: 0.25rem;\\n}\\n.p-1\\\\.5 {\\n  padding: 0.375rem;\\n}\\n.p-12 {\\n  padding: 3rem;\\n}\\n.p-2 {\\n  padding: 0.5rem;\\n}\\n.p-2\\\\.5 {\\n  padding: 0.625rem;\\n}\\n.p-3 {\\n  padding: 0.75rem;\\n}\\n.p-4 {\\n  padding: 1rem;\\n}\\n.p-5 {\\n  padding: 1.25rem;\\n}\\n.p-6 {\\n  padding: 1.5rem;\\n}\\n.p-8 {\\n  padding: 2rem;\\n}\\n.px-1 {\\n  padding-left: 0.25rem;\\n  padding-right: 0.25rem;\\n}\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-2\\\\.5 {\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n}\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\n.py-0\\\\.5 {\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n}\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-12 {\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\n.py-5 {\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\n.pb-1 {\\n  padding-bottom: 0.25rem;\\n}\\n.pb-2 {\\n  padding-bottom: 0.5rem;\\n}\\n.pb-20 {\\n  padding-bottom: 5rem;\\n}\\n.pb-4 {\\n  padding-bottom: 1rem;\\n}\\n.pb-6 {\\n  padding-bottom: 1.5rem;\\n}\\n.pl-10 {\\n  padding-left: 2.5rem;\\n}\\n.pr-10 {\\n  padding-right: 2.5rem;\\n}\\n.pr-3 {\\n  padding-right: 0.75rem;\\n}\\n.pt-2 {\\n  padding-top: 0.5rem;\\n}\\n.pt-4 {\\n  padding-top: 1rem;\\n}\\n.pt-5 {\\n  padding-top: 1.25rem;\\n}\\n.pt-6 {\\n  padding-top: 1.5rem;\\n}\\n.text-left {\\n  text-align: left;\\n}\\n.text-center {\\n  text-align: center;\\n}\\n.text-right {\\n  text-align: right;\\n}\\n.align-bottom {\\n  vertical-align: bottom;\\n}\\n.font-arabic {\\n  font-family: Cairo, ui-sans-serif, system-ui, sans-serif;\\n}\\n.font-english {\\n  font-family: Inter, ui-sans-serif, system-ui, sans-serif;\\n}\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold {\\n  font-weight: 700;\\n}\\n.font-extrabold {\\n  font-weight: 800;\\n}\\n.font-medium {\\n  font-weight: 500;\\n}\\n.font-semibold {\\n  font-weight: 600;\\n}\\n.uppercase {\\n  text-transform: uppercase;\\n}\\n.capitalize {\\n  text-transform: capitalize;\\n}\\n.leading-6 {\\n  line-height: 1.5rem;\\n}\\n.tracking-wider {\\n  letter-spacing: 0.05em;\\n}\\n.text-blue-100 {\\n  --tw-text-opacity: 1;\\n  color: rgb(219 234 254 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-green-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n.text-green-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\\n}\\n.text-green-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n}\\n.text-green-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(20 83 45 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 48 163 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(194 65 12 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(124 45 18 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(3 105 161 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(12 74 110 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-red-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\\n}\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n.text-red-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\\n}\\n.text-red-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n.text-text-primary {\\n  --tw-text-opacity: 1;\\n  color: rgb(23 23 23 / var(--tw-text-opacity, 1));\\n}\\n.text-text-secondary {\\n  --tw-text-opacity: 1;\\n  color: rgb(82 82 82 / var(--tw-text-opacity, 1));\\n}\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n}\\n.placeholder-gray-500::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.placeholder-gray-500::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\n.shadow {\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-large {\\n  --tw-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.12);\\n  --tw-shadow-colored: 0 8px 24px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-soft {\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-xl {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.ring-1 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.ring-black {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));\\n}\\n.ring-opacity-5 {\\n  --tw-ring-opacity: 0.05;\\n}\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity {\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform {\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\n\\n/* Import fonts */\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\\n@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');\\n\\n/* Modern Minimalist Base Styles */\\nhtml {\\n  scroll-behavior: smooth;\\n}\\n\\nbody {\\n  font-family: 'Inter', 'ui-sans-serif', 'system-ui', sans-serif;\\n  line-height: 1.6;\\n  background-color: #fafafa; /* neutral-50 */\\n  color: #171717; /* text-primary */\\n  font-weight: 400;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n/* Arabic font */\\n.font-arabic {\\n  font-family: 'Cairo', sans-serif;\\n}\\n\\n.font-english {\\n  font-family: 'Inter', sans-serif;\\n}\\n\\n/* RTL support */\\n[dir=\\\"rtl\\\"] {\\n  text-align: right;\\n}\\n\\n[dir=\\\"rtl\\\"] .rtl\\\\:text-left {\\n  text-align: left;\\n}\\n\\n[dir=\\\"rtl\\\"] .rtl\\\\:text-right {\\n  text-align: right;\\n}\\n\\n/* Custom scrollbar */\\n::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n/* Modern Form Styles */\\n.form-input {\\n  display: block;\\n  width: 100%;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.form-input::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(115 115 115 / var(--tw-placeholder-opacity, 1));\\n}\\n.form-input::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(115 115 115 / var(--tw-placeholder-opacity, 1));\\n}\\n.form-input {\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n}\\n.form-input:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(56 189 248 / var(--tw-border-opacity, 1));\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(56 189 248 / var(--tw-ring-opacity, 1));\\n}\\n@media (min-width: 640px) {\\n\\n  .form-input {\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n}\\n\\n.form-input:invalid {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\\n}\\n\\n.form-input:invalid:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));\\n}\\n\\n.form-label {\\n  margin-bottom: 0.5rem;\\n  display: block;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(23 23 23 / var(--tw-text-opacity, 1));\\n}\\n\\n.form-error {\\n  margin-top: 0.25rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\\n}\\n\\n/* Modern Button Styles */\\n.btn {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n.btn:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(14 165 233 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.btn-primary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-primary {\\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n.btn-primary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.btn-primary:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(56 189 248 / var(--tw-ring-opacity, 1));\\n}\\n\\n.btn-secondary {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(64 64 64 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.btn-secondary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-secondary {\\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n.btn-secondary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.btn-secondary:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(56 189 248 / var(--tw-ring-opacity, 1));\\n}\\n\\n.btn-danger {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.btn-danger:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-danger {\\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n.btn-danger:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.btn-danger:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));\\n}\\n\\n.btn-success {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.btn-success:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-success {\\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n.btn-success:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.btn-success:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(74 222 128 / var(--tw-ring-opacity, 1));\\n}\\n\\n.btn-sm {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n\\n.btn-lg {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n\\n/* Modern Card Styles */\\n.card {\\n  border-radius: 1rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n.card-header {\\n  border-bottom-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\n\\n.card-body {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\n\\n.card-footer {\\n  border-bottom-right-radius: 1rem;\\n  border-bottom-left-radius: 1rem;\\n  border-top-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\n\\n/* Modern Table Styles */\\n.table {\\n  min-width: 100%;\\n}\\n.table > :not([hidden]) ~ :not([hidden]) {\\n  --tw-divide-y-reverse: 0;\\n  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\\n  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));\\n  --tw-divide-opacity: 1;\\n  border-color: rgb(240 240 240 / var(--tw-divide-opacity, 1));\\n}\\n\\n.table thead {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\\n}\\n\\n.table th {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n  text-align: left;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n  --tw-text-opacity: 1;\\n  color: rgb(82 82 82 / var(--tw-text-opacity, 1));\\n}\\n\\n.table td {\\n  white-space: nowrap;\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(23 23 23 / var(--tw-text-opacity, 1));\\n}\\n\\n.table tbody tr:nth-child(even) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\\n}\\n\\n.table tbody tr:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(248 249 250 / var(--tw-bg-opacity, 1));\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n\\n/* Modern Badge Styles */\\n.badge {\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(3 105 161 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-secondary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(64 64 64 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-success {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-warning {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(161 98 7 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-danger {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-info {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(3 105 161 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n/* Alert styles */\\n.alert {\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n.alert-success {\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n.alert-warning {\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n.alert-danger {\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n.alert-info {\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n/* Animation utilities */\\n.fade-in {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n.slide-up {\\n  animation: slideUp 0.3s ease-out;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideUp {\\n  from {\\n    transform: translateY(10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n  \\n  .print-break {\\n    page-break-before: always;\\n  }\\n  \\n  body {\\n    font-size: 12pt;\\n    line-height: 1.4;\\n  }\\n  \\n  .card {\\n    box-shadow: none;\\n    border: 1px solid #ddd;\\n  }\\n}\\n\\n/* Modern Layout Utilities */\\n.page-container {\\n  margin-left: auto;\\n  margin-right: auto;\\n  max-width: 80rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n@media (min-width: 640px) {\\n\\n  .page-container {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .page-container {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n}\\n\\n.section-spacing {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\n\\n@media (min-width: 1024px) {\\n\\n  .section-spacing {\\n    padding-top: 3rem;\\n    padding-bottom: 3rem;\\n  }\\n}\\n\\n.content-spacing > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n\\n@media (min-width: 1024px) {\\n\\n  .content-spacing > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n  }\\n}\\n\\n/* Modern Shadows */\\n.shadow-soft {\\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n.shadow-medium {\\n  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n}\\n\\n.shadow-large {\\n  box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.12);\\n}\\n\\n.shadow-card {\\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n/* Enhanced RTL Support */\\n[dir=\\\"rtl\\\"] .table th {\\n  text-align: right;\\n}\\n\\n[dir=\\\"rtl\\\"] .table td {\\n  text-align: right;\\n}\\n\\n/* Modern Focus States */\\n.focus-ring:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(56 189 248 / var(--tw-ring-opacity, 1));\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n/* Improved Scrollbar */\\n::-webkit-scrollbar {\\n  width: 8px;\\n  height: 8px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: #f5f5f5;\\n  border-radius: 4px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: #d4d4d4;\\n  border-radius: 4px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: #a3a3a3;\\n}\\n\\n/* Dark mode support (if needed) */\\n@media (prefers-color-scheme: dark) {\\n  .dark-mode {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(23 23 23 / var(--tw-bg-opacity, 1));\\n    --tw-text-opacity: 1;\\n    color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  }\\n\\n  .dark-mode .card {\\n    --tw-border-opacity: 1;\\n    border-color: rgb(64 64 64 / var(--tw-border-opacity, 1));\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(38 38 38 / var(--tw-bg-opacity, 1));\\n  }\\n\\n  .dark-mode .form-input {\\n    --tw-border-opacity: 1;\\n    border-color: rgb(82 82 82 / var(--tw-border-opacity, 1));\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(64 64 64 / var(--tw-bg-opacity, 1));\\n    --tw-text-opacity: 1;\\n    color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  }\\n}\\n.after\\\\:absolute::after {\\n  content: var(--tw-content);\\n  position: absolute;\\n}\\n.after\\\\:left-\\\\[2px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 2px;\\n}\\n.after\\\\:top-\\\\[2px\\\\]::after {\\n  content: var(--tw-content);\\n  top: 2px;\\n}\\n.after\\\\:h-5::after {\\n  content: var(--tw-content);\\n  height: 1.25rem;\\n}\\n.after\\\\:w-5::after {\\n  content: var(--tw-content);\\n  width: 1.25rem;\\n}\\n.after\\\\:rounded-full::after {\\n  content: var(--tw-content);\\n  border-radius: 9999px;\\n}\\n.after\\\\:border::after {\\n  content: var(--tw-content);\\n  border-width: 1px;\\n}\\n.after\\\\:border-gray-300::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.after\\\\:bg-white::after {\\n  content: var(--tw-content);\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.after\\\\:transition-all::after {\\n  content: var(--tw-content);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.after\\\\:content-\\\\[\\\\'\\\\'\\\\]::after {\\n  --tw-content: '';\\n  content: var(--tw-content);\\n}\\n.hover\\\\:border-gray-300:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.hover\\\\:bg-blue-200:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-gray-100:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-gray-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-green-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-neutral-75:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(248 249 250 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-primary-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(3 105 161 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:text-blue-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-blue-800:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-blue-900:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-gray-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-gray-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-gray-900:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-green-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-green-900:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(20 83 45 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-red-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-red-800:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-text-primary:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(23 23 23 / var(--tw-text-opacity, 1));\\n}\\n.focus\\\\:z-10:focus {\\n  z-index: 10;\\n}\\n.focus\\\\:border-primary-500:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\\n}\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.focus\\\\:ring-blue-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-primary-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\n.disabled\\\\:cursor-not-allowed:disabled {\\n  cursor: not-allowed;\\n}\\n.disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\n.group:hover .group-hover\\\\:text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.peer:checked ~ .peer-checked\\\\:bg-primary-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\\n}\\n.peer:checked ~ .peer-checked\\\\:after\\\\:translate-x-full::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.peer:checked ~ .peer-checked\\\\:after\\\\:border-white::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\n.peer:focus ~ .peer-focus\\\\:outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.peer:focus ~ .peer-focus\\\\:ring-4 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.peer:focus ~ .peer-focus\\\\:ring-primary-300 {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(125 211 252 / var(--tw-ring-opacity, 1));\\n}\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:my-8 {\\n    margin-top: 2rem;\\n    margin-bottom: 2rem;\\n  }\\n\\n  .sm\\\\:ml-3 {\\n    margin-left: 0.75rem;\\n  }\\n\\n  .sm\\\\:mt-0 {\\n    margin-top: 0px;\\n  }\\n\\n  .sm\\\\:block {\\n    display: block;\\n  }\\n\\n  .sm\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .sm\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .sm\\\\:w-full {\\n    width: 100%;\\n  }\\n\\n  .sm\\\\:max-w-2xl {\\n    max-width: 42rem;\\n  }\\n\\n  .sm\\\\:max-w-4xl {\\n    max-width: 56rem;\\n  }\\n\\n  .sm\\\\:max-w-6xl {\\n    max-width: 72rem;\\n  }\\n\\n  .sm\\\\:max-w-lg {\\n    max-width: 32rem;\\n  }\\n\\n  .sm\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:flex-row-reverse {\\n    flex-direction: row-reverse;\\n  }\\n\\n  .sm\\\\:p-0 {\\n    padding: 0px;\\n  }\\n\\n  .sm\\\\:p-6 {\\n    padding: 1.5rem;\\n  }\\n\\n  .sm\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n\\n  .sm\\\\:pb-4 {\\n    padding-bottom: 1rem;\\n  }\\n\\n  .sm\\\\:align-middle {\\n    vertical-align: middle;\\n  }\\n\\n  .sm\\\\:text-sm {\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n}\\n@media (min-width: 768px) {\\n\\n  .md\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .md\\\\:col-span-4 {\\n    grid-column: span 4 / span 4;\\n  }\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:ml-20 {\\n    margin-left: 5rem;\\n  }\\n\\n  .lg\\\\:ml-64 {\\n    margin-left: 16rem;\\n  }\\n\\n  .lg\\\\:mr-20 {\\n    margin-right: 5rem;\\n  }\\n\\n  .lg\\\\:mr-64 {\\n    margin-right: 16rem;\\n  }\\n\\n  .lg\\\\:block {\\n    display: block;\\n  }\\n\\n  .lg\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .lg\\\\:w-1\\\\/4 {\\n    width: 25%;\\n  }\\n\\n  .lg\\\\:w-3\\\\/4 {\\n    width: 75%;\\n  }\\n\\n  .lg\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .lg\\\\:px-8 {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n}\\n.rtl\\\\:left-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  left: 0px;\\n}\\n.rtl\\\\:left-auto:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  left: auto;\\n}\\n.rtl\\\\:right-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  right: 0px;\\n}\\n.rtl\\\\:right-3:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  right: 0.75rem;\\n}\\n.rtl\\\\:right-auto:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  right: auto;\\n}\\n.rtl\\\\:ml-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-left: 0px;\\n}\\n.rtl\\\\:ml-1:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-left: 0.25rem;\\n}\\n.rtl\\\\:ml-2:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-left: 0.5rem;\\n}\\n.rtl\\\\:ml-3:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-left: 0.75rem;\\n}\\n.rtl\\\\:mr-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 0px;\\n}\\n.rtl\\\\:mr-1:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 0.25rem;\\n}\\n.rtl\\\\:mr-2:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 0.5rem;\\n}\\n.rtl\\\\:mr-4:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 1rem;\\n}\\n.rtl\\\\:mr-5:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 1.25rem;\\n}\\n.rtl\\\\:-translate-x-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  --tw-translate-x: -0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.rtl\\\\:translate-x-full:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.rtl\\\\:space-x-reverse:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 1;\\n}\\n.rtl\\\\:pl-10:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-left: 2.5rem;\\n}\\n.rtl\\\\:pl-3:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-left: 0.75rem;\\n}\\n.rtl\\\\:pr-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-right: 0px;\\n}\\n.rtl\\\\:pr-10:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-right: 2.5rem;\\n}\\n.rtl\\\\:pr-3:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-right: 0.75rem;\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAA0B;EAA1B,wBAA0B;EAA1B,mBAA0B;EAA1B,mBAA0B;EAA1B,cAA0B;EAA1B,cAA0B;EAA1B,cAA0B;EAA1B,eAA0B;EAA1B,eAA0B;EAA1B,aAA0B;EAA1B,aAA0B;EAA1B,kBAA0B;EAA1B,sCAA0B;EAA1B,8BAA0B;EAA1B,6BAA0B;EAA1B,4BAA0B;EAA1B,eAA0B;EAA1B,oBAA0B;EAA1B,sBAA0B;EAA1B,uBAA0B;EAA1B,wBAA0B;EAA1B,kBAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;EAA1B,sCAA0B;EAA1B,kCAA0B;EAA1B,2BAA0B;EAA1B,sBAA0B;EAA1B,8BAA0B;EAA1B,YAA0B;EAA1B,kBAA0B;EAA1B,gBAA0B;EAA1B,iBAA0B;EAA1B,kBAA0B;EAA1B,cAA0B;EAA1B,gBAA0B;EAA1B,aAA0B;EAA1B,mBAA0B;EAA1B,qBAA0B;EAA1B,2BAA0B;EAA1B,yBAA0B;EAA1B,0BAA0B;EAA1B,2BAA0B;EAA1B,uBAA0B;EAA1B,wBAA0B;EAA1B,yBAA0B;EAA1B,sBAA0B;EAA1B,oBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B;AAA0B;;AAA1B;EAAA,wBAA0B;EAA1B,wBAA0B;EAA1B,mBAA0B;EAA1B,mBAA0B;EAA1B,cAA0B;EAA1B,cAA0B;EAA1B,cAA0B;EAA1B,eAA0B;EAA1B,eAA0B;EAA1B,aAA0B;EAA1B,aAA0B;EAA1B,kBAA0B;EAA1B,sCAA0B;EAA1B,8BAA0B;EAA1B,6BAA0B;EAA1B,4BAA0B;EAA1B,eAA0B;EAA1B,oBAA0B;EAA1B,sBAA0B;EAA1B,uBAA0B;EAA1B,wBAA0B;EAA1B,kBAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;EAA1B,sCAA0B;EAA1B,kCAA0B;EAA1B,2BAA0B;EAA1B,sBAA0B;EAA1B,8BAA0B;EAA1B,YAA0B;EAA1B,kBAA0B;EAA1B,gBAA0B;EAA1B,iBAA0B;EAA1B,kBAA0B;EAA1B,cAA0B;EAA1B,gBAA0B;EAA1B,aAA0B;EAA1B,mBAA0B;EAA1B,qBAA0B;EAA1B,2BAA0B;EAA1B,yBAA0B;EAA1B,0BAA0B;EAA1B,2BAA0B;EAA1B,uBAA0B;EAA1B,wBAA0B;EAA1B,yBAA0B;EAA1B,sBAA0B;EAA1B,oBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B;AAA0B,CAA1B;;CAA0B,CAA1B;;;CAA0B;;AAA1B;;;EAAA,sBAA0B,EAA1B,MAA0B;EAA1B,eAA0B,EAA1B,MAA0B;EAA1B,mBAA0B,EAA1B,MAA0B;EAA1B,qBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;EAAA,gBAA0B;AAAA;;AAA1B;;;;;;;;CAA0B;;AAA1B;;EAAA,gBAA0B,EAA1B,MAA0B;EAA1B,8BAA0B,EAA1B,MAA0B;EAA1B,gBAA0B,EAA1B,MAA0B;EAA1B,cAA0B;KAA1B,WAA0B,EAA1B,MAA0B;EAA1B,wDAA0B,EAA1B,MAA0B;EAA1B,6BAA0B,EAA1B,MAA0B;EAA1B,+BAA0B,EAA1B,MAA0B;EAA1B,wCAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;EAAA,SAA0B,EAA1B,MAA0B;EAA1B,oBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;;;CAA0B;;AAA1B;EAAA,SAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;EAA1B,qBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,yCAA0B;UAA1B,iCAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;;;;;EAAA,kBAA0B;EAA1B,oBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,cAA0B;EAA1B,wBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,mBAA0B;AAAA;;AAA1B;;;;;CAA0B;;AAA1B;;;;EAAA,+GAA0B,EAA1B,MAA0B;EAA1B,6BAA0B,EAA1B,MAA0B;EAA1B,+BAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,cAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,cAA0B;EAA1B,cAA0B;EAA1B,kBAA0B;EAA1B,wBAA0B;AAAA;;AAA1B;EAAA,eAA0B;AAAA;;AAA1B;EAAA,WAA0B;AAAA;;AAA1B;;;;CAA0B;;AAA1B;EAAA,cAA0B,EAA1B,MAA0B;EAA1B,qBAA0B,EAA1B,MAA0B;EAA1B,yBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;;;CAA0B;;AAA1B;;;;;EAAA,oBAA0B,EAA1B,MAA0B;EAA1B,8BAA0B,EAA1B,MAA0B;EAA1B,gCAA0B,EAA1B,MAA0B;EAA1B,eAA0B,EAA1B,MAA0B;EAA1B,oBAA0B,EAA1B,MAA0B;EAA1B,oBAA0B,EAA1B,MAA0B;EAA1B,uBAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;EAA1B,SAA0B,EAA1B,MAA0B;EAA1B,UAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,oBAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;;;;EAAA,0BAA0B,EAA1B,MAA0B;EAA1B,6BAA0B,EAA1B,MAA0B;EAA1B,sBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,aAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,gBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,wBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,YAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;EAAA,6BAA0B,EAA1B,MAA0B;EAA1B,oBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,wBAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;EAAA,0BAA0B,EAA1B,MAA0B;EAA1B,aAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,kBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;;;;;;;;;;;;EAAA,SAA0B;AAAA;;AAA1B;EAAA,SAA0B;EAA1B,UAA0B;AAAA;;AAA1B;EAAA,UAA0B;AAAA;;AAA1B;;;EAAA,gBAA0B;EAA1B,SAA0B;EAA1B,UAA0B;AAAA;;AAA1B;;CAA0B;AAA1B;EAAA,UAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,gBAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;EAAA,UAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;EAAA,UAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,eAA0B;AAAA;;AAA1B;;CAA0B;AAA1B;EAAA,eAA0B;AAAA;;AAA1B;;;;CAA0B;;AAA1B;;;;;;;;EAAA,cAA0B,EAA1B,MAA0B;EAA1B,sBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,eAA0B;EAA1B,YAA0B;AAAA;;AAA1B,wEAA0B;AAA1B;EAAA,aAA0B;AAAA;;AAA1B;EAAA,wBAA0B;KAA1B,qBAA0B;UAA1B,gBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B,iBAA0B;EAA1B,kBAA0B;EAA1B,mBAA0B;EAA1B,sBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B,eAA0B;EAA1B,mBAA0B;EAA1B,sBAA0B;AAAA;;AAA1B;EAAA,8BAA0B;EAA1B,mBAA0B;EAA1B,4CAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;EAA1B,wBAA0B;EAA1B,2GAA0B;EAA1B,yGAA0B;EAA1B,iFAA0B;EAA1B;AAA0B;;AAA1B;EAAA,cAA0B;EAA1B;AAA0B;;AAA1B;EAAA,cAA0B;EAA1B;AAA0B;;AAA1B;EAAA;AAA0B;;AAA1B;EAAA,iBAA0B;EAA1B;AAA0B;;AAA1B;EAAA;AAA0B;;AAA1B;EAAA,cAA0B;EAA1B;AAA0B;;AAA1B;EAAA,mPAA0B;EAA1B,wCAA0B;EAA1B,4BAA0B;EAA1B,4BAA0B;EAA1B,qBAA0B;EAA1B,iCAA0B;UAA1B;AAA0B;;AAA1B;EAAA,yBAA0B;EAA1B,4BAA0B;EAA1B,wBAA0B;EAA1B,wBAA0B;EAA1B,sBAA0B;EAA1B,iCAA0B;UAA1B;AAA0B;;AAA1B;EAAA,wBAA0B;KAA1B,qBAA0B;UAA1B,gBAA0B;EAA1B,UAA0B;EAA1B,iCAA0B;UAA1B,yBAA0B;EAA1B,qBAA0B;EAA1B,sBAA0B;EAA1B,6BAA0B;EAA1B,yBAA0B;KAA1B,sBAA0B;UAA1B,iBAA0B;EAA1B,cAA0B;EAA1B,YAA0B;EAA1B,WAA0B;EAA1B,cAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B,iBAA0B;EAA1B;AAA0B;;AAA1B;EAAA;AAA0B;;AAA1B;EAAA;AAA0B;;AAA1B;EAAA,8BAA0B;EAA1B,mBAA0B;EAA1B,4CAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;EAA1B,wBAA0B;EAA1B,2GAA0B;EAA1B,yGAA0B;EAA1B;AAA0B;;AAA1B;EAAA,yBAA0B;EAA1B,8BAA0B;EAA1B,0BAA0B;EAA1B,2BAA0B;EAA1B;AAA0B;;AAA1B;EAAA,sQAA0B;AAAA;;AAA1B;;EAAA;IAAA,wBAA0B;OAA1B,qBAA0B;YAA1B;EAA0B;AAAA;;AAA1B;EAAA,oKAA0B;AAAA;;AAA1B;;EAAA;IAAA,wBAA0B;OAA1B,qBAA0B;YAA1B;EAA0B;AAAA;;AAA1B;EAAA,yBAA0B;EAA1B;AAA0B;;AAA1B;EAAA,uOAA0B;EAA1B,yBAA0B;EAA1B,8BAA0B;EAA1B,0BAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;AAAA;;AAA1B;;EAAA;IAAA,wBAA0B;OAA1B,qBAA0B;YAA1B;EAA0B;AAAA;;AAA1B;EAAA,yBAA0B;EAA1B;AAA0B;;AAA1B;EAAA,iBAA0B;EAA1B,qBAA0B;EAA1B,eAA0B;EAA1B,gBAA0B;EAA1B,UAA0B;EAA1B,gBAA0B;EAA1B;AAA0B;;AAA1B;EAAA,6BAA0B;EAA1B;AAA0B;AAC1B;EAAA,wBAAgC;KAAhC,qBAAgC;UAAhC,gBAAgC;EAAhC,sBAAgC;EAAhC,qBAAgC;EAAhC,iBAAgC;EAAhC,kBAAgC;EAAhC,mBAAgC;EAAhC,sBAAgC;EAAhC,sBAAgC;EAAhC,qBAAgC;EAAhC,eAAgC;EAAhC,mBAAgC;EAAhC,sBAAgC;AAAA;AAAhC;EAAA,8BAAgC;EAAhC,mBAAgC;EAAhC,4CAAgC;EAAhC,2BAAgC;EAAhC,4BAAgC;EAAhC,wBAAgC;EAAhC,2GAAgC;EAAhC,yGAAgC;EAAhC,iFAAgC;EAAhC;AAAgC;AAAhC;EAAA,cAAgC;EAAhC;AAAgC;AAAhC;EAAA,cAAgC;EAAhC;AAAgC;AAAhC;EAAA;AAAgC;AAAhC;EAAA,iBAAgC;EAAhC;AAAgC;AAAhC;EAAA;AAAgC;AAAhC;EAAA,cAAgC;EAAhC;AAAgC;AAChC;EAAA,kBAA+B;EAA/B,UAA+B;EAA/B,WAA+B;EAA/B,UAA+B;EAA/B,YAA+B;EAA/B,gBAA+B;EAA/B,sBAA+B;EAA/B,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,QAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,iBAA+B;EAA/B;AAA+B;AAA/B;EAAA,iBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,qBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;;EAAA;IAAA;EAA+B;AAAA;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,wBAA+B;KAA/B,qBAA+B;UAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,sDAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,uDAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,oDAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,oDAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,+DAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,8DAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,+DAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,4DAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,8DAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,4DAA+B;EAA/B;AAA+B;AAA/B;EAAA,wBAA+B;EAA/B,kEAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,gBAA+B;EAA/B,uBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,qBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,qBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,qBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,iBAA+B;EAA/B;AAA+B;AAA/B;EAAA,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,iBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,iBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,iBAA+B;EAA/B;AAA+B;AAA/B;EAAA,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,2BAA+B;EAA/B;AAA+B;AAA/B;EAAA,2BAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,0EAA+B;EAA/B,8FAA+B;EAA/B;AAA+B;AAA/B;EAAA,6CAA+B;EAA/B,wDAA+B;EAA/B;AAA+B;AAA/B;EAAA,+EAA+B;EAA/B,mGAA+B;EAA/B;AAA+B;AAA/B;EAAA,4CAA+B;EAA/B,uDAA+B;EAA/B;AAA+B;AAA/B;EAAA,gFAA+B;EAA/B,oGAA+B;EAA/B;AAA+B;AAA/B;EAAA,2GAA+B;EAA/B,yGAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,wBAA+B;EAA/B,wDAA+B;EAA/B;AAA+B;AAA/B;EAAA,+FAA+B;EAA/B,wDAA+B;EAA/B;AAA+B;AAA/B;EAAA,4BAA+B;EAA/B,wDAA+B;EAA/B;AAA+B;AAA/B;EAAA,8BAA+B;EAA/B,wDAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;;AAE/B,iBAAiB;AACjB,mGAAmG;AACnG,mGAAmG;;AAEnG,kCAAkC;AAClC;EACE,uBAAuB;AACzB;;AAEA;EACE,8DAA8D;EAC9D,gBAAgB;EAChB,yBAAyB,EAAE,eAAe;EAC1C,cAAc,EAAE,iBAAiB;EACjC,gBAAgB;EAChB,mCAAmC;EACnC,kCAAkC;AACpC;;AAEA,gBAAgB;AAChB;EACE,gCAAgC;AAClC;;AAEA;EACE,gCAAgC;AAClC;;AAEA,gBAAgB;AAChB;EACE,iBAAiB;AACnB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,iBAAiB;AACnB;;AAEA,qBAAqB;AACrB;EACE,UAAU;EACV,WAAW;AACb;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;AACrB;;AAEA,uBAAuB;AAErB;EAAA,cAAkP;EAAlP,WAAkP;EAAlP,sBAAkP;EAAlP,iBAAkP;EAAlP,sBAAkP;EAAlP,4DAAkP;EAAlP,kBAAkP;EAAlP,4DAAkP;EAAlP,kBAAkP;EAAlP,mBAAkP;EAAlP,oBAAkP;EAAlP;AAAkP;AAAlP;EAAA,2BAAkP;EAAlP;AAAkP;AAAlP;EAAA,2BAAkP;EAAlP;AAAkP;AAAlP;EAAA,4CAAkP;EAAlP,uDAAkP;EAAlP,uGAAkP;EAAlP,wBAAkP;EAAlP,wDAAkP;EAAlP,0BAAkP;EAAlP;AAAkP;AAAlP;EAAA,sBAAkP;EAAlP,2DAAkP;EAAlP,8BAAkP;EAAlP,mBAAkP;EAAlP,2GAAkP;EAAlP,yGAAkP;EAAlP,4FAAkP;EAAlP,oBAAkP;EAAlP;AAAkP;AAAlP;;EAAA;IAAA,mBAAkP;IAAlP;EAAkP;AAAA;;AAIlP;EAAA,sBAA4E;EAA5E;AAA4E;;AAA5E;EAAA,sBAA4E;EAA5E,0DAA4E;EAA5E,oBAA4E;EAA5E;AAA4E;;AAI5E;EAAA,qBAAuD;EAAvD,cAAuD;EAAvD,mBAAuD;EAAvD,oBAAuD;EAAvD,gBAAuD;EAAvD,oBAAuD;EAAvD;AAAuD;;AAIvD;EAAA,mBAAqC;EAArC,mBAAqC;EAArC,oBAAqC;EAArC,oBAAqC;EAArC;AAAqC;;AAGvC,yBAAyB;AAEvB;EAAA,oBAAgO;EAAhO,mBAAgO;EAAhO,uBAAgO;EAAhO,sBAAgO;EAAhO,iBAAgO;EAAhO,yBAAgO;EAAhO,kBAAgO;EAAhO,mBAAgO;EAAhO,qBAAgO;EAAhO,wBAAgO;EAAhO,mBAAgO;EAAhO,oBAAgO;EAAhO,gBAAgO;EAAhO,wBAAgO;EAAhO,wDAAgO;EAAhO;AAAgO;AAAhO;EAAA,8BAAgO;EAAhO,mBAAgO;EAAhO,2GAAgO;EAAhO,yGAAgO;EAAhO,4FAAgO;EAAhO,2BAAgO;EAAhO;AAAgO;;AAIhO;EAAA,kBAAgH;EAAhH,2DAAgH;EAAhH,oBAAgH;EAAhH,mDAAgH;EAAhH,4CAAgH;EAAhH,uDAAgH;EAAhH,uGAAgH;EAAhH,oBAAgH;EAAhH,mBAAgH;EAAhH,uBAAgH;EAAhH,sBAAgH;EAAhH,iBAAgH;EAAhH,yBAAgH;EAAhH,kBAAgH;EAAhH,mBAAgH;EAAhH,qBAAgH;EAAhH,wBAAgH;EAAhH,mBAAgH;EAAhH,oBAAgH;EAAhH,gBAAgH;EAAhH,wBAAgH;EAAhH,wDAAgH;EAAhH;AAAgH;;AAAhH;EAAA,8BAAgH;EAAhH,mBAAgH;EAAhH,2GAAgH;EAAhH,yGAAgH;EAAhH,4FAAgH;EAAhH,2BAAgH;EAAhH;AAAgH;;AAAhH;EAAA,2CAAgH;AAAA;;AAAhH;EAAA,kBAAgH;EAAhH,0DAAgH;EAAhH,6CAAgH;EAAhH,wDAAgH;EAAhH;AAAgH;;AAAhH;EAAA,oBAAgH;EAAhH;AAAgH;;AAIhH;EAAA,sBAAkI;EAAlI,4DAAkI;EAAlI,kBAAkI;EAAlI,4DAAkI;EAAlI,oBAAkI;EAAlI,gDAAkI;EAAlI,4CAAkI;EAAlI,uDAAkI;EAAlI,uGAAkI;EAAlI,oBAAkI;EAAlI,mBAAkI;EAAlI,uBAAkI;EAAlI,sBAAkI;EAAlI,iBAAkI;EAAlI,yBAAkI;EAAlI,kBAAkI;EAAlI,mBAAkI;EAAlI,qBAAkI;EAAlI,wBAAkI;EAAlI,mBAAkI;EAAlI,oBAAkI;EAAlI,gBAAkI;EAAlI,wBAAkI;EAAlI,wDAAkI;EAAlI;AAAkI;;AAAlI;EAAA,8BAAkI;EAAlI,mBAAkI;EAAlI,2GAAkI;EAAlI,yGAAkI;EAAlI,4FAAkI;EAAlI,2BAAkI;EAAlI;AAAkI;;AAAlI;EAAA,2CAAkI;AAAA;;AAAlI;EAAA,kBAAkI;EAAlI,4DAAkI;EAAlI,6CAAkI;EAAlI,wDAAkI;EAAlI;AAAkI;;AAAlI;EAAA,oBAAkI;EAAlI;AAAkI;;AAIlI;EAAA,kBAAyG;EAAzG,0DAAyG;EAAzG,oBAAyG;EAAzG,mDAAyG;EAAzG,4CAAyG;EAAzG,uDAAyG;EAAzG,uGAAyG;EAAzG,oBAAyG;EAAzG,mBAAyG;EAAzG,uBAAyG;EAAzG,sBAAyG;EAAzG,iBAAyG;EAAzG,yBAAyG;EAAzG,kBAAyG;EAAzG,mBAAyG;EAAzG,qBAAyG;EAAzG,wBAAyG;EAAzG,mBAAyG;EAAzG,oBAAyG;EAAzG,gBAAyG;EAAzG,wBAAyG;EAAzG,wDAAyG;EAAzG;AAAyG;;AAAzG;EAAA,8BAAyG;EAAzG,mBAAyG;EAAzG,2GAAyG;EAAzG,yGAAyG;EAAzG,4FAAyG;EAAzG,2BAAyG;EAAzG;AAAyG;;AAAzG;EAAA,2CAAyG;AAAA;;AAAzG;EAAA,kBAAyG;EAAzG,0DAAyG;EAAzG,6CAAyG;EAAzG,wDAAyG;EAAzG;AAAyG;;AAAzG;EAAA,oBAAyG;EAAzG;AAAyG;;AAIzG;EAAA,kBAA6G;EAA7G,0DAA6G;EAA7G,oBAA6G;EAA7G,mDAA6G;EAA7G,4CAA6G;EAA7G,uDAA6G;EAA7G,uGAA6G;EAA7G,oBAA6G;EAA7G,mBAA6G;EAA7G,uBAA6G;EAA7G,sBAA6G;EAA7G,iBAA6G;EAA7G,yBAA6G;EAA7G,kBAA6G;EAA7G,mBAA6G;EAA7G,qBAA6G;EAA7G,wBAA6G;EAA7G,mBAA6G;EAA7G,oBAA6G;EAA7G,gBAA6G;EAA7G,wBAA6G;EAA7G,wDAA6G;EAA7G;AAA6G;;AAA7G;EAAA,8BAA6G;EAA7G,mBAA6G;EAA7G,2GAA6G;EAA7G,yGAA6G;EAA7G,4FAA6G;EAA7G,2BAA6G;EAA7G;AAA6G;;AAA7G;EAAA,2CAA6G;AAAA;;AAA7G;EAAA,kBAA6G;EAA7G,0DAA6G;EAA7G,6CAA6G;EAA7G,wDAA6G;EAA7G;AAA6G;;AAA7G;EAAA,oBAA6G;EAA7G;AAA6G;;AAI7G;EAAA,qBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B,wBAA0B;EAA1B,kBAA0B;EAA1B;AAA0B;;AAI1B;EAAA,oBAA0B;EAA1B,qBAA0B;EAA1B,oBAA0B;EAA1B,uBAA0B;EAA1B,eAA0B;EAA1B;AAA0B;;AAG5B,uBAAuB;AAErB;EAAA,mBAA2E;EAA3E,iBAA2E;EAA3E,sBAA2E;EAA3E,4DAA2E;EAA3E,kBAA2E;EAA3E,4DAA2E;EAA3E,4EAA2E;EAA3E,2FAA2E;EAA3E,uGAA2E;EAA3E;AAA2E;;AAI3E;EAAA,wBAA4C;EAA5C,sBAA4C;EAA5C,4DAA4C;EAA5C,oBAA4C;EAA5C,qBAA4C;EAA5C,oBAA4C;EAA5C;AAA4C;;AAI5C;EAAA,oBAAgB;EAAhB,qBAAgB;EAAhB,oBAAgB;EAAhB;AAAgB;;AAIhB;EAAA,gCAA+E;EAA/E,+BAA+E;EAA/E,qBAA+E;EAA/E,sBAA+E;EAA/E,4DAA+E;EAA/E,kBAA+E;EAA/E,4DAA+E;EAA/E,oBAA+E;EAA/E,qBAA+E;EAA/E,oBAA+E;EAA/E;AAA+E;;AAGjF,wBAAwB;AAEtB;EAAA;AAA6C;AAA7C;EAAA,wBAA6C;EAA7C,kEAA6C;EAA7C,2DAA6C;EAA7C,sBAA6C;EAA7C;AAA6C;;AAI7C;EAAA,kBAA2B;EAA3B;AAA2B;;AAI3B;EAAA,oBAA6F;EAA7F,qBAA6F;EAA7F,iBAA6F;EAA7F,oBAA6F;EAA7F,gBAA6F;EAA7F,kBAA6F;EAA7F,iBAA6F;EAA7F,gBAA6F;EAA7F,yBAA6F;EAA7F,sBAA6F;EAA7F,oBAA6F;EAA7F;AAA6F;;AAI7F;EAAA,mBAA4D;EAA5D,oBAA4D;EAA5D,qBAA4D;EAA5D,iBAA4D;EAA5D,oBAA4D;EAA5D,mBAA4D;EAA5D,oBAA4D;EAA5D,oBAA4D;EAA5D;AAA4D;;AAI5D;EAAA,kBAA2B;EAA3B;AAA2B;;AAI3B;EAAA,kBAAmD;EAAnD,4DAAmD;EAAnD,+FAAmD;EAAnD,wDAAmD;EAAnD;AAAmD;;AAGrD,wBAAwB;AAEtB;EAAA,oBAA0E;EAA1E,mBAA0E;EAA1E,qBAA0E;EAA1E,qBAA0E;EAA1E,sBAA0E;EAA1E,oBAA0E;EAA1E,uBAA0E;EAA1E,kBAA0E;EAA1E,iBAA0E;EAA1E;AAA0E;;AAI1E;EAAA,kBAA4C;EAA5C,4DAA4C;EAA5C,oBAA4C;EAA5C,iDAA4C;EAA5C,oBAA4C;EAA5C,mBAA4C;EAA5C,qBAA4C;EAA5C,qBAA4C;EAA5C,sBAA4C;EAA5C,oBAA4C;EAA5C,uBAA4C;EAA5C,kBAA4C;EAA5C,iBAA4C;EAA5C;AAA4C;;AAI5C;EAAA,kBAA4C;EAA5C,4DAA4C;EAA5C,oBAA4C;EAA5C,gDAA4C;EAA5C,oBAA4C;EAA5C,mBAA4C;EAA5C,qBAA4C;EAA5C,qBAA4C;EAA5C,sBAA4C;EAA5C,oBAA4C;EAA5C,uBAA4C;EAA5C,kBAA4C;EAA5C,iBAA4C;EAA5C;AAA4C;;AAI5C;EAAA,kBAA0C;EAA1C,4DAA0C;EAA1C,oBAA0C;EAA1C,iDAA0C;EAA1C,oBAA0C;EAA1C,mBAA0C;EAA1C,qBAA0C;EAA1C,qBAA0C;EAA1C,sBAA0C;EAA1C,oBAA0C;EAA1C,uBAA0C;EAA1C,kBAA0C;EAA1C,iBAA0C;EAA1C;AAA0C;;AAI1C;EAAA,kBAA0C;EAA1C,4DAA0C;EAA1C,oBAA0C;EAA1C,gDAA0C;EAA1C,oBAA0C;EAA1C,mBAA0C;EAA1C,qBAA0C;EAA1C,qBAA0C;EAA1C,sBAA0C;EAA1C,oBAA0C;EAA1C,uBAA0C;EAA1C,kBAA0C;EAA1C,iBAA0C;EAA1C;AAA0C;;AAI1C;EAAA,kBAAoC;EAApC,4DAAoC;EAApC,oBAAoC;EAApC,iDAAoC;EAApC,oBAAoC;EAApC,mBAAoC;EAApC,qBAAoC;EAApC,qBAAoC;EAApC,sBAAoC;EAApC,oBAAoC;EAApC,uBAAoC;EAApC,kBAAoC;EAApC,iBAAoC;EAApC;AAAoC;;AAIpC;EAAA,kBAA4C;EAA5C,4DAA4C;EAA5C,oBAA4C;EAA5C,iDAA4C;EAA5C,oBAA4C;EAA5C,mBAA4C;EAA5C,qBAA4C;EAA5C,qBAA4C;EAA5C,sBAA4C;EAA5C,oBAA4C;EAA5C,uBAA4C;EAA5C,kBAA4C;EAA5C,iBAA4C;EAA5C;AAA4C;;AAG9C,iBAAiB;AAEf;EAAA,uBAAqB;EAArB;AAAqB;;AAIrB;EAAA,iBAA+D;EAA/D,sBAA+D;EAA/D,4DAA+D;EAA/D,kBAA+D;EAA/D,4DAA+D;EAA/D,oBAA+D;EAA/D,iDAA+D;EAA/D,uBAA+D;EAA/D;AAA+D;;AAI/D;EAAA,iBAAkE;EAAlE,sBAAkE;EAAlE,4DAAkE;EAAlE,kBAAkE;EAAlE,4DAAkE;EAAlE,oBAAkE;EAAlE,iDAAkE;EAAlE,uBAAkE;EAAlE;AAAkE;;AAIlE;EAAA,iBAAyD;EAAzD,sBAAyD;EAAzD,4DAAyD;EAAzD,kBAAyD;EAAzD,4DAAyD;EAAzD,oBAAyD;EAAzD,iDAAyD;EAAzD,uBAAyD;EAAzD;AAAyD;;AAIzD;EAAA,iBAA4D;EAA5D,sBAA4D;EAA5D,4DAA4D;EAA5D,kBAA4D;EAA5D,4DAA4D;EAA5D,oBAA4D;EAA5D,iDAA4D;EAA5D,uBAA4D;EAA5D;AAA4D;;AAG9D,wBAAwB;AACxB;EACE,kCAAkC;AACpC;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE;IACE,2BAA2B;IAC3B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA,iBAAiB;AACjB;EACE;IACE,wBAAwB;EAC1B;;EAEA;IACE,yBAAyB;EAC3B;;EAEA;IACE,eAAe;IACf,gBAAgB;EAClB;;EAEA;IACE,gBAAgB;IAChB,sBAAsB;EACxB;AACF;;AAEA,4BAA4B;AAE1B;EAAA,iBAA6C;EAA7C,kBAA6C;EAA7C,gBAA6C;EAA7C,kBAA6C;EAA7C;AAA6C;AAA7C;;EAAA;IAAA,oBAA6C;IAA7C;EAA6C;AAAA;AAA7C;;EAAA;IAAA,kBAA6C;IAA7C;EAA6C;AAAA;;AAI7C;EAAA,iBAAoB;EAApB;AAAoB;;AAApB;;EAAA;IAAA,iBAAoB;IAApB;EAAoB;AAAA;;AAIpB;EAAA,uBAA6B;EAA7B,8DAA6B;EAA7B;AAA6B;;AAA7B;;EAAA;IAAA,uBAA6B;IAA7B,4DAA6B;IAA7B;EAA6B;AAAA;;AAG/B,mBAAmB;AACnB;EACE,2CAA2C;AAC7C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,2EAA2E;AAC7E;;AAEA,yBAAyB;AAEvB;EAAA;AAAiB;;AAIjB;EAAA;AAAiB;;AAGnB,wBAAwB;AAEtB;EAAA,8BAA8G;EAA9G,mBAA8G;EAA9G,2GAA8G;EAA9G,yGAA8G;EAA9G,4FAA8G;EAA9G,oBAA8G;EAA9G,4DAA8G;EAA9G,2BAA8G;EAA9G;AAA8G;;AAGhH,uBAAuB;AACvB;EACE,UAAU;EACV,WAAW;AACb;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;AACrB;;AAEA,kCAAkC;AAClC;EAEI;IAAA,kBAAuC;IAAvC,yDAAuC;IAAvC,oBAAuC;IAAvC;EAAuC;;EAIvC;IAAA,sBAAwC;IAAxC,yDAAwC;IAAxC,kBAAwC;IAAxC;EAAwC;;EAIxC;IAAA,sBAA0D;IAA1D,yDAA0D;IAA1D,kBAA0D;IAA1D,yDAA0D;IAA1D,oBAA0D;IAA1D;EAA0D;AAE9D;AA1UA;EAAA,0BA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA,sBA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA,kBA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA,wBA2UA;EA3UA,wDA2UA;EA3UA;AA2UA;AA3UA;EAAA,gBA2UA;EA3UA;AA2UA;AA3UA;EAAA,sBA2UA;EA3UA;AA2UA;AA3UA;EAAA,kBA2UA;EA3UA;AA2UA;AA3UA;EAAA,kBA2UA;EA3UA;AA2UA;AA3UA;EAAA,kBA2UA;EA3UA;AA2UA;AA3UA;EAAA,kBA2UA;EA3UA;AA2UA;AA3UA;EAAA,kBA2UA;EA3UA;AA2UA;AA3UA;EAAA,kBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA,sBA2UA;EA3UA;AA2UA;AA3UA;EAAA,8BA2UA;EA3UA;AA2UA;AA3UA;EAAA,2GA2UA;EA3UA,yGA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,kBA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA,sBA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA,sBA2UA;EA3UA;AA2UA;AA3UA;EAAA,8BA2UA;EA3UA;AA2UA;AA3UA;EAAA,2GA2UA;EA3UA,yGA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;;EAAA;IAAA,gBA2UA;IA3UA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA,oBA2UA;IA3UA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA,mBA2UA;IA3UA;EA2UA;AAAA;AA3UA;;EAAA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;AAAA;AA3UA;;EAAA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA,kBA2UA;IA3UA;EA2UA;AAAA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA,sBA2UA;EA3UA;AA2UA;AA3UA;EAAA,sBA2UA;EA3UA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA\",\"sourcesContent\":[\"@import 'tailwindcss/base';\\n@import 'tailwindcss/components';\\n@import 'tailwindcss/utilities';\\n\\n/* Import fonts */\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\\n@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');\\n\\n/* Modern Minimalist Base Styles */\\nhtml {\\n  scroll-behavior: smooth;\\n}\\n\\nbody {\\n  font-family: 'Inter', 'ui-sans-serif', 'system-ui', sans-serif;\\n  line-height: 1.6;\\n  background-color: #fafafa; /* neutral-50 */\\n  color: #171717; /* text-primary */\\n  font-weight: 400;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n/* Arabic font */\\n.font-arabic {\\n  font-family: 'Cairo', sans-serif;\\n}\\n\\n.font-english {\\n  font-family: 'Inter', sans-serif;\\n}\\n\\n/* RTL support */\\n[dir=\\\"rtl\\\"] {\\n  text-align: right;\\n}\\n\\n[dir=\\\"rtl\\\"] .rtl\\\\:text-left {\\n  text-align: left;\\n}\\n\\n[dir=\\\"rtl\\\"] .rtl\\\\:text-right {\\n  text-align: right;\\n}\\n\\n/* Custom scrollbar */\\n::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n/* Modern Form Styles */\\n.form-input {\\n  @apply block w-full px-4 py-3 border border-neutral-200 rounded-xl shadow-soft placeholder-text-tertiary focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-primary-400 sm:text-sm transition-all duration-200 bg-surface-primary;\\n}\\n\\n.form-input:invalid {\\n  @apply border-status-error focus:ring-status-error focus:border-status-error;\\n}\\n\\n.form-label {\\n  @apply block text-sm font-medium text-text-primary mb-2;\\n}\\n\\n.form-error {\\n  @apply mt-1 text-sm text-status-error;\\n}\\n\\n/* Modern Button Styles */\\n.btn {\\n  @apply inline-flex items-center justify-center px-4 py-2.5 border border-transparent text-sm font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-50;\\n}\\n\\n.btn-primary {\\n  @apply btn text-white bg-primary-500 hover:bg-primary-600 focus:ring-primary-400 shadow-soft hover:shadow-medium;\\n}\\n\\n.btn-secondary {\\n  @apply btn text-neutral-700 bg-white border-neutral-200 hover:bg-neutral-50 focus:ring-primary-400 shadow-soft hover:shadow-medium;\\n}\\n\\n.btn-danger {\\n  @apply btn text-white bg-status-error hover:bg-red-600 focus:ring-red-400 shadow-soft hover:shadow-medium;\\n}\\n\\n.btn-success {\\n  @apply btn text-white bg-accent-500 hover:bg-accent-600 focus:ring-accent-400 shadow-soft hover:shadow-medium;\\n}\\n\\n.btn-sm {\\n  @apply px-3 py-1.5 text-xs;\\n}\\n\\n.btn-lg {\\n  @apply px-6 py-3 text-base;\\n}\\n\\n/* Modern Card Styles */\\n.card {\\n  @apply bg-surface-primary shadow-card rounded-2xl border border-neutral-100;\\n}\\n\\n.card-header {\\n  @apply px-6 py-5 border-b border-neutral-100;\\n}\\n\\n.card-body {\\n  @apply px-6 py-5;\\n}\\n\\n.card-footer {\\n  @apply px-6 py-5 border-t border-neutral-100 bg-surface-secondary rounded-b-2xl;\\n}\\n\\n/* Modern Table Styles */\\n.table {\\n  @apply min-w-full divide-y divide-neutral-150;\\n}\\n\\n.table thead {\\n  @apply bg-surface-secondary;\\n}\\n\\n.table th {\\n  @apply px-6 py-4 text-left text-xs font-semibold text-text-secondary uppercase tracking-wider;\\n}\\n\\n.table td {\\n  @apply px-6 py-4 whitespace-nowrap text-sm text-text-primary;\\n}\\n\\n.table tbody tr:nth-child(even) {\\n  @apply bg-surface-secondary;\\n}\\n\\n.table tbody tr:hover {\\n  @apply bg-neutral-75 transition-colors duration-150;\\n}\\n\\n/* Modern Badge Styles */\\n.badge {\\n  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;\\n}\\n\\n.badge-primary {\\n  @apply badge bg-primary-100 text-primary-700;\\n}\\n\\n.badge-secondary {\\n  @apply badge bg-neutral-100 text-neutral-700;\\n}\\n\\n.badge-success {\\n  @apply badge bg-accent-100 text-accent-700;\\n}\\n\\n.badge-warning {\\n  @apply badge bg-yellow-100 text-yellow-700;\\n}\\n\\n.badge-danger {\\n  @apply badge bg-red-100 text-red-700;\\n}\\n\\n.badge-info {\\n  @apply badge bg-primary-100 text-primary-700;\\n}\\n\\n/* Alert styles */\\n.alert {\\n  @apply p-4 rounded-md;\\n}\\n\\n.alert-success {\\n  @apply alert bg-green-50 border border-green-200 text-green-800;\\n}\\n\\n.alert-warning {\\n  @apply alert bg-yellow-50 border border-yellow-200 text-yellow-800;\\n}\\n\\n.alert-danger {\\n  @apply alert bg-red-50 border border-red-200 text-red-800;\\n}\\n\\n.alert-info {\\n  @apply alert bg-blue-50 border border-blue-200 text-blue-800;\\n}\\n\\n/* Animation utilities */\\n.fade-in {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n.slide-up {\\n  animation: slideUp 0.3s ease-out;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideUp {\\n  from {\\n    transform: translateY(10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n  \\n  .print-break {\\n    page-break-before: always;\\n  }\\n  \\n  body {\\n    font-size: 12pt;\\n    line-height: 1.4;\\n  }\\n  \\n  .card {\\n    box-shadow: none;\\n    border: 1px solid #ddd;\\n  }\\n}\\n\\n/* Modern Layout Utilities */\\n.page-container {\\n  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;\\n}\\n\\n.section-spacing {\\n  @apply py-8 lg:py-12;\\n}\\n\\n.content-spacing {\\n  @apply space-y-6 lg:space-y-8;\\n}\\n\\n/* Modern Shadows */\\n.shadow-soft {\\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n.shadow-medium {\\n  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n}\\n\\n.shadow-large {\\n  box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.12);\\n}\\n\\n.shadow-card {\\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n/* Enhanced RTL Support */\\n[dir=\\\"rtl\\\"] .table th {\\n  @apply text-right;\\n}\\n\\n[dir=\\\"rtl\\\"] .table td {\\n  @apply text-right;\\n}\\n\\n/* Modern Focus States */\\n.focus-ring {\\n  @apply focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2 focus:ring-offset-neutral-50;\\n}\\n\\n/* Improved Scrollbar */\\n::-webkit-scrollbar {\\n  width: 8px;\\n  height: 8px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: #f5f5f5;\\n  border-radius: 4px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: #d4d4d4;\\n  border-radius: 4px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: #a3a3a3;\\n}\\n\\n/* Dark mode support (if needed) */\\n@media (prefers-color-scheme: dark) {\\n  .dark-mode {\\n    @apply bg-neutral-900 text-text-inverse;\\n  }\\n\\n  .dark-mode .card {\\n    @apply bg-neutral-800 border-neutral-700;\\n  }\\n\\n  .dark-mode .form-input {\\n    @apply bg-neutral-700 border-neutral-600 text-text-inverse;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9jc3MtbG9hZGVyL3NyYy9pbmRleC5qcz8/cnVsZVNldFsxXS5ydWxlc1s3XS5vbmVPZlsxNF0udXNlWzFdIS4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvcG9zdGNzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzddLm9uZU9mWzE0XS51c2VbMl0hLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7O0FBQUE7QUFDd0g7QUFDeEgsOEJBQThCLGtIQUEyQjtBQUN6RDtBQUNBLGdFQUFnRSw2QkFBNkIsNkJBQTZCLHdCQUF3Qix3QkFBd0IsbUJBQW1CLG1CQUFtQixtQkFBbUIsb0JBQW9CLG9CQUFvQixrQkFBa0Isa0JBQWtCLHVCQUF1QiwyQ0FBMkMsbUNBQW1DLGtDQUFrQyxpQ0FBaUMsb0JBQW9CLHlCQUF5QiwyQkFBMkIsNEJBQTRCLDZCQUE2Qix1QkFBdUIsZ0NBQWdDLGlDQUFpQywyQ0FBMkMsdUNBQXVDLGdDQUFnQywyQkFBMkIsbUNBQW1DLGlCQUFpQix1QkFBdUIscUJBQXFCLHNCQUFzQix1QkFBdUIsbUJBQW1CLHFCQUFxQixrQkFBa0Isd0JBQXdCLDBCQUEwQixnQ0FBZ0MsOEJBQThCLCtCQUErQixnQ0FBZ0MsNEJBQTRCLDZCQUE2Qiw4QkFBOEIsMkJBQTJCLHlCQUF5QiwyQkFBMkIsMEJBQTBCLDBCQUEwQixHQUFHLGdCQUFnQiw2QkFBNkIsNkJBQTZCLHdCQUF3Qix3QkFBd0IsbUJBQW1CLG1CQUFtQixtQkFBbUIsb0JBQW9CLG9CQUFvQixrQkFBa0Isa0JBQWtCLHVCQUF1QiwyQ0FBMkMsbUNBQW1DLGtDQUFrQyxpQ0FBaUMsb0JBQW9CLHlCQUF5QiwyQkFBMkIsNEJBQTRCLDZCQUE2Qix1QkFBdUIsZ0NBQWdDLGlDQUFpQywyQ0FBMkMsdUNBQXVDLGdDQUFnQywyQkFBMkIsbUNBQW1DLGlCQUFpQix1QkFBdUIscUJBQXFCLHNCQUFzQix1QkFBdUIsbUJBQW1CLHFCQUFxQixrQkFBa0Isd0JBQXdCLDBCQUEwQixnQ0FBZ0MsOEJBQThCLCtCQUErQixnQ0FBZ0MsNEJBQTRCLDZCQUE2Qiw4QkFBOEIsMkJBQTJCLHlCQUF5QiwyQkFBMkIsMEJBQTBCLDBCQUEwQixHQUFHLGlWQUFpViw0QkFBNEIsNEJBQTRCLGdDQUFnQyxrQ0FBa0MsVUFBVSx3QkFBd0IscUJBQXFCLEdBQUcsK2FBQSthLHNCQUFzQiwyQ0FBMkMsNkJBQTZCLDBCQUEwQixvQkFBb0IscUVBQXFFLDBDQUEwQyw0Q0FBNEMscURBQXFELFVBQVUsZ0tBQWdLLGVBQWUsaUNBQWlDLFVBQVUsMk5BQTJOLGVBQWUsMkJBQTJCLGtDQUFrQyxVQUFVLGlHQUFpRyw4Q0FBOEMsOENBQThDLEdBQUcsa0dBQWtHLHVCQUF1Qix5QkFBeUIsR0FBRyxpRkFBaUYsbUJBQW1CLDZCQUE2QixHQUFHLDJFQUEyRSx3QkFBd0IsR0FBRywwU0FBMFMseUhBQXlILDBDQUEwQyw0Q0FBNEMsMkJBQTJCLFVBQVUsaUVBQWlFLG1CQUFtQixHQUFHLDJHQUEyRyxtQkFBbUIsbUJBQW1CLHVCQUF1Qiw2QkFBNkIsR0FBRyxTQUFTLG9CQUFvQixHQUFHLFNBQVMsZ0JBQWdCLEdBQUcsZ2JBQWdiLG9CQUFvQixrQ0FBa0Msc0NBQXNDLFVBQVUsa01BQWtNLDBCQUEwQiwyQ0FBMkMsNkNBQTZDLDRCQUE0QixpQ0FBaUMsaUNBQWlDLG9DQUFvQywyQkFBMkIsc0JBQXNCLHVCQUF1QixVQUFVLDhGQUE4Rix5QkFBeUIsR0FBRywwTkFBME4sZ0NBQWdDLDBDQUEwQyxtQ0FBbUMsVUFBVSwrRkFBK0Ysa0JBQWtCLEdBQUcsK01BQStNLHFCQUFxQixHQUFHLG1GQUFtRiw2QkFBNkIsR0FBRyxpSkFBaUosaUJBQWlCLEdBQUcsNkhBQTZILG1DQUFtQyxpQ0FBaUMsVUFBVSxvR0FBb0csNkJBQTZCLEdBQUcscUtBQXFLLGdDQUFnQywwQkFBMEIsVUFBVSxzRUFBc0UsdUJBQXVCLEdBQUcsNEpBQTRKLGNBQWMsR0FBRyxjQUFjLGNBQWMsZUFBZSxHQUFHLFlBQVksZUFBZSxHQUFHLG9CQUFvQixxQkFBcUIsY0FBYyxlQUFlLEdBQUcsd0RBQXdELGVBQWUsR0FBRyw2RUFBNkUscUJBQXFCLEdBQUcsa1FBQWtRLGdCQUFnQiwyQkFBMkIsVUFBVSxnREFBZ0QsZ0JBQWdCLDJCQUEyQixVQUFVLCtFQUErRSxvQkFBb0IsR0FBRyxpRkFBaUYsb0JBQW9CLEdBQUcsbWJBQW1iLG9CQUFvQixtQ0FBbUMsVUFBVSx3S0FBd0ssb0JBQW9CLGlCQUFpQixHQUFHLCtIQUErSCxrQkFBa0IsR0FBRyxvUEFBb1AsNkJBQTZCLDZCQUE2Qiw2QkFBNkIsMkJBQTJCLDBCQUEwQixzQkFBc0IsdUJBQXVCLHdCQUF3QiwyQkFBMkIsMkJBQTJCLDBCQUEwQixvQkFBb0Isd0JBQXdCLDJCQUEyQixHQUFHLG1XQUFtVyxtQ0FBbUMsd0JBQXdCLGlEQUFpRCxnQ0FBZ0MsaUNBQWlDLDZCQUE2QixnSEFBZ0gsOEdBQThHLHNGQUFzRiwwQkFBMEIsR0FBRyx5REFBeUQsbUJBQW1CLGVBQWUsR0FBRyw4Q0FBOEMsbUJBQW1CLGVBQWUsR0FBRyw0Q0FBNEMsZUFBZSxHQUFHLG1DQUFtQyxzQkFBc0Isd0JBQXdCLEdBQUcsNkJBQTZCLHlCQUF5QixHQUFHLG9VQUFvVSxtQkFBbUIsc0JBQXNCLEdBQUcsWUFBWSwwUEFBMFAsNkNBQTZDLGlDQUFpQyxpQ0FBaUMsMEJBQTBCLHNDQUFzQyxzQ0FBc0MsR0FBRyx1REFBdUQsOEJBQThCLGlDQUFpQyw2QkFBNkIsNkJBQTZCLDJCQUEyQixzQ0FBc0Msc0NBQXNDLEdBQUcsc0NBQXNDLDZCQUE2Qiw2QkFBNkIsNkJBQTZCLGVBQWUsc0NBQXNDLHNDQUFzQywwQkFBMEIsMkJBQTJCLGtDQUFrQyw4QkFBOEIsOEJBQThCLDhCQUE4QixtQkFBbUIsaUJBQWlCLGdCQUFnQixtQkFBbUIsMkJBQTJCLDBCQUEwQixzQkFBc0IsMkJBQTJCLEdBQUcsdUJBQXVCLHVCQUF1QixHQUFHLG9CQUFvQix3QkFBd0IsR0FBRyxrREFBa0QsbUNBQW1DLHdCQUF3QixpREFBaUQsZ0NBQWdDLGlDQUFpQyw2QkFBNkIsZ0hBQWdILDhHQUE4RyxzRkFBc0YsR0FBRyxzREFBc0QsOEJBQThCLG1DQUFtQywrQkFBK0IsZ0NBQWdDLGlDQUFpQyxHQUFHLCtCQUErQiw2UUFBNlEsR0FBRyxxQ0FBcUMsaUNBQWlDLCtCQUErQiwrQkFBK0IsK0JBQStCLEtBQUssR0FBRyw0QkFBNEIsMktBQTJLLEdBQUcscUNBQXFDLDhCQUE4QiwrQkFBK0IsK0JBQStCLCtCQUErQixLQUFLLEdBQUcsK0hBQStILDhCQUE4QixtQ0FBbUMsR0FBRyxxQ0FBcUMsOE9BQThPLDhCQUE4QixtQ0FBbUMsK0JBQStCLGdDQUFnQyxpQ0FBaUMsR0FBRyxxQ0FBcUMsdUNBQXVDLCtCQUErQiwrQkFBK0IsK0JBQStCLEtBQUssR0FBRyxpRkFBaUYsOEJBQThCLG1DQUFtQyxHQUFHLG1CQUFtQixzQkFBc0IsMEJBQTBCLG9CQUFvQixxQkFBcUIsZUFBZSxxQkFBcUIseUJBQXlCLEdBQUcseUJBQXlCLGtDQUFrQywrQ0FBK0MsR0FBRyw2REFBNkQsNkJBQTZCLDZCQUE2Qiw2QkFBNkIsMkJBQTJCLDBCQUEwQixzQkFBc0IsdUJBQXVCLHdCQUF3QiwyQkFBMkIsMkJBQTJCLDBCQUEwQixvQkFBb0Isd0JBQXdCLDJCQUEyQixHQUFHLHdGQUF3RixtQ0FBbUMsd0JBQXdCLGlEQUFpRCxnQ0FBZ0MsaUNBQWlDLDZCQUE2QixnSEFBZ0gsOEdBQThHLHNGQUFzRiwwQkFBMEIsR0FBRyxtRUFBbUUsbUJBQW1CLGVBQWUsR0FBRyx3REFBd0QsbUJBQW1CLGVBQWUsR0FBRyxxREFBcUQsZUFBZSxHQUFHLDRDQUE0QyxzQkFBc0Isd0JBQXdCLEdBQUcsc0NBQXNDLHlCQUF5QixHQUFHLHFhQUFxYSxtQkFBbUIsc0JBQXNCLEdBQUcsWUFBWSx1QkFBdUIsZUFBZSxnQkFBZ0IsZUFBZSxpQkFBaUIscUJBQXFCLDJCQUEyQix3QkFBd0Isb0JBQW9CLEdBQUcsVUFBVSxvQkFBb0IsR0FBRyxhQUFhLHVCQUF1QixHQUFHLGFBQWEsdUJBQXVCLEdBQUcsWUFBWSxlQUFlLEdBQUcsY0FBYyxhQUFhLGdCQUFnQixHQUFHLGFBQWEsb0JBQW9CLEdBQUcsV0FBVyxrQkFBa0IsR0FBRyxXQUFXLGNBQWMsR0FBRyxXQUFXLGlCQUFpQixHQUFHLFdBQVcsa0JBQWtCLEdBQUcsWUFBWSxlQUFlLEdBQUcsY0FBYyxhQUFhLEdBQUcsU0FBUyxnQkFBZ0IsR0FBRyxTQUFTLGdCQUFnQixHQUFHLGVBQWUsaUNBQWlDLEdBQUcsZUFBZSxpQ0FBaUMsR0FBRyxlQUFlLGlDQUFpQyxHQUFHLFNBQVMsc0JBQXNCLHVCQUF1QixHQUFHLFlBQVksc0JBQXNCLHVCQUF1QixHQUFHLFdBQVcsd0JBQXdCLEdBQUcsU0FBUywyQkFBMkIsR0FBRyxTQUFTLDBCQUEwQixHQUFHLFNBQVMsMkJBQTJCLEdBQUcsU0FBUyx3QkFBd0IsR0FBRyxTQUFTLDBCQUEwQixHQUFHLFNBQVMsd0JBQXdCLEdBQUcsU0FBUyx5QkFBeUIsR0FBRyxTQUFTLHdCQUF3QixHQUFHLFNBQVMseUJBQXlCLEdBQUcsU0FBUyxzQkFBc0IsR0FBRyxTQUFTLHlCQUF5QixHQUFHLFlBQVksc0JBQXNCLEdBQUcsU0FBUywwQkFBMEIsR0FBRyxTQUFTLHlCQUF5QixHQUFHLFNBQVMsMEJBQTBCLEdBQUcsYUFBYSx5QkFBeUIsR0FBRyxTQUFTLHdCQUF3QixHQUFHLFNBQVMsdUJBQXVCLEdBQUcsU0FBUyx3QkFBd0IsR0FBRyxTQUFTLHFCQUFxQixHQUFHLFNBQVMsdUJBQXVCLEdBQUcsU0FBUyxxQkFBcUIsR0FBRyxVQUFVLG1CQUFtQixHQUFHLGlCQUFpQiwwQkFBMEIsR0FBRyxXQUFXLG9CQUFvQixHQUFHLFNBQVMsa0JBQWtCLEdBQUcsZ0JBQWdCLHlCQUF5QixHQUFHLFVBQVUsbUJBQW1CLEdBQUcsU0FBUyxrQkFBa0IsR0FBRyxXQUFXLGtCQUFrQixHQUFHLFNBQVMsaUJBQWlCLEdBQUcsUUFBUSxtQkFBbUIsR0FBRyxTQUFTLGlCQUFpQixHQUFHLFNBQVMsaUJBQWlCLEdBQUcsUUFBUSxpQkFBaUIsR0FBRyxRQUFRLG9CQUFvQixHQUFHLFFBQVEsbUJBQW1CLEdBQUcsU0FBUyxrQkFBa0IsR0FBRyxRQUFRLGlCQUFpQixHQUFHLFdBQVcsaUJBQWlCLEdBQUcsYUFBYSxzQkFBc0IsR0FBRyxxQkFBcUIscUJBQXFCLEdBQUcsaUJBQWlCLHNCQUFzQixHQUFHLFFBQVEsZUFBZSxHQUFHLFNBQVMsbUJBQW1CLEdBQUcsU0FBUyxnQkFBZ0IsR0FBRyxRQUFRLGtCQUFrQixHQUFHLFNBQVMsZ0JBQWdCLEdBQUcsU0FBUyxnQkFBZ0IsR0FBRyxRQUFRLGdCQUFnQixHQUFHLFNBQVMsaUJBQWlCLEdBQUcsUUFBUSxtQkFBbUIsR0FBRyxRQUFRLGtCQUFrQixHQUFHLFNBQVMsaUJBQWlCLEdBQUcsUUFBUSxnQkFBZ0IsR0FBRyxTQUFTLGlCQUFpQixHQUFHLFdBQVcsZ0JBQWdCLEdBQUcsWUFBWSxtQkFBbUIsR0FBRyxlQUFlLG9CQUFvQixHQUFHLGNBQWMscUJBQXFCLEdBQUcsY0FBYyxxQkFBcUIsR0FBRyxjQUFjLHFCQUFxQixHQUFHLGFBQWEscUJBQXFCLEdBQUcsYUFBYSxxQkFBcUIsR0FBRyxXQUFXLGlCQUFpQixHQUFHLGtCQUFrQixtQkFBbUIsR0FBRyxvQkFBb0IsOEJBQThCLEdBQUcsc0JBQXNCLDRCQUE0QixvTUFBb00sR0FBRyx1QkFBdUIsMkJBQTJCLG9NQUFvTSxHQUFHLGtCQUFrQiwwQkFBMEIsb01BQW9NLEdBQUcsY0FBYyxvTUFBb00sR0FBRyxtQkFBbUIsVUFBVSxnQ0FBZ0MsS0FBSyxHQUFHLGlCQUFpQix1Q0FBdUMsR0FBRyx1QkFBdUIsd0JBQXdCLEdBQUcsbUJBQW1CLG9CQUFvQixHQUFHLGdCQUFnQixnQ0FBZ0MsR0FBRyxjQUFjLDBCQUEwQixHQUFHLG9CQUFvQiw2QkFBNkIsNkJBQTZCLDZCQUE2QixHQUFHLGdCQUFnQixxREFBcUQsR0FBRyxpQkFBaUIsc0RBQXNELEdBQUcsZ0JBQWdCLHFEQUFxRCxHQUFHLGFBQWEsMkJBQTJCLEdBQUcsZ0JBQWdCLDRCQUE0QixHQUFHLGNBQWMsMEJBQTBCLEdBQUcsaUJBQWlCLHdCQUF3QixHQUFHLGdCQUFnQiw4QkFBOEIsR0FBRyxtQkFBbUIsNEJBQTRCLEdBQUcsb0JBQW9CLG1DQUFtQyxHQUFHLFVBQVUsaUJBQWlCLEdBQUcsVUFBVSxjQUFjLEdBQUcsVUFBVSxpQkFBaUIsR0FBRyxVQUFVLGdCQUFnQixHQUFHLFVBQVUsY0FBYyxHQUFHLFVBQVUsZ0JBQWdCLEdBQUcsZ0RBQWdELDRCQUE0QiwyREFBMkQsb0VBQW9FLEdBQUcsZ0RBQWdELDRCQUE0Qiw0REFBNEQscUVBQXFFLEdBQUcsZ0RBQWdELDRCQUE0Qix5REFBeUQsa0VBQWtFLEdBQUcsZ0RBQWdELDRCQUE0Qix5REFBeUQsa0VBQWtFLEdBQUcsZ0RBQWdELDRCQUE0QixvRUFBb0UsNkRBQTZELEdBQUcsZ0RBQWdELDRCQUE0QixtRUFBbUUsNERBQTRELEdBQUcsZ0RBQWdELDRCQUE0QixvRUFBb0UsNkRBQTZELEdBQUcsZ0RBQWdELDRCQUE0QixpRUFBaUUsMERBQTBELEdBQUcsZ0RBQWdELDRCQUE0QixtRUFBbUUsNERBQTRELEdBQUcsZ0RBQWdELDRCQUE0QixpRUFBaUUsMERBQTBELEdBQUcsK0NBQStDLDZCQUE2Qix1RUFBdUUsZ0VBQWdFLEdBQUcsc0RBQXNELDJCQUEyQixpRUFBaUUsR0FBRyxvQkFBb0IscUJBQXFCLEdBQUcsb0JBQW9CLHFCQUFxQixHQUFHLG9CQUFvQixxQkFBcUIsR0FBRyxhQUFhLHFCQUFxQiw0QkFBNEIsd0JBQXdCLEdBQUcsc0JBQXNCLHdCQUF3QixHQUFHLFlBQVksMkJBQTJCLEdBQUcsZ0JBQWdCLHdCQUF3QixHQUFHLGlCQUFpQiwwQkFBMEIsR0FBRyxlQUFlLDBCQUEwQixHQUFHLGVBQWUsNEJBQTRCLEdBQUcsZUFBZSwyQkFBMkIsR0FBRyxXQUFXLHNCQUFzQixHQUFHLGFBQWEsc0JBQXNCLEdBQUcsYUFBYSw2QkFBNkIsR0FBRyxlQUFlLDZCQUE2QixHQUFHLGFBQWEsMEJBQTBCLEdBQUcsZUFBZSwwQkFBMEIsR0FBRyxvQkFBb0IsMkJBQTJCLGdFQUFnRSxHQUFHLG9CQUFvQiwyQkFBMkIsK0RBQStELEdBQUcsb0JBQW9CLDJCQUEyQixpRUFBaUUsR0FBRyxvQkFBb0IsMkJBQTJCLGlFQUFpRSxHQUFHLG9CQUFvQiwyQkFBMkIsaUVBQWlFLEdBQUcsdUJBQXVCLDJCQUEyQixpRUFBaUUsR0FBRyx1QkFBdUIsMkJBQTJCLGlFQUFpRSxHQUFHLHNCQUFzQiwyQkFBMkIsaUVBQWlFLEdBQUcsdUJBQXVCLDJCQUEyQixpRUFBaUUsR0FBRyx1QkFBdUIsMkJBQTJCLGdFQUFnRSxHQUFHLHVCQUF1QiwyQkFBMkIsK0RBQStELEdBQUcsbUJBQW1CLDJCQUEyQixpRUFBaUUsR0FBRyxtQkFBbUIsMkJBQTJCLGlFQUFpRSxHQUFHLG1CQUFtQiwyQkFBMkIsK0RBQStELEdBQUcsdUJBQXVCLDhCQUE4QixHQUFHLHlCQUF5QiwyQkFBMkIsbUVBQW1FLEdBQUcsYUFBYSx1QkFBdUIsMkRBQTJELEdBQUcsZ0JBQWdCLHVCQUF1QixpRUFBaUUsR0FBRyxlQUFlLHVCQUF1QixpRUFBaUUsR0FBRyxnQkFBZ0IsdUJBQXVCLGdFQUFnRSxHQUFHLGdCQUFnQix1QkFBdUIsK0RBQStELEdBQUcsbUJBQW1CLHVCQUF1QixnRUFBZ0UsR0FBRyxnQkFBZ0IsdUJBQXVCLGlFQUFpRSxHQUFHLGdCQUFnQix1QkFBdUIsaUVBQWlFLEdBQUcsZ0JBQWdCLHVCQUF1QixpRUFBaUUsR0FBRyxlQUFlLHVCQUF1QixpRUFBaUUsR0FBRyxnQkFBZ0IsdUJBQXVCLGlFQUFpRSxHQUFHLGlCQUFpQix1QkFBdUIsaUVBQWlFLEdBQUcsZ0JBQWdCLHVCQUF1QixpRUFBaUUsR0FBRyxpQkFBaUIsdUJBQXVCLCtEQUErRCxHQUFHLGlCQUFpQix1QkFBdUIsK0RBQStELEdBQUcsa0JBQWtCLHVCQUF1QixpRUFBaUUsR0FBRyxrQkFBa0IsdUJBQXVCLGdFQUFnRSxHQUFHLGtCQUFrQix1QkFBdUIsaUVBQWlFLEdBQUcsa0JBQWtCLHVCQUF1QixpRUFBaUUsR0FBRyxpQkFBaUIsdUJBQXVCLGlFQUFpRSxHQUFHLGdCQUFnQix1QkFBdUIsZ0VBQWdFLEdBQUcsbUJBQW1CLHVCQUF1QixpRUFBaUUsR0FBRyxrQkFBa0IsdUJBQXVCLGlFQUFpRSxHQUFHLG1CQUFtQix1QkFBdUIsK0RBQStELEdBQUcsa0JBQWtCLHVCQUF1QixpRUFBaUUsR0FBRyxrQkFBa0IsdUJBQXVCLGdFQUFnRSxHQUFHLGVBQWUsdUJBQXVCLGlFQUFpRSxHQUFHLGNBQWMsdUJBQXVCLGlFQUFpRSxHQUFHLGVBQWUsdUJBQXVCLCtEQUErRCxHQUFHLG9CQUFvQix1QkFBdUIsK0RBQStELEdBQUcsdUJBQXVCLHVCQUF1QixpRUFBaUUsR0FBRyxhQUFhLHVCQUF1QixpRUFBaUUsR0FBRyxrQkFBa0IsdUJBQXVCLGlFQUFpRSxHQUFHLGtCQUFrQix1QkFBdUIsK0RBQStELEdBQUcsa0JBQWtCLHlCQUF5QixHQUFHLGtCQUFrQiwwQkFBMEIsR0FBRyxRQUFRLHFCQUFxQixHQUFHLFlBQVksc0JBQXNCLEdBQUcsU0FBUyxrQkFBa0IsR0FBRyxRQUFRLG9CQUFvQixHQUFHLFlBQVksc0JBQXNCLEdBQUcsUUFBUSxxQkFBcUIsR0FBRyxRQUFRLGtCQUFrQixHQUFHLFFBQVEscUJBQXFCLEdBQUcsUUFBUSxvQkFBb0IsR0FBRyxRQUFRLGtCQUFrQixHQUFHLFNBQVMsMEJBQTBCLDJCQUEyQixHQUFHLFNBQVMseUJBQXlCLDBCQUEwQixHQUFHLGFBQWEsMkJBQTJCLDRCQUE0QixHQUFHLFNBQVMsMEJBQTBCLDJCQUEyQixHQUFHLFNBQVMsdUJBQXVCLHdCQUF3QixHQUFHLFNBQVMseUJBQXlCLDBCQUEwQixHQUFHLGFBQWEsMEJBQTBCLDZCQUE2QixHQUFHLFNBQVMseUJBQXlCLDRCQUE0QixHQUFHLFVBQVUsc0JBQXNCLHlCQUF5QixHQUFHLFNBQVMsd0JBQXdCLDJCQUEyQixHQUFHLFNBQVMseUJBQXlCLDRCQUE0QixHQUFHLFNBQVMsc0JBQXNCLHlCQUF5QixHQUFHLFNBQVMseUJBQXlCLDRCQUE0QixHQUFHLFNBQVMsc0JBQXNCLHlCQUF5QixHQUFHLFNBQVMsNEJBQTRCLEdBQUcsU0FBUywyQkFBMkIsR0FBRyxVQUFVLHlCQUF5QixHQUFHLFNBQVMseUJBQXlCLEdBQUcsU0FBUywyQkFBMkIsR0FBRyxVQUFVLHlCQUF5QixHQUFHLFVBQVUsMEJBQTBCLEdBQUcsU0FBUywyQkFBMkIsR0FBRyxTQUFTLHdCQUF3QixHQUFHLFNBQVMsc0JBQXNCLEdBQUcsU0FBUyx5QkFBeUIsR0FBRyxTQUFTLHdCQUF3QixHQUFHLGNBQWMscUJBQXFCLEdBQUcsZ0JBQWdCLHVCQUF1QixHQUFHLGVBQWUsc0JBQXNCLEdBQUcsaUJBQWlCLDJCQUEyQixHQUFHLGdCQUFnQiw2REFBNkQsR0FBRyxpQkFBaUIsNkRBQTZELEdBQUcsYUFBYSxzQkFBc0Isc0JBQXNCLEdBQUcsYUFBYSx3QkFBd0IseUJBQXlCLEdBQUcsWUFBWSx3QkFBd0IseUJBQXlCLEdBQUcsWUFBWSx3QkFBd0IseUJBQXlCLEdBQUcsWUFBWSx1QkFBdUIseUJBQXlCLEdBQUcsWUFBWSx1QkFBdUIsc0JBQXNCLEdBQUcsY0FBYyxxQkFBcUIsR0FBRyxtQkFBbUIscUJBQXFCLEdBQUcsZ0JBQWdCLHFCQUFxQixHQUFHLGtCQUFrQixxQkFBcUIsR0FBRyxjQUFjLDhCQUE4QixHQUFHLGVBQWUsK0JBQStCLEdBQUcsY0FBYyx3QkFBd0IsR0FBRyxtQkFBbUIsMkJBQTJCLEdBQUcsa0JBQWtCLHlCQUF5Qix3REFBd0QsR0FBRyxrQkFBa0IseUJBQXlCLHVEQUF1RCxHQUFHLGtCQUFrQix5QkFBeUIsc0RBQXNELEdBQUcsa0JBQWtCLHlCQUF5QixzREFBc0QsR0FBRyxrQkFBa0IseUJBQXlCLHNEQUFzRCxHQUFHLGtCQUFrQix5QkFBeUIsc0RBQXNELEdBQUcsa0JBQWtCLHlCQUF5Qix3REFBd0QsR0FBRyxrQkFBa0IseUJBQXlCLHdEQUF3RCxHQUFHLGtCQUFrQix5QkFBeUIscURBQXFELEdBQUcsa0JBQWtCLHlCQUF5QixxREFBcUQsR0FBRyxrQkFBa0IseUJBQXlCLHFEQUFxRCxHQUFHLGtCQUFrQix5QkFBeUIscURBQXFELEdBQUcsbUJBQW1CLHlCQUF5Qix1REFBdUQsR0FBRyxtQkFBbUIseUJBQXlCLHNEQUFzRCxHQUFHLG1CQUFtQix5QkFBeUIsc0RBQXNELEdBQUcsbUJBQW1CLHlCQUF5QixzREFBc0QsR0FBRyxtQkFBbUIseUJBQXlCLHFEQUFxRCxHQUFHLG9CQUFvQix5QkFBeUIsc0RBQXNELEdBQUcsb0JBQW9CLHlCQUF5Qix1REFBdUQsR0FBRyxvQkFBb0IseUJBQXlCLHNEQUFzRCxHQUFHLG9CQUFvQix5QkFBeUIsc0RBQXNELEdBQUcsb0JBQW9CLHlCQUF5QixzREFBc0QsR0FBRyxvQkFBb0IseUJBQXlCLHNEQUFzRCxHQUFHLHFCQUFxQix5QkFBeUIsc0RBQXNELEdBQUcscUJBQXFCLHlCQUF5QixzREFBc0QsR0FBRyxxQkFBcUIseUJBQXlCLHNEQUFzRCxHQUFHLG9CQUFvQix5QkFBeUIsdURBQXVELEdBQUcsaUJBQWlCLHlCQUF5Qix3REFBd0QsR0FBRyxpQkFBaUIseUJBQXlCLHNEQUFzRCxHQUFHLGlCQUFpQix5QkFBeUIsc0RBQXNELEdBQUcsaUJBQWlCLHlCQUF5QixzREFBc0QsR0FBRyxpQkFBaUIseUJBQXlCLHNEQUFzRCxHQUFHLHNCQUFzQix5QkFBeUIscURBQXFELEdBQUcsd0JBQXdCLHlCQUF5QixxREFBcUQsR0FBRyxlQUFlLHlCQUF5Qix3REFBd0QsR0FBRyxvQkFBb0IseUJBQXlCLHVEQUF1RCxHQUFHLG9CQUFvQix5QkFBeUIsc0RBQXNELEdBQUcsb0JBQW9CLHlCQUF5QixzREFBc0QsR0FBRywyQ0FBMkMsZ0NBQWdDLCtEQUErRCxHQUFHLHNDQUFzQyxnQ0FBZ0MsK0RBQStELEdBQUcsZUFBZSxpQkFBaUIsR0FBRyxXQUFXLCtFQUErRSxtR0FBbUcsNEdBQTRHLEdBQUcsaUJBQWlCLGtEQUFrRCw2REFBNkQsNEdBQTRHLEdBQUcsY0FBYyxvRkFBb0Ysd0dBQXdHLDRHQUE0RyxHQUFHLGdCQUFnQixpREFBaUQsNERBQTRELDRHQUE0RyxHQUFHLGNBQWMscUZBQXFGLHlHQUF5Ryw0R0FBNEcsR0FBRyxXQUFXLGdIQUFnSCw4R0FBOEcsaUdBQWlHLEdBQUcsZUFBZSx5QkFBeUIsNERBQTRELEdBQUcsbUJBQW1CLDRCQUE0QixHQUFHLFdBQVcsc0xBQXNMLEdBQUcsbUJBQW1CLDZCQUE2Qiw2REFBNkQsK0JBQStCLEdBQUcsc0JBQXNCLG9HQUFvRyw2REFBNkQsK0JBQStCLEdBQUcsdUJBQXVCLGlDQUFpQyw2REFBNkQsK0JBQStCLEdBQUcseUJBQXlCLG1DQUFtQyw2REFBNkQsK0JBQStCLEdBQUcsaUJBQWlCLCtCQUErQixHQUFHLGlCQUFpQiwrQkFBK0IsR0FBRyw2RkFBNkYsSUFBSSxJQUFJLElBQUksbUJBQW1CLHVFQUF1RSxJQUFJLElBQUksSUFBSSxtQkFBbUIsK0NBQStDLDRCQUE0QixHQUFHLFVBQVUsbUVBQW1FLHFCQUFxQiwrQkFBK0Isb0NBQW9DLHVDQUF1Qyx3Q0FBd0MsdUNBQXVDLEdBQUcscUNBQXFDLHFDQUFxQyxHQUFHLG1CQUFtQixxQ0FBcUMsR0FBRyxzQ0FBc0Msc0JBQXNCLEdBQUcsb0NBQW9DLHFCQUFxQixHQUFHLHFDQUFxQyxzQkFBc0IsR0FBRyxpREFBaUQsZUFBZSxnQkFBZ0IsR0FBRywrQkFBK0Isd0JBQXdCLHVCQUF1QixHQUFHLCtCQUErQix3QkFBd0IsdUJBQXVCLEdBQUcscUNBQXFDLHdCQUF3QixHQUFHLDJDQUEyQyxtQkFBbUIsZ0JBQWdCLDJCQUEyQixzQkFBc0IsMkJBQTJCLGlFQUFpRSx1QkFBdUIsaUVBQWlFLHVCQUF1Qix3QkFBd0IseUJBQXlCLDRCQUE0QixHQUFHLGlDQUFpQyxnQ0FBZ0MsK0RBQStELEdBQUcsNEJBQTRCLGdDQUFnQywrREFBK0QsR0FBRyxlQUFlLGlEQUFpRCw0REFBNEQsNEdBQTRHLDZCQUE2Qiw2REFBNkQsK0JBQStCLGdEQUFnRCxHQUFHLHFCQUFxQiwyQkFBMkIsZ0VBQWdFLG1DQUFtQyx3QkFBd0IsZ0hBQWdILDhHQUE4RyxpR0FBaUcseUJBQXlCLGlFQUFpRSxHQUFHLDZCQUE2QixtQkFBbUIsMEJBQTBCLDJCQUEyQixLQUFLLEdBQUcseUJBQXlCLDJCQUEyQiwrREFBK0QsR0FBRywrQkFBK0IsMkJBQTJCLCtEQUErRCx5QkFBeUIsZ0VBQWdFLEdBQUcsaUJBQWlCLDBCQUEwQixtQkFBbUIsd0JBQXdCLHlCQUF5QixxQkFBcUIseUJBQXlCLHFEQUFxRCxHQUFHLGlCQUFpQix3QkFBd0Isd0JBQXdCLHlCQUF5Qix5QkFBeUIsc0RBQXNELEdBQUcsc0NBQXNDLHlCQUF5Qix3QkFBd0IsNEJBQTRCLDJCQUEyQixzQkFBc0IsOEJBQThCLHVCQUF1Qix3QkFBd0IsMEJBQTBCLDZCQUE2Qix3QkFBd0IseUJBQXlCLHFCQUFxQiw2QkFBNkIsNkRBQTZELCtCQUErQixHQUFHLGNBQWMsbUNBQW1DLHdCQUF3QixnSEFBZ0gsOEdBQThHLGlHQUFpRyxnQ0FBZ0Msb0NBQW9DLEdBQUcsa0JBQWtCLHVCQUF1QixnRUFBZ0UseUJBQXlCLHdEQUF3RCxpREFBaUQsNERBQTRELDRHQUE0Ryx5QkFBeUIsd0JBQXdCLDRCQUE0QiwyQkFBMkIsc0JBQXNCLDhCQUE4Qix1QkFBdUIsd0JBQXdCLDBCQUEwQiw2QkFBNkIsd0JBQXdCLHlCQUF5QixxQkFBcUIsNkJBQTZCLDZEQUE2RCwrQkFBK0IsR0FBRyx3QkFBd0IsbUNBQW1DLHdCQUF3QixnSEFBZ0gsOEdBQThHLGlHQUFpRyxnQ0FBZ0Msb0NBQW9DLEdBQUcsa0JBQWtCLGdEQUFnRCxHQUFHLHdCQUF3Qix1QkFBdUIsK0RBQStELGtEQUFrRCw2REFBNkQsNEdBQTRHLEdBQUcsd0JBQXdCLHlCQUF5QixpRUFBaUUsR0FBRyxvQkFBb0IsMkJBQTJCLGlFQUFpRSx1QkFBdUIsaUVBQWlFLHlCQUF5QixxREFBcUQsaURBQWlELDREQUE0RCw0R0FBNEcseUJBQXlCLHdCQUF3Qiw0QkFBNEIsMkJBQTJCLHNCQUFzQiw4QkFBOEIsdUJBQXVCLHdCQUF3QiwwQkFBMEIsNkJBQTZCLHdCQUF3Qix5QkFBeUIscUJBQXFCLDZCQUE2Qiw2REFBNkQsK0JBQStCLEdBQUcsMEJBQTBCLG1DQUFtQyx3QkFBd0IsZ0hBQWdILDhHQUE4RyxpR0FBaUcsZ0NBQWdDLG9DQUFvQyxHQUFHLG9CQUFvQixnREFBZ0QsR0FBRywwQkFBMEIsdUJBQXVCLGlFQUFpRSxrREFBa0QsNkRBQTZELDRHQUE0RyxHQUFHLDBCQUEwQix5QkFBeUIsaUVBQWlFLEdBQUcsaUJBQWlCLHVCQUF1QiwrREFBK0QseUJBQXlCLHdEQUF3RCxpREFBaUQsNERBQTRELDRHQUE0Ryx5QkFBeUIsd0JBQXdCLDRCQUE0QiwyQkFBMkIsc0JBQXNCLDhCQUE4Qix1QkFBdUIsd0JBQXdCLDBCQUEwQiw2QkFBNkIsd0JBQXdCLHlCQUF5QixxQkFBcUIsNkJBQTZCLDZEQUE2RCwrQkFBK0IsR0FBRyx1QkFBdUIsbUNBQW1DLHdCQUF3QixnSEFBZ0gsOEdBQThHLGlHQUFpRyxnQ0FBZ0Msb0NBQW9DLEdBQUcsaUJBQWlCLGdEQUFnRCxHQUFHLHVCQUF1Qix1QkFBdUIsK0RBQStELGtEQUFrRCw2REFBNkQsNEdBQTRHLEdBQUcsdUJBQXVCLHlCQUF5QixrRUFBa0UsR0FBRyxrQkFBa0IsdUJBQXVCLCtEQUErRCx5QkFBeUIsd0RBQXdELGlEQUFpRCw0REFBNEQsNEdBQTRHLHlCQUF5Qix3QkFBd0IsNEJBQTRCLDJCQUEyQixzQkFBc0IsOEJBQThCLHVCQUF1Qix3QkFBd0IsMEJBQTBCLDZCQUE2Qix3QkFBd0IseUJBQXlCLHFCQUFxQiw2QkFBNkIsNkRBQTZELCtCQUErQixHQUFHLHdCQUF3QixtQ0FBbUMsd0JBQXdCLGdIQUFnSCw4R0FBOEcsaUdBQWlHLGdDQUFnQyxvQ0FBb0MsR0FBRyxrQkFBa0IsZ0RBQWdELEdBQUcsd0JBQXdCLHVCQUF1QiwrREFBK0Qsa0RBQWtELDZEQUE2RCw0R0FBNEcsR0FBRyx3QkFBd0IseUJBQXlCLGlFQUFpRSxHQUFHLGFBQWEsMEJBQTBCLDJCQUEyQiwwQkFBMEIsNkJBQTZCLHVCQUF1QixzQkFBc0IsR0FBRyxhQUFhLHlCQUF5QiwwQkFBMEIseUJBQXlCLDRCQUE0QixvQkFBb0Isd0JBQXdCLEdBQUcscUNBQXFDLHdCQUF3QixzQkFBc0IsMkJBQTJCLGlFQUFpRSx1QkFBdUIsaUVBQWlFLGlGQUFpRixnR0FBZ0csNEdBQTRHLGdGQUFnRixHQUFHLGtCQUFrQiw2QkFBNkIsMkJBQTJCLGlFQUFpRSx5QkFBeUIsMEJBQTBCLHlCQUF5Qiw0QkFBNEIsR0FBRyxnQkFBZ0IseUJBQXlCLDBCQUEwQix5QkFBeUIsNEJBQTRCLEdBQUcsa0JBQWtCLHFDQUFxQyxvQ0FBb0MsMEJBQTBCLDJCQUEyQixpRUFBaUUsdUJBQXVCLGlFQUFpRSx5QkFBeUIsMEJBQTBCLHlCQUF5Qiw0QkFBNEIsR0FBRyx1Q0FBdUMsb0JBQW9CLEdBQUcsNENBQTRDLDZCQUE2Qix1RUFBdUUsZ0VBQWdFLDJCQUEyQixpRUFBaUUsR0FBRyxrQkFBa0IsdUJBQXVCLGlFQUFpRSxHQUFHLGVBQWUseUJBQXlCLDBCQUEwQixzQkFBc0IseUJBQXlCLHFCQUFxQix1QkFBdUIsc0JBQXNCLHFCQUFxQiw4QkFBOEIsMkJBQTJCLHlCQUF5QixxREFBcUQsR0FBRyxlQUFlLHdCQUF3Qix5QkFBeUIsMEJBQTBCLHNCQUFzQix5QkFBeUIsd0JBQXdCLHlCQUF5Qix5QkFBeUIscURBQXFELEdBQUcscUNBQXFDLHVCQUF1QixpRUFBaUUsR0FBRywyQkFBMkIsdUJBQXVCLGlFQUFpRSxvR0FBb0csNkRBQTZELCtCQUErQixHQUFHLHVDQUF1Qyx5QkFBeUIsd0JBQXdCLDBCQUEwQiwwQkFBMEIsMkJBQTJCLHlCQUF5Qiw0QkFBNEIsdUJBQXVCLHNCQUFzQixxQkFBcUIsR0FBRyxvQkFBb0IsdUJBQXVCLGlFQUFpRSx5QkFBeUIsc0RBQXNELHlCQUF5Qix3QkFBd0IsMEJBQTBCLDBCQUEwQiwyQkFBMkIseUJBQXlCLDRCQUE0Qix1QkFBdUIsc0JBQXNCLHFCQUFxQixHQUFHLHNCQUFzQix1QkFBdUIsaUVBQWlFLHlCQUF5QixxREFBcUQseUJBQXlCLHdCQUF3QiwwQkFBMEIsMEJBQTBCLDJCQUEyQix5QkFBeUIsNEJBQTRCLHVCQUF1QixzQkFBc0IscUJBQXFCLEdBQUcsb0JBQW9CLHVCQUF1QixpRUFBaUUseUJBQXlCLHNEQUFzRCx5QkFBeUIsd0JBQXdCLDBCQUEwQiwwQkFBMEIsMkJBQTJCLHlCQUF5Qiw0QkFBNEIsdUJBQXVCLHNCQUFzQixxQkFBcUIsR0FBRyxvQkFBb0IsdUJBQXVCLGlFQUFpRSx5QkFBeUIscURBQXFELHlCQUF5Qix3QkFBd0IsMEJBQTBCLDBCQUEwQiwyQkFBMkIseUJBQXlCLDRCQUE0Qix1QkFBdUIsc0JBQXNCLHFCQUFxQixHQUFHLG1CQUFtQix1QkFBdUIsaUVBQWlFLHlCQUF5QixzREFBc0QseUJBQXlCLHdCQUF3QiwwQkFBMEIsMEJBQTBCLDJCQUEyQix5QkFBeUIsNEJBQTRCLHVCQUF1QixzQkFBc0IscUJBQXFCLEdBQUcsaUJBQWlCLHVCQUF1QixpRUFBaUUseUJBQXlCLHNEQUFzRCx5QkFBeUIsd0JBQXdCLDBCQUEwQiwwQkFBMEIsMkJBQTJCLHlCQUF5Qiw0QkFBNEIsdUJBQXVCLHNCQUFzQixxQkFBcUIsR0FBRyxnQ0FBZ0MsNEJBQTRCLGtCQUFrQixHQUFHLG9CQUFvQixzQkFBc0IsMkJBQTJCLGlFQUFpRSx1QkFBdUIsaUVBQWlFLHlCQUF5QixzREFBc0QsNEJBQTRCLGtCQUFrQixHQUFHLG9CQUFvQixzQkFBc0IsMkJBQTJCLGlFQUFpRSx1QkFBdUIsaUVBQWlFLHlCQUF5QixzREFBc0QsNEJBQTRCLGtCQUFrQixHQUFHLG1CQUFtQixzQkFBc0IsMkJBQTJCLGlFQUFpRSx1QkFBdUIsaUVBQWlFLHlCQUF5QixzREFBc0QsNEJBQTRCLGtCQUFrQixHQUFHLGlCQUFpQixzQkFBc0IsMkJBQTJCLGlFQUFpRSx1QkFBdUIsaUVBQWlFLHlCQUF5QixzREFBc0QsNEJBQTRCLGtCQUFrQixHQUFHLHlDQUF5Qyx1Q0FBdUMsR0FBRyxlQUFlLHFDQUFxQyxHQUFHLHVCQUF1QixVQUFVLGlCQUFpQixLQUFLLFFBQVEsaUJBQWlCLEtBQUssR0FBRyx3QkFBd0IsVUFBVSxrQ0FBa0MsaUJBQWlCLEtBQUssUUFBUSwrQkFBK0IsaUJBQWlCLEtBQUssR0FBRyxzQ0FBc0MsZUFBZSwrQkFBK0IsS0FBSyxzQkFBc0IsZ0NBQWdDLEtBQUssY0FBYyxzQkFBc0IsdUJBQXVCLEtBQUssZUFBZSx1QkFBdUIsNkJBQTZCLEtBQUssR0FBRyxvREFBb0Qsc0JBQXNCLHVCQUF1QixxQkFBcUIsdUJBQXVCLHdCQUF3QixHQUFHLDZCQUE2Qix1QkFBdUIsMkJBQTJCLDRCQUE0QixLQUFLLEdBQUcsOEJBQThCLHVCQUF1Qix5QkFBeUIsMEJBQTBCLEtBQUssR0FBRyxzQkFBc0Isc0JBQXNCLHlCQUF5QixHQUFHLGdDQUFnQyx3QkFBd0Isd0JBQXdCLDJCQUEyQixLQUFLLEdBQUcsd0RBQXdELDRCQUE0QixtRUFBbUUsNERBQTRELEdBQUcsZ0NBQWdDLDBEQUEwRCw4QkFBOEIsbUVBQW1FLDREQUE0RCxLQUFLLEdBQUcsd0NBQXdDLGdEQUFnRCxHQUFHLG9CQUFvQixpREFBaUQsR0FBRyxtQkFBbUIsaURBQWlELEdBQUcsa0JBQWtCLGdGQUFnRixHQUFHLHlEQUF5RCxzQkFBc0IsR0FBRyw2QkFBNkIsc0JBQXNCLEdBQUcsa0RBQWtELG1DQUFtQyx3QkFBd0IsZ0hBQWdILDhHQUE4RyxpR0FBaUcseUJBQXlCLGlFQUFpRSxnQ0FBZ0Msb0NBQW9DLEdBQUcsbURBQW1ELGVBQWUsZ0JBQWdCLEdBQUcsK0JBQStCLHdCQUF3Qix1QkFBdUIsR0FBRywrQkFBK0Isd0JBQXdCLHVCQUF1QixHQUFHLHFDQUFxQyx3QkFBd0IsR0FBRyw4RUFBOEUsZ0JBQWdCLHlCQUF5QixnRUFBZ0UsMkJBQTJCLDBEQUEwRCxLQUFLLHdCQUF3Qiw2QkFBNkIsZ0VBQWdFLHlCQUF5QixnRUFBZ0UsS0FBSyw4QkFBOEIsNkJBQTZCLGdFQUFnRSx5QkFBeUIsZ0VBQWdFLDJCQUEyQiwwREFBMEQsS0FBSyxHQUFHLDRCQUE0QiwrQkFBK0IsdUJBQXVCLEdBQUcsa0NBQWtDLCtCQUErQixjQUFjLEdBQUcsaUNBQWlDLCtCQUErQixhQUFhLEdBQUcsdUJBQXVCLCtCQUErQixvQkFBb0IsR0FBRyx1QkFBdUIsK0JBQStCLG1CQUFtQixHQUFHLGdDQUFnQywrQkFBK0IsMEJBQTBCLEdBQUcsMEJBQTBCLCtCQUErQixzQkFBc0IsR0FBRyxtQ0FBbUMsK0JBQStCLDJCQUEyQixpRUFBaUUsR0FBRyw0QkFBNEIsK0JBQStCLHVCQUF1QixpRUFBaUUsR0FBRyxrQ0FBa0MsK0JBQStCLDZCQUE2Qiw2REFBNkQsK0JBQStCLEdBQUcsd0NBQXdDLHFCQUFxQiwrQkFBK0IsR0FBRyxrQ0FBa0MsMkJBQTJCLGlFQUFpRSxHQUFHLDhCQUE4Qix1QkFBdUIsaUVBQWlFLEdBQUcsOEJBQThCLHVCQUF1QixpRUFBaUUsR0FBRyw2QkFBNkIsdUJBQXVCLGlFQUFpRSxHQUFHLCtCQUErQix1QkFBdUIsK0RBQStELEdBQUcsZ0NBQWdDLHVCQUF1QixpRUFBaUUsR0FBRyxpQ0FBaUMsdUJBQXVCLCtEQUErRCxHQUFHLGdDQUFnQyx5QkFBeUIsc0RBQXNELEdBQUcsZ0NBQWdDLHlCQUF5QixzREFBc0QsR0FBRyxnQ0FBZ0MseUJBQXlCLHNEQUFzRCxHQUFHLGdDQUFnQyx5QkFBeUIscURBQXFELEdBQUcsZ0NBQWdDLHlCQUF5QixxREFBcUQsR0FBRyxnQ0FBZ0MseUJBQXlCLHFEQUFxRCxHQUFHLGlDQUFpQyx5QkFBeUIsc0RBQXNELEdBQUcsaUNBQWlDLHlCQUF5QixxREFBcUQsR0FBRywrQkFBK0IseUJBQXlCLHNEQUFzRCxHQUFHLCtCQUErQix5QkFBeUIsc0RBQXNELEdBQUcsb0NBQW9DLHlCQUF5QixxREFBcUQsR0FBRyx1QkFBdUIsZ0JBQWdCLEdBQUcscUNBQXFDLDJCQUEyQixnRUFBZ0UsR0FBRywrQkFBK0IsbUNBQW1DLHdCQUF3QixHQUFHLHlCQUF5QixnSEFBZ0gsOEdBQThHLGlHQUFpRyxHQUFHLGdDQUFnQyx5QkFBeUIsaUVBQWlFLEdBQUcsbUNBQW1DLHlCQUF5QixpRUFBaUUsR0FBRyxnQ0FBZ0MsZ0NBQWdDLEdBQUcsMkNBQTJDLHdCQUF3QixHQUFHLG1DQUFtQyxpQkFBaUIsR0FBRyw2Q0FBNkMseUJBQXlCLHdEQUF3RCxHQUFHLGtEQUFrRCx1QkFBdUIsK0RBQStELEdBQUcsbUVBQW1FLCtCQUErQiwyQkFBMkIsb01BQW9NLEdBQUcsK0RBQStELCtCQUErQiwyQkFBMkIsaUVBQWlFLEdBQUcsNENBQTRDLG1DQUFtQyx3QkFBd0IsR0FBRyxzQ0FBc0MsZ0hBQWdILDhHQUE4RyxpR0FBaUcsR0FBRyxnREFBZ0QseUJBQXlCLGtFQUFrRSxHQUFHLDZCQUE2QixrQkFBa0IsdUJBQXVCLDBCQUEwQixLQUFLLGtCQUFrQiwyQkFBMkIsS0FBSyxrQkFBa0Isc0JBQXNCLEtBQUssbUJBQW1CLHFCQUFxQixLQUFLLGtCQUFrQixvQkFBb0IsS0FBSyxvQkFBb0Isa0JBQWtCLEtBQUssb0JBQW9CLGtCQUFrQixLQUFLLHVCQUF1Qix1QkFBdUIsS0FBSyx1QkFBdUIsdUJBQXVCLEtBQUssdUJBQXVCLHVCQUF1QixLQUFLLHNCQUFzQix1QkFBdUIsS0FBSyx5QkFBeUIsdURBQXVELEtBQUssc0JBQXNCLDBCQUEwQixLQUFLLDhCQUE4QixrQ0FBa0MsS0FBSyxpQkFBaUIsbUJBQW1CLEtBQUssaUJBQWlCLHNCQUFzQixLQUFLLGtCQUFrQiwyQkFBMkIsNEJBQTRCLEtBQUssa0JBQWtCLDJCQUEyQixLQUFLLDBCQUEwQiw2QkFBNkIsS0FBSyxxQkFBcUIsMEJBQTBCLDJCQUEyQixLQUFLLEdBQUcsNkJBQTZCLHdCQUF3QixtQ0FBbUMsS0FBSyx3QkFBd0IsbUNBQW1DLEtBQUsseUJBQXlCLHVEQUF1RCxLQUFLLHlCQUF5Qix1REFBdUQsS0FBSyx5QkFBeUIsdURBQXVELEtBQUssR0FBRyw4QkFBOEIsd0JBQXdCLG1DQUFtQyxLQUFLLG1CQUFtQix3QkFBd0IsS0FBSyxtQkFBbUIseUJBQXlCLEtBQUssbUJBQW1CLHlCQUF5QixLQUFLLG1CQUFtQiwwQkFBMEIsS0FBSyxtQkFBbUIscUJBQXFCLEtBQUssb0JBQW9CLG9CQUFvQixLQUFLLHFCQUFxQixpQkFBaUIsS0FBSyxxQkFBcUIsaUJBQWlCLEtBQUsseUJBQXlCLHVEQUF1RCxLQUFLLHlCQUF5Qix1REFBdUQsS0FBSyx5QkFBeUIsdURBQXVELEtBQUssc0JBQXNCLDBCQUEwQixLQUFLLGtCQUFrQix5QkFBeUIsMEJBQTBCLEtBQUssR0FBRyx1REFBdUQsY0FBYyxHQUFHLDBEQUEwRCxlQUFlLEdBQUcsd0RBQXdELGVBQWUsR0FBRyx3REFBd0QsbUJBQW1CLEdBQUcsMkRBQTJELGdCQUFnQixHQUFHLHFEQUFxRCxxQkFBcUIsR0FBRyxxREFBcUQseUJBQXlCLEdBQUcscURBQXFELHdCQUF3QixHQUFHLHFEQUFxRCx5QkFBeUIsR0FBRyxxREFBcUQsc0JBQXNCLEdBQUcscURBQXFELDBCQUEwQixHQUFHLHFEQUFxRCx5QkFBeUIsR0FBRyxxREFBcUQsdUJBQXVCLEdBQUcscURBQXFELDBCQUEwQixHQUFHLCtEQUErRCwyQkFBMkIsb01BQW9NLEdBQUcsaUVBQWlFLDJCQUEyQixvTUFBb00sR0FBRyxrR0FBa0csNEJBQTRCLEdBQUcsc0RBQXNELHlCQUF5QixHQUFHLHFEQUFxRCwwQkFBMEIsR0FBRyxxREFBcUQsdUJBQXVCLEdBQUcsc0RBQXNELDBCQUEwQixHQUFHLHFEQUFxRCwyQkFBMkIsR0FBRyxTQUFTLG1GQUFtRixZQUFZLGFBQWEsYUFBYSxhQUFhLFlBQVksWUFBWSxZQUFZLFlBQVksWUFBWSxZQUFZLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLFlBQVksYUFBYSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxZQUFZLFlBQVksWUFBWSxZQUFZLFlBQVksWUFBWSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxZQUFZLGFBQWEsWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sYUFBYSxjQUFjLE9BQU8sUUFBUSx3QkFBd0Isd0JBQXdCLHlCQUF5Qix5QkFBeUIsTUFBTSxPQUFPLFlBQVksTUFBTSxhQUFhLE9BQU8sT0FBTyx3QkFBd0IseUJBQXlCLHlCQUF5QixZQUFZLHdCQUF3Qix5QkFBeUIseUJBQXlCLHlCQUF5Qix5QkFBeUIsTUFBTSxRQUFRLE9BQU8sTUFBTSx1QkFBdUIseUJBQXlCLE1BQU0sU0FBUyxPQUFPLE1BQU0sdUJBQXVCLHdCQUF3Qix5QkFBeUIsTUFBTSxPQUFPLE9BQU8sTUFBTSxZQUFZLGFBQWEsTUFBTSxPQUFPLE9BQU8sV0FBVyxZQUFZLGFBQWEsTUFBTSxPQUFPLE9BQU8sTUFBTSxXQUFXLGFBQWEsTUFBTSxPQUFPLE9BQU8sT0FBTyxZQUFZLE1BQU0sVUFBVSxPQUFPLFNBQVMsd0JBQXdCLHlCQUF5Qix5QkFBeUIsd0JBQXdCLE1BQU0sT0FBTyxPQUFPLE1BQU0sV0FBVyxNQUFNLE9BQU8sT0FBTyxPQUFPLFdBQVcsWUFBWSxhQUFhLGFBQWEsTUFBTSxNQUFNLFdBQVcsTUFBTSxNQUFNLFdBQVcsTUFBTSxTQUFTLE9BQU8sTUFBTSx1QkFBdUIseUJBQXlCLHlCQUF5QixNQUFNLFNBQVMsT0FBTyxVQUFVLHdCQUF3Qix5QkFBeUIseUJBQXlCLHdCQUF3Qix5QkFBeUIseUJBQXlCLHlCQUF5Qix3QkFBd0Isd0JBQXdCLHdCQUF3QixNQUFNLE9BQU8sT0FBTyxPQUFPLFlBQVksTUFBTSxRQUFRLE9BQU8sU0FBUyx3QkFBd0IseUJBQXlCLHlCQUF5QixNQUFNLE9BQU8sT0FBTyxNQUFNLFdBQVcsTUFBTSxPQUFPLE9BQU8sTUFBTSxZQUFZLE1BQU0sT0FBTyxPQUFPLE1BQU0sWUFBWSxNQUFNLE9BQU8sT0FBTyxPQUFPLFdBQVcsTUFBTSxRQUFRLE9BQU8sTUFBTSx3QkFBd0IseUJBQXlCLE1BQU0sT0FBTyxPQUFPLE1BQU0sWUFBWSxNQUFNLFFBQVEsT0FBTyxNQUFNLHdCQUF3Qix3QkFBd0IsTUFBTSxPQUFPLE9BQU8sTUFBTSxZQUFZLE1BQU0sT0FBTyxPQUFPLGtCQUFrQixXQUFXLE1BQU0sTUFBTSxXQUFXLFlBQVksTUFBTSxNQUFNLFdBQVcsTUFBTSxRQUFRLFlBQVksWUFBWSxZQUFZLE1BQU0sT0FBTyxNQUFNLE1BQU0sV0FBVyxNQUFNLE9BQU8sT0FBTyxNQUFNLFlBQVksTUFBTSxRQUFRLE9BQU8sTUFBTSx1QkFBdUIsd0JBQXdCLE1BQU0sT0FBTyx1QkFBdUIsd0JBQXdCLE1BQU0sT0FBTyxPQUFPLE9BQU8sV0FBVyxNQUFNLE9BQU8sTUFBTSxNQUFNLFdBQVcsTUFBTSxTQUFTLE9BQU8sYUFBYSx1QkFBdUIseUJBQXlCLE1BQU0sT0FBTyxPQUFPLE9BQU8sV0FBVyxZQUFZLE1BQU0sYUFBYSxNQUFNLFdBQVcsTUFBTSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxZQUFZLGFBQWEsYUFBYSxNQUFNLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sV0FBVyxNQUFNLE9BQU8sTUFBTSxXQUFXLE1BQU0sT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sV0FBVyxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxZQUFZLFlBQVksWUFBWSxZQUFZLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLEtBQUssWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE9BQU8sS0FBSyxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxLQUFLLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLFlBQVksYUFBYSxZQUFZLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsWUFBWSxhQUFhLGFBQWEsS0FBSyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sTUFBTSxNQUFNLFdBQVcsTUFBTSxNQUFNLE1BQU0sV0FBVyxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFdBQVcsTUFBTSxNQUFNLE1BQU0sWUFBWSxZQUFZLFlBQVksWUFBWSxZQUFZLGFBQWEsYUFBYSxhQUFhLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFdBQVcsTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxPQUFPLEtBQUssS0FBSyxNQUFNLEtBQUssTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssT0FBTyxhQUFhLGFBQWEsY0FBYyxhQUFhLE1BQU0sWUFBWSxPQUFPLEtBQUssWUFBWSxhQUFhLHVCQUF1Qix1QkFBdUIsYUFBYSxhQUFhLGFBQWEsT0FBTyxZQUFZLE1BQU0sWUFBWSxPQUFPLEtBQUssWUFBWSxPQUFPLFlBQVksTUFBTSxZQUFZLE9BQU8sS0FBSyxZQUFZLE9BQU8sS0FBSyxZQUFZLE9BQU8sWUFBWSxNQUFNLFVBQVUsVUFBVSxNQUFNLEtBQUssWUFBWSxhQUFhLE9BQU8sS0FBSyxZQUFZLGFBQWEsT0FBTyxLQUFLLFlBQVksT0FBTyxZQUFZLE1BQU0sV0FBVyxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLE9BQU8sS0FBSyxZQUFZLE1BQU0sTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxhQUFhLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLE1BQU0sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxNQUFNLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxNQUFNLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxNQUFNLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLFlBQVksTUFBTSxPQUFPLGFBQWEsTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxhQUFhLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxhQUFhLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLGFBQWEsS0FBSyxZQUFZLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sYUFBYSxNQUFNLFlBQVksT0FBTyxLQUFLLFlBQVksT0FBTyxLQUFLLEtBQUssVUFBVSxLQUFLLEtBQUssVUFBVSxLQUFLLE1BQU0sS0FBSyxLQUFLLFlBQVksV0FBVyxLQUFLLEtBQUssWUFBWSxXQUFXLEtBQUssTUFBTSxZQUFZLE1BQU0sS0FBSyxZQUFZLE9BQU8sS0FBSyxZQUFZLE9BQU8sS0FBSyxVQUFVLFlBQVksT0FBTyxLQUFLLFlBQVksYUFBYSxNQUFNLE1BQU0sWUFBWSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLE9BQU8sS0FBSyxZQUFZLE1BQU0sTUFBTSxLQUFLLE9BQU8sS0FBSyxZQUFZLE1BQU0sTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE9BQU8sT0FBTyxLQUFLLFlBQVksTUFBTSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxPQUFPLE9BQU8sS0FBSyxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sYUFBYSxNQUFNLFlBQVksT0FBTyxLQUFLLFlBQVksT0FBTyxLQUFLLFlBQVksT0FBTyxLQUFLLFlBQVksT0FBTyxZQUFZLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLGFBQWEsTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLGFBQWEsTUFBTSxVQUFVLFVBQVUsTUFBTSxLQUFLLFlBQVksYUFBYSxPQUFPLEtBQUssWUFBWSxhQUFhLE9BQU8sS0FBSyxZQUFZLE9BQU8sWUFBWSxNQUFNLEtBQUssWUFBWSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sT0FBTyxLQUFLLFlBQVksTUFBTSxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sWUFBWSxNQUFNLE9BQU8sTUFBTSxLQUFLLE9BQU8sTUFBTSxLQUFLLE9BQU8sTUFBTSxZQUFZLE1BQU0sTUFBTSxLQUFLLE9BQU8sS0FBSyxLQUFLLE9BQU8sTUFBTSxLQUFLLE9BQU8sTUFBTSxLQUFLLE9BQU8sTUFBTSxLQUFLLE9BQU8sTUFBTSxLQUFLLE1BQU0sS0FBSyxPQUFPLEtBQUssS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sWUFBWSxNQUFNLE1BQU0sS0FBSyxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLHFEQUFxRCxtQ0FBbUMsa0NBQWtDLDZGQUE2RixJQUFJLElBQUksSUFBSSxtQkFBbUIsdUVBQXVFLElBQUksSUFBSSxJQUFJLG1CQUFtQiwrQ0FBK0MsNEJBQTRCLEdBQUcsVUFBVSxtRUFBbUUscUJBQXFCLCtCQUErQixvQ0FBb0MsdUNBQXVDLHdDQUF3Qyx1Q0FBdUMsR0FBRyxxQ0FBcUMscUNBQXFDLEdBQUcsbUJBQW1CLHFDQUFxQyxHQUFHLHNDQUFzQyxzQkFBc0IsR0FBRyxvQ0FBb0MscUJBQXFCLEdBQUcscUNBQXFDLHNCQUFzQixHQUFHLGlEQUFpRCxlQUFlLGdCQUFnQixHQUFHLCtCQUErQix3QkFBd0IsdUJBQXVCLEdBQUcsK0JBQStCLHdCQUF3Qix1QkFBdUIsR0FBRyxxQ0FBcUMsd0JBQXdCLEdBQUcsMkNBQTJDLHVQQUF1UCxHQUFHLHlCQUF5QixpRkFBaUYsR0FBRyxpQkFBaUIsNERBQTRELEdBQUcsaUJBQWlCLDBDQUEwQyxHQUFHLHNDQUFzQyxxT0FBcU8sR0FBRyxrQkFBa0IscUhBQXFILEdBQUcsb0JBQW9CLHVJQUF1SSxHQUFHLGlCQUFpQiw4R0FBOEcsR0FBRyxrQkFBa0Isa0hBQWtILEdBQUcsYUFBYSwrQkFBK0IsR0FBRyxhQUFhLCtCQUErQixHQUFHLHFDQUFxQyxnRkFBZ0YsR0FBRyxrQkFBa0IsaURBQWlELEdBQUcsZ0JBQWdCLHFCQUFxQixHQUFHLGtCQUFrQixvRkFBb0YsR0FBRyx1Q0FBdUMsa0RBQWtELEdBQUcsa0JBQWtCLGdDQUFnQyxHQUFHLGVBQWUsa0dBQWtHLEdBQUcsZUFBZSxpRUFBaUUsR0FBRyxxQ0FBcUMsZ0NBQWdDLEdBQUcsMkJBQTJCLHdEQUF3RCxHQUFHLHVDQUF1QywrRUFBK0UsR0FBRyxvQkFBb0IsaURBQWlELEdBQUcsc0JBQXNCLGlEQUFpRCxHQUFHLG9CQUFvQiwrQ0FBK0MsR0FBRyxvQkFBb0IsK0NBQStDLEdBQUcsbUJBQW1CLHlDQUF5QyxHQUFHLGlCQUFpQixpREFBaUQsR0FBRyxnQ0FBZ0MsMEJBQTBCLEdBQUcsb0JBQW9CLG9FQUFvRSxHQUFHLG9CQUFvQix1RUFBdUUsR0FBRyxtQkFBbUIsOERBQThELEdBQUcsaUJBQWlCLGlFQUFpRSxHQUFHLHlDQUF5Qyx1Q0FBdUMsR0FBRyxlQUFlLHFDQUFxQyxHQUFHLHVCQUF1QixVQUFVLGlCQUFpQixLQUFLLFFBQVEsaUJBQWlCLEtBQUssR0FBRyx3QkFBd0IsVUFBVSxrQ0FBa0MsaUJBQWlCLEtBQUssUUFBUSwrQkFBK0IsaUJBQWlCLEtBQUssR0FBRyxzQ0FBc0MsZUFBZSwrQkFBK0IsS0FBSyxzQkFBc0IsZ0NBQWdDLEtBQUssY0FBYyxzQkFBc0IsdUJBQXVCLEtBQUssZUFBZSx1QkFBdUIsNkJBQTZCLEtBQUssR0FBRyxvREFBb0Qsa0RBQWtELEdBQUcsc0JBQXNCLHlCQUF5QixHQUFHLHNCQUFzQixrQ0FBa0MsR0FBRyx3Q0FBd0MsZ0RBQWdELEdBQUcsb0JBQW9CLGlEQUFpRCxHQUFHLG1CQUFtQixpREFBaUQsR0FBRyxrQkFBa0IsZ0ZBQWdGLEdBQUcseURBQXlELHNCQUFzQixHQUFHLDZCQUE2QixzQkFBc0IsR0FBRyw0Q0FBNEMsbUhBQW1ILEdBQUcsbURBQW1ELGVBQWUsZ0JBQWdCLEdBQUcsK0JBQStCLHdCQUF3Qix1QkFBdUIsR0FBRywrQkFBK0Isd0JBQXdCLHVCQUF1QixHQUFHLHFDQUFxQyx3QkFBd0IsR0FBRyw4RUFBOEUsZ0JBQWdCLDhDQUE4QyxLQUFLLHdCQUF3QiwrQ0FBK0MsS0FBSyw4QkFBOEIsaUVBQWlFLEtBQUssR0FBRyxxQkFBcUI7QUFDcCt5RztBQUNBLCtEQUFlLHVCQUF1QixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3N0eWxlcy9nbG9iYWxzLmNzcz84YzUzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEltcG9ydHNcbmltcG9ydCBfX19DU1NfTE9BREVSX0FQSV9JTVBPUlRfX18gZnJvbSBcIi4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL2Nzcy1sb2FkZXIvc3JjL3J1bnRpbWUvYXBpLmpzXCI7XG52YXIgX19fQ1NTX0xPQURFUl9FWFBPUlRfX18gPSBfX19DU1NfTE9BREVSX0FQSV9JTVBPUlRfX18odHJ1ZSk7XG4vLyBNb2R1bGVcbl9fX0NTU19MT0FERVJfRVhQT1JUX19fLnB1c2goW21vZHVsZS5pZCwgXCIqLCA6OmJlZm9yZSwgOjphZnRlciB7XFxuICAtLXR3LWJvcmRlci1zcGFjaW5nLXg6IDA7XFxuICAtLXR3LWJvcmRlci1zcGFjaW5nLXk6IDA7XFxuICAtLXR3LXRyYW5zbGF0ZS14OiAwO1xcbiAgLS10dy10cmFuc2xhdGUteTogMDtcXG4gIC0tdHctcm90YXRlOiAwO1xcbiAgLS10dy1za2V3LXg6IDA7XFxuICAtLXR3LXNrZXcteTogMDtcXG4gIC0tdHctc2NhbGUteDogMTtcXG4gIC0tdHctc2NhbGUteTogMTtcXG4gIC0tdHctcGFuLXg6ICA7XFxuICAtLXR3LXBhbi15OiAgO1xcbiAgLS10dy1waW5jaC16b29tOiAgO1xcbiAgLS10dy1zY3JvbGwtc25hcC1zdHJpY3RuZXNzOiBwcm94aW1pdHk7XFxuICAtLXR3LWdyYWRpZW50LWZyb20tcG9zaXRpb246ICA7XFxuICAtLXR3LWdyYWRpZW50LXZpYS1wb3NpdGlvbjogIDtcXG4gIC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb246ICA7XFxuICAtLXR3LW9yZGluYWw6ICA7XFxuICAtLXR3LXNsYXNoZWQtemVybzogIDtcXG4gIC0tdHctbnVtZXJpYy1maWd1cmU6ICA7XFxuICAtLXR3LW51bWVyaWMtc3BhY2luZzogIDtcXG4gIC0tdHctbnVtZXJpYy1mcmFjdGlvbjogIDtcXG4gIC0tdHctcmluZy1pbnNldDogIDtcXG4gIC0tdHctcmluZy1vZmZzZXQtd2lkdGg6IDBweDtcXG4gIC0tdHctcmluZy1vZmZzZXQtY29sb3I6ICNmZmY7XFxuICAtLXR3LXJpbmctY29sb3I6IHJnYig1OSAxMzAgMjQ2IC8gMC41KTtcXG4gIC0tdHctcmluZy1vZmZzZXQtc2hhZG93OiAwIDAgIzAwMDA7XFxuICAtLXR3LXJpbmctc2hhZG93OiAwIDAgIzAwMDA7XFxuICAtLXR3LXNoYWRvdzogMCAwICMwMDAwO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCAwICMwMDAwO1xcbiAgLS10dy1ibHVyOiAgO1xcbiAgLS10dy1icmlnaHRuZXNzOiAgO1xcbiAgLS10dy1jb250cmFzdDogIDtcXG4gIC0tdHctZ3JheXNjYWxlOiAgO1xcbiAgLS10dy1odWUtcm90YXRlOiAgO1xcbiAgLS10dy1pbnZlcnQ6ICA7XFxuICAtLXR3LXNhdHVyYXRlOiAgO1xcbiAgLS10dy1zZXBpYTogIDtcXG4gIC0tdHctZHJvcC1zaGFkb3c6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWJsdXI6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWJyaWdodG5lc3M6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWNvbnRyYXN0OiAgO1xcbiAgLS10dy1iYWNrZHJvcC1ncmF5c2NhbGU6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWh1ZS1yb3RhdGU6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWludmVydDogIDtcXG4gIC0tdHctYmFja2Ryb3Atb3BhY2l0eTogIDtcXG4gIC0tdHctYmFja2Ryb3Atc2F0dXJhdGU6ICA7XFxuICAtLXR3LWJhY2tkcm9wLXNlcGlhOiAgO1xcbiAgLS10dy1jb250YWluLXNpemU6ICA7XFxuICAtLXR3LWNvbnRhaW4tbGF5b3V0OiAgO1xcbiAgLS10dy1jb250YWluLXBhaW50OiAgO1xcbiAgLS10dy1jb250YWluLXN0eWxlOiAgO1xcbn1cXG5cXG46OmJhY2tkcm9wIHtcXG4gIC0tdHctYm9yZGVyLXNwYWNpbmcteDogMDtcXG4gIC0tdHctYm9yZGVyLXNwYWNpbmcteTogMDtcXG4gIC0tdHctdHJhbnNsYXRlLXg6IDA7XFxuICAtLXR3LXRyYW5zbGF0ZS15OiAwO1xcbiAgLS10dy1yb3RhdGU6IDA7XFxuICAtLXR3LXNrZXcteDogMDtcXG4gIC0tdHctc2tldy15OiAwO1xcbiAgLS10dy1zY2FsZS14OiAxO1xcbiAgLS10dy1zY2FsZS15OiAxO1xcbiAgLS10dy1wYW4teDogIDtcXG4gIC0tdHctcGFuLXk6ICA7XFxuICAtLXR3LXBpbmNoLXpvb206ICA7XFxuICAtLXR3LXNjcm9sbC1zbmFwLXN0cmljdG5lc3M6IHByb3hpbWl0eTtcXG4gIC0tdHctZ3JhZGllbnQtZnJvbS1wb3NpdGlvbjogIDtcXG4gIC0tdHctZ3JhZGllbnQtdmlhLXBvc2l0aW9uOiAgO1xcbiAgLS10dy1ncmFkaWVudC10by1wb3NpdGlvbjogIDtcXG4gIC0tdHctb3JkaW5hbDogIDtcXG4gIC0tdHctc2xhc2hlZC16ZXJvOiAgO1xcbiAgLS10dy1udW1lcmljLWZpZ3VyZTogIDtcXG4gIC0tdHctbnVtZXJpYy1zcGFjaW5nOiAgO1xcbiAgLS10dy1udW1lcmljLWZyYWN0aW9uOiAgO1xcbiAgLS10dy1yaW5nLWluc2V0OiAgO1xcbiAgLS10dy1yaW5nLW9mZnNldC13aWR0aDogMHB4O1xcbiAgLS10dy1yaW5nLW9mZnNldC1jb2xvcjogI2ZmZjtcXG4gIC0tdHctcmluZy1jb2xvcjogcmdiKDU5IDEzMCAyNDYgLyAwLjUpO1xcbiAgLS10dy1yaW5nLW9mZnNldC1zaGFkb3c6IDAgMCAjMDAwMDtcXG4gIC0tdHctcmluZy1zaGFkb3c6IDAgMCAjMDAwMDtcXG4gIC0tdHctc2hhZG93OiAwIDAgIzAwMDA7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDAgIzAwMDA7XFxuICAtLXR3LWJsdXI6ICA7XFxuICAtLXR3LWJyaWdodG5lc3M6ICA7XFxuICAtLXR3LWNvbnRyYXN0OiAgO1xcbiAgLS10dy1ncmF5c2NhbGU6ICA7XFxuICAtLXR3LWh1ZS1yb3RhdGU6ICA7XFxuICAtLXR3LWludmVydDogIDtcXG4gIC0tdHctc2F0dXJhdGU6ICA7XFxuICAtLXR3LXNlcGlhOiAgO1xcbiAgLS10dy1kcm9wLXNoYWRvdzogIDtcXG4gIC0tdHctYmFja2Ryb3AtYmx1cjogIDtcXG4gIC0tdHctYmFja2Ryb3AtYnJpZ2h0bmVzczogIDtcXG4gIC0tdHctYmFja2Ryb3AtY29udHJhc3Q6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWdyYXlzY2FsZTogIDtcXG4gIC0tdHctYmFja2Ryb3AtaHVlLXJvdGF0ZTogIDtcXG4gIC0tdHctYmFja2Ryb3AtaW52ZXJ0OiAgO1xcbiAgLS10dy1iYWNrZHJvcC1vcGFjaXR5OiAgO1xcbiAgLS10dy1iYWNrZHJvcC1zYXR1cmF0ZTogIDtcXG4gIC0tdHctYmFja2Ryb3Atc2VwaWE6ICA7XFxuICAtLXR3LWNvbnRhaW4tc2l6ZTogIDtcXG4gIC0tdHctY29udGFpbi1sYXlvdXQ6ICA7XFxuICAtLXR3LWNvbnRhaW4tcGFpbnQ6ICA7XFxuICAtLXR3LWNvbnRhaW4tc3R5bGU6ICA7XFxufS8qXFxuISB0YWlsd2luZGNzcyB2My40LjE3IHwgTUlUIExpY2Vuc2UgfCBodHRwczovL3RhaWx3aW5kY3NzLmNvbVxcbiovLypcXG4xLiBQcmV2ZW50IHBhZGRpbmcgYW5kIGJvcmRlciBmcm9tIGFmZmVjdGluZyBlbGVtZW50IHdpZHRoLiAoaHR0cHM6Ly9naXRodWIuY29tL21vemRldnMvY3NzcmVtZWR5L2lzc3Vlcy80KVxcbjIuIEFsbG93IGFkZGluZyBhIGJvcmRlciB0byBhbiBlbGVtZW50IGJ5IGp1c3QgYWRkaW5nIGEgYm9yZGVyLXdpZHRoLiAoaHR0cHM6Ly9naXRodWIuY29tL3RhaWx3aW5kY3NzL3RhaWx3aW5kY3NzL3B1bGwvMTE2KVxcbiovXFxuXFxuKixcXG46OmJlZm9yZSxcXG46OmFmdGVyIHtcXG4gIGJveC1zaXppbmc6IGJvcmRlci1ib3g7IC8qIDEgKi9cXG4gIGJvcmRlci13aWR0aDogMDsgLyogMiAqL1xcbiAgYm9yZGVyLXN0eWxlOiBzb2xpZDsgLyogMiAqL1xcbiAgYm9yZGVyLWNvbG9yOiAjZTVlN2ViOyAvKiAyICovXFxufVxcblxcbjo6YmVmb3JlLFxcbjo6YWZ0ZXIge1xcbiAgLS10dy1jb250ZW50OiAnJztcXG59XFxuXFxuLypcXG4xLiBVc2UgYSBjb25zaXN0ZW50IHNlbnNpYmxlIGxpbmUtaGVpZ2h0IGluIGFsbCBicm93c2Vycy5cXG4yLiBQcmV2ZW50IGFkanVzdG1lbnRzIG9mIGZvbnQgc2l6ZSBhZnRlciBvcmllbnRhdGlvbiBjaGFuZ2VzIGluIGlPUy5cXG4zLiBVc2UgYSBtb3JlIHJlYWRhYmxlIHRhYiBzaXplLlxcbjQuIFVzZSB0aGUgdXNlcidzIGNvbmZpZ3VyZWQgYHNhbnNgIGZvbnQtZmFtaWx5IGJ5IGRlZmF1bHQuXFxuNS4gVXNlIHRoZSB1c2VyJ3MgY29uZmlndXJlZCBgc2Fuc2AgZm9udC1mZWF0dXJlLXNldHRpbmdzIGJ5IGRlZmF1bHQuXFxuNi4gVXNlIHRoZSB1c2VyJ3MgY29uZmlndXJlZCBgc2Fuc2AgZm9udC12YXJpYXRpb24tc2V0dGluZ3MgYnkgZGVmYXVsdC5cXG43LiBEaXNhYmxlIHRhcCBoaWdobGlnaHRzIG9uIGlPU1xcbiovXFxuXFxuaHRtbCxcXG46aG9zdCB7XFxuICBsaW5lLWhlaWdodDogMS41OyAvKiAxICovXFxuICAtd2Via2l0LXRleHQtc2l6ZS1hZGp1c3Q6IDEwMCU7IC8qIDIgKi9cXG4gIC1tb3otdGFiLXNpemU6IDQ7IC8qIDMgKi9cXG4gIC1vLXRhYi1zaXplOiA0O1xcbiAgICAgdGFiLXNpemU6IDQ7IC8qIDMgKi9cXG4gIGZvbnQtZmFtaWx5OiBJbnRlciwgdWktc2Fucy1zZXJpZiwgc3lzdGVtLXVpLCBzYW5zLXNlcmlmOyAvKiA0ICovXFxuICBmb250LWZlYXR1cmUtc2V0dGluZ3M6IG5vcm1hbDsgLyogNSAqL1xcbiAgZm9udC12YXJpYXRpb24tc2V0dGluZ3M6IG5vcm1hbDsgLyogNiAqL1xcbiAgLXdlYmtpdC10YXAtaGlnaGxpZ2h0LWNvbG9yOiB0cmFuc3BhcmVudDsgLyogNyAqL1xcbn1cXG5cXG4vKlxcbjEuIFJlbW92ZSB0aGUgbWFyZ2luIGluIGFsbCBicm93c2Vycy5cXG4yLiBJbmhlcml0IGxpbmUtaGVpZ2h0IGZyb20gYGh0bWxgIHNvIHVzZXJzIGNhbiBzZXQgdGhlbSBhcyBhIGNsYXNzIGRpcmVjdGx5IG9uIHRoZSBgaHRtbGAgZWxlbWVudC5cXG4qL1xcblxcbmJvZHkge1xcbiAgbWFyZ2luOiAwOyAvKiAxICovXFxuICBsaW5lLWhlaWdodDogaW5oZXJpdDsgLyogMiAqL1xcbn1cXG5cXG4vKlxcbjEuIEFkZCB0aGUgY29ycmVjdCBoZWlnaHQgaW4gRmlyZWZveC5cXG4yLiBDb3JyZWN0IHRoZSBpbmhlcml0YW5jZSBvZiBib3JkZXIgY29sb3IgaW4gRmlyZWZveC4gKGh0dHBzOi8vYnVnemlsbGEubW96aWxsYS5vcmcvc2hvd19idWcuY2dpP2lkPTE5MDY1NSlcXG4zLiBFbnN1cmUgaG9yaXpvbnRhbCBydWxlcyBhcmUgdmlzaWJsZSBieSBkZWZhdWx0LlxcbiovXFxuXFxuaHIge1xcbiAgaGVpZ2h0OiAwOyAvKiAxICovXFxuICBjb2xvcjogaW5oZXJpdDsgLyogMiAqL1xcbiAgYm9yZGVyLXRvcC13aWR0aDogMXB4OyAvKiAzICovXFxufVxcblxcbi8qXFxuQWRkIHRoZSBjb3JyZWN0IHRleHQgZGVjb3JhdGlvbiBpbiBDaHJvbWUsIEVkZ2UsIGFuZCBTYWZhcmkuXFxuKi9cXG5cXG5hYmJyOndoZXJlKFt0aXRsZV0pIHtcXG4gIC13ZWJraXQtdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmUgZG90dGVkO1xcbiAgICAgICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZSBkb3R0ZWQ7XFxufVxcblxcbi8qXFxuUmVtb3ZlIHRoZSBkZWZhdWx0IGZvbnQgc2l6ZSBhbmQgd2VpZ2h0IGZvciBoZWFkaW5ncy5cXG4qL1xcblxcbmgxLFxcbmgyLFxcbmgzLFxcbmg0LFxcbmg1LFxcbmg2IHtcXG4gIGZvbnQtc2l6ZTogaW5oZXJpdDtcXG4gIGZvbnQtd2VpZ2h0OiBpbmhlcml0O1xcbn1cXG5cXG4vKlxcblJlc2V0IGxpbmtzIHRvIG9wdGltaXplIGZvciBvcHQtaW4gc3R5bGluZyBpbnN0ZWFkIG9mIG9wdC1vdXQuXFxuKi9cXG5cXG5hIHtcXG4gIGNvbG9yOiBpbmhlcml0O1xcbiAgdGV4dC1kZWNvcmF0aW9uOiBpbmhlcml0O1xcbn1cXG5cXG4vKlxcbkFkZCB0aGUgY29ycmVjdCBmb250IHdlaWdodCBpbiBFZGdlIGFuZCBTYWZhcmkuXFxuKi9cXG5cXG5iLFxcbnN0cm9uZyB7XFxuICBmb250LXdlaWdodDogYm9sZGVyO1xcbn1cXG5cXG4vKlxcbjEuIFVzZSB0aGUgdXNlcidzIGNvbmZpZ3VyZWQgYG1vbm9gIGZvbnQtZmFtaWx5IGJ5IGRlZmF1bHQuXFxuMi4gVXNlIHRoZSB1c2VyJ3MgY29uZmlndXJlZCBgbW9ub2AgZm9udC1mZWF0dXJlLXNldHRpbmdzIGJ5IGRlZmF1bHQuXFxuMy4gVXNlIHRoZSB1c2VyJ3MgY29uZmlndXJlZCBgbW9ub2AgZm9udC12YXJpYXRpb24tc2V0dGluZ3MgYnkgZGVmYXVsdC5cXG40LiBDb3JyZWN0IHRoZSBvZGQgYGVtYCBmb250IHNpemluZyBpbiBhbGwgYnJvd3NlcnMuXFxuKi9cXG5cXG5jb2RlLFxcbmtiZCxcXG5zYW1wLFxcbnByZSB7XFxuICBmb250LWZhbWlseTogdWktbW9ub3NwYWNlLCBTRk1vbm8tUmVndWxhciwgTWVubG8sIE1vbmFjbywgQ29uc29sYXMsIFxcXCJMaWJlcmF0aW9uIE1vbm9cXFwiLCBcXFwiQ291cmllciBOZXdcXFwiLCBtb25vc3BhY2U7IC8qIDEgKi9cXG4gIGZvbnQtZmVhdHVyZS1zZXR0aW5nczogbm9ybWFsOyAvKiAyICovXFxuICBmb250LXZhcmlhdGlvbi1zZXR0aW5nczogbm9ybWFsOyAvKiAzICovXFxuICBmb250LXNpemU6IDFlbTsgLyogNCAqL1xcbn1cXG5cXG4vKlxcbkFkZCB0aGUgY29ycmVjdCBmb250IHNpemUgaW4gYWxsIGJyb3dzZXJzLlxcbiovXFxuXFxuc21hbGwge1xcbiAgZm9udC1zaXplOiA4MCU7XFxufVxcblxcbi8qXFxuUHJldmVudCBgc3ViYCBhbmQgYHN1cGAgZWxlbWVudHMgZnJvbSBhZmZlY3RpbmcgdGhlIGxpbmUgaGVpZ2h0IGluIGFsbCBicm93c2Vycy5cXG4qL1xcblxcbnN1YixcXG5zdXAge1xcbiAgZm9udC1zaXplOiA3NSU7XFxuICBsaW5lLWhlaWdodDogMDtcXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcXG4gIHZlcnRpY2FsLWFsaWduOiBiYXNlbGluZTtcXG59XFxuXFxuc3ViIHtcXG4gIGJvdHRvbTogLTAuMjVlbTtcXG59XFxuXFxuc3VwIHtcXG4gIHRvcDogLTAuNWVtO1xcbn1cXG5cXG4vKlxcbjEuIFJlbW92ZSB0ZXh0IGluZGVudGF0aW9uIGZyb20gdGFibGUgY29udGVudHMgaW4gQ2hyb21lIGFuZCBTYWZhcmkuIChodHRwczovL2J1Z3MuY2hyb21pdW0ub3JnL3AvY2hyb21pdW0vaXNzdWVzL2RldGFpbD9pZD05OTkwODgsIGh0dHBzOi8vYnVncy53ZWJraXQub3JnL3Nob3dfYnVnLmNnaT9pZD0yMDEyOTcpXFxuMi4gQ29ycmVjdCB0YWJsZSBib3JkZXIgY29sb3IgaW5oZXJpdGFuY2UgaW4gYWxsIENocm9tZSBhbmQgU2FmYXJpLiAoaHR0cHM6Ly9idWdzLmNocm9taXVtLm9yZy9wL2Nocm9taXVtL2lzc3Vlcy9kZXRhaWw/aWQ9OTM1NzI5LCBodHRwczovL2J1Z3Mud2Via2l0Lm9yZy9zaG93X2J1Zy5jZ2k/aWQ9MTk1MDE2KVxcbjMuIFJlbW92ZSBnYXBzIGJldHdlZW4gdGFibGUgYm9yZGVycyBieSBkZWZhdWx0LlxcbiovXFxuXFxudGFibGUge1xcbiAgdGV4dC1pbmRlbnQ6IDA7IC8qIDEgKi9cXG4gIGJvcmRlci1jb2xvcjogaW5oZXJpdDsgLyogMiAqL1xcbiAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsgLyogMyAqL1xcbn1cXG5cXG4vKlxcbjEuIENoYW5nZSB0aGUgZm9udCBzdHlsZXMgaW4gYWxsIGJyb3dzZXJzLlxcbjIuIFJlbW92ZSB0aGUgbWFyZ2luIGluIEZpcmVmb3ggYW5kIFNhZmFyaS5cXG4zLiBSZW1vdmUgZGVmYXVsdCBwYWRkaW5nIGluIGFsbCBicm93c2Vycy5cXG4qL1xcblxcbmJ1dHRvbixcXG5pbnB1dCxcXG5vcHRncm91cCxcXG5zZWxlY3QsXFxudGV4dGFyZWEge1xcbiAgZm9udC1mYW1pbHk6IGluaGVyaXQ7IC8qIDEgKi9cXG4gIGZvbnQtZmVhdHVyZS1zZXR0aW5nczogaW5oZXJpdDsgLyogMSAqL1xcbiAgZm9udC12YXJpYXRpb24tc2V0dGluZ3M6IGluaGVyaXQ7IC8qIDEgKi9cXG4gIGZvbnQtc2l6ZTogMTAwJTsgLyogMSAqL1xcbiAgZm9udC13ZWlnaHQ6IGluaGVyaXQ7IC8qIDEgKi9cXG4gIGxpbmUtaGVpZ2h0OiBpbmhlcml0OyAvKiAxICovXFxuICBsZXR0ZXItc3BhY2luZzogaW5oZXJpdDsgLyogMSAqL1xcbiAgY29sb3I6IGluaGVyaXQ7IC8qIDEgKi9cXG4gIG1hcmdpbjogMDsgLyogMiAqL1xcbiAgcGFkZGluZzogMDsgLyogMyAqL1xcbn1cXG5cXG4vKlxcblJlbW92ZSB0aGUgaW5oZXJpdGFuY2Ugb2YgdGV4dCB0cmFuc2Zvcm0gaW4gRWRnZSBhbmQgRmlyZWZveC5cXG4qL1xcblxcbmJ1dHRvbixcXG5zZWxlY3Qge1xcbiAgdGV4dC10cmFuc2Zvcm06IG5vbmU7XFxufVxcblxcbi8qXFxuMS4gQ29ycmVjdCB0aGUgaW5hYmlsaXR5IHRvIHN0eWxlIGNsaWNrYWJsZSB0eXBlcyBpbiBpT1MgYW5kIFNhZmFyaS5cXG4yLiBSZW1vdmUgZGVmYXVsdCBidXR0b24gc3R5bGVzLlxcbiovXFxuXFxuYnV0dG9uLFxcbmlucHV0OndoZXJlKFt0eXBlPSdidXR0b24nXSksXFxuaW5wdXQ6d2hlcmUoW3R5cGU9J3Jlc2V0J10pLFxcbmlucHV0OndoZXJlKFt0eXBlPSdzdWJtaXQnXSkge1xcbiAgLXdlYmtpdC1hcHBlYXJhbmNlOiBidXR0b247IC8qIDEgKi9cXG4gIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50OyAvKiAyICovXFxuICBiYWNrZ3JvdW5kLWltYWdlOiBub25lOyAvKiAyICovXFxufVxcblxcbi8qXFxuVXNlIHRoZSBtb2Rlcm4gRmlyZWZveCBmb2N1cyBzdHlsZSBmb3IgYWxsIGZvY3VzYWJsZSBlbGVtZW50cy5cXG4qL1xcblxcbjotbW96LWZvY3VzcmluZyB7XFxuICBvdXRsaW5lOiBhdXRvO1xcbn1cXG5cXG4vKlxcblJlbW92ZSB0aGUgYWRkaXRpb25hbCBgOmludmFsaWRgIHN0eWxlcyBpbiBGaXJlZm94LiAoaHR0cHM6Ly9naXRodWIuY29tL21vemlsbGEvZ2Vja28tZGV2L2Jsb2IvMmY5ZWFjZDlkM2Q5OTVjOTM3YjQyNTFhNTU1N2Q5NWQ0OTRjOWJlMS9sYXlvdXQvc3R5bGUvcmVzL2Zvcm1zLmNzcyNMNzI4LUw3MzcpXFxuKi9cXG5cXG46LW1vei11aS1pbnZhbGlkIHtcXG4gIGJveC1zaGFkb3c6IG5vbmU7XFxufVxcblxcbi8qXFxuQWRkIHRoZSBjb3JyZWN0IHZlcnRpY2FsIGFsaWdubWVudCBpbiBDaHJvbWUgYW5kIEZpcmVmb3guXFxuKi9cXG5cXG5wcm9ncmVzcyB7XFxuICB2ZXJ0aWNhbC1hbGlnbjogYmFzZWxpbmU7XFxufVxcblxcbi8qXFxuQ29ycmVjdCB0aGUgY3Vyc29yIHN0eWxlIG9mIGluY3JlbWVudCBhbmQgZGVjcmVtZW50IGJ1dHRvbnMgaW4gU2FmYXJpLlxcbiovXFxuXFxuOjotd2Via2l0LWlubmVyLXNwaW4tYnV0dG9uLFxcbjo6LXdlYmtpdC1vdXRlci1zcGluLWJ1dHRvbiB7XFxuICBoZWlnaHQ6IGF1dG87XFxufVxcblxcbi8qXFxuMS4gQ29ycmVjdCB0aGUgb2RkIGFwcGVhcmFuY2UgaW4gQ2hyb21lIGFuZCBTYWZhcmkuXFxuMi4gQ29ycmVjdCB0aGUgb3V0bGluZSBzdHlsZSBpbiBTYWZhcmkuXFxuKi9cXG5cXG5bdHlwZT0nc2VhcmNoJ10ge1xcbiAgLXdlYmtpdC1hcHBlYXJhbmNlOiB0ZXh0ZmllbGQ7IC8qIDEgKi9cXG4gIG91dGxpbmUtb2Zmc2V0OiAtMnB4OyAvKiAyICovXFxufVxcblxcbi8qXFxuUmVtb3ZlIHRoZSBpbm5lciBwYWRkaW5nIGluIENocm9tZSBhbmQgU2FmYXJpIG9uIG1hY09TLlxcbiovXFxuXFxuOjotd2Via2l0LXNlYXJjaC1kZWNvcmF0aW9uIHtcXG4gIC13ZWJraXQtYXBwZWFyYW5jZTogbm9uZTtcXG59XFxuXFxuLypcXG4xLiBDb3JyZWN0IHRoZSBpbmFiaWxpdHkgdG8gc3R5bGUgY2xpY2thYmxlIHR5cGVzIGluIGlPUyBhbmQgU2FmYXJpLlxcbjIuIENoYW5nZSBmb250IHByb3BlcnRpZXMgdG8gYGluaGVyaXRgIGluIFNhZmFyaS5cXG4qL1xcblxcbjo6LXdlYmtpdC1maWxlLXVwbG9hZC1idXR0b24ge1xcbiAgLXdlYmtpdC1hcHBlYXJhbmNlOiBidXR0b247IC8qIDEgKi9cXG4gIGZvbnQ6IGluaGVyaXQ7IC8qIDIgKi9cXG59XFxuXFxuLypcXG5BZGQgdGhlIGNvcnJlY3QgZGlzcGxheSBpbiBDaHJvbWUgYW5kIFNhZmFyaS5cXG4qL1xcblxcbnN1bW1hcnkge1xcbiAgZGlzcGxheTogbGlzdC1pdGVtO1xcbn1cXG5cXG4vKlxcblJlbW92ZXMgdGhlIGRlZmF1bHQgc3BhY2luZyBhbmQgYm9yZGVyIGZvciBhcHByb3ByaWF0ZSBlbGVtZW50cy5cXG4qL1xcblxcbmJsb2NrcXVvdGUsXFxuZGwsXFxuZGQsXFxuaDEsXFxuaDIsXFxuaDMsXFxuaDQsXFxuaDUsXFxuaDYsXFxuaHIsXFxuZmlndXJlLFxcbnAsXFxucHJlIHtcXG4gIG1hcmdpbjogMDtcXG59XFxuXFxuZmllbGRzZXQge1xcbiAgbWFyZ2luOiAwO1xcbiAgcGFkZGluZzogMDtcXG59XFxuXFxubGVnZW5kIHtcXG4gIHBhZGRpbmc6IDA7XFxufVxcblxcbm9sLFxcbnVsLFxcbm1lbnUge1xcbiAgbGlzdC1zdHlsZTogbm9uZTtcXG4gIG1hcmdpbjogMDtcXG4gIHBhZGRpbmc6IDA7XFxufVxcblxcbi8qXFxuUmVzZXQgZGVmYXVsdCBzdHlsaW5nIGZvciBkaWFsb2dzLlxcbiovXFxuZGlhbG9nIHtcXG4gIHBhZGRpbmc6IDA7XFxufVxcblxcbi8qXFxuUHJldmVudCByZXNpemluZyB0ZXh0YXJlYXMgaG9yaXpvbnRhbGx5IGJ5IGRlZmF1bHQuXFxuKi9cXG5cXG50ZXh0YXJlYSB7XFxuICByZXNpemU6IHZlcnRpY2FsO1xcbn1cXG5cXG4vKlxcbjEuIFJlc2V0IHRoZSBkZWZhdWx0IHBsYWNlaG9sZGVyIG9wYWNpdHkgaW4gRmlyZWZveC4gKGh0dHBzOi8vZ2l0aHViLmNvbS90YWlsd2luZGxhYnMvdGFpbHdpbmRjc3MvaXNzdWVzLzMzMDApXFxuMi4gU2V0IHRoZSBkZWZhdWx0IHBsYWNlaG9sZGVyIGNvbG9yIHRvIHRoZSB1c2VyJ3MgY29uZmlndXJlZCBncmF5IDQwMCBjb2xvci5cXG4qL1xcblxcbmlucHV0OjotbW96LXBsYWNlaG9sZGVyLCB0ZXh0YXJlYTo6LW1vei1wbGFjZWhvbGRlciB7XFxuICBvcGFjaXR5OiAxOyAvKiAxICovXFxuICBjb2xvcjogIzljYTNhZjsgLyogMiAqL1xcbn1cXG5cXG5pbnB1dDo6cGxhY2Vob2xkZXIsXFxudGV4dGFyZWE6OnBsYWNlaG9sZGVyIHtcXG4gIG9wYWNpdHk6IDE7IC8qIDEgKi9cXG4gIGNvbG9yOiAjOWNhM2FmOyAvKiAyICovXFxufVxcblxcbi8qXFxuU2V0IHRoZSBkZWZhdWx0IGN1cnNvciBmb3IgYnV0dG9ucy5cXG4qL1xcblxcbmJ1dHRvbixcXG5bcm9sZT1cXFwiYnV0dG9uXFxcIl0ge1xcbiAgY3Vyc29yOiBwb2ludGVyO1xcbn1cXG5cXG4vKlxcbk1ha2Ugc3VyZSBkaXNhYmxlZCBidXR0b25zIGRvbid0IGdldCB0aGUgcG9pbnRlciBjdXJzb3IuXFxuKi9cXG46ZGlzYWJsZWQge1xcbiAgY3Vyc29yOiBkZWZhdWx0O1xcbn1cXG5cXG4vKlxcbjEuIE1ha2UgcmVwbGFjZWQgZWxlbWVudHMgYGRpc3BsYXk6IGJsb2NrYCBieSBkZWZhdWx0LiAoaHR0cHM6Ly9naXRodWIuY29tL21vemRldnMvY3NzcmVtZWR5L2lzc3Vlcy8xNClcXG4yLiBBZGQgYHZlcnRpY2FsLWFsaWduOiBtaWRkbGVgIHRvIGFsaWduIHJlcGxhY2VkIGVsZW1lbnRzIG1vcmUgc2Vuc2libHkgYnkgZGVmYXVsdC4gKGh0dHBzOi8vZ2l0aHViLmNvbS9qZW5zaW1tb25zL2Nzc3JlbWVkeS9pc3N1ZXMvMTQjaXNzdWVjb21tZW50LTYzNDkzNDIxMClcXG4gICBUaGlzIGNhbiB0cmlnZ2VyIGEgcG9vcmx5IGNvbnNpZGVyZWQgbGludCBlcnJvciBpbiBzb21lIHRvb2xzIGJ1dCBpcyBpbmNsdWRlZCBieSBkZXNpZ24uXFxuKi9cXG5cXG5pbWcsXFxuc3ZnLFxcbnZpZGVvLFxcbmNhbnZhcyxcXG5hdWRpbyxcXG5pZnJhbWUsXFxuZW1iZWQsXFxub2JqZWN0IHtcXG4gIGRpc3BsYXk6IGJsb2NrOyAvKiAxICovXFxuICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOyAvKiAyICovXFxufVxcblxcbi8qXFxuQ29uc3RyYWluIGltYWdlcyBhbmQgdmlkZW9zIHRvIHRoZSBwYXJlbnQgd2lkdGggYW5kIHByZXNlcnZlIHRoZWlyIGludHJpbnNpYyBhc3BlY3QgcmF0aW8uIChodHRwczovL2dpdGh1Yi5jb20vbW96ZGV2cy9jc3NyZW1lZHkvaXNzdWVzLzE0KVxcbiovXFxuXFxuaW1nLFxcbnZpZGVvIHtcXG4gIG1heC13aWR0aDogMTAwJTtcXG4gIGhlaWdodDogYXV0bztcXG59XFxuXFxuLyogTWFrZSBlbGVtZW50cyB3aXRoIHRoZSBIVE1MIGhpZGRlbiBhdHRyaWJ1dGUgc3RheSBoaWRkZW4gYnkgZGVmYXVsdCAqL1xcbltoaWRkZW5dOndoZXJlKDpub3QoW2hpZGRlbj1cXFwidW50aWwtZm91bmRcXFwiXSkpIHtcXG4gIGRpc3BsYXk6IG5vbmU7XFxufVxcblxcblt0eXBlPSd0ZXh0J10saW5wdXQ6d2hlcmUoOm5vdChbdHlwZV0pKSxbdHlwZT0nZW1haWwnXSxbdHlwZT0ndXJsJ10sW3R5cGU9J3Bhc3N3b3JkJ10sW3R5cGU9J251bWJlciddLFt0eXBlPSdkYXRlJ10sW3R5cGU9J2RhdGV0aW1lLWxvY2FsJ10sW3R5cGU9J21vbnRoJ10sW3R5cGU9J3NlYXJjaCddLFt0eXBlPSd0ZWwnXSxbdHlwZT0ndGltZSddLFt0eXBlPSd3ZWVrJ10sW211bHRpcGxlXSx0ZXh0YXJlYSxzZWxlY3Qge1xcbiAgLXdlYmtpdC1hcHBlYXJhbmNlOiBub25lO1xcbiAgICAgLW1vei1hcHBlYXJhbmNlOiBub25lO1xcbiAgICAgICAgICBhcHBlYXJhbmNlOiBub25lO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjtcXG4gIGJvcmRlci1jb2xvcjogIzZiNzI4MDtcXG4gIGJvcmRlci13aWR0aDogMXB4O1xcbiAgYm9yZGVyLXJhZGl1czogMHB4O1xcbiAgcGFkZGluZy10b3A6IDAuNXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDAuNzVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC41cmVtO1xcbiAgcGFkZGluZy1sZWZ0OiAwLjc1cmVtO1xcbiAgZm9udC1zaXplOiAxcmVtO1xcbiAgbGluZS1oZWlnaHQ6IDEuNXJlbTtcXG4gIC0tdHctc2hhZG93OiAwIDAgIzAwMDA7XFxufVxcblxcblt0eXBlPSd0ZXh0J106Zm9jdXMsIGlucHV0OndoZXJlKDpub3QoW3R5cGVdKSk6Zm9jdXMsIFt0eXBlPSdlbWFpbCddOmZvY3VzLCBbdHlwZT0ndXJsJ106Zm9jdXMsIFt0eXBlPSdwYXNzd29yZCddOmZvY3VzLCBbdHlwZT0nbnVtYmVyJ106Zm9jdXMsIFt0eXBlPSdkYXRlJ106Zm9jdXMsIFt0eXBlPSdkYXRldGltZS1sb2NhbCddOmZvY3VzLCBbdHlwZT0nbW9udGgnXTpmb2N1cywgW3R5cGU9J3NlYXJjaCddOmZvY3VzLCBbdHlwZT0ndGVsJ106Zm9jdXMsIFt0eXBlPSd0aW1lJ106Zm9jdXMsIFt0eXBlPSd3ZWVrJ106Zm9jdXMsIFttdWx0aXBsZV06Zm9jdXMsIHRleHRhcmVhOmZvY3VzLCBzZWxlY3Q6Zm9jdXMge1xcbiAgb3V0bGluZTogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xcbiAgb3V0bGluZS1vZmZzZXQ6IDJweDtcXG4gIC0tdHctcmluZy1pbnNldDogdmFyKC0tdHctZW1wdHksLyohKi8gLyohKi8pO1xcbiAgLS10dy1yaW5nLW9mZnNldC13aWR0aDogMHB4O1xcbiAgLS10dy1yaW5nLW9mZnNldC1jb2xvcjogI2ZmZjtcXG4gIC0tdHctcmluZy1jb2xvcjogIzI1NjNlYjtcXG4gIC0tdHctcmluZy1vZmZzZXQtc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkgdmFyKC0tdHctcmluZy1vZmZzZXQtY29sb3IpO1xcbiAgLS10dy1yaW5nLXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgY2FsYygxcHggKyB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkpIHZhcigtLXR3LXJpbmctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93KSwgdmFyKC0tdHctcmluZy1zaGFkb3cpLCB2YXIoLS10dy1zaGFkb3cpO1xcbiAgYm9yZGVyLWNvbG9yOiAjMjU2M2ViO1xcbn1cXG5cXG5pbnB1dDo6LW1vei1wbGFjZWhvbGRlciwgdGV4dGFyZWE6Oi1tb3otcGxhY2Vob2xkZXIge1xcbiAgY29sb3I6ICM2YjcyODA7XFxuICBvcGFjaXR5OiAxO1xcbn1cXG5cXG5pbnB1dDo6cGxhY2Vob2xkZXIsdGV4dGFyZWE6OnBsYWNlaG9sZGVyIHtcXG4gIGNvbG9yOiAjNmI3MjgwO1xcbiAgb3BhY2l0eTogMTtcXG59XFxuXFxuOjotd2Via2l0LWRhdGV0aW1lLWVkaXQtZmllbGRzLXdyYXBwZXIge1xcbiAgcGFkZGluZzogMDtcXG59XFxuXFxuOjotd2Via2l0LWRhdGUtYW5kLXRpbWUtdmFsdWUge1xcbiAgbWluLWhlaWdodDogMS41ZW07XFxuICB0ZXh0LWFsaWduOiBpbmhlcml0O1xcbn1cXG5cXG46Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdCB7XFxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcXG59XFxuXFxuOjotd2Via2l0LWRhdGV0aW1lLWVkaXQsOjotd2Via2l0LWRhdGV0aW1lLWVkaXQteWVhci1maWVsZCw6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1tb250aC1maWVsZCw6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1kYXktZmllbGQsOjotd2Via2l0LWRhdGV0aW1lLWVkaXQtaG91ci1maWVsZCw6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1taW51dGUtZmllbGQsOjotd2Via2l0LWRhdGV0aW1lLWVkaXQtc2Vjb25kLWZpZWxkLDo6LXdlYmtpdC1kYXRldGltZS1lZGl0LW1pbGxpc2Vjb25kLWZpZWxkLDo6LXdlYmtpdC1kYXRldGltZS1lZGl0LW1lcmlkaWVtLWZpZWxkIHtcXG4gIHBhZGRpbmctdG9wOiAwO1xcbiAgcGFkZGluZy1ib3R0b206IDA7XFxufVxcblxcbnNlbGVjdCB7XFxuICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoXFxcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM2NzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyBmaWxsPSdub25lJyB2aWV3Qm94PScwIDAgMjAgMjAnJTNlJTNjcGF0aCBzdHJva2U9JyUyMzZiNzI4MCcgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJyBzdHJva2UtbGluZWpvaW49J3JvdW5kJyBzdHJva2Utd2lkdGg9JzEuNScgZD0nTTYgOGw0IDQgNC00Jy8lM2UlM2Mvc3ZnJTNlXFxcIik7XFxuICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiByaWdodCAwLjVyZW0gY2VudGVyO1xcbiAgYmFja2dyb3VuZC1yZXBlYXQ6IG5vLXJlcGVhdDtcXG4gIGJhY2tncm91bmQtc2l6ZTogMS41ZW0gMS41ZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAyLjVyZW07XFxuICAtd2Via2l0LXByaW50LWNvbG9yLWFkanVzdDogZXhhY3Q7XFxuICAgICAgICAgIHByaW50LWNvbG9yLWFkanVzdDogZXhhY3Q7XFxufVxcblxcblttdWx0aXBsZV0sW3NpemVdOndoZXJlKHNlbGVjdDpub3QoW3NpemU9XFxcIjFcXFwiXSkpIHtcXG4gIGJhY2tncm91bmQtaW1hZ2U6IGluaXRpYWw7XFxuICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiBpbml0aWFsO1xcbiAgYmFja2dyb3VuZC1yZXBlYXQ6IHVuc2V0O1xcbiAgYmFja2dyb3VuZC1zaXplOiBpbml0aWFsO1xcbiAgcGFkZGluZy1yaWdodDogMC43NXJlbTtcXG4gIC13ZWJraXQtcHJpbnQtY29sb3ItYWRqdXN0OiB1bnNldDtcXG4gICAgICAgICAgcHJpbnQtY29sb3ItYWRqdXN0OiB1bnNldDtcXG59XFxuXFxuW3R5cGU9J2NoZWNrYm94J10sW3R5cGU9J3JhZGlvJ10ge1xcbiAgLXdlYmtpdC1hcHBlYXJhbmNlOiBub25lO1xcbiAgICAgLW1vei1hcHBlYXJhbmNlOiBub25lO1xcbiAgICAgICAgICBhcHBlYXJhbmNlOiBub25lO1xcbiAgcGFkZGluZzogMDtcXG4gIC13ZWJraXQtcHJpbnQtY29sb3ItYWRqdXN0OiBleGFjdDtcXG4gICAgICAgICAgcHJpbnQtY29sb3ItYWRqdXN0OiBleGFjdDtcXG4gIGRpc3BsYXk6IGlubGluZS1ibG9jaztcXG4gIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7XFxuICBiYWNrZ3JvdW5kLW9yaWdpbjogYm9yZGVyLWJveDtcXG4gIC13ZWJraXQtdXNlci1zZWxlY3Q6IG5vbmU7XFxuICAgICAtbW96LXVzZXItc2VsZWN0OiBub25lO1xcbiAgICAgICAgICB1c2VyLXNlbGVjdDogbm9uZTtcXG4gIGZsZXgtc2hyaW5rOiAwO1xcbiAgaGVpZ2h0OiAxcmVtO1xcbiAgd2lkdGg6IDFyZW07XFxuICBjb2xvcjogIzI1NjNlYjtcXG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmY7XFxuICBib3JkZXItY29sb3I6ICM2YjcyODA7XFxuICBib3JkZXItd2lkdGg6IDFweDtcXG4gIC0tdHctc2hhZG93OiAwIDAgIzAwMDA7XFxufVxcblxcblt0eXBlPSdjaGVja2JveCddIHtcXG4gIGJvcmRlci1yYWRpdXM6IDBweDtcXG59XFxuXFxuW3R5cGU9J3JhZGlvJ10ge1xcbiAgYm9yZGVyLXJhZGl1czogMTAwJTtcXG59XFxuXFxuW3R5cGU9J2NoZWNrYm94J106Zm9jdXMsW3R5cGU9J3JhZGlvJ106Zm9jdXMge1xcbiAgb3V0bGluZTogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xcbiAgb3V0bGluZS1vZmZzZXQ6IDJweDtcXG4gIC0tdHctcmluZy1pbnNldDogdmFyKC0tdHctZW1wdHksLyohKi8gLyohKi8pO1xcbiAgLS10dy1yaW5nLW9mZnNldC13aWR0aDogMnB4O1xcbiAgLS10dy1yaW5nLW9mZnNldC1jb2xvcjogI2ZmZjtcXG4gIC0tdHctcmluZy1jb2xvcjogIzI1NjNlYjtcXG4gIC0tdHctcmluZy1vZmZzZXQtc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkgdmFyKC0tdHctcmluZy1vZmZzZXQtY29sb3IpO1xcbiAgLS10dy1yaW5nLXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgY2FsYygycHggKyB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkpIHZhcigtLXR3LXJpbmctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93KSwgdmFyKC0tdHctcmluZy1zaGFkb3cpLCB2YXIoLS10dy1zaGFkb3cpO1xcbn1cXG5cXG5bdHlwZT0nY2hlY2tib3gnXTpjaGVja2VkLFt0eXBlPSdyYWRpbyddOmNoZWNrZWQge1xcbiAgYm9yZGVyLWNvbG9yOiB0cmFuc3BhcmVudDtcXG4gIGJhY2tncm91bmQtY29sb3I6IGN1cnJlbnRDb2xvcjtcXG4gIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlO1xcbiAgYmFja2dyb3VuZC1wb3NpdGlvbjogY2VudGVyO1xcbiAgYmFja2dyb3VuZC1yZXBlYXQ6IG5vLXJlcGVhdDtcXG59XFxuXFxuW3R5cGU9J2NoZWNrYm94J106Y2hlY2tlZCB7XFxuICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoXFxcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM2Nzdmcgdmlld0JveD0nMCAwIDE2IDE2JyBmaWxsPSd3aGl0ZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyUzZSUzY3BhdGggZD0nTTEyLjIwNyA0Ljc5M2ExIDEgMCAwMTAgMS40MTRsLTUgNWExIDEgMCAwMS0xLjQxNCAwbC0yLTJhMSAxIDAgMDExLjQxNC0xLjQxNEw2LjUgOS4wODZsNC4yOTMtNC4yOTNhMSAxIDAgMDExLjQxNCAweicvJTNlJTNjL3N2ZyUzZVxcXCIpO1xcbn1cXG5cXG5AbWVkaWEgKGZvcmNlZC1jb2xvcnM6IGFjdGl2ZSkgIHtcXG5cXG4gIFt0eXBlPSdjaGVja2JveCddOmNoZWNrZWQge1xcbiAgICAtd2Via2l0LWFwcGVhcmFuY2U6IGF1dG87XFxuICAgICAgIC1tb3otYXBwZWFyYW5jZTogYXV0bztcXG4gICAgICAgICAgICBhcHBlYXJhbmNlOiBhdXRvO1xcbiAgfVxcbn1cXG5cXG5bdHlwZT0ncmFkaW8nXTpjaGVja2VkIHtcXG4gIGJhY2tncm91bmQtaW1hZ2U6IHVybChcXFwiZGF0YTppbWFnZS9zdmcreG1sLCUzY3N2ZyB2aWV3Qm94PScwIDAgMTYgMTYnIGZpbGw9J3doaXRlJyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnJTNlJTNjY2lyY2xlIGN4PSc4JyBjeT0nOCcgcj0nMycvJTNlJTNjL3N2ZyUzZVxcXCIpO1xcbn1cXG5cXG5AbWVkaWEgKGZvcmNlZC1jb2xvcnM6IGFjdGl2ZSkgIHtcXG5cXG4gIFt0eXBlPSdyYWRpbyddOmNoZWNrZWQge1xcbiAgICAtd2Via2l0LWFwcGVhcmFuY2U6IGF1dG87XFxuICAgICAgIC1tb3otYXBwZWFyYW5jZTogYXV0bztcXG4gICAgICAgICAgICBhcHBlYXJhbmNlOiBhdXRvO1xcbiAgfVxcbn1cXG5cXG5bdHlwZT0nY2hlY2tib3gnXTpjaGVja2VkOmhvdmVyLFt0eXBlPSdjaGVja2JveCddOmNoZWNrZWQ6Zm9jdXMsW3R5cGU9J3JhZGlvJ106Y2hlY2tlZDpob3ZlcixbdHlwZT0ncmFkaW8nXTpjaGVja2VkOmZvY3VzIHtcXG4gIGJvcmRlci1jb2xvcjogdHJhbnNwYXJlbnQ7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBjdXJyZW50Q29sb3I7XFxufVxcblxcblt0eXBlPSdjaGVja2JveCddOmluZGV0ZXJtaW5hdGUge1xcbiAgYmFja2dyb3VuZC1pbWFnZTogdXJsKFxcXCJkYXRhOmltYWdlL3N2Zyt4bWwsJTNjc3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycgZmlsbD0nbm9uZScgdmlld0JveD0nMCAwIDE2IDE2JyUzZSUzY3BhdGggc3Ryb2tlPSd3aGl0ZScgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJyBzdHJva2UtbGluZWpvaW49J3JvdW5kJyBzdHJva2Utd2lkdGg9JzInIGQ9J000IDhoOCcvJTNlJTNjL3N2ZyUzZVxcXCIpO1xcbiAgYm9yZGVyLWNvbG9yOiB0cmFuc3BhcmVudDtcXG4gIGJhY2tncm91bmQtY29sb3I6IGN1cnJlbnRDb2xvcjtcXG4gIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlO1xcbiAgYmFja2dyb3VuZC1wb3NpdGlvbjogY2VudGVyO1xcbiAgYmFja2dyb3VuZC1yZXBlYXQ6IG5vLXJlcGVhdDtcXG59XFxuXFxuQG1lZGlhIChmb3JjZWQtY29sb3JzOiBhY3RpdmUpICB7XFxuXFxuICBbdHlwZT0nY2hlY2tib3gnXTppbmRldGVybWluYXRlIHtcXG4gICAgLXdlYmtpdC1hcHBlYXJhbmNlOiBhdXRvO1xcbiAgICAgICAtbW96LWFwcGVhcmFuY2U6IGF1dG87XFxuICAgICAgICAgICAgYXBwZWFyYW5jZTogYXV0bztcXG4gIH1cXG59XFxuXFxuW3R5cGU9J2NoZWNrYm94J106aW5kZXRlcm1pbmF0ZTpob3ZlcixbdHlwZT0nY2hlY2tib3gnXTppbmRldGVybWluYXRlOmZvY3VzIHtcXG4gIGJvcmRlci1jb2xvcjogdHJhbnNwYXJlbnQ7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBjdXJyZW50Q29sb3I7XFxufVxcblxcblt0eXBlPSdmaWxlJ10ge1xcbiAgYmFja2dyb3VuZDogdW5zZXQ7XFxuICBib3JkZXItY29sb3I6IGluaGVyaXQ7XFxuICBib3JkZXItd2lkdGg6IDA7XFxuICBib3JkZXItcmFkaXVzOiAwO1xcbiAgcGFkZGluZzogMDtcXG4gIGZvbnQtc2l6ZTogdW5zZXQ7XFxuICBsaW5lLWhlaWdodDogaW5oZXJpdDtcXG59XFxuXFxuW3R5cGU9J2ZpbGUnXTpmb2N1cyB7XFxuICBvdXRsaW5lOiAxcHggc29saWQgQnV0dG9uVGV4dDtcXG4gIG91dGxpbmU6IDFweCBhdXRvIC13ZWJraXQtZm9jdXMtcmluZy1jb2xvcjtcXG59XFxuLmZvcm0taW5wdXQsLmZvcm0tdGV4dGFyZWEsLmZvcm0tc2VsZWN0LC5mb3JtLW11bHRpc2VsZWN0IHtcXG4gIC13ZWJraXQtYXBwZWFyYW5jZTogbm9uZTtcXG4gICAgIC1tb3otYXBwZWFyYW5jZTogbm9uZTtcXG4gICAgICAgICAgYXBwZWFyYW5jZTogbm9uZTtcXG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmY7XFxuICBib3JkZXItY29sb3I6ICM2YjcyODA7XFxuICBib3JkZXItd2lkdGg6IDFweDtcXG4gIGJvcmRlci1yYWRpdXM6IDBweDtcXG4gIHBhZGRpbmctdG9wOiAwLjVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjc1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuNXJlbTtcXG4gIHBhZGRpbmctbGVmdDogMC43NXJlbTtcXG4gIGZvbnQtc2l6ZTogMXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxLjVyZW07XFxuICAtLXR3LXNoYWRvdzogMCAwICMwMDAwO1xcbn1cXG4uZm9ybS1pbnB1dDpmb2N1cywgLmZvcm0tdGV4dGFyZWE6Zm9jdXMsIC5mb3JtLXNlbGVjdDpmb2N1cywgLmZvcm0tbXVsdGlzZWxlY3Q6Zm9jdXMge1xcbiAgb3V0bGluZTogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xcbiAgb3V0bGluZS1vZmZzZXQ6IDJweDtcXG4gIC0tdHctcmluZy1pbnNldDogdmFyKC0tdHctZW1wdHksLyohKi8gLyohKi8pO1xcbiAgLS10dy1yaW5nLW9mZnNldC13aWR0aDogMHB4O1xcbiAgLS10dy1yaW5nLW9mZnNldC1jb2xvcjogI2ZmZjtcXG4gIC0tdHctcmluZy1jb2xvcjogIzI1NjNlYjtcXG4gIC0tdHctcmluZy1vZmZzZXQtc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkgdmFyKC0tdHctcmluZy1vZmZzZXQtY29sb3IpO1xcbiAgLS10dy1yaW5nLXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgY2FsYygxcHggKyB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkpIHZhcigtLXR3LXJpbmctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93KSwgdmFyKC0tdHctcmluZy1zaGFkb3cpLCB2YXIoLS10dy1zaGFkb3cpO1xcbiAgYm9yZGVyLWNvbG9yOiAjMjU2M2ViO1xcbn1cXG4uZm9ybS1pbnB1dDo6LW1vei1wbGFjZWhvbGRlciwgLmZvcm0tdGV4dGFyZWE6Oi1tb3otcGxhY2Vob2xkZXIge1xcbiAgY29sb3I6ICM2YjcyODA7XFxuICBvcGFjaXR5OiAxO1xcbn1cXG4uZm9ybS1pbnB1dDo6cGxhY2Vob2xkZXIsLmZvcm0tdGV4dGFyZWE6OnBsYWNlaG9sZGVyIHtcXG4gIGNvbG9yOiAjNmI3MjgwO1xcbiAgb3BhY2l0eTogMTtcXG59XFxuLmZvcm0taW5wdXQ6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1maWVsZHMtd3JhcHBlciB7XFxuICBwYWRkaW5nOiAwO1xcbn1cXG4uZm9ybS1pbnB1dDo6LXdlYmtpdC1kYXRlLWFuZC10aW1lLXZhbHVlIHtcXG4gIG1pbi1oZWlnaHQ6IDEuNWVtO1xcbiAgdGV4dC1hbGlnbjogaW5oZXJpdDtcXG59XFxuLmZvcm0taW5wdXQ6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdCB7XFxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcXG59XFxuLmZvcm0taW5wdXQ6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdCwuZm9ybS1pbnB1dDo6LXdlYmtpdC1kYXRldGltZS1lZGl0LXllYXItZmllbGQsLmZvcm0taW5wdXQ6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1tb250aC1maWVsZCwuZm9ybS1pbnB1dDo6LXdlYmtpdC1kYXRldGltZS1lZGl0LWRheS1maWVsZCwuZm9ybS1pbnB1dDo6LXdlYmtpdC1kYXRldGltZS1lZGl0LWhvdXItZmllbGQsLmZvcm0taW5wdXQ6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1taW51dGUtZmllbGQsLmZvcm0taW5wdXQ6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1zZWNvbmQtZmllbGQsLmZvcm0taW5wdXQ6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1taWxsaXNlY29uZC1maWVsZCwuZm9ybS1pbnB1dDo6LXdlYmtpdC1kYXRldGltZS1lZGl0LW1lcmlkaWVtLWZpZWxkIHtcXG4gIHBhZGRpbmctdG9wOiAwO1xcbiAgcGFkZGluZy1ib3R0b206IDA7XFxufVxcbi5zci1vbmx5IHtcXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcXG4gIHdpZHRoOiAxcHg7XFxuICBoZWlnaHQ6IDFweDtcXG4gIHBhZGRpbmc6IDA7XFxuICBtYXJnaW46IC0xcHg7XFxuICBvdmVyZmxvdzogaGlkZGVuO1xcbiAgY2xpcDogcmVjdCgwLCAwLCAwLCAwKTtcXG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7XFxuICBib3JkZXItd2lkdGg6IDA7XFxufVxcbi5maXhlZCB7XFxuICBwb3NpdGlvbjogZml4ZWQ7XFxufVxcbi5hYnNvbHV0ZSB7XFxuICBwb3NpdGlvbjogYWJzb2x1dGU7XFxufVxcbi5yZWxhdGl2ZSB7XFxuICBwb3NpdGlvbjogcmVsYXRpdmU7XFxufVxcbi5pbnNldC0wIHtcXG4gIGluc2V0OiAwcHg7XFxufVxcbi5pbnNldC15LTAge1xcbiAgdG9wOiAwcHg7XFxuICBib3R0b206IDBweDtcXG59XFxuLi1yaWdodC0xIHtcXG4gIHJpZ2h0OiAtMC4yNXJlbTtcXG59XFxuLi10b3AtMSB7XFxuICB0b3A6IC0wLjI1cmVtO1xcbn1cXG4ubGVmdC0wIHtcXG4gIGxlZnQ6IDBweDtcXG59XFxuLmxlZnQtMiB7XFxuICBsZWZ0OiAwLjVyZW07XFxufVxcbi5sZWZ0LTMge1xcbiAgbGVmdDogMC43NXJlbTtcXG59XFxuLnJpZ2h0LTAge1xcbiAgcmlnaHQ6IDBweDtcXG59XFxuLnRvcC0xXFxcXC8yIHtcXG4gIHRvcDogNTAlO1xcbn1cXG4uei00MCB7XFxuICB6LWluZGV4OiA0MDtcXG59XFxuLnotNTAge1xcbiAgei1pbmRleDogNTA7XFxufVxcbi5jb2wtc3Bhbi0xIHtcXG4gIGdyaWQtY29sdW1uOiBzcGFuIDEgLyBzcGFuIDE7XFxufVxcbi5jb2wtc3Bhbi0yIHtcXG4gIGdyaWQtY29sdW1uOiBzcGFuIDIgLyBzcGFuIDI7XFxufVxcbi5jb2wtc3Bhbi00IHtcXG4gIGdyaWQtY29sdW1uOiBzcGFuIDQgLyBzcGFuIDQ7XFxufVxcbi5teC00IHtcXG4gIG1hcmdpbi1sZWZ0OiAxcmVtO1xcbiAgbWFyZ2luLXJpZ2h0OiAxcmVtO1xcbn1cXG4ubXgtYXV0byB7XFxuICBtYXJnaW4tbGVmdDogYXV0bztcXG4gIG1hcmdpbi1yaWdodDogYXV0bztcXG59XFxuLi1tYi1weCB7XFxuICBtYXJnaW4tYm90dG9tOiAtMXB4O1xcbn1cXG4ubWItMSB7XFxuICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtO1xcbn1cXG4ubWItMiB7XFxuICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XFxufVxcbi5tYi0zIHtcXG4gIG1hcmdpbi1ib3R0b206IDAuNzVyZW07XFxufVxcbi5tYi00IHtcXG4gIG1hcmdpbi1ib3R0b206IDFyZW07XFxufVxcbi5tYi02IHtcXG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcXG59XFxuLm1iLTgge1xcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcXG59XFxuLm1sLTEge1xcbiAgbWFyZ2luLWxlZnQ6IDAuMjVyZW07XFxufVxcbi5tbC0yIHtcXG4gIG1hcmdpbi1sZWZ0OiAwLjVyZW07XFxufVxcbi5tbC0zIHtcXG4gIG1hcmdpbi1sZWZ0OiAwLjc1cmVtO1xcbn1cXG4ubWwtNCB7XFxuICBtYXJnaW4tbGVmdDogMXJlbTtcXG59XFxuLm1sLTUge1xcbiAgbWFyZ2luLWxlZnQ6IDEuMjVyZW07XFxufVxcbi5tbC1hdXRvIHtcXG4gIG1hcmdpbi1sZWZ0OiBhdXRvO1xcbn1cXG4ubXItMSB7XFxuICBtYXJnaW4tcmlnaHQ6IDAuMjVyZW07XFxufVxcbi5tci0yIHtcXG4gIG1hcmdpbi1yaWdodDogMC41cmVtO1xcbn1cXG4ubXItMyB7XFxuICBtYXJnaW4tcmlnaHQ6IDAuNzVyZW07XFxufVxcbi5tdC0wXFxcXC41IHtcXG4gIG1hcmdpbi10b3A6IDAuMTI1cmVtO1xcbn1cXG4ubXQtMSB7XFxuICBtYXJnaW4tdG9wOiAwLjI1cmVtO1xcbn1cXG4ubXQtMiB7XFxuICBtYXJnaW4tdG9wOiAwLjVyZW07XFxufVxcbi5tdC0zIHtcXG4gIG1hcmdpbi10b3A6IDAuNzVyZW07XFxufVxcbi5tdC00IHtcXG4gIG1hcmdpbi10b3A6IDFyZW07XFxufVxcbi5tdC02IHtcXG4gIG1hcmdpbi10b3A6IDEuNXJlbTtcXG59XFxuLm10LTgge1xcbiAgbWFyZ2luLXRvcDogMnJlbTtcXG59XFxuLmJsb2NrIHtcXG4gIGRpc3BsYXk6IGJsb2NrO1xcbn1cXG4uaW5saW5lLWJsb2NrIHtcXG4gIGRpc3BsYXk6IGlubGluZS1ibG9jaztcXG59XFxuLmlubGluZSB7XFxuICBkaXNwbGF5OiBpbmxpbmU7XFxufVxcbi5mbGV4IHtcXG4gIGRpc3BsYXk6IGZsZXg7XFxufVxcbi5pbmxpbmUtZmxleCB7XFxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcXG59XFxuLnRhYmxlIHtcXG4gIGRpc3BsYXk6IHRhYmxlO1xcbn1cXG4uZ3JpZCB7XFxuICBkaXNwbGF5OiBncmlkO1xcbn1cXG4uaGlkZGVuIHtcXG4gIGRpc3BsYXk6IG5vbmU7XFxufVxcbi5oLTEyIHtcXG4gIGhlaWdodDogM3JlbTtcXG59XFxuLmgtMiB7XFxuICBoZWlnaHQ6IDAuNXJlbTtcXG59XFxuLmgtMjAge1xcbiAgaGVpZ2h0OiA1cmVtO1xcbn1cXG4uaC0zMiB7XFxuICBoZWlnaHQ6IDhyZW07XFxufVxcbi5oLTQge1xcbiAgaGVpZ2h0OiAxcmVtO1xcbn1cXG4uaC01IHtcXG4gIGhlaWdodDogMS4yNXJlbTtcXG59XFxuLmgtNiB7XFxuICBoZWlnaHQ6IDEuNXJlbTtcXG59XFxuLmgtNjQge1xcbiAgaGVpZ2h0OiAxNnJlbTtcXG59XFxuLmgtOCB7XFxuICBoZWlnaHQ6IDJyZW07XFxufVxcbi5oLWZ1bGwge1xcbiAgaGVpZ2h0OiAxMDAlO1xcbn1cXG4ubWF4LWgtOTYge1xcbiAgbWF4LWhlaWdodDogMjRyZW07XFxufVxcbi5tYXgtaC1cXFxcWzkwdmhcXFxcXSB7XFxuICBtYXgtaGVpZ2h0OiA5MHZoO1xcbn1cXG4ubWluLWgtc2NyZWVuIHtcXG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xcbn1cXG4udy0wIHtcXG4gIHdpZHRoOiAwcHg7XFxufVxcbi53LTExIHtcXG4gIHdpZHRoOiAyLjc1cmVtO1xcbn1cXG4udy0xMiB7XFxuICB3aWR0aDogM3JlbTtcXG59XFxuLnctMiB7XFxuICB3aWR0aDogMC41cmVtO1xcbn1cXG4udy0yMCB7XFxuICB3aWR0aDogNXJlbTtcXG59XFxuLnctMzIge1xcbiAgd2lkdGg6IDhyZW07XFxufVxcbi53LTQge1xcbiAgd2lkdGg6IDFyZW07XFxufVxcbi53LTQ4IHtcXG4gIHdpZHRoOiAxMnJlbTtcXG59XFxuLnctNSB7XFxuICB3aWR0aDogMS4yNXJlbTtcXG59XFxuLnctNiB7XFxuICB3aWR0aDogMS41cmVtO1xcbn1cXG4udy02NCB7XFxuICB3aWR0aDogMTZyZW07XFxufVxcbi53LTgge1xcbiAgd2lkdGg6IDJyZW07XFxufVxcbi53LTgwIHtcXG4gIHdpZHRoOiAyMHJlbTtcXG59XFxuLnctZnVsbCB7XFxuICB3aWR0aDogMTAwJTtcXG59XFxuLm1pbi13LTAge1xcbiAgbWluLXdpZHRoOiAwcHg7XFxufVxcbi5taW4tdy1mdWxsIHtcXG4gIG1pbi13aWR0aDogMTAwJTtcXG59XFxuLm1heC13LTJ4bCB7XFxuICBtYXgtd2lkdGg6IDQycmVtO1xcbn1cXG4ubWF4LXctNHhsIHtcXG4gIG1heC13aWR0aDogNTZyZW07XFxufVxcbi5tYXgtdy01eGwge1xcbiAgbWF4LXdpZHRoOiA2NHJlbTtcXG59XFxuLm1heC13LW1kIHtcXG4gIG1heC13aWR0aDogMjhyZW07XFxufVxcbi5tYXgtdy14cyB7XFxuICBtYXgtd2lkdGg6IDIwcmVtO1xcbn1cXG4uZmxleC0xIHtcXG4gIGZsZXg6IDEgMSAwJTtcXG59XFxuLmZsZXgtc2hyaW5rLTAge1xcbiAgZmxleC1zaHJpbms6IDA7XFxufVxcbi5ib3JkZXItY29sbGFwc2Uge1xcbiAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTtcXG59XFxuLi10cmFuc2xhdGUteC1mdWxsIHtcXG4gIC0tdHctdHJhbnNsYXRlLXg6IC0xMDAlO1xcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUodmFyKC0tdHctdHJhbnNsYXRlLXgpLCB2YXIoLS10dy10cmFuc2xhdGUteSkpIHJvdGF0ZSh2YXIoLS10dy1yb3RhdGUpKSBza2V3WCh2YXIoLS10dy1za2V3LXgpKSBza2V3WSh2YXIoLS10dy1za2V3LXkpKSBzY2FsZVgodmFyKC0tdHctc2NhbGUteCkpIHNjYWxlWSh2YXIoLS10dy1zY2FsZS15KSk7XFxufVxcbi4tdHJhbnNsYXRlLXktMVxcXFwvMiB7XFxuICAtLXR3LXRyYW5zbGF0ZS15OiAtNTAlO1xcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUodmFyKC0tdHctdHJhbnNsYXRlLXgpLCB2YXIoLS10dy10cmFuc2xhdGUteSkpIHJvdGF0ZSh2YXIoLS10dy1yb3RhdGUpKSBza2V3WCh2YXIoLS10dy1za2V3LXgpKSBza2V3WSh2YXIoLS10dy1za2V3LXkpKSBzY2FsZVgodmFyKC0tdHctc2NhbGUteCkpIHNjYWxlWSh2YXIoLS10dy1zY2FsZS15KSk7XFxufVxcbi50cmFuc2xhdGUteC0wIHtcXG4gIC0tdHctdHJhbnNsYXRlLXg6IDBweDtcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKHZhcigtLXR3LXRyYW5zbGF0ZS14KSwgdmFyKC0tdHctdHJhbnNsYXRlLXkpKSByb3RhdGUodmFyKC0tdHctcm90YXRlKSkgc2tld1godmFyKC0tdHctc2tldy14KSkgc2tld1kodmFyKC0tdHctc2tldy15KSkgc2NhbGVYKHZhcigtLXR3LXNjYWxlLXgpKSBzY2FsZVkodmFyKC0tdHctc2NhbGUteSkpO1xcbn1cXG4udHJhbnNmb3JtIHtcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKHZhcigtLXR3LXRyYW5zbGF0ZS14KSwgdmFyKC0tdHctdHJhbnNsYXRlLXkpKSByb3RhdGUodmFyKC0tdHctcm90YXRlKSkgc2tld1godmFyKC0tdHctc2tldy14KSkgc2tld1kodmFyKC0tdHctc2tldy15KSkgc2NhbGVYKHZhcigtLXR3LXNjYWxlLXgpKSBzY2FsZVkodmFyKC0tdHctc2NhbGUteSkpO1xcbn1cXG5Aa2V5ZnJhbWVzIHNwaW4ge1xcblxcbiAgdG8ge1xcbiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpO1xcbiAgfVxcbn1cXG4uYW5pbWF0ZS1zcGluIHtcXG4gIGFuaW1hdGlvbjogc3BpbiAxcyBsaW5lYXIgaW5maW5pdGU7XFxufVxcbi5jdXJzb3Itbm90LWFsbG93ZWQge1xcbiAgY3Vyc29yOiBub3QtYWxsb3dlZDtcXG59XFxuLmN1cnNvci1wb2ludGVyIHtcXG4gIGN1cnNvcjogcG9pbnRlcjtcXG59XFxuLmxpc3QtaW5zaWRlIHtcXG4gIGxpc3Qtc3R5bGUtcG9zaXRpb246IGluc2lkZTtcXG59XFxuLmxpc3QtZGlzYyB7XFxuICBsaXN0LXN0eWxlLXR5cGU6IGRpc2M7XFxufVxcbi5hcHBlYXJhbmNlLW5vbmUge1xcbiAgLXdlYmtpdC1hcHBlYXJhbmNlOiBub25lO1xcbiAgICAgLW1vei1hcHBlYXJhbmNlOiBub25lO1xcbiAgICAgICAgICBhcHBlYXJhbmNlOiBub25lO1xcbn1cXG4uZ3JpZC1jb2xzLTEge1xcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMSwgbWlubWF4KDAsIDFmcikpO1xcbn1cXG4uZ3JpZC1jb2xzLTEyIHtcXG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDEyLCBtaW5tYXgoMCwgMWZyKSk7XFxufVxcbi5ncmlkLWNvbHMtMiB7XFxuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCBtaW5tYXgoMCwgMWZyKSk7XFxufVxcbi5mbGV4LWNvbCB7XFxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xcbn1cXG4uaXRlbXMtc3RhcnQge1xcbiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XFxufVxcbi5pdGVtcy1lbmQge1xcbiAgYWxpZ24taXRlbXM6IGZsZXgtZW5kO1xcbn1cXG4uaXRlbXMtY2VudGVyIHtcXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XFxufVxcbi5qdXN0aWZ5LWVuZCB7XFxuICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xcbn1cXG4uanVzdGlmeS1jZW50ZXIge1xcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XFxufVxcbi5qdXN0aWZ5LWJldHdlZW4ge1xcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xcbn1cXG4uZ2FwLTMge1xcbiAgZ2FwOiAwLjc1cmVtO1xcbn1cXG4uZ2FwLTQge1xcbiAgZ2FwOiAxcmVtO1xcbn1cXG4uZ2FwLTUge1xcbiAgZ2FwOiAxLjI1cmVtO1xcbn1cXG4uZ2FwLTYge1xcbiAgZ2FwOiAxLjVyZW07XFxufVxcbi5nYXAtOCB7XFxuICBnYXA6IDJyZW07XFxufVxcbi5nYXAtMiB7XFxuICBnYXA6IDAuNXJlbTtcXG59XFxuLnNwYWNlLXgtMiA+IDpub3QoW2hpZGRlbl0pIH4gOm5vdChbaGlkZGVuXSkge1xcbiAgLS10dy1zcGFjZS14LXJldmVyc2U6IDA7XFxuICBtYXJnaW4tcmlnaHQ6IGNhbGMoMC41cmVtICogdmFyKC0tdHctc3BhY2UteC1yZXZlcnNlKSk7XFxuICBtYXJnaW4tbGVmdDogY2FsYygwLjVyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS14LXJldmVyc2UpKSk7XFxufVxcbi5zcGFjZS14LTMgPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteC1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXJpZ2h0OiBjYWxjKDAuNzVyZW0gKiB2YXIoLS10dy1zcGFjZS14LXJldmVyc2UpKTtcXG4gIG1hcmdpbi1sZWZ0OiBjYWxjKDAuNzVyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS14LXJldmVyc2UpKSk7XFxufVxcbi5zcGFjZS14LTQgPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteC1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXJpZ2h0OiBjYWxjKDFyZW0gKiB2YXIoLS10dy1zcGFjZS14LXJldmVyc2UpKTtcXG4gIG1hcmdpbi1sZWZ0OiBjYWxjKDFyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS14LXJldmVyc2UpKSk7XFxufVxcbi5zcGFjZS14LTggPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteC1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXJpZ2h0OiBjYWxjKDJyZW0gKiB2YXIoLS10dy1zcGFjZS14LXJldmVyc2UpKTtcXG4gIG1hcmdpbi1sZWZ0OiBjYWxjKDJyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS14LXJldmVyc2UpKSk7XFxufVxcbi5zcGFjZS15LTEgPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteS1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXRvcDogY2FsYygwLjI1cmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteS1yZXZlcnNlKSkpO1xcbiAgbWFyZ2luLWJvdHRvbTogY2FsYygwLjI1cmVtICogdmFyKC0tdHctc3BhY2UteS1yZXZlcnNlKSk7XFxufVxcbi5zcGFjZS15LTIgPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteS1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXRvcDogY2FsYygwLjVyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKSk7XFxuICBtYXJnaW4tYm90dG9tOiBjYWxjKDAuNXJlbSAqIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpO1xcbn1cXG4uc3BhY2UteS0zID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXktcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi10b3A6IGNhbGMoMC43NXJlbSAqIGNhbGMoMSAtIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpKTtcXG4gIG1hcmdpbi1ib3R0b206IGNhbGMoMC43NXJlbSAqIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpO1xcbn1cXG4uc3BhY2UteS00ID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXktcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi10b3A6IGNhbGMoMXJlbSAqIGNhbGMoMSAtIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpKTtcXG4gIG1hcmdpbi1ib3R0b206IGNhbGMoMXJlbSAqIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpO1xcbn1cXG4uc3BhY2UteS02ID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXktcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi10b3A6IGNhbGMoMS41cmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteS1yZXZlcnNlKSkpO1xcbiAgbWFyZ2luLWJvdHRvbTogY2FsYygxLjVyZW0gKiB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKTtcXG59XFxuLnNwYWNlLXktOCA+IDpub3QoW2hpZGRlbl0pIH4gOm5vdChbaGlkZGVuXSkge1xcbiAgLS10dy1zcGFjZS15LXJldmVyc2U6IDA7XFxuICBtYXJnaW4tdG9wOiBjYWxjKDJyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKSk7XFxuICBtYXJnaW4tYm90dG9tOiBjYWxjKDJyZW0gKiB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKTtcXG59XFxuLmRpdmlkZS15ID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LWRpdmlkZS15LXJldmVyc2U6IDA7XFxuICBib3JkZXItdG9wLXdpZHRoOiBjYWxjKDFweCAqIGNhbGMoMSAtIHZhcigtLXR3LWRpdmlkZS15LXJldmVyc2UpKSk7XFxuICBib3JkZXItYm90dG9tLXdpZHRoOiBjYWxjKDFweCAqIHZhcigtLXR3LWRpdmlkZS15LXJldmVyc2UpKTtcXG59XFxuLmRpdmlkZS1ncmF5LTIwMCA+IDpub3QoW2hpZGRlbl0pIH4gOm5vdChbaGlkZGVuXSkge1xcbiAgLS10dy1kaXZpZGUtb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDIyOSAyMzEgMjM1IC8gdmFyKC0tdHctZGl2aWRlLW9wYWNpdHksIDEpKTtcXG59XFxuLm92ZXJmbG93LWhpZGRlbiB7XFxuICBvdmVyZmxvdzogaGlkZGVuO1xcbn1cXG4ub3ZlcmZsb3cteC1hdXRvIHtcXG4gIG92ZXJmbG93LXg6IGF1dG87XFxufVxcbi5vdmVyZmxvdy15LWF1dG8ge1xcbiAgb3ZlcmZsb3cteTogYXV0bztcXG59XFxuLnRydW5jYXRlIHtcXG4gIG92ZXJmbG93OiBoaWRkZW47XFxuICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpcztcXG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7XFxufVxcbi53aGl0ZXNwYWNlLW5vd3JhcCB7XFxuICB3aGl0ZS1zcGFjZTogbm93cmFwO1xcbn1cXG4ucm91bmRlZCB7XFxuICBib3JkZXItcmFkaXVzOiAwLjI1cmVtO1xcbn1cXG4ucm91bmRlZC0yeGwge1xcbiAgYm9yZGVyLXJhZGl1czogMXJlbTtcXG59XFxuLnJvdW5kZWQtZnVsbCB7XFxuICBib3JkZXItcmFkaXVzOiA5OTk5cHg7XFxufVxcbi5yb3VuZGVkLWxnIHtcXG4gIGJvcmRlci1yYWRpdXM6IDAuNXJlbTtcXG59XFxuLnJvdW5kZWQtbWQge1xcbiAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XFxufVxcbi5yb3VuZGVkLXhsIHtcXG4gIGJvcmRlci1yYWRpdXM6IDAuNzVyZW07XFxufVxcbi5ib3JkZXIge1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxufVxcbi5ib3JkZXItMiB7XFxuICBib3JkZXItd2lkdGg6IDJweDtcXG59XFxuLmJvcmRlci1iIHtcXG4gIGJvcmRlci1ib3R0b20td2lkdGg6IDFweDtcXG59XFxuLmJvcmRlci1iLTIge1xcbiAgYm9yZGVyLWJvdHRvbS13aWR0aDogMnB4O1xcbn1cXG4uYm9yZGVyLXQge1xcbiAgYm9yZGVyLXRvcC13aWR0aDogMXB4O1xcbn1cXG4uYm9yZGVyLXQtMiB7XFxuICBib3JkZXItdG9wLXdpZHRoOiAycHg7XFxufVxcbi5ib3JkZXItYmx1ZS01MDAge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDU5IDEzMCAyNDYgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbn1cXG4uYm9yZGVyLWJsdWUtNjAwIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigzNyA5OSAyMzUgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbn1cXG4uYm9yZGVyLWdyYXktMTAwIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigyNDMgMjQ0IDI0NiAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5ib3JkZXItZ3JheS0yMDAge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDIyOSAyMzEgMjM1IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLmJvcmRlci1ncmF5LTMwMCB7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjA5IDIxMyAyMTkgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbn1cXG4uYm9yZGVyLW5ldXRyYWwtMTAwIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigyNDUgMjQ1IDI0NSAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5ib3JkZXItbmV1dHJhbC0xNTAge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDI0MCAyNDAgMjQwIC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLmJvcmRlci1vcmFuZ2UtMjAwIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigyNTQgMjE1IDE3MCAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5ib3JkZXItcHJpbWFyeS0zMDAge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDEyNSAyMTEgMjUyIC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLmJvcmRlci1wcmltYXJ5LTUwMCB7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMTQgMTY1IDIzMyAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5ib3JkZXItcHJpbWFyeS02MDAge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDIgMTMyIDE5OSAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5ib3JkZXItcmVkLTIwMCB7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjU0IDIwMiAyMDIgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbn1cXG4uYm9yZGVyLXJlZC0zMDAge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDI1MiAxNjUgMTY1IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLmJvcmRlci1yZWQtNTAwIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigyMzkgNjggNjggLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbn1cXG4uYm9yZGVyLXRyYW5zcGFyZW50IHtcXG4gIGJvcmRlci1jb2xvcjogdHJhbnNwYXJlbnQ7XFxufVxcbi5ib3JkZXItdC1wcmltYXJ5LTYwMCB7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLXRvcC1jb2xvcjogcmdiKDIgMTMyIDE5OSAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1ibGFjayB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMCAwIDAgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1ibHVlLTEwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjE5IDIzNCAyNTQgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1ibHVlLTUwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyMzkgMjQ2IDI1NSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLWJsdWUtNTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYig1OSAxMzAgMjQ2IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctYmx1ZS02MDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDM3IDk5IDIzNSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLWVtZXJhbGQtNTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigxNiAxODUgMTI5IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctZ3JheS0xMDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0MyAyNDQgMjQ2IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctZ3JheS0yMDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIyOSAyMzEgMjM1IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctZ3JheS0zMDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIwOSAyMTMgMjE5IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctZ3JheS01MCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjQ5IDI1MCAyNTEgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1ncmF5LTUwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMTA3IDExNCAxMjggLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1ncmVlbi0xMDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIyMCAyNTIgMjMxIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctZ3JlZW4tNTAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0MCAyNTMgMjQ0IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctZ3JlZW4tNTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigzNCAxOTcgOTQgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1ncmVlbi02MDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIyIDE2MyA3NCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLWluZGlnby0xMDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIyNCAyMzEgMjU1IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctaW5kaWdvLTUwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoOTkgMTAyIDI0MSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLW5ldXRyYWwtNTAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1MCAyNTAgMjUwIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctb3JhbmdlLTEwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU1IDIzNyAyMTMgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1vcmFuZ2UtNTAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NSAyNDcgMjM3IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctcGluay01MDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIzNiA3MiAxNTMgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1wcmltYXJ5LTEwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjI0IDI0MiAyNTQgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1wcmltYXJ5LTUwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNDAgMjQ5IDI1NSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLXByaW1hcnktNjAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyIDEzMiAxOTkgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1wdXJwbGUtMTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNDMgMjMyIDI1NSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLXB1cnBsZS01MDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDE2OCA4NSAyNDcgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1yZWQtMTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTQgMjI2IDIyNiAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLXJlZC01MCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU0IDI0MiAyNDIgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1yZWQtNTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyMzkgNjggNjggLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1zdGF0dXMtZXJyb3Ige1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIzOSA2OCA2OCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLXN1cmZhY2UtcHJpbWFyeSB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy13aGl0ZSB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy15ZWxsb3ctMTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTQgMjQ5IDE5NSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLXllbGxvdy01MDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIzNCAxNzkgOCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLW9wYWNpdHktNTAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAwLjU7XFxufVxcbi5iZy1vcGFjaXR5LTc1IHtcXG4gIC0tdHctYmctb3BhY2l0eTogMC43NTtcXG59XFxuLnAtMSB7XFxuICBwYWRkaW5nOiAwLjI1cmVtO1xcbn1cXG4ucC0xXFxcXC41IHtcXG4gIHBhZGRpbmc6IDAuMzc1cmVtO1xcbn1cXG4ucC0xMiB7XFxuICBwYWRkaW5nOiAzcmVtO1xcbn1cXG4ucC0yIHtcXG4gIHBhZGRpbmc6IDAuNXJlbTtcXG59XFxuLnAtMlxcXFwuNSB7XFxuICBwYWRkaW5nOiAwLjYyNXJlbTtcXG59XFxuLnAtMyB7XFxuICBwYWRkaW5nOiAwLjc1cmVtO1xcbn1cXG4ucC00IHtcXG4gIHBhZGRpbmc6IDFyZW07XFxufVxcbi5wLTUge1xcbiAgcGFkZGluZzogMS4yNXJlbTtcXG59XFxuLnAtNiB7XFxuICBwYWRkaW5nOiAxLjVyZW07XFxufVxcbi5wLTgge1xcbiAgcGFkZGluZzogMnJlbTtcXG59XFxuLnB4LTEge1xcbiAgcGFkZGluZy1sZWZ0OiAwLjI1cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMC4yNXJlbTtcXG59XFxuLnB4LTIge1xcbiAgcGFkZGluZy1sZWZ0OiAwLjVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjVyZW07XFxufVxcbi5weC0yXFxcXC41IHtcXG4gIHBhZGRpbmctbGVmdDogMC42MjVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjYyNXJlbTtcXG59XFxuLnB4LTMge1xcbiAgcGFkZGluZy1sZWZ0OiAwLjc1cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMC43NXJlbTtcXG59XFxuLnB4LTQge1xcbiAgcGFkZGluZy1sZWZ0OiAxcmVtO1xcbiAgcGFkZGluZy1yaWdodDogMXJlbTtcXG59XFxuLnB4LTYge1xcbiAgcGFkZGluZy1sZWZ0OiAxLjVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAxLjVyZW07XFxufVxcbi5weS0wXFxcXC41IHtcXG4gIHBhZGRpbmctdG9wOiAwLjEyNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjEyNXJlbTtcXG59XFxuLnB5LTEge1xcbiAgcGFkZGluZy10b3A6IDAuMjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC4yNXJlbTtcXG59XFxuLnB5LTEyIHtcXG4gIHBhZGRpbmctdG9wOiAzcmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDNyZW07XFxufVxcbi5weS0yIHtcXG4gIHBhZGRpbmctdG9wOiAwLjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC41cmVtO1xcbn1cXG4ucHktMyB7XFxuICBwYWRkaW5nLXRvcDogMC43NXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjc1cmVtO1xcbn1cXG4ucHktNCB7XFxuICBwYWRkaW5nLXRvcDogMXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAxcmVtO1xcbn1cXG4ucHktNSB7XFxuICBwYWRkaW5nLXRvcDogMS4yNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAxLjI1cmVtO1xcbn1cXG4ucHktOCB7XFxuICBwYWRkaW5nLXRvcDogMnJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAycmVtO1xcbn1cXG4ucGItMSB7XFxuICBwYWRkaW5nLWJvdHRvbTogMC4yNXJlbTtcXG59XFxuLnBiLTIge1xcbiAgcGFkZGluZy1ib3R0b206IDAuNXJlbTtcXG59XFxuLnBiLTIwIHtcXG4gIHBhZGRpbmctYm90dG9tOiA1cmVtO1xcbn1cXG4ucGItNCB7XFxuICBwYWRkaW5nLWJvdHRvbTogMXJlbTtcXG59XFxuLnBiLTYge1xcbiAgcGFkZGluZy1ib3R0b206IDEuNXJlbTtcXG59XFxuLnBsLTEwIHtcXG4gIHBhZGRpbmctbGVmdDogMi41cmVtO1xcbn1cXG4ucHItMTAge1xcbiAgcGFkZGluZy1yaWdodDogMi41cmVtO1xcbn1cXG4ucHItMyB7XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjc1cmVtO1xcbn1cXG4ucHQtMiB7XFxuICBwYWRkaW5nLXRvcDogMC41cmVtO1xcbn1cXG4ucHQtNCB7XFxuICBwYWRkaW5nLXRvcDogMXJlbTtcXG59XFxuLnB0LTUge1xcbiAgcGFkZGluZy10b3A6IDEuMjVyZW07XFxufVxcbi5wdC02IHtcXG4gIHBhZGRpbmctdG9wOiAxLjVyZW07XFxufVxcbi50ZXh0LWxlZnQge1xcbiAgdGV4dC1hbGlnbjogbGVmdDtcXG59XFxuLnRleHQtY2VudGVyIHtcXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcXG59XFxuLnRleHQtcmlnaHQge1xcbiAgdGV4dC1hbGlnbjogcmlnaHQ7XFxufVxcbi5hbGlnbi1ib3R0b20ge1xcbiAgdmVydGljYWwtYWxpZ246IGJvdHRvbTtcXG59XFxuLmZvbnQtYXJhYmljIHtcXG4gIGZvbnQtZmFtaWx5OiBDYWlybywgdWktc2Fucy1zZXJpZiwgc3lzdGVtLXVpLCBzYW5zLXNlcmlmO1xcbn1cXG4uZm9udC1lbmdsaXNoIHtcXG4gIGZvbnQtZmFtaWx5OiBJbnRlciwgdWktc2Fucy1zZXJpZiwgc3lzdGVtLXVpLCBzYW5zLXNlcmlmO1xcbn1cXG4udGV4dC0yeGwge1xcbiAgZm9udC1zaXplOiAxLjVyZW07XFxuICBsaW5lLWhlaWdodDogMnJlbTtcXG59XFxuLnRleHQtM3hsIHtcXG4gIGZvbnQtc2l6ZTogMS44NzVyZW07XFxuICBsaW5lLWhlaWdodDogMi4yNXJlbTtcXG59XFxuLnRleHQtbGcge1xcbiAgZm9udC1zaXplOiAxLjEyNXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxLjc1cmVtO1xcbn1cXG4udGV4dC1zbSB7XFxuICBmb250LXNpemU6IDAuODc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDEuMjVyZW07XFxufVxcbi50ZXh0LXhsIHtcXG4gIGZvbnQtc2l6ZTogMS4yNXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxLjc1cmVtO1xcbn1cXG4udGV4dC14cyB7XFxuICBmb250LXNpemU6IDAuNzVyZW07XFxuICBsaW5lLWhlaWdodDogMXJlbTtcXG59XFxuLmZvbnQtYm9sZCB7XFxuICBmb250LXdlaWdodDogNzAwO1xcbn1cXG4uZm9udC1leHRyYWJvbGQge1xcbiAgZm9udC13ZWlnaHQ6IDgwMDtcXG59XFxuLmZvbnQtbWVkaXVtIHtcXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XFxufVxcbi5mb250LXNlbWlib2xkIHtcXG4gIGZvbnQtd2VpZ2h0OiA2MDA7XFxufVxcbi51cHBlcmNhc2Uge1xcbiAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcXG59XFxuLmNhcGl0YWxpemUge1xcbiAgdGV4dC10cmFuc2Zvcm06IGNhcGl0YWxpemU7XFxufVxcbi5sZWFkaW5nLTYge1xcbiAgbGluZS1oZWlnaHQ6IDEuNXJlbTtcXG59XFxuLnRyYWNraW5nLXdpZGVyIHtcXG4gIGxldHRlci1zcGFjaW5nOiAwLjA1ZW07XFxufVxcbi50ZXh0LWJsdWUtMTAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyMTkgMjM0IDI1NCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ibHVlLTQwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoOTYgMTY1IDI1MCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ibHVlLTYwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMzcgOTkgMjM1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LWJsdWUtNzAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyOSA3OCAyMTYgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtYmx1ZS04MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDMwIDY0IDE3NSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ibHVlLTkwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMzAgNTggMTM4IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LWdyYXktNDAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigxNTYgMTYzIDE3NSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ncmF5LTUwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTA3IDExNCAxMjggLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtZ3JheS02MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDc1IDg1IDk5IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LWdyYXktNzAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYig1NSA2NSA4MSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ncmF5LTgwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMzEgNDEgNTUgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtZ3JheS05MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDE3IDI0IDM5IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LWdyZWVuLTQwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoNzQgMjIyIDEyOCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ncmVlbi02MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIyIDE2MyA3NCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ncmVlbi03MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIxIDEyOCA2MSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ncmVlbi04MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIyIDEwMSA1MiAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ncmVlbi05MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIwIDgzIDQ1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LWluZGlnby04MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDU1IDQ4IDE2MyAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1vcmFuZ2UtNDAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyNTEgMTQ2IDYwIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LW9yYW5nZS02MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIzNCA4OCAxMiAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1vcmFuZ2UtNzAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigxOTQgNjUgMTIgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtb3JhbmdlLTgwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTU0IDUyIDE4IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LW9yYW5nZS05MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDEyNCA0NSAxOCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1wcmltYXJ5LTYwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMiAxMzIgMTk5IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LXByaW1hcnktNzAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigzIDEwNSAxNjEgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtcHJpbWFyeS05MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDEyIDc0IDExMCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1wdXJwbGUtODAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigxMDcgMzMgMTY4IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LXJlZC00MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDI0OCAxMTMgMTEzIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LXJlZC01MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIzOSA2OCA2OCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1yZWQtNjAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyMjAgMzggMzggLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtcmVkLTcwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTg1IDI4IDI4IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LXJlZC04MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDE1MyAyNyAyNyAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC10ZXh0LXByaW1hcnkge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIzIDIzIDIzIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LXRleHQtc2Vjb25kYXJ5IHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYig4MiA4MiA4MiAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC13aGl0ZSB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQteWVsbG93LTQwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjUwIDIwNCAyMSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC15ZWxsb3ctNjAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyMDIgMTM4IDQgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQteWVsbG93LTgwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTMzIDc3IDE0IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi5wbGFjZWhvbGRlci1ncmF5LTUwMDo6LW1vei1wbGFjZWhvbGRlciB7XFxuICAtLXR3LXBsYWNlaG9sZGVyLW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDEwNyAxMTQgMTI4IC8gdmFyKC0tdHctcGxhY2Vob2xkZXItb3BhY2l0eSwgMSkpO1xcbn1cXG4ucGxhY2Vob2xkZXItZ3JheS01MDA6OnBsYWNlaG9sZGVyIHtcXG4gIC0tdHctcGxhY2Vob2xkZXItb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTA3IDExNCAxMjggLyB2YXIoLS10dy1wbGFjZWhvbGRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5vcGFjaXR5LTUwIHtcXG4gIG9wYWNpdHk6IDAuNTtcXG59XFxuLnNoYWRvdyB7XFxuICAtLXR3LXNoYWRvdzogMCAxcHggM3B4IDAgcmdiKDAgMCAwIC8gMC4xKSwgMCAxcHggMnB4IC0xcHggcmdiKDAgMCAwIC8gMC4xKTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMXB4IDNweCAwIHZhcigtLXR3LXNoYWRvdy1jb2xvciksIDAgMXB4IDJweCAtMXB4IHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbn1cXG4uc2hhZG93LWxhcmdlIHtcXG4gIC0tdHctc2hhZG93OiAwIDhweCAyNHB4IDAgcmdiYSgwLCAwLCAwLCAwLjEyKTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgOHB4IDI0cHggMCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG59XFxuLnNoYWRvdy1sZyB7XFxuICAtLXR3LXNoYWRvdzogMCAxMHB4IDE1cHggLTNweCByZ2IoMCAwIDAgLyAwLjEpLCAwIDRweCA2cHggLTRweCByZ2IoMCAwIDAgLyAwLjEpO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCAxMHB4IDE1cHggLTNweCB2YXIoLS10dy1zaGFkb3ctY29sb3IpLCAwIDRweCA2cHggLTRweCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG59XFxuLnNoYWRvdy1zb2Z0IHtcXG4gIC0tdHctc2hhZG93OiAwIDJweCA4cHggMCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCAycHggOHB4IDAgdmFyKC0tdHctc2hhZG93LWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctcmluZy1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXNoYWRvdyk7XFxufVxcbi5zaGFkb3cteGwge1xcbiAgLS10dy1zaGFkb3c6IDAgMjBweCAyNXB4IC01cHggcmdiKDAgMCAwIC8gMC4xKSwgMCA4cHggMTBweCAtNnB4IHJnYigwIDAgMCAvIDAuMSk7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDIwcHggMjVweCAtNXB4IHZhcigtLXR3LXNoYWRvdy1jb2xvciksIDAgOHB4IDEwcHggLTZweCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG59XFxuLnJpbmctMSB7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpIHZhcigtLXR3LXJpbmctb2Zmc2V0LWNvbG9yKTtcXG4gIC0tdHctcmluZy1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIGNhbGMoMXB4ICsgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpKSB2YXIoLS10dy1yaW5nLWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdyksIHZhcigtLXR3LXJpbmctc2hhZG93KSwgdmFyKC0tdHctc2hhZG93LCAwIDAgIzAwMDApO1xcbn1cXG4ucmluZy1ibGFjayB7XFxuICAtLXR3LXJpbmctb3BhY2l0eTogMTtcXG4gIC0tdHctcmluZy1jb2xvcjogcmdiKDAgMCAwIC8gdmFyKC0tdHctcmluZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5yaW5nLW9wYWNpdHktNSB7XFxuICAtLXR3LXJpbmctb3BhY2l0eTogMC4wNTtcXG59XFxuLmZpbHRlciB7XFxuICBmaWx0ZXI6IHZhcigtLXR3LWJsdXIpIHZhcigtLXR3LWJyaWdodG5lc3MpIHZhcigtLXR3LWNvbnRyYXN0KSB2YXIoLS10dy1ncmF5c2NhbGUpIHZhcigtLXR3LWh1ZS1yb3RhdGUpIHZhcigtLXR3LWludmVydCkgdmFyKC0tdHctc2F0dXJhdGUpIHZhcigtLXR3LXNlcGlhKSB2YXIoLS10dy1kcm9wLXNoYWRvdyk7XFxufVxcbi50cmFuc2l0aW9uLWFsbCB7XFxuICB0cmFuc2l0aW9uLXByb3BlcnR5OiBhbGw7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDE1MG1zO1xcbn1cXG4udHJhbnNpdGlvbi1jb2xvcnMge1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogY29sb3IsIGJhY2tncm91bmQtY29sb3IsIGJvcmRlci1jb2xvciwgdGV4dC1kZWNvcmF0aW9uLWNvbG9yLCBmaWxsLCBzdHJva2U7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDE1MG1zO1xcbn1cXG4udHJhbnNpdGlvbi1vcGFjaXR5IHtcXG4gIHRyYW5zaXRpb24tcHJvcGVydHk6IG9wYWNpdHk7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDE1MG1zO1xcbn1cXG4udHJhbnNpdGlvbi10cmFuc2Zvcm0ge1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogdHJhbnNmb3JtO1xcbiAgdHJhbnNpdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAxNTBtcztcXG59XFxuLmR1cmF0aW9uLTIwMCB7XFxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAyMDBtcztcXG59XFxuLmR1cmF0aW9uLTMwMCB7XFxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAzMDBtcztcXG59XFxuXFxuLyogSW1wb3J0IGZvbnRzICovXFxuQGltcG9ydCB1cmwoJ2h0dHBzOi8vZm9udHMuZ29vZ2xlYXBpcy5jb20vY3NzMj9mYW1pbHk9SW50ZXI6d2dodEAzMDA7NDAwOzUwMDs2MDA7NzAwJmRpc3BsYXk9c3dhcCcpO1xcbkBpbXBvcnQgdXJsKCdodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2NzczI/ZmFtaWx5PUNhaXJvOndnaHRAMzAwOzQwMDs1MDA7NjAwOzcwMCZkaXNwbGF5PXN3YXAnKTtcXG5cXG4vKiBNb2Rlcm4gTWluaW1hbGlzdCBCYXNlIFN0eWxlcyAqL1xcbmh0bWwge1xcbiAgc2Nyb2xsLWJlaGF2aW9yOiBzbW9vdGg7XFxufVxcblxcbmJvZHkge1xcbiAgZm9udC1mYW1pbHk6ICdJbnRlcicsICd1aS1zYW5zLXNlcmlmJywgJ3N5c3RlbS11aScsIHNhbnMtc2VyaWY7XFxuICBsaW5lLWhlaWdodDogMS42O1xcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZhZmFmYTsgLyogbmV1dHJhbC01MCAqL1xcbiAgY29sb3I6ICMxNzE3MTc7IC8qIHRleHQtcHJpbWFyeSAqL1xcbiAgZm9udC13ZWlnaHQ6IDQwMDtcXG4gIC13ZWJraXQtZm9udC1zbW9vdGhpbmc6IGFudGlhbGlhc2VkO1xcbiAgLW1vei1vc3gtZm9udC1zbW9vdGhpbmc6IGdyYXlzY2FsZTtcXG59XFxuXFxuLyogQXJhYmljIGZvbnQgKi9cXG4uZm9udC1hcmFiaWMge1xcbiAgZm9udC1mYW1pbHk6ICdDYWlybycsIHNhbnMtc2VyaWY7XFxufVxcblxcbi5mb250LWVuZ2xpc2gge1xcbiAgZm9udC1mYW1pbHk6ICdJbnRlcicsIHNhbnMtc2VyaWY7XFxufVxcblxcbi8qIFJUTCBzdXBwb3J0ICovXFxuW2Rpcj1cXFwicnRsXFxcIl0ge1xcbiAgdGV4dC1hbGlnbjogcmlnaHQ7XFxufVxcblxcbltkaXI9XFxcInJ0bFxcXCJdIC5ydGxcXFxcOnRleHQtbGVmdCB7XFxuICB0ZXh0LWFsaWduOiBsZWZ0O1xcbn1cXG5cXG5bZGlyPVxcXCJydGxcXFwiXSAucnRsXFxcXDp0ZXh0LXJpZ2h0IHtcXG4gIHRleHQtYWxpZ246IHJpZ2h0O1xcbn1cXG5cXG4vKiBDdXN0b20gc2Nyb2xsYmFyICovXFxuOjotd2Via2l0LXNjcm9sbGJhciB7XFxuICB3aWR0aDogNnB4O1xcbiAgaGVpZ2h0OiA2cHg7XFxufVxcblxcbjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sge1xcbiAgYmFja2dyb3VuZDogI2YxZjFmMTtcXG4gIGJvcmRlci1yYWRpdXM6IDNweDtcXG59XFxuXFxuOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XFxuICBiYWNrZ3JvdW5kOiAjYzFjMWMxO1xcbiAgYm9yZGVyLXJhZGl1czogM3B4O1xcbn1cXG5cXG46Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHtcXG4gIGJhY2tncm91bmQ6ICNhOGE4YTg7XFxufVxcblxcbi8qIE1vZGVybiBGb3JtIFN0eWxlcyAqL1xcbi5mb3JtLWlucHV0IHtcXG4gIGRpc3BsYXk6IGJsb2NrO1xcbiAgd2lkdGg6IDEwMCU7XFxuICBib3JkZXItcmFkaXVzOiAwLjc1cmVtO1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjI5IDIyOSAyMjkgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgcGFkZGluZy1sZWZ0OiAxcmVtO1xcbiAgcGFkZGluZy1yaWdodDogMXJlbTtcXG4gIHBhZGRpbmctdG9wOiAwLjc1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuNzVyZW07XFxufVxcbi5mb3JtLWlucHV0OjotbW96LXBsYWNlaG9sZGVyIHtcXG4gIC0tdHctcGxhY2Vob2xkZXItb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTE1IDExNSAxMTUgLyB2YXIoLS10dy1wbGFjZWhvbGRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5mb3JtLWlucHV0OjpwbGFjZWhvbGRlciB7XFxuICAtLXR3LXBsYWNlaG9sZGVyLW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDExNSAxMTUgMTE1IC8gdmFyKC0tdHctcGxhY2Vob2xkZXItb3BhY2l0eSwgMSkpO1xcbn1cXG4uZm9ybS1pbnB1dCB7XFxuICAtLXR3LXNoYWRvdzogMCAycHggOHB4IDAgcmdiYSgwLCAwLCAwLCAwLjA2KTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMnB4IDhweCAwIHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogYWxsO1xcbiAgdHJhbnNpdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAyMDBtcztcXG4gIGJveC1zaGFkb3c6IDAgMnB4IDhweCAwIHJnYmEoMCwgMCwgMCwgMC4wNik7XFxufVxcbi5mb3JtLWlucHV0OmZvY3VzIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYig1NiAxODkgMjQ4IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG4gIG91dGxpbmU6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcXG4gIG91dGxpbmUtb2Zmc2V0OiAycHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpIHZhcigtLXR3LXJpbmctb2Zmc2V0LWNvbG9yKTtcXG4gIC0tdHctcmluZy1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIGNhbGMoMnB4ICsgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpKSB2YXIoLS10dy1yaW5nLWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdyksIHZhcigtLXR3LXJpbmctc2hhZG93KSwgdmFyKC0tdHctc2hhZG93LCAwIDAgIzAwMDApO1xcbiAgLS10dy1yaW5nLW9wYWNpdHk6IDE7XFxuICAtLXR3LXJpbmctY29sb3I6IHJnYig1NiAxODkgMjQ4IC8gdmFyKC0tdHctcmluZy1vcGFjaXR5LCAxKSk7XFxufVxcbkBtZWRpYSAobWluLXdpZHRoOiA2NDBweCkge1xcblxcbiAgLmZvcm0taW5wdXQge1xcbiAgICBmb250LXNpemU6IDAuODc1cmVtO1xcbiAgICBsaW5lLWhlaWdodDogMS4yNXJlbTtcXG4gIH1cXG59XFxuXFxuLmZvcm0taW5wdXQ6aW52YWxpZCB7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjM5IDY4IDY4IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuXFxuLmZvcm0taW5wdXQ6aW52YWxpZDpmb2N1cyB7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjM5IDY4IDY4IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctcmluZy1vcGFjaXR5OiAxO1xcbiAgLS10dy1yaW5nLWNvbG9yOiByZ2IoMjM5IDY4IDY4IC8gdmFyKC0tdHctcmluZy1vcGFjaXR5LCAxKSk7XFxufVxcblxcbi5mb3JtLWxhYmVsIHtcXG4gIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcXG4gIGRpc3BsYXk6IGJsb2NrO1xcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxLjI1cmVtO1xcbiAgZm9udC13ZWlnaHQ6IDUwMDtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyMyAyMyAyMyAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG5cXG4uZm9ybS1lcnJvciB7XFxuICBtYXJnaW4tdG9wOiAwLjI1cmVtO1xcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxLjI1cmVtO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIzOSA2OCA2OCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG5cXG4vKiBNb2Rlcm4gQnV0dG9uIFN0eWxlcyAqL1xcbi5idG4ge1xcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XFxuICBhbGlnbi1pdGVtczogY2VudGVyO1xcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XFxuICBib3JkZXItcmFkaXVzOiAwLjc1cmVtO1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxuICBib3JkZXItY29sb3I6IHRyYW5zcGFyZW50O1xcbiAgcGFkZGluZy1sZWZ0OiAxcmVtO1xcbiAgcGFkZGluZy1yaWdodDogMXJlbTtcXG4gIHBhZGRpbmctdG9wOiAwLjYyNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjYyNXJlbTtcXG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XFxuICBsaW5lLWhlaWdodDogMS4yNXJlbTtcXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XFxuICB0cmFuc2l0aW9uLXByb3BlcnR5OiBhbGw7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDIwMG1zO1xcbn1cXG4uYnRuOmZvY3VzIHtcXG4gIG91dGxpbmU6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcXG4gIG91dGxpbmUtb2Zmc2V0OiAycHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpIHZhcigtLXR3LXJpbmctb2Zmc2V0LWNvbG9yKTtcXG4gIC0tdHctcmluZy1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIGNhbGMoMnB4ICsgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpKSB2YXIoLS10dy1yaW5nLWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdyksIHZhcigtLXR3LXJpbmctc2hhZG93KSwgdmFyKC0tdHctc2hhZG93LCAwIDAgIzAwMDApO1xcbiAgLS10dy1yaW5nLW9mZnNldC13aWR0aDogMnB4O1xcbiAgLS10dy1yaW5nLW9mZnNldC1jb2xvcjogI2ZhZmFmYTtcXG59XFxuXFxuLmJ0bi1wcmltYXJ5IHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigxNCAxNjUgMjMzIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXNoYWRvdzogMCAycHggOHB4IDAgcmdiYSgwLCAwLCAwLCAwLjA2KTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMnB4IDhweCAwIHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XFxuICBhbGlnbi1pdGVtczogY2VudGVyO1xcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XFxuICBib3JkZXItcmFkaXVzOiAwLjc1cmVtO1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxuICBib3JkZXItY29sb3I6IHRyYW5zcGFyZW50O1xcbiAgcGFkZGluZy1sZWZ0OiAxcmVtO1xcbiAgcGFkZGluZy1yaWdodDogMXJlbTtcXG4gIHBhZGRpbmctdG9wOiAwLjYyNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjYyNXJlbTtcXG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XFxuICBsaW5lLWhlaWdodDogMS4yNXJlbTtcXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XFxuICB0cmFuc2l0aW9uLXByb3BlcnR5OiBhbGw7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDIwMG1zO1xcbn1cXG5cXG4uYnRuLXByaW1hcnk6Zm9jdXMge1xcbiAgb3V0bGluZTogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xcbiAgb3V0bGluZS1vZmZzZXQ6IDJweDtcXG4gIC0tdHctcmluZy1vZmZzZXQtc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkgdmFyKC0tdHctcmluZy1vZmZzZXQtY29sb3IpO1xcbiAgLS10dy1yaW5nLXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgY2FsYygycHggKyB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkpIHZhcigtLXR3LXJpbmctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93KSwgdmFyKC0tdHctcmluZy1zaGFkb3cpLCB2YXIoLS10dy1zaGFkb3csIDAgMCAjMDAwMCk7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXdpZHRoOiAycHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LWNvbG9yOiAjZmFmYWZhO1xcbn1cXG5cXG4uYnRuLXByaW1hcnkge1xcbiAgYm94LXNoYWRvdzogMCAycHggOHB4IDAgcmdiYSgwLCAwLCAwLCAwLjA2KTtcXG59XFxuXFxuLmJ0bi1wcmltYXJ5OmhvdmVyIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyIDEzMiAxOTkgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXNoYWRvdzogMCA0cHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4wOCk7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDRweCAxMnB4IDAgdmFyKC0tdHctc2hhZG93LWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctcmluZy1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXNoYWRvdyk7XFxufVxcblxcbi5idG4tcHJpbWFyeTpmb2N1cyB7XFxuICAtLXR3LXJpbmctb3BhY2l0eTogMTtcXG4gIC0tdHctcmluZy1jb2xvcjogcmdiKDU2IDE4OSAyNDggLyB2YXIoLS10dy1yaW5nLW9wYWNpdHksIDEpKTtcXG59XFxuXFxuLmJ0bi1zZWNvbmRhcnkge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDIyOSAyMjkgMjI5IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYig2NCA2NCA2NCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1zaGFkb3c6IDAgMnB4IDhweCAwIHJnYmEoMCwgMCwgMCwgMC4wNik7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDJweCA4cHggMCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xcbiAgYm9yZGVyLXJhZGl1czogMC43NXJlbTtcXG4gIGJvcmRlci13aWR0aDogMXB4O1xcbiAgYm9yZGVyLWNvbG9yOiB0cmFuc3BhcmVudDtcXG4gIHBhZGRpbmctbGVmdDogMXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDFyZW07XFxuICBwYWRkaW5nLXRvcDogMC42MjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC42MjVyZW07XFxuICBmb250LXNpemU6IDAuODc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDEuMjVyZW07XFxuICBmb250LXdlaWdodDogNTAwO1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogYWxsO1xcbiAgdHJhbnNpdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAyMDBtcztcXG59XFxuXFxuLmJ0bi1zZWNvbmRhcnk6Zm9jdXMge1xcbiAgb3V0bGluZTogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xcbiAgb3V0bGluZS1vZmZzZXQ6IDJweDtcXG4gIC0tdHctcmluZy1vZmZzZXQtc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkgdmFyKC0tdHctcmluZy1vZmZzZXQtY29sb3IpO1xcbiAgLS10dy1yaW5nLXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgY2FsYygycHggKyB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkpIHZhcigtLXR3LXJpbmctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93KSwgdmFyKC0tdHctcmluZy1zaGFkb3cpLCB2YXIoLS10dy1zaGFkb3csIDAgMCAjMDAwMCk7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXdpZHRoOiAycHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LWNvbG9yOiAjZmFmYWZhO1xcbn1cXG5cXG4uYnRuLXNlY29uZGFyeSB7XFxuICBib3gtc2hhZG93OiAwIDJweCA4cHggMCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xcbn1cXG5cXG4uYnRuLXNlY29uZGFyeTpob3ZlciB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjUwIDI1MCAyNTAgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXNoYWRvdzogMCA0cHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4wOCk7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDRweCAxMnB4IDAgdmFyKC0tdHctc2hhZG93LWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctcmluZy1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXNoYWRvdyk7XFxufVxcblxcbi5idG4tc2Vjb25kYXJ5OmZvY3VzIHtcXG4gIC0tdHctcmluZy1vcGFjaXR5OiAxO1xcbiAgLS10dy1yaW5nLWNvbG9yOiByZ2IoNTYgMTg5IDI0OCAvIHZhcigtLXR3LXJpbmctb3BhY2l0eSwgMSkpO1xcbn1cXG5cXG4uYnRuLWRhbmdlciB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjM5IDY4IDY4IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXNoYWRvdzogMCAycHggOHB4IDAgcmdiYSgwLCAwLCAwLCAwLjA2KTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMnB4IDhweCAwIHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XFxuICBhbGlnbi1pdGVtczogY2VudGVyO1xcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XFxuICBib3JkZXItcmFkaXVzOiAwLjc1cmVtO1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxuICBib3JkZXItY29sb3I6IHRyYW5zcGFyZW50O1xcbiAgcGFkZGluZy1sZWZ0OiAxcmVtO1xcbiAgcGFkZGluZy1yaWdodDogMXJlbTtcXG4gIHBhZGRpbmctdG9wOiAwLjYyNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjYyNXJlbTtcXG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XFxuICBsaW5lLWhlaWdodDogMS4yNXJlbTtcXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XFxuICB0cmFuc2l0aW9uLXByb3BlcnR5OiBhbGw7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDIwMG1zO1xcbn1cXG5cXG4uYnRuLWRhbmdlcjpmb2N1cyB7XFxuICBvdXRsaW5lOiAycHggc29saWQgdHJhbnNwYXJlbnQ7XFxuICBvdXRsaW5lLW9mZnNldDogMnB4O1xcbiAgLS10dy1yaW5nLW9mZnNldC1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSB2YXIoLS10dy1yaW5nLW9mZnNldC1jb2xvcik7XFxuICAtLXR3LXJpbmctc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCBjYWxjKDJweCArIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSkgdmFyKC0tdHctcmluZy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3cpLCB2YXIoLS10dy1yaW5nLXNoYWRvdyksIHZhcigtLXR3LXNoYWRvdywgMCAwICMwMDAwKTtcXG4gIC0tdHctcmluZy1vZmZzZXQtd2lkdGg6IDJweDtcXG4gIC0tdHctcmluZy1vZmZzZXQtY29sb3I6ICNmYWZhZmE7XFxufVxcblxcbi5idG4tZGFuZ2VyIHtcXG4gIGJveC1zaGFkb3c6IDAgMnB4IDhweCAwIHJnYmEoMCwgMCwgMCwgMC4wNik7XFxufVxcblxcbi5idG4tZGFuZ2VyOmhvdmVyIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyMjAgMzggMzggLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXNoYWRvdzogMCA0cHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4wOCk7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDRweCAxMnB4IDAgdmFyKC0tdHctc2hhZG93LWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctcmluZy1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXNoYWRvdyk7XFxufVxcblxcbi5idG4tZGFuZ2VyOmZvY3VzIHtcXG4gIC0tdHctcmluZy1vcGFjaXR5OiAxO1xcbiAgLS10dy1yaW5nLWNvbG9yOiByZ2IoMjQ4IDExMyAxMTMgLyB2YXIoLS10dy1yaW5nLW9wYWNpdHksIDEpKTtcXG59XFxuXFxuLmJ0bi1zdWNjZXNzIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigzNCAxOTcgOTQgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG4gIC0tdHctc2hhZG93OiAwIDJweCA4cHggMCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCAycHggOHB4IDAgdmFyKC0tdHctc2hhZG93LWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctcmluZy1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXNoYWRvdyk7XFxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XFxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcXG4gIGJvcmRlci1yYWRpdXM6IDAuNzVyZW07XFxuICBib3JkZXItd2lkdGg6IDFweDtcXG4gIGJvcmRlci1jb2xvcjogdHJhbnNwYXJlbnQ7XFxuICBwYWRkaW5nLWxlZnQ6IDFyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAxcmVtO1xcbiAgcGFkZGluZy10b3A6IDAuNjI1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuNjI1cmVtO1xcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxLjI1cmVtO1xcbiAgZm9udC13ZWlnaHQ6IDUwMDtcXG4gIHRyYW5zaXRpb24tcHJvcGVydHk6IGFsbDtcXG4gIHRyYW5zaXRpb24tdGltaW5nLWZ1bmN0aW9uOiBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMjAwbXM7XFxufVxcblxcbi5idG4tc3VjY2Vzczpmb2N1cyB7XFxuICBvdXRsaW5lOiAycHggc29saWQgdHJhbnNwYXJlbnQ7XFxuICBvdXRsaW5lLW9mZnNldDogMnB4O1xcbiAgLS10dy1yaW5nLW9mZnNldC1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSB2YXIoLS10dy1yaW5nLW9mZnNldC1jb2xvcik7XFxuICAtLXR3LXJpbmctc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCBjYWxjKDJweCArIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSkgdmFyKC0tdHctcmluZy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3cpLCB2YXIoLS10dy1yaW5nLXNoYWRvdyksIHZhcigtLXR3LXNoYWRvdywgMCAwICMwMDAwKTtcXG4gIC0tdHctcmluZy1vZmZzZXQtd2lkdGg6IDJweDtcXG4gIC0tdHctcmluZy1vZmZzZXQtY29sb3I6ICNmYWZhZmE7XFxufVxcblxcbi5idG4tc3VjY2VzcyB7XFxuICBib3gtc2hhZG93OiAwIDJweCA4cHggMCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xcbn1cXG5cXG4uYnRuLXN1Y2Nlc3M6aG92ZXIge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIyIDE2MyA3NCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctc2hhZG93OiAwIDRweCAxMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjA4KTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgNHB4IDEycHggMCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG59XFxuXFxuLmJ0bi1zdWNjZXNzOmZvY3VzIHtcXG4gIC0tdHctcmluZy1vcGFjaXR5OiAxO1xcbiAgLS10dy1yaW5nLWNvbG9yOiByZ2IoNzQgMjIyIDEyOCAvIHZhcigtLXR3LXJpbmctb3BhY2l0eSwgMSkpO1xcbn1cXG5cXG4uYnRuLXNtIHtcXG4gIHBhZGRpbmctbGVmdDogMC43NXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDAuNzVyZW07XFxuICBwYWRkaW5nLXRvcDogMC4zNzVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC4zNzVyZW07XFxuICBmb250LXNpemU6IDAuNzVyZW07XFxuICBsaW5lLWhlaWdodDogMXJlbTtcXG59XFxuXFxuLmJ0bi1sZyB7XFxuICBwYWRkaW5nLWxlZnQ6IDEuNXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDEuNXJlbTtcXG4gIHBhZGRpbmctdG9wOiAwLjc1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuNzVyZW07XFxuICBmb250LXNpemU6IDFyZW07XFxuICBsaW5lLWhlaWdodDogMS41cmVtO1xcbn1cXG5cXG4vKiBNb2Rlcm4gQ2FyZCBTdHlsZXMgKi9cXG4uY2FyZCB7XFxuICBib3JkZXItcmFkaXVzOiAxcmVtO1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjQ1IDI0NSAyNDUgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1zaGFkb3c6IDAgMXB4IDNweCAwIHJnYmEoMCwgMCwgMCwgMC4xKSwgMCAxcHggMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjA2KTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMXB4IDNweCAwIHZhcigtLXR3LXNoYWRvdy1jb2xvciksIDAgMXB4IDJweCAwIHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbiAgYm94LXNoYWRvdzogMCAxcHggM3B4IDAgcmdiYSgwLCAwLCAwLCAwLjEpLCAwIDFweCAycHggMCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xcbn1cXG5cXG4uY2FyZC1oZWFkZXIge1xcbiAgYm9yZGVyLWJvdHRvbS13aWR0aDogMXB4O1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDI0NSAyNDUgMjQ1IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG4gIHBhZGRpbmctbGVmdDogMS41cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMS41cmVtO1xcbiAgcGFkZGluZy10b3A6IDEuMjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMS4yNXJlbTtcXG59XFxuXFxuLmNhcmQtYm9keSB7XFxuICBwYWRkaW5nLWxlZnQ6IDEuNXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDEuNXJlbTtcXG4gIHBhZGRpbmctdG9wOiAxLjI1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDEuMjVyZW07XFxufVxcblxcbi5jYXJkLWZvb3RlciB7XFxuICBib3JkZXItYm90dG9tLXJpZ2h0LXJhZGl1czogMXJlbTtcXG4gIGJvcmRlci1ib3R0b20tbGVmdC1yYWRpdXM6IDFyZW07XFxuICBib3JkZXItdG9wLXdpZHRoOiAxcHg7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjQ1IDI0NSAyNDUgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1MCAyNTAgMjUwIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgcGFkZGluZy1sZWZ0OiAxLjVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAxLjVyZW07XFxuICBwYWRkaW5nLXRvcDogMS4yNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAxLjI1cmVtO1xcbn1cXG5cXG4vKiBNb2Rlcm4gVGFibGUgU3R5bGVzICovXFxuLnRhYmxlIHtcXG4gIG1pbi13aWR0aDogMTAwJTtcXG59XFxuLnRhYmxlID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LWRpdmlkZS15LXJldmVyc2U6IDA7XFxuICBib3JkZXItdG9wLXdpZHRoOiBjYWxjKDFweCAqIGNhbGMoMSAtIHZhcigtLXR3LWRpdmlkZS15LXJldmVyc2UpKSk7XFxuICBib3JkZXItYm90dG9tLXdpZHRoOiBjYWxjKDFweCAqIHZhcigtLXR3LWRpdmlkZS15LXJldmVyc2UpKTtcXG4gIC0tdHctZGl2aWRlLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigyNDAgMjQwIDI0MCAvIHZhcigtLXR3LWRpdmlkZS1vcGFjaXR5LCAxKSk7XFxufVxcblxcbi50YWJsZSB0aGVhZCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjUwIDI1MCAyNTAgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcblxcbi50YWJsZSB0aCB7XFxuICBwYWRkaW5nLWxlZnQ6IDEuNXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDEuNXJlbTtcXG4gIHBhZGRpbmctdG9wOiAxcmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDFyZW07XFxuICB0ZXh0LWFsaWduOiBsZWZ0O1xcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDFyZW07XFxuICBmb250LXdlaWdodDogNjAwO1xcbiAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcXG4gIGxldHRlci1zcGFjaW5nOiAwLjA1ZW07XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoODIgODIgODIgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuXFxuLnRhYmxlIHRkIHtcXG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7XFxuICBwYWRkaW5nLWxlZnQ6IDEuNXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDEuNXJlbTtcXG4gIHBhZGRpbmctdG9wOiAxcmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDFyZW07XFxuICBmb250LXNpemU6IDAuODc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDEuMjVyZW07XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjMgMjMgMjMgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuXFxuLnRhYmxlIHRib2R5IHRyOm50aC1jaGlsZChldmVuKSB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjUwIDI1MCAyNTAgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcblxcbi50YWJsZSB0Ym9keSB0cjpob3ZlciB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjQ4IDI0OSAyNTAgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICB0cmFuc2l0aW9uLXByb3BlcnR5OiBjb2xvciwgYmFja2dyb3VuZC1jb2xvciwgYm9yZGVyLWNvbG9yLCB0ZXh0LWRlY29yYXRpb24tY29sb3IsIGZpbGwsIHN0cm9rZTtcXG4gIHRyYW5zaXRpb24tdGltaW5nLWZ1bmN0aW9uOiBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMTUwbXM7XFxufVxcblxcbi8qIE1vZGVybiBCYWRnZSBTdHlsZXMgKi9cXG4uYmFkZ2Uge1xcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XFxuICBhbGlnbi1pdGVtczogY2VudGVyO1xcbiAgYm9yZGVyLXJhZGl1czogOTk5OXB4O1xcbiAgcGFkZGluZy1sZWZ0OiAwLjc1cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMC43NXJlbTtcXG4gIHBhZGRpbmctdG9wOiAwLjI1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuMjVyZW07XFxuICBmb250LXNpemU6IDAuNzVyZW07XFxuICBsaW5lLWhlaWdodDogMXJlbTtcXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XFxufVxcblxcbi5iYWRnZS1wcmltYXJ5IHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyMjQgMjQyIDI1NCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigzIDEwNSAxNjEgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcXG4gIGJvcmRlci1yYWRpdXM6IDk5OTlweDtcXG4gIHBhZGRpbmctbGVmdDogMC43NXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDAuNzVyZW07XFxuICBwYWRkaW5nLXRvcDogMC4yNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjI1cmVtO1xcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDFyZW07XFxuICBmb250LXdlaWdodDogNTAwO1xcbn1cXG5cXG4uYmFkZ2Utc2Vjb25kYXJ5IHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNDUgMjQ1IDI0NSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYig2NCA2NCA2NCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XFxuICBhbGlnbi1pdGVtczogY2VudGVyO1xcbiAgYm9yZGVyLXJhZGl1czogOTk5OXB4O1xcbiAgcGFkZGluZy1sZWZ0OiAwLjc1cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMC43NXJlbTtcXG4gIHBhZGRpbmctdG9wOiAwLjI1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuMjVyZW07XFxuICBmb250LXNpemU6IDAuNzVyZW07XFxuICBsaW5lLWhlaWdodDogMXJlbTtcXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XFxufVxcblxcbi5iYWRnZS1zdWNjZXNzIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyMjAgMjUyIDIzMSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyMSAxMjggNjEgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcXG4gIGJvcmRlci1yYWRpdXM6IDk5OTlweDtcXG4gIHBhZGRpbmctbGVmdDogMC43NXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDAuNzVyZW07XFxuICBwYWRkaW5nLXRvcDogMC4yNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjI1cmVtO1xcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDFyZW07XFxuICBmb250LXdlaWdodDogNTAwO1xcbn1cXG5cXG4uYmFkZ2Utd2FybmluZyB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU0IDI0OSAxOTUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTYxIDk4IDcgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcXG4gIGJvcmRlci1yYWRpdXM6IDk5OTlweDtcXG4gIHBhZGRpbmctbGVmdDogMC43NXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDAuNzVyZW07XFxuICBwYWRkaW5nLXRvcDogMC4yNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjI1cmVtO1xcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDFyZW07XFxuICBmb250LXdlaWdodDogNTAwO1xcbn1cXG5cXG4uYmFkZ2UtZGFuZ2VyIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTQgMjI2IDIyNiAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigxODUgMjggMjggLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcXG4gIGJvcmRlci1yYWRpdXM6IDk5OTlweDtcXG4gIHBhZGRpbmctbGVmdDogMC43NXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDAuNzVyZW07XFxuICBwYWRkaW5nLXRvcDogMC4yNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjI1cmVtO1xcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDFyZW07XFxuICBmb250LXdlaWdodDogNTAwO1xcbn1cXG5cXG4uYmFkZ2UtaW5mbyB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjI0IDI0MiAyNTQgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMyAxMDUgMTYxIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XFxuICBib3JkZXItcmFkaXVzOiA5OTk5cHg7XFxuICBwYWRkaW5nLWxlZnQ6IDAuNzVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjc1cmVtO1xcbiAgcGFkZGluZy10b3A6IDAuMjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC4yNXJlbTtcXG4gIGZvbnQtc2l6ZTogMC43NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxcmVtO1xcbiAgZm9udC13ZWlnaHQ6IDUwMDtcXG59XFxuXFxuLyogQWxlcnQgc3R5bGVzICovXFxuLmFsZXJ0IHtcXG4gIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xcbiAgcGFkZGluZzogMXJlbTtcXG59XFxuXFxuLmFsZXJ0LXN1Y2Nlc3Mge1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMTg3IDI0NyAyMDggLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0MCAyNTMgMjQ0IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIyIDEwMSA1MiAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbiAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XFxuICBwYWRkaW5nOiAxcmVtO1xcbn1cXG5cXG4uYWxlcnQtd2FybmluZyB7XFxuICBib3JkZXItd2lkdGg6IDFweDtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigyNTQgMjQwIDEzOCAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU0IDI1MiAyMzIgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTMzIDc3IDE0IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxuICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcXG4gIHBhZGRpbmc6IDFyZW07XFxufVxcblxcbi5hbGVydC1kYW5nZXIge1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjU0IDIwMiAyMDIgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NCAyNDIgMjQyIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDE1MyAyNyAyNyAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbiAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XFxuICBwYWRkaW5nOiAxcmVtO1xcbn1cXG5cXG4uYWxlcnQtaW5mbyB7XFxuICBib3JkZXItd2lkdGg6IDFweDtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigxOTEgMjE5IDI1NCAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjM5IDI0NiAyNTUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMzAgNjQgMTc1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxuICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcXG4gIHBhZGRpbmc6IDFyZW07XFxufVxcblxcbi8qIEFuaW1hdGlvbiB1dGlsaXRpZXMgKi9cXG4uZmFkZS1pbiB7XFxuICBhbmltYXRpb246IGZhZGVJbiAwLjVzIGVhc2UtaW4tb3V0O1xcbn1cXG5cXG4uc2xpZGUtdXAge1xcbiAgYW5pbWF0aW9uOiBzbGlkZVVwIDAuM3MgZWFzZS1vdXQ7XFxufVxcblxcbkBrZXlmcmFtZXMgZmFkZUluIHtcXG4gIGZyb20ge1xcbiAgICBvcGFjaXR5OiAwO1xcbiAgfVxcbiAgdG8ge1xcbiAgICBvcGFjaXR5OiAxO1xcbiAgfVxcbn1cXG5cXG5Aa2V5ZnJhbWVzIHNsaWRlVXAge1xcbiAgZnJvbSB7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgxMHB4KTtcXG4gICAgb3BhY2l0eTogMDtcXG4gIH1cXG4gIHRvIHtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xcbiAgICBvcGFjaXR5OiAxO1xcbiAgfVxcbn1cXG5cXG4vKiBQcmludCBzdHlsZXMgKi9cXG5AbWVkaWEgcHJpbnQge1xcbiAgLm5vLXByaW50IHtcXG4gICAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50O1xcbiAgfVxcbiAgXFxuICAucHJpbnQtYnJlYWsge1xcbiAgICBwYWdlLWJyZWFrLWJlZm9yZTogYWx3YXlzO1xcbiAgfVxcbiAgXFxuICBib2R5IHtcXG4gICAgZm9udC1zaXplOiAxMnB0O1xcbiAgICBsaW5lLWhlaWdodDogMS40O1xcbiAgfVxcbiAgXFxuICAuY2FyZCB7XFxuICAgIGJveC1zaGFkb3c6IG5vbmU7XFxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7XFxuICB9XFxufVxcblxcbi8qIE1vZGVybiBMYXlvdXQgVXRpbGl0aWVzICovXFxuLnBhZ2UtY29udGFpbmVyIHtcXG4gIG1hcmdpbi1sZWZ0OiBhdXRvO1xcbiAgbWFyZ2luLXJpZ2h0OiBhdXRvO1xcbiAgbWF4LXdpZHRoOiA4MHJlbTtcXG4gIHBhZGRpbmctbGVmdDogMXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDFyZW07XFxufVxcbkBtZWRpYSAobWluLXdpZHRoOiA2NDBweCkge1xcblxcbiAgLnBhZ2UtY29udGFpbmVyIHtcXG4gICAgcGFkZGluZy1sZWZ0OiAxLjVyZW07XFxuICAgIHBhZGRpbmctcmlnaHQ6IDEuNXJlbTtcXG4gIH1cXG59XFxuQG1lZGlhIChtaW4td2lkdGg6IDEwMjRweCkge1xcblxcbiAgLnBhZ2UtY29udGFpbmVyIHtcXG4gICAgcGFkZGluZy1sZWZ0OiAycmVtO1xcbiAgICBwYWRkaW5nLXJpZ2h0OiAycmVtO1xcbiAgfVxcbn1cXG5cXG4uc2VjdGlvbi1zcGFjaW5nIHtcXG4gIHBhZGRpbmctdG9wOiAycmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDJyZW07XFxufVxcblxcbkBtZWRpYSAobWluLXdpZHRoOiAxMDI0cHgpIHtcXG5cXG4gIC5zZWN0aW9uLXNwYWNpbmcge1xcbiAgICBwYWRkaW5nLXRvcDogM3JlbTtcXG4gICAgcGFkZGluZy1ib3R0b206IDNyZW07XFxuICB9XFxufVxcblxcbi5jb250ZW50LXNwYWNpbmcgPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteS1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXRvcDogY2FsYygxLjVyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKSk7XFxuICBtYXJnaW4tYm90dG9tOiBjYWxjKDEuNXJlbSAqIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpO1xcbn1cXG5cXG5AbWVkaWEgKG1pbi13aWR0aDogMTAyNHB4KSB7XFxuXFxuICAuY29udGVudC1zcGFjaW5nID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAgIC0tdHctc3BhY2UteS1yZXZlcnNlOiAwO1xcbiAgICBtYXJnaW4tdG9wOiBjYWxjKDJyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKSk7XFxuICAgIG1hcmdpbi1ib3R0b206IGNhbGMoMnJlbSAqIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpO1xcbiAgfVxcbn1cXG5cXG4vKiBNb2Rlcm4gU2hhZG93cyAqL1xcbi5zaGFkb3ctc29mdCB7XFxuICBib3gtc2hhZG93OiAwIDJweCA4cHggMCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xcbn1cXG5cXG4uc2hhZG93LW1lZGl1bSB7XFxuICBib3gtc2hhZG93OiAwIDRweCAxMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjA4KTtcXG59XFxuXFxuLnNoYWRvdy1sYXJnZSB7XFxuICBib3gtc2hhZG93OiAwIDhweCAyNHB4IDAgcmdiYSgwLCAwLCAwLCAwLjEyKTtcXG59XFxuXFxuLnNoYWRvdy1jYXJkIHtcXG4gIGJveC1zaGFkb3c6IDAgMXB4IDNweCAwIHJnYmEoMCwgMCwgMCwgMC4xKSwgMCAxcHggMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjA2KTtcXG59XFxuXFxuLyogRW5oYW5jZWQgUlRMIFN1cHBvcnQgKi9cXG5bZGlyPVxcXCJydGxcXFwiXSAudGFibGUgdGgge1xcbiAgdGV4dC1hbGlnbjogcmlnaHQ7XFxufVxcblxcbltkaXI9XFxcInJ0bFxcXCJdIC50YWJsZSB0ZCB7XFxuICB0ZXh0LWFsaWduOiByaWdodDtcXG59XFxuXFxuLyogTW9kZXJuIEZvY3VzIFN0YXRlcyAqL1xcbi5mb2N1cy1yaW5nOmZvY3VzIHtcXG4gIG91dGxpbmU6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcXG4gIG91dGxpbmUtb2Zmc2V0OiAycHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpIHZhcigtLXR3LXJpbmctb2Zmc2V0LWNvbG9yKTtcXG4gIC0tdHctcmluZy1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIGNhbGMoMnB4ICsgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpKSB2YXIoLS10dy1yaW5nLWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdyksIHZhcigtLXR3LXJpbmctc2hhZG93KSwgdmFyKC0tdHctc2hhZG93LCAwIDAgIzAwMDApO1xcbiAgLS10dy1yaW5nLW9wYWNpdHk6IDE7XFxuICAtLXR3LXJpbmctY29sb3I6IHJnYig1NiAxODkgMjQ4IC8gdmFyKC0tdHctcmluZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXdpZHRoOiAycHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LWNvbG9yOiAjZmFmYWZhO1xcbn1cXG5cXG4vKiBJbXByb3ZlZCBTY3JvbGxiYXIgKi9cXG46Oi13ZWJraXQtc2Nyb2xsYmFyIHtcXG4gIHdpZHRoOiA4cHg7XFxuICBoZWlnaHQ6IDhweDtcXG59XFxuXFxuOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XFxuICBiYWNrZ3JvdW5kOiAjZjVmNWY1O1xcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xcbn1cXG5cXG46Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcXG4gIGJhY2tncm91bmQ6ICNkNGQ0ZDQ7XFxuICBib3JkZXItcmFkaXVzOiA0cHg7XFxufVxcblxcbjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIge1xcbiAgYmFja2dyb3VuZDogI2EzYTNhMztcXG59XFxuXFxuLyogRGFyayBtb2RlIHN1cHBvcnQgKGlmIG5lZWRlZCkgKi9cXG5AbWVkaWEgKHByZWZlcnMtY29sb3Itc2NoZW1lOiBkYXJrKSB7XFxuICAuZGFyay1tb2RlIHtcXG4gICAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjMgMjMgMjMgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAgIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgICBjb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxuICB9XFxuXFxuICAuZGFyay1tb2RlIC5jYXJkIHtcXG4gICAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gICAgYm9yZGVyLWNvbG9yOiByZ2IoNjQgNjQgNjQgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbiAgICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICAgIGJhY2tncm91bmQtY29sb3I6IHJnYigzOCAzOCAzOCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gIH1cXG5cXG4gIC5kYXJrLW1vZGUgLmZvcm0taW5wdXQge1xcbiAgICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgICBib3JkZXItY29sb3I6IHJnYig4MiA4MiA4MiAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxuICAgIC0tdHctYmctb3BhY2l0eTogMTtcXG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDY0IDY0IDY0IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gICAgY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbiAgfVxcbn1cXG4uYWZ0ZXJcXFxcOmFic29sdXRlOjphZnRlciB7XFxuICBjb250ZW50OiB2YXIoLS10dy1jb250ZW50KTtcXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcXG59XFxuLmFmdGVyXFxcXDpsZWZ0LVxcXFxbMnB4XFxcXF06OmFmdGVyIHtcXG4gIGNvbnRlbnQ6IHZhcigtLXR3LWNvbnRlbnQpO1xcbiAgbGVmdDogMnB4O1xcbn1cXG4uYWZ0ZXJcXFxcOnRvcC1cXFxcWzJweFxcXFxdOjphZnRlciB7XFxuICBjb250ZW50OiB2YXIoLS10dy1jb250ZW50KTtcXG4gIHRvcDogMnB4O1xcbn1cXG4uYWZ0ZXJcXFxcOmgtNTo6YWZ0ZXIge1xcbiAgY29udGVudDogdmFyKC0tdHctY29udGVudCk7XFxuICBoZWlnaHQ6IDEuMjVyZW07XFxufVxcbi5hZnRlclxcXFw6dy01OjphZnRlciB7XFxuICBjb250ZW50OiB2YXIoLS10dy1jb250ZW50KTtcXG4gIHdpZHRoOiAxLjI1cmVtO1xcbn1cXG4uYWZ0ZXJcXFxcOnJvdW5kZWQtZnVsbDo6YWZ0ZXIge1xcbiAgY29udGVudDogdmFyKC0tdHctY29udGVudCk7XFxuICBib3JkZXItcmFkaXVzOiA5OTk5cHg7XFxufVxcbi5hZnRlclxcXFw6Ym9yZGVyOjphZnRlciB7XFxuICBjb250ZW50OiB2YXIoLS10dy1jb250ZW50KTtcXG4gIGJvcmRlci13aWR0aDogMXB4O1xcbn1cXG4uYWZ0ZXJcXFxcOmJvcmRlci1ncmF5LTMwMDo6YWZ0ZXIge1xcbiAgY29udGVudDogdmFyKC0tdHctY29udGVudCk7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjA5IDIxMyAyMTkgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbn1cXG4uYWZ0ZXJcXFxcOmJnLXdoaXRlOjphZnRlciB7XFxuICBjb250ZW50OiB2YXIoLS10dy1jb250ZW50KTtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmFmdGVyXFxcXDp0cmFuc2l0aW9uLWFsbDo6YWZ0ZXIge1xcbiAgY29udGVudDogdmFyKC0tdHctY29udGVudCk7XFxuICB0cmFuc2l0aW9uLXByb3BlcnR5OiBhbGw7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDE1MG1zO1xcbn1cXG4uYWZ0ZXJcXFxcOmNvbnRlbnQtXFxcXFtcXFxcJ1xcXFwnXFxcXF06OmFmdGVyIHtcXG4gIC0tdHctY29udGVudDogJyc7XFxuICBjb250ZW50OiB2YXIoLS10dy1jb250ZW50KTtcXG59XFxuLmhvdmVyXFxcXDpib3JkZXItZ3JheS0zMDA6aG92ZXIge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDIwOSAyMTMgMjE5IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLmhvdmVyXFxcXDpiZy1ibHVlLTIwMDpob3ZlciB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMTkxIDIxOSAyNTQgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6YmctZ3JheS0xMDA6aG92ZXIge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0MyAyNDQgMjQ2IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uaG92ZXJcXFxcOmJnLWdyYXktNTA6aG92ZXIge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0OSAyNTAgMjUxIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uaG92ZXJcXFxcOmJnLWdyZWVuLTcwMDpob3ZlciB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjEgMTI4IDYxIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uaG92ZXJcXFxcOmJnLW5ldXRyYWwtNzU6aG92ZXIge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0OCAyNDkgMjUwIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uaG92ZXJcXFxcOmJnLXByaW1hcnktNzAwOmhvdmVyIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigzIDEwNSAxNjEgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6dGV4dC1ibHVlLTYwMDpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMzcgOTkgMjM1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6dGV4dC1ibHVlLTgwMDpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMzAgNjQgMTc1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6dGV4dC1ibHVlLTkwMDpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMzAgNTggMTM4IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6dGV4dC1ncmF5LTYwMDpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoNzUgODUgOTkgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLmhvdmVyXFxcXDp0ZXh0LWdyYXktNzAwOmhvdmVyIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYig1NSA2NSA4MSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4uaG92ZXJcXFxcOnRleHQtZ3JheS05MDA6aG92ZXIge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDE3IDI0IDM5IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6dGV4dC1ncmVlbi02MDA6aG92ZXIge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIyIDE2MyA3NCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4uaG92ZXJcXFxcOnRleHQtZ3JlZW4tOTAwOmhvdmVyIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyMCA4MyA0NSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4uaG92ZXJcXFxcOnRleHQtcmVkLTYwMDpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjIwIDM4IDM4IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6dGV4dC1yZWQtODAwOmhvdmVyIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigxNTMgMjcgMjcgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLmhvdmVyXFxcXDp0ZXh0LXRleHQtcHJpbWFyeTpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjMgMjMgMjMgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLmZvY3VzXFxcXDp6LTEwOmZvY3VzIHtcXG4gIHotaW5kZXg6IDEwO1xcbn1cXG4uZm9jdXNcXFxcOmJvcmRlci1wcmltYXJ5LTUwMDpmb2N1cyB7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMTQgMTY1IDIzMyAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5mb2N1c1xcXFw6b3V0bGluZS1ub25lOmZvY3VzIHtcXG4gIG91dGxpbmU6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcXG4gIG91dGxpbmUtb2Zmc2V0OiAycHg7XFxufVxcbi5mb2N1c1xcXFw6cmluZy0yOmZvY3VzIHtcXG4gIC0tdHctcmluZy1vZmZzZXQtc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkgdmFyKC0tdHctcmluZy1vZmZzZXQtY29sb3IpO1xcbiAgLS10dy1yaW5nLXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgY2FsYygycHggKyB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkpIHZhcigtLXR3LXJpbmctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93KSwgdmFyKC0tdHctcmluZy1zaGFkb3cpLCB2YXIoLS10dy1zaGFkb3csIDAgMCAjMDAwMCk7XFxufVxcbi5mb2N1c1xcXFw6cmluZy1ibHVlLTUwMDpmb2N1cyB7XFxuICAtLXR3LXJpbmctb3BhY2l0eTogMTtcXG4gIC0tdHctcmluZy1jb2xvcjogcmdiKDU5IDEzMCAyNDYgLyB2YXIoLS10dy1yaW5nLW9wYWNpdHksIDEpKTtcXG59XFxuLmZvY3VzXFxcXDpyaW5nLXByaW1hcnktNTAwOmZvY3VzIHtcXG4gIC0tdHctcmluZy1vcGFjaXR5OiAxO1xcbiAgLS10dy1yaW5nLWNvbG9yOiByZ2IoMTQgMTY1IDIzMyAvIHZhcigtLXR3LXJpbmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uZm9jdXNcXFxcOnJpbmctb2Zmc2V0LTI6Zm9jdXMge1xcbiAgLS10dy1yaW5nLW9mZnNldC13aWR0aDogMnB4O1xcbn1cXG4uZGlzYWJsZWRcXFxcOmN1cnNvci1ub3QtYWxsb3dlZDpkaXNhYmxlZCB7XFxuICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xcbn1cXG4uZGlzYWJsZWRcXFxcOm9wYWNpdHktNTA6ZGlzYWJsZWQge1xcbiAgb3BhY2l0eTogMC41O1xcbn1cXG4uZ3JvdXA6aG92ZXIgLmdyb3VwLWhvdmVyXFxcXDp0ZXh0LWdyYXktNTAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigxMDcgMTE0IDEyOCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4ucGVlcjpjaGVja2VkIH4gLnBlZXItY2hlY2tlZFxcXFw6YmctcHJpbWFyeS02MDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIgMTMyIDE5OSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLnBlZXI6Y2hlY2tlZCB+IC5wZWVyLWNoZWNrZWRcXFxcOmFmdGVyXFxcXDp0cmFuc2xhdGUteC1mdWxsOjphZnRlciB7XFxuICBjb250ZW50OiB2YXIoLS10dy1jb250ZW50KTtcXG4gIC0tdHctdHJhbnNsYXRlLXg6IDEwMCU7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG59XFxuLnBlZXI6Y2hlY2tlZCB+IC5wZWVyLWNoZWNrZWRcXFxcOmFmdGVyXFxcXDpib3JkZXItd2hpdGU6OmFmdGVyIHtcXG4gIGNvbnRlbnQ6IHZhcigtLXR3LWNvbnRlbnQpO1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLnBlZXI6Zm9jdXMgfiAucGVlci1mb2N1c1xcXFw6b3V0bGluZS1ub25lIHtcXG4gIG91dGxpbmU6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcXG4gIG91dGxpbmUtb2Zmc2V0OiAycHg7XFxufVxcbi5wZWVyOmZvY3VzIH4gLnBlZXItZm9jdXNcXFxcOnJpbmctNCB7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpIHZhcigtLXR3LXJpbmctb2Zmc2V0LWNvbG9yKTtcXG4gIC0tdHctcmluZy1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIGNhbGMoNHB4ICsgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpKSB2YXIoLS10dy1yaW5nLWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdyksIHZhcigtLXR3LXJpbmctc2hhZG93KSwgdmFyKC0tdHctc2hhZG93LCAwIDAgIzAwMDApO1xcbn1cXG4ucGVlcjpmb2N1cyB+IC5wZWVyLWZvY3VzXFxcXDpyaW5nLXByaW1hcnktMzAwIHtcXG4gIC0tdHctcmluZy1vcGFjaXR5OiAxO1xcbiAgLS10dy1yaW5nLWNvbG9yOiByZ2IoMTI1IDIxMSAyNTIgLyB2YXIoLS10dy1yaW5nLW9wYWNpdHksIDEpKTtcXG59XFxuQG1lZGlhIChtaW4td2lkdGg6IDY0MHB4KSB7XFxuXFxuICAuc21cXFxcOm15LTgge1xcbiAgICBtYXJnaW4tdG9wOiAycmVtO1xcbiAgICBtYXJnaW4tYm90dG9tOiAycmVtO1xcbiAgfVxcblxcbiAgLnNtXFxcXDptbC0zIHtcXG4gICAgbWFyZ2luLWxlZnQ6IDAuNzVyZW07XFxuICB9XFxuXFxuICAuc21cXFxcOm10LTAge1xcbiAgICBtYXJnaW4tdG9wOiAwcHg7XFxuICB9XFxuXFxuICAuc21cXFxcOmJsb2NrIHtcXG4gICAgZGlzcGxheTogYmxvY2s7XFxuICB9XFxuXFxuICAuc21cXFxcOmZsZXgge1xcbiAgICBkaXNwbGF5OiBmbGV4O1xcbiAgfVxcblxcbiAgLnNtXFxcXDp3LWF1dG8ge1xcbiAgICB3aWR0aDogYXV0bztcXG4gIH1cXG5cXG4gIC5zbVxcXFw6dy1mdWxsIHtcXG4gICAgd2lkdGg6IDEwMCU7XFxuICB9XFxuXFxuICAuc21cXFxcOm1heC13LTJ4bCB7XFxuICAgIG1heC13aWR0aDogNDJyZW07XFxuICB9XFxuXFxuICAuc21cXFxcOm1heC13LTR4bCB7XFxuICAgIG1heC13aWR0aDogNTZyZW07XFxuICB9XFxuXFxuICAuc21cXFxcOm1heC13LTZ4bCB7XFxuICAgIG1heC13aWR0aDogNzJyZW07XFxuICB9XFxuXFxuICAuc21cXFxcOm1heC13LWxnIHtcXG4gICAgbWF4LXdpZHRoOiAzMnJlbTtcXG4gIH1cXG5cXG4gIC5zbVxcXFw6Z3JpZC1jb2xzLTIge1xcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCBtaW5tYXgoMCwgMWZyKSk7XFxuICB9XFxuXFxuICAuc21cXFxcOmZsZXgtcm93IHtcXG4gICAgZmxleC1kaXJlY3Rpb246IHJvdztcXG4gIH1cXG5cXG4gIC5zbVxcXFw6ZmxleC1yb3ctcmV2ZXJzZSB7XFxuICAgIGZsZXgtZGlyZWN0aW9uOiByb3ctcmV2ZXJzZTtcXG4gIH1cXG5cXG4gIC5zbVxcXFw6cC0wIHtcXG4gICAgcGFkZGluZzogMHB4O1xcbiAgfVxcblxcbiAgLnNtXFxcXDpwLTYge1xcbiAgICBwYWRkaW5nOiAxLjVyZW07XFxuICB9XFxuXFxuICAuc21cXFxcOnB4LTYge1xcbiAgICBwYWRkaW5nLWxlZnQ6IDEuNXJlbTtcXG4gICAgcGFkZGluZy1yaWdodDogMS41cmVtO1xcbiAgfVxcblxcbiAgLnNtXFxcXDpwYi00IHtcXG4gICAgcGFkZGluZy1ib3R0b206IDFyZW07XFxuICB9XFxuXFxuICAuc21cXFxcOmFsaWduLW1pZGRsZSB7XFxuICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7XFxuICB9XFxuXFxuICAuc21cXFxcOnRleHQtc20ge1xcbiAgICBmb250LXNpemU6IDAuODc1cmVtO1xcbiAgICBsaW5lLWhlaWdodDogMS4yNXJlbTtcXG4gIH1cXG59XFxuQG1lZGlhIChtaW4td2lkdGg6IDc2OHB4KSB7XFxuXFxuICAubWRcXFxcOmNvbC1zcGFuLTIge1xcbiAgICBncmlkLWNvbHVtbjogc3BhbiAyIC8gc3BhbiAyO1xcbiAgfVxcblxcbiAgLm1kXFxcXDpjb2wtc3Bhbi00IHtcXG4gICAgZ3JpZC1jb2x1bW46IHNwYW4gNCAvIHNwYW4gNDtcXG4gIH1cXG5cXG4gIC5tZFxcXFw6Z3JpZC1jb2xzLTIge1xcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCBtaW5tYXgoMCwgMWZyKSk7XFxuICB9XFxuXFxuICAubWRcXFxcOmdyaWQtY29scy0zIHtcXG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMywgbWlubWF4KDAsIDFmcikpO1xcbiAgfVxcblxcbiAgLm1kXFxcXDpncmlkLWNvbHMtNCB7XFxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDQsIG1pbm1heCgwLCAxZnIpKTtcXG4gIH1cXG59XFxuQG1lZGlhIChtaW4td2lkdGg6IDEwMjRweCkge1xcblxcbiAgLmxnXFxcXDpjb2wtc3Bhbi0yIHtcXG4gICAgZ3JpZC1jb2x1bW46IHNwYW4gMiAvIHNwYW4gMjtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6bWwtMjAge1xcbiAgICBtYXJnaW4tbGVmdDogNXJlbTtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6bWwtNjQge1xcbiAgICBtYXJnaW4tbGVmdDogMTZyZW07XFxuICB9XFxuXFxuICAubGdcXFxcOm1yLTIwIHtcXG4gICAgbWFyZ2luLXJpZ2h0OiA1cmVtO1xcbiAgfVxcblxcbiAgLmxnXFxcXDptci02NCB7XFxuICAgIG1hcmdpbi1yaWdodDogMTZyZW07XFxuICB9XFxuXFxuICAubGdcXFxcOmJsb2NrIHtcXG4gICAgZGlzcGxheTogYmxvY2s7XFxuICB9XFxuXFxuICAubGdcXFxcOmhpZGRlbiB7XFxuICAgIGRpc3BsYXk6IG5vbmU7XFxuICB9XFxuXFxuICAubGdcXFxcOnctMVxcXFwvNCB7XFxuICAgIHdpZHRoOiAyNSU7XFxuICB9XFxuXFxuICAubGdcXFxcOnctM1xcXFwvNCB7XFxuICAgIHdpZHRoOiA3NSU7XFxuICB9XFxuXFxuICAubGdcXFxcOmdyaWQtY29scy0yIHtcXG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMiwgbWlubWF4KDAsIDFmcikpO1xcbiAgfVxcblxcbiAgLmxnXFxcXDpncmlkLWNvbHMtMyB7XFxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDMsIG1pbm1heCgwLCAxZnIpKTtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6Z3JpZC1jb2xzLTQge1xcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCg0LCBtaW5tYXgoMCwgMWZyKSk7XFxuICB9XFxuXFxuICAubGdcXFxcOmZsZXgtcm93IHtcXG4gICAgZmxleC1kaXJlY3Rpb246IHJvdztcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6cHgtOCB7XFxuICAgIHBhZGRpbmctbGVmdDogMnJlbTtcXG4gICAgcGFkZGluZy1yaWdodDogMnJlbTtcXG4gIH1cXG59XFxuLnJ0bFxcXFw6bGVmdC0wOndoZXJlKFtkaXI9XFxcInJ0bFxcXCJdLCBbZGlyPVxcXCJydGxcXFwiXSAqKSB7XFxuICBsZWZ0OiAwcHg7XFxufVxcbi5ydGxcXFxcOmxlZnQtYXV0bzp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgbGVmdDogYXV0bztcXG59XFxuLnJ0bFxcXFw6cmlnaHQtMDp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgcmlnaHQ6IDBweDtcXG59XFxuLnJ0bFxcXFw6cmlnaHQtMzp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgcmlnaHQ6IDAuNzVyZW07XFxufVxcbi5ydGxcXFxcOnJpZ2h0LWF1dG86d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopIHtcXG4gIHJpZ2h0OiBhdXRvO1xcbn1cXG4ucnRsXFxcXDptbC0wOndoZXJlKFtkaXI9XFxcInJ0bFxcXCJdLCBbZGlyPVxcXCJydGxcXFwiXSAqKSB7XFxuICBtYXJnaW4tbGVmdDogMHB4O1xcbn1cXG4ucnRsXFxcXDptbC0xOndoZXJlKFtkaXI9XFxcInJ0bFxcXCJdLCBbZGlyPVxcXCJydGxcXFwiXSAqKSB7XFxuICBtYXJnaW4tbGVmdDogMC4yNXJlbTtcXG59XFxuLnJ0bFxcXFw6bWwtMjp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgbWFyZ2luLWxlZnQ6IDAuNXJlbTtcXG59XFxuLnJ0bFxcXFw6bWwtMzp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgbWFyZ2luLWxlZnQ6IDAuNzVyZW07XFxufVxcbi5ydGxcXFxcOm1yLTA6d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopIHtcXG4gIG1hcmdpbi1yaWdodDogMHB4O1xcbn1cXG4ucnRsXFxcXDptci0xOndoZXJlKFtkaXI9XFxcInJ0bFxcXCJdLCBbZGlyPVxcXCJydGxcXFwiXSAqKSB7XFxuICBtYXJnaW4tcmlnaHQ6IDAuMjVyZW07XFxufVxcbi5ydGxcXFxcOm1yLTI6d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopIHtcXG4gIG1hcmdpbi1yaWdodDogMC41cmVtO1xcbn1cXG4ucnRsXFxcXDptci00OndoZXJlKFtkaXI9XFxcInJ0bFxcXCJdLCBbZGlyPVxcXCJydGxcXFwiXSAqKSB7XFxuICBtYXJnaW4tcmlnaHQ6IDFyZW07XFxufVxcbi5ydGxcXFxcOm1yLTU6d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopIHtcXG4gIG1hcmdpbi1yaWdodDogMS4yNXJlbTtcXG59XFxuLnJ0bFxcXFw6LXRyYW5zbGF0ZS14LTA6d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopIHtcXG4gIC0tdHctdHJhbnNsYXRlLXg6IC0wcHg7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG59XFxuLnJ0bFxcXFw6dHJhbnNsYXRlLXgtZnVsbDp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgLS10dy10cmFuc2xhdGUteDogMTAwJTtcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKHZhcigtLXR3LXRyYW5zbGF0ZS14KSwgdmFyKC0tdHctdHJhbnNsYXRlLXkpKSByb3RhdGUodmFyKC0tdHctcm90YXRlKSkgc2tld1godmFyKC0tdHctc2tldy14KSkgc2tld1kodmFyKC0tdHctc2tldy15KSkgc2NhbGVYKHZhcigtLXR3LXNjYWxlLXgpKSBzY2FsZVkodmFyKC0tdHctc2NhbGUteSkpO1xcbn1cXG4ucnRsXFxcXDpzcGFjZS14LXJldmVyc2U6d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXgtcmV2ZXJzZTogMTtcXG59XFxuLnJ0bFxcXFw6cGwtMTA6d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopIHtcXG4gIHBhZGRpbmctbGVmdDogMi41cmVtO1xcbn1cXG4ucnRsXFxcXDpwbC0zOndoZXJlKFtkaXI9XFxcInJ0bFxcXCJdLCBbZGlyPVxcXCJydGxcXFwiXSAqKSB7XFxuICBwYWRkaW5nLWxlZnQ6IDAuNzVyZW07XFxufVxcbi5ydGxcXFxcOnByLTA6d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopIHtcXG4gIHBhZGRpbmctcmlnaHQ6IDBweDtcXG59XFxuLnJ0bFxcXFw6cHItMTA6d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopIHtcXG4gIHBhZGRpbmctcmlnaHQ6IDIuNXJlbTtcXG59XFxuLnJ0bFxcXFw6cHItMzp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgcGFkZGluZy1yaWdodDogMC43NXJlbTtcXG59XFxuXCIsIFwiXCIse1widmVyc2lvblwiOjMsXCJzb3VyY2VzXCI6W1wid2VicGFjazovL3N0eWxlcy9nbG9iYWxzLmNzc1wiXSxcIm5hbWVzXCI6W10sXCJtYXBwaW5nc1wiOlwiQUFBQTtFQUFBLHdCQUEwQjtFQUExQix3QkFBMEI7RUFBMUIsbUJBQTBCO0VBQTFCLG1CQUEwQjtFQUExQixjQUEwQjtFQUExQixjQUEwQjtFQUExQixjQUEwQjtFQUExQixlQUEwQjtFQUExQixlQUEwQjtFQUExQixhQUEwQjtFQUExQixhQUEwQjtFQUExQixrQkFBMEI7RUFBMUIsc0NBQTBCO0VBQTFCLDhCQUEwQjtFQUExQiw2QkFBMEI7RUFBMUIsNEJBQTBCO0VBQTFCLGVBQTBCO0VBQTFCLG9CQUEwQjtFQUExQixzQkFBMEI7RUFBMUIsdUJBQTBCO0VBQTFCLHdCQUEwQjtFQUExQixrQkFBMEI7RUFBMUIsMkJBQTBCO0VBQTFCLDRCQUEwQjtFQUExQixzQ0FBMEI7RUFBMUIsa0NBQTBCO0VBQTFCLDJCQUEwQjtFQUExQixzQkFBMEI7RUFBMUIsOEJBQTBCO0VBQTFCLFlBQTBCO0VBQTFCLGtCQUEwQjtFQUExQixnQkFBMEI7RUFBMUIsaUJBQTBCO0VBQTFCLGtCQUEwQjtFQUExQixjQUEwQjtFQUExQixnQkFBMEI7RUFBMUIsYUFBMEI7RUFBMUIsbUJBQTBCO0VBQTFCLHFCQUEwQjtFQUExQiwyQkFBMEI7RUFBMUIseUJBQTBCO0VBQTFCLDBCQUEwQjtFQUExQiwyQkFBMEI7RUFBMUIsdUJBQTBCO0VBQTFCLHdCQUEwQjtFQUExQix5QkFBMEI7RUFBMUIsc0JBQTBCO0VBQTFCLG9CQUEwQjtFQUExQixzQkFBMEI7RUFBMUIscUJBQTBCO0VBQTFCO0FBQTBCOztBQUExQjtFQUFBLHdCQUEwQjtFQUExQix3QkFBMEI7RUFBMUIsbUJBQTBCO0VBQTFCLG1CQUEwQjtFQUExQixjQUEwQjtFQUExQixjQUEwQjtFQUExQixjQUEwQjtFQUExQixlQUEwQjtFQUExQixlQUEwQjtFQUExQixhQUEwQjtFQUExQixhQUEwQjtFQUExQixrQkFBMEI7RUFBMUIsc0NBQTBCO0VBQTFCLDhCQUEwQjtFQUExQiw2QkFBMEI7RUFBMUIsNEJBQTBCO0VBQTFCLGVBQTBCO0VBQTFCLG9CQUEwQjtFQUExQixzQkFBMEI7RUFBMUIsdUJBQTBCO0VBQTFCLHdCQUEwQjtFQUExQixrQkFBMEI7RUFBMUIsMkJBQTBCO0VBQTFCLDRCQUEwQjtFQUExQixzQ0FBMEI7RUFBMUIsa0NBQTBCO0VBQTFCLDJCQUEwQjtFQUExQixzQkFBMEI7RUFBMUIsOEJBQTBCO0VBQTFCLFlBQTBCO0VBQTFCLGtCQUEwQjtFQUExQixnQkFBMEI7RUFBMUIsaUJBQTBCO0VBQTFCLGtCQUEwQjtFQUExQixjQUEwQjtFQUExQixnQkFBMEI7RUFBMUIsYUFBMEI7RUFBMUIsbUJBQTBCO0VBQTFCLHFCQUEwQjtFQUExQiwyQkFBMEI7RUFBMUIseUJBQTBCO0VBQTFCLDBCQUEwQjtFQUExQiwyQkFBMEI7RUFBMUIsdUJBQTBCO0VBQTFCLHdCQUEwQjtFQUExQix5QkFBMEI7RUFBMUIsc0JBQTBCO0VBQTFCLG9CQUEwQjtFQUExQixzQkFBMEI7RUFBMUIscUJBQTBCO0VBQTFCO0FBQTBCLENBQTFCOztDQUEwQixDQUExQjs7O0NBQTBCOztBQUExQjs7O0VBQUEsc0JBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLGVBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLG1CQUEwQixFQUExQixNQUEwQjtFQUExQixxQkFBMEIsRUFBMUIsTUFBMEI7QUFBQTs7QUFBMUI7O0VBQUEsZ0JBQTBCO0FBQUE7O0FBQTFCOzs7Ozs7OztDQUEwQjs7QUFBMUI7O0VBQUEsZ0JBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLDhCQUEwQixFQUExQixNQUEwQjtFQUExQixnQkFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsY0FBMEI7S0FBMUIsV0FBMEIsRUFBMUIsTUFBMEI7RUFBMUIsd0RBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLDZCQUEwQixFQUExQixNQUEwQjtFQUExQiwrQkFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsd0NBQTBCLEVBQTFCLE1BQTBCO0FBQUE7O0FBQTFCOzs7Q0FBMEI7O0FBQTFCO0VBQUEsU0FBMEIsRUFBMUIsTUFBMEI7RUFBMUIsb0JBQTBCLEVBQTFCLE1BQTBCO0FBQUE7O0FBQTFCOzs7O0NBQTBCOztBQUExQjtFQUFBLFNBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLGNBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLHFCQUEwQixFQUExQixNQUEwQjtBQUFBOztBQUExQjs7Q0FBMEI7O0FBQTFCO0VBQUEseUNBQTBCO1VBQTFCLGlDQUEwQjtBQUFBOztBQUExQjs7Q0FBMEI7O0FBQTFCOzs7Ozs7RUFBQSxrQkFBMEI7RUFBMUIsb0JBQTBCO0FBQUE7O0FBQTFCOztDQUEwQjs7QUFBMUI7RUFBQSxjQUEwQjtFQUExQix3QkFBMEI7QUFBQTs7QUFBMUI7O0NBQTBCOztBQUExQjs7RUFBQSxtQkFBMEI7QUFBQTs7QUFBMUI7Ozs7O0NBQTBCOztBQUExQjs7OztFQUFBLCtHQUEwQixFQUExQixNQUEwQjtFQUExQiw2QkFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsK0JBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLGNBQTBCLEVBQTFCLE1BQTBCO0FBQUE7O0FBQTFCOztDQUEwQjs7QUFBMUI7RUFBQSxjQUEwQjtBQUFBOztBQUExQjs7Q0FBMEI7O0FBQTFCOztFQUFBLGNBQTBCO0VBQTFCLGNBQTBCO0VBQTFCLGtCQUEwQjtFQUExQix3QkFBMEI7QUFBQTs7QUFBMUI7RUFBQSxlQUEwQjtBQUFBOztBQUExQjtFQUFBLFdBQTBCO0FBQUE7O0FBQTFCOzs7O0NBQTBCOztBQUExQjtFQUFBLGNBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLHFCQUEwQixFQUExQixNQUEwQjtFQUExQix5QkFBMEIsRUFBMUIsTUFBMEI7QUFBQTs7QUFBMUI7Ozs7Q0FBMEI7O0FBQTFCOzs7OztFQUFBLG9CQUEwQixFQUExQixNQUEwQjtFQUExQiw4QkFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsZ0NBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLGVBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLG9CQUEwQixFQUExQixNQUEwQjtFQUExQixvQkFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsdUJBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLGNBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLFNBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLFVBQTBCLEVBQTFCLE1BQTBCO0FBQUE7O0FBQTFCOztDQUEwQjs7QUFBMUI7O0VBQUEsb0JBQTBCO0FBQUE7O0FBQTFCOzs7Q0FBMEI7O0FBQTFCOzs7O0VBQUEsMEJBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLDZCQUEwQixFQUExQixNQUEwQjtFQUExQixzQkFBMEIsRUFBMUIsTUFBMEI7QUFBQTs7QUFBMUI7O0NBQTBCOztBQUExQjtFQUFBLGFBQTBCO0FBQUE7O0FBQTFCOztDQUEwQjs7QUFBMUI7RUFBQSxnQkFBMEI7QUFBQTs7QUFBMUI7O0NBQTBCOztBQUExQjtFQUFBLHdCQUEwQjtBQUFBOztBQUExQjs7Q0FBMEI7O0FBQTFCOztFQUFBLFlBQTBCO0FBQUE7O0FBQTFCOzs7Q0FBMEI7O0FBQTFCO0VBQUEsNkJBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLG9CQUEwQixFQUExQixNQUEwQjtBQUFBOztBQUExQjs7Q0FBMEI7O0FBQTFCO0VBQUEsd0JBQTBCO0FBQUE7O0FBQTFCOzs7Q0FBMEI7O0FBQTFCO0VBQUEsMEJBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLGFBQTBCLEVBQTFCLE1BQTBCO0FBQUE7O0FBQTFCOztDQUEwQjs7QUFBMUI7RUFBQSxrQkFBMEI7QUFBQTs7QUFBMUI7O0NBQTBCOztBQUExQjs7Ozs7Ozs7Ozs7OztFQUFBLFNBQTBCO0FBQUE7O0FBQTFCO0VBQUEsU0FBMEI7RUFBMUIsVUFBMEI7QUFBQTs7QUFBMUI7RUFBQSxVQUEwQjtBQUFBOztBQUExQjs7O0VBQUEsZ0JBQTBCO0VBQTFCLFNBQTBCO0VBQTFCLFVBQTBCO0FBQUE7O0FBQTFCOztDQUEwQjtBQUExQjtFQUFBLFVBQTBCO0FBQUE7O0FBQTFCOztDQUEwQjs7QUFBMUI7RUFBQSxnQkFBMEI7QUFBQTs7QUFBMUI7OztDQUEwQjs7QUFBMUI7RUFBQSxVQUEwQixFQUExQixNQUEwQjtFQUExQixjQUEwQixFQUExQixNQUEwQjtBQUFBOztBQUExQjs7RUFBQSxVQUEwQixFQUExQixNQUEwQjtFQUExQixjQUEwQixFQUExQixNQUEwQjtBQUFBOztBQUExQjs7Q0FBMEI7O0FBQTFCOztFQUFBLGVBQTBCO0FBQUE7O0FBQTFCOztDQUEwQjtBQUExQjtFQUFBLGVBQTBCO0FBQUE7O0FBQTFCOzs7O0NBQTBCOztBQUExQjs7Ozs7Ozs7RUFBQSxjQUEwQixFQUExQixNQUEwQjtFQUExQixzQkFBMEIsRUFBMUIsTUFBMEI7QUFBQTs7QUFBMUI7O0NBQTBCOztBQUExQjs7RUFBQSxlQUEwQjtFQUExQixZQUEwQjtBQUFBOztBQUExQix3RUFBMEI7QUFBMUI7RUFBQSxhQUEwQjtBQUFBOztBQUExQjtFQUFBLHdCQUEwQjtLQUExQixxQkFBMEI7VUFBMUIsZ0JBQTBCO0VBQTFCLHNCQUEwQjtFQUExQixxQkFBMEI7RUFBMUIsaUJBQTBCO0VBQTFCLGtCQUEwQjtFQUExQixtQkFBMEI7RUFBMUIsc0JBQTBCO0VBQTFCLHNCQUEwQjtFQUExQixxQkFBMEI7RUFBMUIsZUFBMEI7RUFBMUIsbUJBQTBCO0VBQTFCLHNCQUEwQjtBQUFBOztBQUExQjtFQUFBLDhCQUEwQjtFQUExQixtQkFBMEI7RUFBMUIsNENBQTBCO0VBQTFCLDJCQUEwQjtFQUExQiw0QkFBMEI7RUFBMUIsd0JBQTBCO0VBQTFCLDJHQUEwQjtFQUExQix5R0FBMEI7RUFBMUIsaUZBQTBCO0VBQTFCO0FBQTBCOztBQUExQjtFQUFBLGNBQTBCO0VBQTFCO0FBQTBCOztBQUExQjtFQUFBLGNBQTBCO0VBQTFCO0FBQTBCOztBQUExQjtFQUFBO0FBQTBCOztBQUExQjtFQUFBLGlCQUEwQjtFQUExQjtBQUEwQjs7QUFBMUI7RUFBQTtBQUEwQjs7QUFBMUI7RUFBQSxjQUEwQjtFQUExQjtBQUEwQjs7QUFBMUI7RUFBQSxtUEFBMEI7RUFBMUIsd0NBQTBCO0VBQTFCLDRCQUEwQjtFQUExQiw0QkFBMEI7RUFBMUIscUJBQTBCO0VBQTFCLGlDQUEwQjtVQUExQjtBQUEwQjs7QUFBMUI7RUFBQSx5QkFBMEI7RUFBMUIsNEJBQTBCO0VBQTFCLHdCQUEwQjtFQUExQix3QkFBMEI7RUFBMUIsc0JBQTBCO0VBQTFCLGlDQUEwQjtVQUExQjtBQUEwQjs7QUFBMUI7RUFBQSx3QkFBMEI7S0FBMUIscUJBQTBCO1VBQTFCLGdCQUEwQjtFQUExQixVQUEwQjtFQUExQixpQ0FBMEI7VUFBMUIseUJBQTBCO0VBQTFCLHFCQUEwQjtFQUExQixzQkFBMEI7RUFBMUIsNkJBQTBCO0VBQTFCLHlCQUEwQjtLQUExQixzQkFBMEI7VUFBMUIsaUJBQTBCO0VBQTFCLGNBQTBCO0VBQTFCLFlBQTBCO0VBQTFCLFdBQTBCO0VBQTFCLGNBQTBCO0VBQTFCLHNCQUEwQjtFQUExQixxQkFBMEI7RUFBMUIsaUJBQTBCO0VBQTFCO0FBQTBCOztBQUExQjtFQUFBO0FBQTBCOztBQUExQjtFQUFBO0FBQTBCOztBQUExQjtFQUFBLDhCQUEwQjtFQUExQixtQkFBMEI7RUFBMUIsNENBQTBCO0VBQTFCLDJCQUEwQjtFQUExQiw0QkFBMEI7RUFBMUIsd0JBQTBCO0VBQTFCLDJHQUEwQjtFQUExQix5R0FBMEI7RUFBMUI7QUFBMEI7O0FBQTFCO0VBQUEseUJBQTBCO0VBQTFCLDhCQUEwQjtFQUExQiwwQkFBMEI7RUFBMUIsMkJBQTBCO0VBQTFCO0FBQTBCOztBQUExQjtFQUFBLHNRQUEwQjtBQUFBOztBQUExQjs7RUFBQTtJQUFBLHdCQUEwQjtPQUExQixxQkFBMEI7WUFBMUI7RUFBMEI7QUFBQTs7QUFBMUI7RUFBQSxvS0FBMEI7QUFBQTs7QUFBMUI7O0VBQUE7SUFBQSx3QkFBMEI7T0FBMUIscUJBQTBCO1lBQTFCO0VBQTBCO0FBQUE7O0FBQTFCO0VBQUEseUJBQTBCO0VBQTFCO0FBQTBCOztBQUExQjtFQUFBLHVPQUEwQjtFQUExQix5QkFBMEI7RUFBMUIsOEJBQTBCO0VBQTFCLDBCQUEwQjtFQUExQiwyQkFBMEI7RUFBMUIsNEJBQTBCO0FBQUE7O0FBQTFCOztFQUFBO0lBQUEsd0JBQTBCO09BQTFCLHFCQUEwQjtZQUExQjtFQUEwQjtBQUFBOztBQUExQjtFQUFBLHlCQUEwQjtFQUExQjtBQUEwQjs7QUFBMUI7RUFBQSxpQkFBMEI7RUFBMUIscUJBQTBCO0VBQTFCLGVBQTBCO0VBQTFCLGdCQUEwQjtFQUExQixVQUEwQjtFQUExQixnQkFBMEI7RUFBMUI7QUFBMEI7O0FBQTFCO0VBQUEsNkJBQTBCO0VBQTFCO0FBQTBCO0FBQzFCO0VBQUEsd0JBQWdDO0tBQWhDLHFCQUFnQztVQUFoQyxnQkFBZ0M7RUFBaEMsc0JBQWdDO0VBQWhDLHFCQUFnQztFQUFoQyxpQkFBZ0M7RUFBaEMsa0JBQWdDO0VBQWhDLG1CQUFnQztFQUFoQyxzQkFBZ0M7RUFBaEMsc0JBQWdDO0VBQWhDLHFCQUFnQztFQUFoQyxlQUFnQztFQUFoQyxtQkFBZ0M7RUFBaEMsc0JBQWdDO0FBQUE7QUFBaEM7RUFBQSw4QkFBZ0M7RUFBaEMsbUJBQWdDO0VBQWhDLDRDQUFnQztFQUFoQywyQkFBZ0M7RUFBaEMsNEJBQWdDO0VBQWhDLHdCQUFnQztFQUFoQywyR0FBZ0M7RUFBaEMseUdBQWdDO0VBQWhDLGlGQUFnQztFQUFoQztBQUFnQztBQUFoQztFQUFBLGNBQWdDO0VBQWhDO0FBQWdDO0FBQWhDO0VBQUEsY0FBZ0M7RUFBaEM7QUFBZ0M7QUFBaEM7RUFBQTtBQUFnQztBQUFoQztFQUFBLGlCQUFnQztFQUFoQztBQUFnQztBQUFoQztFQUFBO0FBQWdDO0FBQWhDO0VBQUEsY0FBZ0M7RUFBaEM7QUFBZ0M7QUFDaEM7RUFBQSxrQkFBK0I7RUFBL0IsVUFBK0I7RUFBL0IsV0FBK0I7RUFBL0IsVUFBK0I7RUFBL0IsWUFBK0I7RUFBL0IsZ0JBQStCO0VBQS9CLHNCQUErQjtFQUEvQixtQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBLFFBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUEsaUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsaUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBLHVCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHFCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9COztFQUFBO0lBQUE7RUFBK0I7QUFBQTtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQSx3QkFBK0I7S0FBL0IscUJBQStCO1VBQS9CO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQSx1QkFBK0I7RUFBL0Isc0RBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsdUJBQStCO0VBQS9CLHVEQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHVCQUErQjtFQUEvQixvREFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSx1QkFBK0I7RUFBL0Isb0RBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsdUJBQStCO0VBQS9CLCtEQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHVCQUErQjtFQUEvQiw4REFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSx1QkFBK0I7RUFBL0IsK0RBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsdUJBQStCO0VBQS9CLDREQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHVCQUErQjtFQUEvQiw4REFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSx1QkFBK0I7RUFBL0IsNERBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsd0JBQStCO0VBQS9CLGtFQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBLGdCQUErQjtFQUEvQix1QkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUEsc0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUEscUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsb0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsc0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEscUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsb0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEscUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsb0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsaUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsbUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsb0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsaUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsb0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsaUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUEsaUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsbUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsbUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsbUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsa0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLG9CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLDJCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLDJCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUEsMEVBQStCO0VBQS9CLDhGQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLDZDQUErQjtFQUEvQix3REFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSwrRUFBK0I7RUFBL0IsbUdBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsNENBQStCO0VBQS9CLHVEQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGdGQUErQjtFQUEvQixvR0FBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSwyR0FBK0I7RUFBL0IseUdBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsb0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBLHdCQUErQjtFQUEvQix3REFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSwrRkFBK0I7RUFBL0Isd0RBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsNEJBQStCO0VBQS9CLHdEQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLDhCQUErQjtFQUEvQix3REFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCOztBQUUvQixpQkFBaUI7QUFDakIsbUdBQW1HO0FBQ25HLG1HQUFtRzs7QUFFbkcsa0NBQWtDO0FBQ2xDO0VBQ0UsdUJBQXVCO0FBQ3pCOztBQUVBO0VBQ0UsOERBQThEO0VBQzlELGdCQUFnQjtFQUNoQix5QkFBeUIsRUFBRSxlQUFlO0VBQzFDLGNBQWMsRUFBRSxpQkFBaUI7RUFDakMsZ0JBQWdCO0VBQ2hCLG1DQUFtQztFQUNuQyxrQ0FBa0M7QUFDcEM7O0FBRUEsZ0JBQWdCO0FBQ2hCO0VBQ0UsZ0NBQWdDO0FBQ2xDOztBQUVBO0VBQ0UsZ0NBQWdDO0FBQ2xDOztBQUVBLGdCQUFnQjtBQUNoQjtFQUNFLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGlCQUFpQjtBQUNuQjs7QUFFQSxxQkFBcUI7QUFDckI7RUFDRSxVQUFVO0VBQ1YsV0FBVztBQUNiOztBQUVBO0VBQ0UsbUJBQW1CO0VBQ25CLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxtQkFBbUI7QUFDckI7O0FBRUEsdUJBQXVCO0FBRXJCO0VBQUEsY0FBa1A7RUFBbFAsV0FBa1A7RUFBbFAsc0JBQWtQO0VBQWxQLGlCQUFrUDtFQUFsUCxzQkFBa1A7RUFBbFAsNERBQWtQO0VBQWxQLGtCQUFrUDtFQUFsUCw0REFBa1A7RUFBbFAsa0JBQWtQO0VBQWxQLG1CQUFrUDtFQUFsUCxvQkFBa1A7RUFBbFA7QUFBa1A7QUFBbFA7RUFBQSwyQkFBa1A7RUFBbFA7QUFBa1A7QUFBbFA7RUFBQSwyQkFBa1A7RUFBbFA7QUFBa1A7QUFBbFA7RUFBQSw0Q0FBa1A7RUFBbFAsdURBQWtQO0VBQWxQLHVHQUFrUDtFQUFsUCx3QkFBa1A7RUFBbFAsd0RBQWtQO0VBQWxQLDBCQUFrUDtFQUFsUDtBQUFrUDtBQUFsUDtFQUFBLHNCQUFrUDtFQUFsUCwyREFBa1A7RUFBbFAsOEJBQWtQO0VBQWxQLG1CQUFrUDtFQUFsUCwyR0FBa1A7RUFBbFAseUdBQWtQO0VBQWxQLDRGQUFrUDtFQUFsUCxvQkFBa1A7RUFBbFA7QUFBa1A7QUFBbFA7O0VBQUE7SUFBQSxtQkFBa1A7SUFBbFA7RUFBa1A7QUFBQTs7QUFJbFA7RUFBQSxzQkFBNEU7RUFBNUU7QUFBNEU7O0FBQTVFO0VBQUEsc0JBQTRFO0VBQTVFLDBEQUE0RTtFQUE1RSxvQkFBNEU7RUFBNUU7QUFBNEU7O0FBSTVFO0VBQUEscUJBQXVEO0VBQXZELGNBQXVEO0VBQXZELG1CQUF1RDtFQUF2RCxvQkFBdUQ7RUFBdkQsZ0JBQXVEO0VBQXZELG9CQUF1RDtFQUF2RDtBQUF1RDs7QUFJdkQ7RUFBQSxtQkFBcUM7RUFBckMsbUJBQXFDO0VBQXJDLG9CQUFxQztFQUFyQyxvQkFBcUM7RUFBckM7QUFBcUM7O0FBR3ZDLHlCQUF5QjtBQUV2QjtFQUFBLG9CQUFnTztFQUFoTyxtQkFBZ087RUFBaE8sdUJBQWdPO0VBQWhPLHNCQUFnTztFQUFoTyxpQkFBZ087RUFBaE8seUJBQWdPO0VBQWhPLGtCQUFnTztFQUFoTyxtQkFBZ087RUFBaE8scUJBQWdPO0VBQWhPLHdCQUFnTztFQUFoTyxtQkFBZ087RUFBaE8sb0JBQWdPO0VBQWhPLGdCQUFnTztFQUFoTyx3QkFBZ087RUFBaE8sd0RBQWdPO0VBQWhPO0FBQWdPO0FBQWhPO0VBQUEsOEJBQWdPO0VBQWhPLG1CQUFnTztFQUFoTywyR0FBZ087RUFBaE8seUdBQWdPO0VBQWhPLDRGQUFnTztFQUFoTywyQkFBZ087RUFBaE87QUFBZ087O0FBSWhPO0VBQUEsa0JBQWdIO0VBQWhILDJEQUFnSDtFQUFoSCxvQkFBZ0g7RUFBaEgsbURBQWdIO0VBQWhILDRDQUFnSDtFQUFoSCx1REFBZ0g7RUFBaEgsdUdBQWdIO0VBQWhILG9CQUFnSDtFQUFoSCxtQkFBZ0g7RUFBaEgsdUJBQWdIO0VBQWhILHNCQUFnSDtFQUFoSCxpQkFBZ0g7RUFBaEgseUJBQWdIO0VBQWhILGtCQUFnSDtFQUFoSCxtQkFBZ0g7RUFBaEgscUJBQWdIO0VBQWhILHdCQUFnSDtFQUFoSCxtQkFBZ0g7RUFBaEgsb0JBQWdIO0VBQWhILGdCQUFnSDtFQUFoSCx3QkFBZ0g7RUFBaEgsd0RBQWdIO0VBQWhIO0FBQWdIOztBQUFoSDtFQUFBLDhCQUFnSDtFQUFoSCxtQkFBZ0g7RUFBaEgsMkdBQWdIO0VBQWhILHlHQUFnSDtFQUFoSCw0RkFBZ0g7RUFBaEgsMkJBQWdIO0VBQWhIO0FBQWdIOztBQUFoSDtFQUFBLDJDQUFnSDtBQUFBOztBQUFoSDtFQUFBLGtCQUFnSDtFQUFoSCwwREFBZ0g7RUFBaEgsNkNBQWdIO0VBQWhILHdEQUFnSDtFQUFoSDtBQUFnSDs7QUFBaEg7RUFBQSxvQkFBZ0g7RUFBaEg7QUFBZ0g7O0FBSWhIO0VBQUEsc0JBQWtJO0VBQWxJLDREQUFrSTtFQUFsSSxrQkFBa0k7RUFBbEksNERBQWtJO0VBQWxJLG9CQUFrSTtFQUFsSSxnREFBa0k7RUFBbEksNENBQWtJO0VBQWxJLHVEQUFrSTtFQUFsSSx1R0FBa0k7RUFBbEksb0JBQWtJO0VBQWxJLG1CQUFrSTtFQUFsSSx1QkFBa0k7RUFBbEksc0JBQWtJO0VBQWxJLGlCQUFrSTtFQUFsSSx5QkFBa0k7RUFBbEksa0JBQWtJO0VBQWxJLG1CQUFrSTtFQUFsSSxxQkFBa0k7RUFBbEksd0JBQWtJO0VBQWxJLG1CQUFrSTtFQUFsSSxvQkFBa0k7RUFBbEksZ0JBQWtJO0VBQWxJLHdCQUFrSTtFQUFsSSx3REFBa0k7RUFBbEk7QUFBa0k7O0FBQWxJO0VBQUEsOEJBQWtJO0VBQWxJLG1CQUFrSTtFQUFsSSwyR0FBa0k7RUFBbEkseUdBQWtJO0VBQWxJLDRGQUFrSTtFQUFsSSwyQkFBa0k7RUFBbEk7QUFBa0k7O0FBQWxJO0VBQUEsMkNBQWtJO0FBQUE7O0FBQWxJO0VBQUEsa0JBQWtJO0VBQWxJLDREQUFrSTtFQUFsSSw2Q0FBa0k7RUFBbEksd0RBQWtJO0VBQWxJO0FBQWtJOztBQUFsSTtFQUFBLG9CQUFrSTtFQUFsSTtBQUFrSTs7QUFJbEk7RUFBQSxrQkFBeUc7RUFBekcsMERBQXlHO0VBQXpHLG9CQUF5RztFQUF6RyxtREFBeUc7RUFBekcsNENBQXlHO0VBQXpHLHVEQUF5RztFQUF6Ryx1R0FBeUc7RUFBekcsb0JBQXlHO0VBQXpHLG1CQUF5RztFQUF6Ryx1QkFBeUc7RUFBekcsc0JBQXlHO0VBQXpHLGlCQUF5RztFQUF6Ryx5QkFBeUc7RUFBekcsa0JBQXlHO0VBQXpHLG1CQUF5RztFQUF6RyxxQkFBeUc7RUFBekcsd0JBQXlHO0VBQXpHLG1CQUF5RztFQUF6RyxvQkFBeUc7RUFBekcsZ0JBQXlHO0VBQXpHLHdCQUF5RztFQUF6Ryx3REFBeUc7RUFBekc7QUFBeUc7O0FBQXpHO0VBQUEsOEJBQXlHO0VBQXpHLG1CQUF5RztFQUF6RywyR0FBeUc7RUFBekcseUdBQXlHO0VBQXpHLDRGQUF5RztFQUF6RywyQkFBeUc7RUFBekc7QUFBeUc7O0FBQXpHO0VBQUEsMkNBQXlHO0FBQUE7O0FBQXpHO0VBQUEsa0JBQXlHO0VBQXpHLDBEQUF5RztFQUF6Ryw2Q0FBeUc7RUFBekcsd0RBQXlHO0VBQXpHO0FBQXlHOztBQUF6RztFQUFBLG9CQUF5RztFQUF6RztBQUF5Rzs7QUFJekc7RUFBQSxrQkFBNkc7RUFBN0csMERBQTZHO0VBQTdHLG9CQUE2RztFQUE3RyxtREFBNkc7RUFBN0csNENBQTZHO0VBQTdHLHVEQUE2RztFQUE3Ryx1R0FBNkc7RUFBN0csb0JBQTZHO0VBQTdHLG1CQUE2RztFQUE3Ryx1QkFBNkc7RUFBN0csc0JBQTZHO0VBQTdHLGlCQUE2RztFQUE3Ryx5QkFBNkc7RUFBN0csa0JBQTZHO0VBQTdHLG1CQUE2RztFQUE3RyxxQkFBNkc7RUFBN0csd0JBQTZHO0VBQTdHLG1CQUE2RztFQUE3RyxvQkFBNkc7RUFBN0csZ0JBQTZHO0VBQTdHLHdCQUE2RztFQUE3Ryx3REFBNkc7RUFBN0c7QUFBNkc7O0FBQTdHO0VBQUEsOEJBQTZHO0VBQTdHLG1CQUE2RztFQUE3RywyR0FBNkc7RUFBN0cseUdBQTZHO0VBQTdHLDRGQUE2RztFQUE3RywyQkFBNkc7RUFBN0c7QUFBNkc7O0FBQTdHO0VBQUEsMkNBQTZHO0FBQUE7O0FBQTdHO0VBQUEsa0JBQTZHO0VBQTdHLDBEQUE2RztFQUE3Ryw2Q0FBNkc7RUFBN0csd0RBQTZHO0VBQTdHO0FBQTZHOztBQUE3RztFQUFBLG9CQUE2RztFQUE3RztBQUE2Rzs7QUFJN0c7RUFBQSxxQkFBMEI7RUFBMUIsc0JBQTBCO0VBQTFCLHFCQUEwQjtFQUExQix3QkFBMEI7RUFBMUIsa0JBQTBCO0VBQTFCO0FBQTBCOztBQUkxQjtFQUFBLG9CQUEwQjtFQUExQixxQkFBMEI7RUFBMUIsb0JBQTBCO0VBQTFCLHVCQUEwQjtFQUExQixlQUEwQjtFQUExQjtBQUEwQjs7QUFHNUIsdUJBQXVCO0FBRXJCO0VBQUEsbUJBQTJFO0VBQTNFLGlCQUEyRTtFQUEzRSxzQkFBMkU7RUFBM0UsNERBQTJFO0VBQTNFLGtCQUEyRTtFQUEzRSw0REFBMkU7RUFBM0UsNEVBQTJFO0VBQTNFLDJGQUEyRTtFQUEzRSx1R0FBMkU7RUFBM0U7QUFBMkU7O0FBSTNFO0VBQUEsd0JBQTRDO0VBQTVDLHNCQUE0QztFQUE1Qyw0REFBNEM7RUFBNUMsb0JBQTRDO0VBQTVDLHFCQUE0QztFQUE1QyxvQkFBNEM7RUFBNUM7QUFBNEM7O0FBSTVDO0VBQUEsb0JBQWdCO0VBQWhCLHFCQUFnQjtFQUFoQixvQkFBZ0I7RUFBaEI7QUFBZ0I7O0FBSWhCO0VBQUEsZ0NBQStFO0VBQS9FLCtCQUErRTtFQUEvRSxxQkFBK0U7RUFBL0Usc0JBQStFO0VBQS9FLDREQUErRTtFQUEvRSxrQkFBK0U7RUFBL0UsNERBQStFO0VBQS9FLG9CQUErRTtFQUEvRSxxQkFBK0U7RUFBL0Usb0JBQStFO0VBQS9FO0FBQStFOztBQUdqRix3QkFBd0I7QUFFdEI7RUFBQTtBQUE2QztBQUE3QztFQUFBLHdCQUE2QztFQUE3QyxrRUFBNkM7RUFBN0MsMkRBQTZDO0VBQTdDLHNCQUE2QztFQUE3QztBQUE2Qzs7QUFJN0M7RUFBQSxrQkFBMkI7RUFBM0I7QUFBMkI7O0FBSTNCO0VBQUEsb0JBQTZGO0VBQTdGLHFCQUE2RjtFQUE3RixpQkFBNkY7RUFBN0Ysb0JBQTZGO0VBQTdGLGdCQUE2RjtFQUE3RixrQkFBNkY7RUFBN0YsaUJBQTZGO0VBQTdGLGdCQUE2RjtFQUE3Rix5QkFBNkY7RUFBN0Ysc0JBQTZGO0VBQTdGLG9CQUE2RjtFQUE3RjtBQUE2Rjs7QUFJN0Y7RUFBQSxtQkFBNEQ7RUFBNUQsb0JBQTREO0VBQTVELHFCQUE0RDtFQUE1RCxpQkFBNEQ7RUFBNUQsb0JBQTREO0VBQTVELG1CQUE0RDtFQUE1RCxvQkFBNEQ7RUFBNUQsb0JBQTREO0VBQTVEO0FBQTREOztBQUk1RDtFQUFBLGtCQUEyQjtFQUEzQjtBQUEyQjs7QUFJM0I7RUFBQSxrQkFBbUQ7RUFBbkQsNERBQW1EO0VBQW5ELCtGQUFtRDtFQUFuRCx3REFBbUQ7RUFBbkQ7QUFBbUQ7O0FBR3JELHdCQUF3QjtBQUV0QjtFQUFBLG9CQUEwRTtFQUExRSxtQkFBMEU7RUFBMUUscUJBQTBFO0VBQTFFLHFCQUEwRTtFQUExRSxzQkFBMEU7RUFBMUUsb0JBQTBFO0VBQTFFLHVCQUEwRTtFQUExRSxrQkFBMEU7RUFBMUUsaUJBQTBFO0VBQTFFO0FBQTBFOztBQUkxRTtFQUFBLGtCQUE0QztFQUE1Qyw0REFBNEM7RUFBNUMsb0JBQTRDO0VBQTVDLGlEQUE0QztFQUE1QyxvQkFBNEM7RUFBNUMsbUJBQTRDO0VBQTVDLHFCQUE0QztFQUE1QyxxQkFBNEM7RUFBNUMsc0JBQTRDO0VBQTVDLG9CQUE0QztFQUE1Qyx1QkFBNEM7RUFBNUMsa0JBQTRDO0VBQTVDLGlCQUE0QztFQUE1QztBQUE0Qzs7QUFJNUM7RUFBQSxrQkFBNEM7RUFBNUMsNERBQTRDO0VBQTVDLG9CQUE0QztFQUE1QyxnREFBNEM7RUFBNUMsb0JBQTRDO0VBQTVDLG1CQUE0QztFQUE1QyxxQkFBNEM7RUFBNUMscUJBQTRDO0VBQTVDLHNCQUE0QztFQUE1QyxvQkFBNEM7RUFBNUMsdUJBQTRDO0VBQTVDLGtCQUE0QztFQUE1QyxpQkFBNEM7RUFBNUM7QUFBNEM7O0FBSTVDO0VBQUEsa0JBQTBDO0VBQTFDLDREQUEwQztFQUExQyxvQkFBMEM7RUFBMUMsaURBQTBDO0VBQTFDLG9CQUEwQztFQUExQyxtQkFBMEM7RUFBMUMscUJBQTBDO0VBQTFDLHFCQUEwQztFQUExQyxzQkFBMEM7RUFBMUMsb0JBQTBDO0VBQTFDLHVCQUEwQztFQUExQyxrQkFBMEM7RUFBMUMsaUJBQTBDO0VBQTFDO0FBQTBDOztBQUkxQztFQUFBLGtCQUEwQztFQUExQyw0REFBMEM7RUFBMUMsb0JBQTBDO0VBQTFDLGdEQUEwQztFQUExQyxvQkFBMEM7RUFBMUMsbUJBQTBDO0VBQTFDLHFCQUEwQztFQUExQyxxQkFBMEM7RUFBMUMsc0JBQTBDO0VBQTFDLG9CQUEwQztFQUExQyx1QkFBMEM7RUFBMUMsa0JBQTBDO0VBQTFDLGlCQUEwQztFQUExQztBQUEwQzs7QUFJMUM7RUFBQSxrQkFBb0M7RUFBcEMsNERBQW9DO0VBQXBDLG9CQUFvQztFQUFwQyxpREFBb0M7RUFBcEMsb0JBQW9DO0VBQXBDLG1CQUFvQztFQUFwQyxxQkFBb0M7RUFBcEMscUJBQW9DO0VBQXBDLHNCQUFvQztFQUFwQyxvQkFBb0M7RUFBcEMsdUJBQW9DO0VBQXBDLGtCQUFvQztFQUFwQyxpQkFBb0M7RUFBcEM7QUFBb0M7O0FBSXBDO0VBQUEsa0JBQTRDO0VBQTVDLDREQUE0QztFQUE1QyxvQkFBNEM7RUFBNUMsaURBQTRDO0VBQTVDLG9CQUE0QztFQUE1QyxtQkFBNEM7RUFBNUMscUJBQTRDO0VBQTVDLHFCQUE0QztFQUE1QyxzQkFBNEM7RUFBNUMsb0JBQTRDO0VBQTVDLHVCQUE0QztFQUE1QyxrQkFBNEM7RUFBNUMsaUJBQTRDO0VBQTVDO0FBQTRDOztBQUc5QyxpQkFBaUI7QUFFZjtFQUFBLHVCQUFxQjtFQUFyQjtBQUFxQjs7QUFJckI7RUFBQSxpQkFBK0Q7RUFBL0Qsc0JBQStEO0VBQS9ELDREQUErRDtFQUEvRCxrQkFBK0Q7RUFBL0QsNERBQStEO0VBQS9ELG9CQUErRDtFQUEvRCxpREFBK0Q7RUFBL0QsdUJBQStEO0VBQS9EO0FBQStEOztBQUkvRDtFQUFBLGlCQUFrRTtFQUFsRSxzQkFBa0U7RUFBbEUsNERBQWtFO0VBQWxFLGtCQUFrRTtFQUFsRSw0REFBa0U7RUFBbEUsb0JBQWtFO0VBQWxFLGlEQUFrRTtFQUFsRSx1QkFBa0U7RUFBbEU7QUFBa0U7O0FBSWxFO0VBQUEsaUJBQXlEO0VBQXpELHNCQUF5RDtFQUF6RCw0REFBeUQ7RUFBekQsa0JBQXlEO0VBQXpELDREQUF5RDtFQUF6RCxvQkFBeUQ7RUFBekQsaURBQXlEO0VBQXpELHVCQUF5RDtFQUF6RDtBQUF5RDs7QUFJekQ7RUFBQSxpQkFBNEQ7RUFBNUQsc0JBQTREO0VBQTVELDREQUE0RDtFQUE1RCxrQkFBNEQ7RUFBNUQsNERBQTREO0VBQTVELG9CQUE0RDtFQUE1RCxpREFBNEQ7RUFBNUQsdUJBQTREO0VBQTVEO0FBQTREOztBQUc5RCx3QkFBd0I7QUFDeEI7RUFDRSxrQ0FBa0M7QUFDcEM7O0FBRUE7RUFDRSxnQ0FBZ0M7QUFDbEM7O0FBRUE7RUFDRTtJQUNFLFVBQVU7RUFDWjtFQUNBO0lBQ0UsVUFBVTtFQUNaO0FBQ0Y7O0FBRUE7RUFDRTtJQUNFLDJCQUEyQjtJQUMzQixVQUFVO0VBQ1o7RUFDQTtJQUNFLHdCQUF3QjtJQUN4QixVQUFVO0VBQ1o7QUFDRjs7QUFFQSxpQkFBaUI7QUFDakI7RUFDRTtJQUNFLHdCQUF3QjtFQUMxQjs7RUFFQTtJQUNFLHlCQUF5QjtFQUMzQjs7RUFFQTtJQUNFLGVBQWU7SUFDZixnQkFBZ0I7RUFDbEI7O0VBRUE7SUFDRSxnQkFBZ0I7SUFDaEIsc0JBQXNCO0VBQ3hCO0FBQ0Y7O0FBRUEsNEJBQTRCO0FBRTFCO0VBQUEsaUJBQTZDO0VBQTdDLGtCQUE2QztFQUE3QyxnQkFBNkM7RUFBN0Msa0JBQTZDO0VBQTdDO0FBQTZDO0FBQTdDOztFQUFBO0lBQUEsb0JBQTZDO0lBQTdDO0VBQTZDO0FBQUE7QUFBN0M7O0VBQUE7SUFBQSxrQkFBNkM7SUFBN0M7RUFBNkM7QUFBQTs7QUFJN0M7RUFBQSxpQkFBb0I7RUFBcEI7QUFBb0I7O0FBQXBCOztFQUFBO0lBQUEsaUJBQW9CO0lBQXBCO0VBQW9CO0FBQUE7O0FBSXBCO0VBQUEsdUJBQTZCO0VBQTdCLDhEQUE2QjtFQUE3QjtBQUE2Qjs7QUFBN0I7O0VBQUE7SUFBQSx1QkFBNkI7SUFBN0IsNERBQTZCO0lBQTdCO0VBQTZCO0FBQUE7O0FBRy9CLG1CQUFtQjtBQUNuQjtFQUNFLDJDQUEyQztBQUM3Qzs7QUFFQTtFQUNFLDRDQUE0QztBQUM5Qzs7QUFFQTtFQUNFLDRDQUE0QztBQUM5Qzs7QUFFQTtFQUNFLDJFQUEyRTtBQUM3RTs7QUFFQSx5QkFBeUI7QUFFdkI7RUFBQTtBQUFpQjs7QUFJakI7RUFBQTtBQUFpQjs7QUFHbkIsd0JBQXdCO0FBRXRCO0VBQUEsOEJBQThHO0VBQTlHLG1CQUE4RztFQUE5RywyR0FBOEc7RUFBOUcseUdBQThHO0VBQTlHLDRGQUE4RztFQUE5RyxvQkFBOEc7RUFBOUcsNERBQThHO0VBQTlHLDJCQUE4RztFQUE5RztBQUE4Rzs7QUFHaEgsdUJBQXVCO0FBQ3ZCO0VBQ0UsVUFBVTtFQUNWLFdBQVc7QUFDYjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIsa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBLGtDQUFrQztBQUNsQztFQUVJO0lBQUEsa0JBQXVDO0lBQXZDLHlEQUF1QztJQUF2QyxvQkFBdUM7SUFBdkM7RUFBdUM7O0VBSXZDO0lBQUEsc0JBQXdDO0lBQXhDLHlEQUF3QztJQUF4QyxrQkFBd0M7SUFBeEM7RUFBd0M7O0VBSXhDO0lBQUEsc0JBQTBEO0lBQTFELHlEQUEwRDtJQUExRCxrQkFBMEQ7SUFBMUQseURBQTBEO0lBQTFELG9CQUEwRDtJQUExRDtFQUEwRDtBQUU5RDtBQTFVQTtFQUFBLDBCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDBCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDBCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDBCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDBCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDBCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDBCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDBCQTJVQTtFQTNVQSxzQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSwwQkEyVUE7RUEzVUEsa0JBMlVBO0VBM1VBO0FBMlVBO0FBM1VBO0VBQUEsMEJBMlVBO0VBM1VBLHdCQTJVQTtFQTNVQSx3REEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxnQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxzQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxrQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxrQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxrQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxrQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxrQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxrQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBLHNCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDhCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDJHQTJVQTtFQTNVQSx5R0EyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBO0FBMlVBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxrQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSwwQkEyVUE7RUEzVUEsc0JBMlVBO0VBM1VBO0FBMlVBO0FBM1VBO0VBQUEsMEJBMlVBO0VBM1VBLHNCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDhCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDJHQTJVQTtFQTNVQSx5R0EyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7O0VBQUE7SUFBQSxnQkEyVUE7SUEzVUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUEsb0JBMlVBO0lBM1VBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBLG1CQTJVQTtJQTNVQTtFQTJVQTtBQUFBO0FBM1VBOztFQUFBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7QUFBQTtBQTNVQTs7RUFBQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBLGtCQTJVQTtJQTNVQTtFQTJVQTtBQUFBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBO0FBMlVBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBO0FBMlVBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBO0FBMlVBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBO0FBMlVBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBLHNCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLHNCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBO0FBMlVBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBO0FBMlVBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQVwiLFwic291cmNlc0NvbnRlbnRcIjpbXCJAaW1wb3J0ICd0YWlsd2luZGNzcy9iYXNlJztcXG5AaW1wb3J0ICd0YWlsd2luZGNzcy9jb21wb25lbnRzJztcXG5AaW1wb3J0ICd0YWlsd2luZGNzcy91dGlsaXRpZXMnO1xcblxcbi8qIEltcG9ydCBmb250cyAqL1xcbkBpbXBvcnQgdXJsKCdodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2NzczI/ZmFtaWx5PUludGVyOndnaHRAMzAwOzQwMDs1MDA7NjAwOzcwMCZkaXNwbGF5PXN3YXAnKTtcXG5AaW1wb3J0IHVybCgnaHR0cHM6Ly9mb250cy5nb29nbGVhcGlzLmNvbS9jc3MyP2ZhbWlseT1DYWlybzp3Z2h0QDMwMDs0MDA7NTAwOzYwMDs3MDAmZGlzcGxheT1zd2FwJyk7XFxuXFxuLyogTW9kZXJuIE1pbmltYWxpc3QgQmFzZSBTdHlsZXMgKi9cXG5odG1sIHtcXG4gIHNjcm9sbC1iZWhhdmlvcjogc21vb3RoO1xcbn1cXG5cXG5ib2R5IHtcXG4gIGZvbnQtZmFtaWx5OiAnSW50ZXInLCAndWktc2Fucy1zZXJpZicsICdzeXN0ZW0tdWknLCBzYW5zLXNlcmlmO1xcbiAgbGluZS1oZWlnaHQ6IDEuNjtcXG4gIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7IC8qIG5ldXRyYWwtNTAgKi9cXG4gIGNvbG9yOiAjMTcxNzE3OyAvKiB0ZXh0LXByaW1hcnkgKi9cXG4gIGZvbnQtd2VpZ2h0OiA0MDA7XFxuICAtd2Via2l0LWZvbnQtc21vb3RoaW5nOiBhbnRpYWxpYXNlZDtcXG4gIC1tb3otb3N4LWZvbnQtc21vb3RoaW5nOiBncmF5c2NhbGU7XFxufVxcblxcbi8qIEFyYWJpYyBmb250ICovXFxuLmZvbnQtYXJhYmljIHtcXG4gIGZvbnQtZmFtaWx5OiAnQ2Fpcm8nLCBzYW5zLXNlcmlmO1xcbn1cXG5cXG4uZm9udC1lbmdsaXNoIHtcXG4gIGZvbnQtZmFtaWx5OiAnSW50ZXInLCBzYW5zLXNlcmlmO1xcbn1cXG5cXG4vKiBSVEwgc3VwcG9ydCAqL1xcbltkaXI9XFxcInJ0bFxcXCJdIHtcXG4gIHRleHQtYWxpZ246IHJpZ2h0O1xcbn1cXG5cXG5bZGlyPVxcXCJydGxcXFwiXSAucnRsXFxcXDp0ZXh0LWxlZnQge1xcbiAgdGV4dC1hbGlnbjogbGVmdDtcXG59XFxuXFxuW2Rpcj1cXFwicnRsXFxcIl0gLnJ0bFxcXFw6dGV4dC1yaWdodCB7XFxuICB0ZXh0LWFsaWduOiByaWdodDtcXG59XFxuXFxuLyogQ3VzdG9tIHNjcm9sbGJhciAqL1xcbjo6LXdlYmtpdC1zY3JvbGxiYXIge1xcbiAgd2lkdGg6IDZweDtcXG4gIGhlaWdodDogNnB4O1xcbn1cXG5cXG46Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcXG4gIGJhY2tncm91bmQ6ICNmMWYxZjE7XFxuICBib3JkZXItcmFkaXVzOiAzcHg7XFxufVxcblxcbjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xcbiAgYmFja2dyb3VuZDogI2MxYzFjMTtcXG4gIGJvcmRlci1yYWRpdXM6IDNweDtcXG59XFxuXFxuOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7XFxuICBiYWNrZ3JvdW5kOiAjYThhOGE4O1xcbn1cXG5cXG4vKiBNb2Rlcm4gRm9ybSBTdHlsZXMgKi9cXG4uZm9ybS1pbnB1dCB7XFxuICBAYXBwbHkgYmxvY2sgdy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMjAwIHJvdW5kZWQteGwgc2hhZG93LXNvZnQgcGxhY2Vob2xkZXItdGV4dC10ZXJ0aWFyeSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeS00MDAgZm9jdXM6Ym9yZGVyLXByaW1hcnktNDAwIHNtOnRleHQtc20gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGJnLXN1cmZhY2UtcHJpbWFyeTtcXG59XFxuXFxuLmZvcm0taW5wdXQ6aW52YWxpZCB7XFxuICBAYXBwbHkgYm9yZGVyLXN0YXR1cy1lcnJvciBmb2N1czpyaW5nLXN0YXR1cy1lcnJvciBmb2N1czpib3JkZXItc3RhdHVzLWVycm9yO1xcbn1cXG5cXG4uZm9ybS1sYWJlbCB7XFxuICBAYXBwbHkgYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXRleHQtcHJpbWFyeSBtYi0yO1xcbn1cXG5cXG4uZm9ybS1lcnJvciB7XFxuICBAYXBwbHkgbXQtMSB0ZXh0LXNtIHRleHQtc3RhdHVzLWVycm9yO1xcbn1cXG5cXG4vKiBNb2Rlcm4gQnV0dG9uIFN0eWxlcyAqL1xcbi5idG4ge1xcbiAgQGFwcGx5IGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweC00IHB5LTIuNSBib3JkZXIgYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctb2Zmc2V0LW5ldXRyYWwtNTA7XFxufVxcblxcbi5idG4tcHJpbWFyeSB7XFxuICBAYXBwbHkgYnRuIHRleHQtd2hpdGUgYmctcHJpbWFyeS01MDAgaG92ZXI6YmctcHJpbWFyeS02MDAgZm9jdXM6cmluZy1wcmltYXJ5LTQwMCBzaGFkb3ctc29mdCBob3ZlcjpzaGFkb3ctbWVkaXVtO1xcbn1cXG5cXG4uYnRuLXNlY29uZGFyeSB7XFxuICBAYXBwbHkgYnRuIHRleHQtbmV1dHJhbC03MDAgYmctd2hpdGUgYm9yZGVyLW5ldXRyYWwtMjAwIGhvdmVyOmJnLW5ldXRyYWwtNTAgZm9jdXM6cmluZy1wcmltYXJ5LTQwMCBzaGFkb3ctc29mdCBob3ZlcjpzaGFkb3ctbWVkaXVtO1xcbn1cXG5cXG4uYnRuLWRhbmdlciB7XFxuICBAYXBwbHkgYnRuIHRleHQtd2hpdGUgYmctc3RhdHVzLWVycm9yIGhvdmVyOmJnLXJlZC02MDAgZm9jdXM6cmluZy1yZWQtNDAwIHNoYWRvdy1zb2Z0IGhvdmVyOnNoYWRvdy1tZWRpdW07XFxufVxcblxcbi5idG4tc3VjY2VzcyB7XFxuICBAYXBwbHkgYnRuIHRleHQtd2hpdGUgYmctYWNjZW50LTUwMCBob3ZlcjpiZy1hY2NlbnQtNjAwIGZvY3VzOnJpbmctYWNjZW50LTQwMCBzaGFkb3ctc29mdCBob3ZlcjpzaGFkb3ctbWVkaXVtO1xcbn1cXG5cXG4uYnRuLXNtIHtcXG4gIEBhcHBseSBweC0zIHB5LTEuNSB0ZXh0LXhzO1xcbn1cXG5cXG4uYnRuLWxnIHtcXG4gIEBhcHBseSBweC02IHB5LTMgdGV4dC1iYXNlO1xcbn1cXG5cXG4vKiBNb2Rlcm4gQ2FyZCBTdHlsZXMgKi9cXG4uY2FyZCB7XFxuICBAYXBwbHkgYmctc3VyZmFjZS1wcmltYXJ5IHNoYWRvdy1jYXJkIHJvdW5kZWQtMnhsIGJvcmRlciBib3JkZXItbmV1dHJhbC0xMDA7XFxufVxcblxcbi5jYXJkLWhlYWRlciB7XFxuICBAYXBwbHkgcHgtNiBweS01IGJvcmRlci1iIGJvcmRlci1uZXV0cmFsLTEwMDtcXG59XFxuXFxuLmNhcmQtYm9keSB7XFxuICBAYXBwbHkgcHgtNiBweS01O1xcbn1cXG5cXG4uY2FyZC1mb290ZXIge1xcbiAgQGFwcGx5IHB4LTYgcHktNSBib3JkZXItdCBib3JkZXItbmV1dHJhbC0xMDAgYmctc3VyZmFjZS1zZWNvbmRhcnkgcm91bmRlZC1iLTJ4bDtcXG59XFxuXFxuLyogTW9kZXJuIFRhYmxlIFN0eWxlcyAqL1xcbi50YWJsZSB7XFxuICBAYXBwbHkgbWluLXctZnVsbCBkaXZpZGUteSBkaXZpZGUtbmV1dHJhbC0xNTA7XFxufVxcblxcbi50YWJsZSB0aGVhZCB7XFxuICBAYXBwbHkgYmctc3VyZmFjZS1zZWNvbmRhcnk7XFxufVxcblxcbi50YWJsZSB0aCB7XFxuICBAYXBwbHkgcHgtNiBweS00IHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC10ZXh0LXNlY29uZGFyeSB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXI7XFxufVxcblxcbi50YWJsZSB0ZCB7XFxuICBAYXBwbHkgcHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC10ZXh0LXByaW1hcnk7XFxufVxcblxcbi50YWJsZSB0Ym9keSB0cjpudGgtY2hpbGQoZXZlbikge1xcbiAgQGFwcGx5IGJnLXN1cmZhY2Utc2Vjb25kYXJ5O1xcbn1cXG5cXG4udGFibGUgdGJvZHkgdHI6aG92ZXIge1xcbiAgQGFwcGx5IGJnLW5ldXRyYWwtNzUgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMTUwO1xcbn1cXG5cXG4vKiBNb2Rlcm4gQmFkZ2UgU3R5bGVzICovXFxuLmJhZGdlIHtcXG4gIEBhcHBseSBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtO1xcbn1cXG5cXG4uYmFkZ2UtcHJpbWFyeSB7XFxuICBAYXBwbHkgYmFkZ2UgYmctcHJpbWFyeS0xMDAgdGV4dC1wcmltYXJ5LTcwMDtcXG59XFxuXFxuLmJhZGdlLXNlY29uZGFyeSB7XFxuICBAYXBwbHkgYmFkZ2UgYmctbmV1dHJhbC0xMDAgdGV4dC1uZXV0cmFsLTcwMDtcXG59XFxuXFxuLmJhZGdlLXN1Y2Nlc3Mge1xcbiAgQGFwcGx5IGJhZGdlIGJnLWFjY2VudC0xMDAgdGV4dC1hY2NlbnQtNzAwO1xcbn1cXG5cXG4uYmFkZ2Utd2FybmluZyB7XFxuICBAYXBwbHkgYmFkZ2UgYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy03MDA7XFxufVxcblxcbi5iYWRnZS1kYW5nZXIge1xcbiAgQGFwcGx5IGJhZGdlIGJnLXJlZC0xMDAgdGV4dC1yZWQtNzAwO1xcbn1cXG5cXG4uYmFkZ2UtaW5mbyB7XFxuICBAYXBwbHkgYmFkZ2UgYmctcHJpbWFyeS0xMDAgdGV4dC1wcmltYXJ5LTcwMDtcXG59XFxuXFxuLyogQWxlcnQgc3R5bGVzICovXFxuLmFsZXJ0IHtcXG4gIEBhcHBseSBwLTQgcm91bmRlZC1tZDtcXG59XFxuXFxuLmFsZXJ0LXN1Y2Nlc3Mge1xcbiAgQGFwcGx5IGFsZXJ0IGJnLWdyZWVuLTUwIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIHRleHQtZ3JlZW4tODAwO1xcbn1cXG5cXG4uYWxlcnQtd2FybmluZyB7XFxuICBAYXBwbHkgYWxlcnQgYmcteWVsbG93LTUwIGJvcmRlciBib3JkZXIteWVsbG93LTIwMCB0ZXh0LXllbGxvdy04MDA7XFxufVxcblxcbi5hbGVydC1kYW5nZXIge1xcbiAgQGFwcGx5IGFsZXJ0IGJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgdGV4dC1yZWQtODAwO1xcbn1cXG5cXG4uYWxlcnQtaW5mbyB7XFxuICBAYXBwbHkgYWxlcnQgYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHRleHQtYmx1ZS04MDA7XFxufVxcblxcbi8qIEFuaW1hdGlvbiB1dGlsaXRpZXMgKi9cXG4uZmFkZS1pbiB7XFxuICBhbmltYXRpb246IGZhZGVJbiAwLjVzIGVhc2UtaW4tb3V0O1xcbn1cXG5cXG4uc2xpZGUtdXAge1xcbiAgYW5pbWF0aW9uOiBzbGlkZVVwIDAuM3MgZWFzZS1vdXQ7XFxufVxcblxcbkBrZXlmcmFtZXMgZmFkZUluIHtcXG4gIGZyb20ge1xcbiAgICBvcGFjaXR5OiAwO1xcbiAgfVxcbiAgdG8ge1xcbiAgICBvcGFjaXR5OiAxO1xcbiAgfVxcbn1cXG5cXG5Aa2V5ZnJhbWVzIHNsaWRlVXAge1xcbiAgZnJvbSB7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgxMHB4KTtcXG4gICAgb3BhY2l0eTogMDtcXG4gIH1cXG4gIHRvIHtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xcbiAgICBvcGFjaXR5OiAxO1xcbiAgfVxcbn1cXG5cXG4vKiBQcmludCBzdHlsZXMgKi9cXG5AbWVkaWEgcHJpbnQge1xcbiAgLm5vLXByaW50IHtcXG4gICAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50O1xcbiAgfVxcbiAgXFxuICAucHJpbnQtYnJlYWsge1xcbiAgICBwYWdlLWJyZWFrLWJlZm9yZTogYWx3YXlzO1xcbiAgfVxcbiAgXFxuICBib2R5IHtcXG4gICAgZm9udC1zaXplOiAxMnB0O1xcbiAgICBsaW5lLWhlaWdodDogMS40O1xcbiAgfVxcbiAgXFxuICAuY2FyZCB7XFxuICAgIGJveC1zaGFkb3c6IG5vbmU7XFxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7XFxuICB9XFxufVxcblxcbi8qIE1vZGVybiBMYXlvdXQgVXRpbGl0aWVzICovXFxuLnBhZ2UtY29udGFpbmVyIHtcXG4gIEBhcHBseSBtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtODtcXG59XFxuXFxuLnNlY3Rpb24tc3BhY2luZyB7XFxuICBAYXBwbHkgcHktOCBsZzpweS0xMjtcXG59XFxuXFxuLmNvbnRlbnQtc3BhY2luZyB7XFxuICBAYXBwbHkgc3BhY2UteS02IGxnOnNwYWNlLXktODtcXG59XFxuXFxuLyogTW9kZXJuIFNoYWRvd3MgKi9cXG4uc2hhZG93LXNvZnQge1xcbiAgYm94LXNoYWRvdzogMCAycHggOHB4IDAgcmdiYSgwLCAwLCAwLCAwLjA2KTtcXG59XFxuXFxuLnNoYWRvdy1tZWRpdW0ge1xcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4wOCk7XFxufVxcblxcbi5zaGFkb3ctbGFyZ2Uge1xcbiAgYm94LXNoYWRvdzogMCA4cHggMjRweCAwIHJnYmEoMCwgMCwgMCwgMC4xMik7XFxufVxcblxcbi5zaGFkb3ctY2FyZCB7XFxuICBib3gtc2hhZG93OiAwIDFweCAzcHggMCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgMXB4IDJweCAwIHJnYmEoMCwgMCwgMCwgMC4wNik7XFxufVxcblxcbi8qIEVuaGFuY2VkIFJUTCBTdXBwb3J0ICovXFxuW2Rpcj1cXFwicnRsXFxcIl0gLnRhYmxlIHRoIHtcXG4gIEBhcHBseSB0ZXh0LXJpZ2h0O1xcbn1cXG5cXG5bZGlyPVxcXCJydGxcXFwiXSAudGFibGUgdGQge1xcbiAgQGFwcGx5IHRleHQtcmlnaHQ7XFxufVxcblxcbi8qIE1vZGVybiBGb2N1cyBTdGF0ZXMgKi9cXG4uZm9jdXMtcmluZyB7XFxuICBAYXBwbHkgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktNDAwIGZvY3VzOnJpbmctb2Zmc2V0LTIgZm9jdXM6cmluZy1vZmZzZXQtbmV1dHJhbC01MDtcXG59XFxuXFxuLyogSW1wcm92ZWQgU2Nyb2xsYmFyICovXFxuOjotd2Via2l0LXNjcm9sbGJhciB7XFxuICB3aWR0aDogOHB4O1xcbiAgaGVpZ2h0OiA4cHg7XFxufVxcblxcbjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sge1xcbiAgYmFja2dyb3VuZDogI2Y1ZjVmNTtcXG4gIGJvcmRlci1yYWRpdXM6IDRweDtcXG59XFxuXFxuOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XFxuICBiYWNrZ3JvdW5kOiAjZDRkNGQ0O1xcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xcbn1cXG5cXG46Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHtcXG4gIGJhY2tncm91bmQ6ICNhM2EzYTM7XFxufVxcblxcbi8qIERhcmsgbW9kZSBzdXBwb3J0IChpZiBuZWVkZWQpICovXFxuQG1lZGlhIChwcmVmZXJzLWNvbG9yLXNjaGVtZTogZGFyaykge1xcbiAgLmRhcmstbW9kZSB7XFxuICAgIEBhcHBseSBiZy1uZXV0cmFsLTkwMCB0ZXh0LXRleHQtaW52ZXJzZTtcXG4gIH1cXG5cXG4gIC5kYXJrLW1vZGUgLmNhcmQge1xcbiAgICBAYXBwbHkgYmctbmV1dHJhbC04MDAgYm9yZGVyLW5ldXRyYWwtNzAwO1xcbiAgfVxcblxcbiAgLmRhcmstbW9kZSAuZm9ybS1pbnB1dCB7XFxuICAgIEBhcHBseSBiZy1uZXV0cmFsLTcwMCBib3JkZXItbmV1dHJhbC02MDAgdGV4dC10ZXh0LWludmVyc2U7XFxuICB9XFxufVxcblwiXSxcInNvdXJjZVJvb3RcIjpcIlwifV0pO1xuLy8gRXhwb3J0c1xuZXhwb3J0IGRlZmF1bHQgX19fQ1NTX0xPQURFUl9FWFBPUlRfX187XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});