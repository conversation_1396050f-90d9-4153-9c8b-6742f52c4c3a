/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "/_error";
exports.ids = ["/_error"];
exports.modules = {

/***/ "__barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArchiveBoxIcon: () => (/* reexport safe */ _ArchiveBoxIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CalculatorIcon: () => (/* reexport safe */ _CalculatorIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ChevronLeftIcon: () => (/* reexport safe */ _ChevronLeftIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   ChevronRightIcon: () => (/* reexport safe */ _ChevronRightIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Cog6ToothIcon: () => (/* reexport safe */ _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   CubeIcon: () => (/* reexport safe */ _CubeIcon_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   DocumentChartBarIcon: () => (/* reexport safe */ _DocumentChartBarIcon_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   HomeIcon: () => (/* reexport safe */ _HomeIcon_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   ShoppingBagIcon: () => (/* reexport safe */ _ShoppingBagIcon_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   ShoppingCartIcon: () => (/* reexport safe */ _ShoppingCartIcon_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   UsersIcon: () => (/* reexport safe */ _UsersIcon_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   WrenchScrewdriverIcon: () => (/* reexport safe */ _WrenchScrewdriverIcon_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArchiveBoxIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArchiveBoxIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArchiveBoxIcon.js\");\n/* harmony import */ var _CalculatorIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CalculatorIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CalculatorIcon.js\");\n/* harmony import */ var _ChevronLeftIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChevronLeftIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChevronLeftIcon.js\");\n/* harmony import */ var _ChevronRightIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ChevronRightIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _Cog6ToothIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Cog6ToothIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _CubeIcon_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CubeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _DocumentChartBarIcon_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DocumentChartBarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/DocumentChartBarIcon.js\");\n/* harmony import */ var _HomeIcon_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./HomeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _ShoppingBagIcon_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ShoppingBagIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShoppingBagIcon.js\");\n/* harmony import */ var _ShoppingCartIcon_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ShoppingCartIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ShoppingCartIcon.js\");\n/* harmony import */ var _UsersIcon_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./UsersIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _WrenchScrewdriverIcon_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./WrenchScrewdriverIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcmNoaXZlQm94SWNvbixDYWxjdWxhdG9ySWNvbixDaGV2cm9uTGVmdEljb24sQ2hldnJvblJpZ2h0SWNvbixDb2c2VG9vdGhJY29uLEN1YmVJY29uLERvY3VtZW50Q2hhcnRCYXJJY29uLEhvbWVJY29uLFNob3BwaW5nQmFnSWNvbixTaG9wcGluZ0NhcnRJY29uLFVzZXJzSWNvbixXcmVuY2hTY3Jld2RyaXZlckljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDK0Q7QUFDQTtBQUNFO0FBQ0U7QUFDTjtBQUNWO0FBQ3dCO0FBQ3hCO0FBQ2M7QUFDRTtBQUNkIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVzaW5lc3MtbWFuYWdlbWVudC1zeXN0ZW0vLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9pbmRleC5qcz9mMDkwIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcmNoaXZlQm94SWNvbiB9IGZyb20gXCIuL0FyY2hpdmVCb3hJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2FsY3VsYXRvckljb24gfSBmcm9tIFwiLi9DYWxjdWxhdG9ySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZXZyb25MZWZ0SWNvbiB9IGZyb20gXCIuL0NoZXZyb25MZWZ0SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZXZyb25SaWdodEljb24gfSBmcm9tIFwiLi9DaGV2cm9uUmlnaHRJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29nNlRvb3RoSWNvbiB9IGZyb20gXCIuL0NvZzZUb290aEljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDdWJlSWNvbiB9IGZyb20gXCIuL0N1YmVJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRG9jdW1lbnRDaGFydEJhckljb24gfSBmcm9tIFwiLi9Eb2N1bWVudENoYXJ0QmFySWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhvbWVJY29uIH0gZnJvbSBcIi4vSG9tZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTaG9wcGluZ0JhZ0ljb24gfSBmcm9tIFwiLi9TaG9wcGluZ0JhZ0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTaG9wcGluZ0NhcnRJY29uIH0gZnJvbSBcIi4vU2hvcHBpbmdDYXJ0SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXJzSWNvbiB9IGZyb20gXCIuL1VzZXJzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFdyZW5jaFNjcmV3ZHJpdmVySWNvbiB9IGZyb20gXCIuL1dyZW5jaFNjcmV3ZHJpdmVySWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArrowRightOnRectangleIcon: () => (/* reexport safe */ _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Bars3Icon: () => (/* reexport safe */ _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   BellIcon: () => (/* reexport safe */ _BellIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   LanguageIcon: () => (/* reexport safe */ _LanguageIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   UserCircleIcon: () => (/* reexport safe */ _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ArrowRightOnRectangleIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArrowRightOnRectangleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArrowRightOnRectangleIcon.js\");\n/* harmony import */ var _Bars3Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Bars3Icon.js */ \"./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _BellIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BellIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _LanguageIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LanguageIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/LanguageIcon.js\");\n/* harmony import */ var _UserCircleIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserCircleIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/UserCircleIcon.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcnJvd1JpZ2h0T25SZWN0YW5nbGVJY29uLEJhcnMzSWNvbixCZWxsSWNvbixMYW5ndWFnZUljb24sVXNlckNpcmNsZUljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQ3FGO0FBQ2hDO0FBQ0Y7QUFDUSIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/ODA3MiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJyb3dSaWdodE9uUmVjdGFuZ2xlSWNvbiB9IGZyb20gXCIuL0Fycm93UmlnaHRPblJlY3RhbmdsZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCYXJzM0ljb24gfSBmcm9tIFwiLi9CYXJzM0ljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCZWxsSWNvbiB9IGZyb20gXCIuL0JlbGxJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTGFuZ3VhZ2VJY29uIH0gZnJvbSBcIi4vTGFuZ3VhZ2VJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVXNlckNpcmNsZUljb24gfSBmcm9tIFwiLi9Vc2VyQ2lyY2xlSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_modules\\next\\dist\\pages\\_error.js */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\nprivate_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/Header.js":
/*!******************************!*\
  !*** ./components/Header.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/SocketContext */ \"./contexts/SocketContext.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightOnRectangleIcon,Bars3Icon,BellIcon,LanguageIcon,UserCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_5__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction Header({ sidebarOpen, setSidebarOpen }) {\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUserMenu, setShowUserMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { notifications, markNotificationAsRead } = (0,_contexts_SocketContext__WEBPACK_IMPORTED_MODULE_5__.useSocket)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const toggleLanguage = ()=>{\n        const newLocale = router.locale === \"ar\" ? \"en\" : \"ar\";\n        router.push(router.pathname, router.asPath, {\n            locale: newLocale\n        });\n    };\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-surface-primary shadow-soft border-b border-neutral-150\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between px-6 py-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setSidebarOpen(!sidebarOpen),\n                        className: \"p-2.5 rounded-xl text-text-secondary hover:text-text-primary hover:bg-neutral-75 focus-ring transition-all duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.Bars3Icon, {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleLanguage,\n                            className: \"p-2.5 rounded-xl text-text-secondary hover:text-text-primary hover:bg-neutral-75 focus-ring transition-all duration-200\",\n                            title: t(\"common.language\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.LanguageIcon, {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowNotifications(!showNotifications),\n                                    className: \"p-2.5 rounded-xl text-text-secondary hover:text-text-primary hover:bg-neutral-75 focus-ring transition-all duration-200 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.BellIcon, {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 bg-status-error text-white text-xs rounded-full h-5 w-5 flex items-center justify-center shadow-soft\",\n                                            children: unreadCount > 9 ? \"9+\" : unreadCount\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 rtl:right-auto rtl:left-0 mt-3 w-80 bg-surface-primary rounded-2xl shadow-large border border-neutral-100 z-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-5 border-b border-neutral-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-text-primary\",\n                                                children: t(\"dashboard.notifications\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 71,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-96 overflow-y-auto\",\n                                            children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 text-center text-gray-500\",\n                                                children: t(\"common.noData\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 77,\n                                                columnNumber: 21\n                                            }, this) : notifications.slice(0, 10).map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${!notification.read ? \"bg-blue-50\" : \"\"}`,\n                                                    onClick: ()=>markNotificationAsRead(notification.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-3 rtl:space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `flex-shrink-0 w-2 h-2 rounded-full mt-2 ${notification.type === \"error\" ? \"bg-red-500\" : notification.type === \"warning\" ? \"bg-yellow-500\" : notification.type === \"success\" ? \"bg-green-500\" : \"bg-blue-500\"}`\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                lineNumber: 90,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: router.locale === \"ar\" ? notification.titleAr : notification.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                        lineNumber: 97,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: router.locale === \"ar\" ? notification.messageAr : notification.message\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                        lineNumber: 100,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                                        children: new Date(notification.timestamp).toLocaleString(router.locale)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                        lineNumber: 103,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                                lineNumber: 96,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, notification.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowUserMenu(!showUserMenu),\n                                    className: \"flex items-center space-x-2 rtl:space-x-reverse p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.UserCircleIcon, {\n                                            className: \"h-8 w-8\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: [\n                                                user?.firstName,\n                                                \" \",\n                                                user?.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                showUserMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute right-0 rtl:right-auto rtl:left-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 py-2 text-sm text-gray-700 border-b border-gray-100\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            user?.firstName,\n                                                            \" \",\n                                                            user?.lastName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500\",\n                                                        children: user?.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 capitalize\",\n                                                        children: user?.role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowUserMenu(false);\n                                                    router.push(\"/profile\");\n                                                },\n                                                className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                children: t(\"auth.profile\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowUserMenu(false);\n                                                    logout();\n                                                },\n                                                className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightOnRectangleIcon_Bars3Icon_BellIcon_LanguageIcon_UserCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__.ArrowRightOnRectangleIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t(\"auth.logout\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Header.js\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Header.js\n");

/***/ }),

/***/ "./components/Layout.js":
/*!******************************!*\
  !*** ./components/Layout.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Sidebar */ \"./components/Sidebar.js\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Header */ \"./components/Header.js\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./LoadingSpinner */ \"./components/LoadingSpinner.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _Header__WEBPACK_IMPORTED_MODULE_6__]);\n([_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__, _Header__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction Layout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAuthenticated, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    // Set document direction based on locale\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (typeof document !== \"undefined\") {\n            const isArabic = router.locale === \"ar\";\n            document.documentElement.dir = isArabic ? \"rtl\" : \"ltr\";\n            document.documentElement.lang = router.locale || \"ar\";\n            // Add Arabic font class\n            if (isArabic) {\n                document.body.classList.add(\"font-arabic\");\n                document.body.classList.remove(\"font-english\");\n            } else {\n                document.body.classList.add(\"font-english\");\n                document.body.classList.remove(\"font-arabic\");\n            }\n        }\n    }, [\n        router.locale\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && !isAuthenticated) {\n            router.push(\"/login\");\n        }\n    }, [\n        isAuthenticated,\n        isLoading,\n        router\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null; // Will redirect to login\n    }\n    const isRTL = router.locale === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-h-screen bg-neutral-50 ${isRTL ? \"rtl\" : \"ltr\"}`,\n        dir: isRTL ? \"rtl\" : \"ltr\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                open: sidebarOpen,\n                setOpen: setSidebarOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${sidebarOpen ? isRTL ? \"lg:mr-64\" : \"lg:ml-64\" : isRTL ? \"lg:mr-20\" : \"lg:ml-20\"} transition-all duration-300`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sidebarOpen: sidebarOpen,\n                        setSidebarOpen: setSidebarOpen\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"section-spacing\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"page-container\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"content-spacing\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Layout.js\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout.js\n");

/***/ }),

/***/ "./components/LoadingSpinner.js":
/*!**************************************!*\
  !*** ./components/LoadingSpinner.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction LoadingSpinner({ size = \"medium\", className = \"\" }) {\n    const sizeClasses = {\n        small: \"h-4 w-4\",\n        medium: \"h-8 w-8\",\n        large: \"h-12 w-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `animate-spin rounded-full border-2 border-gray-300 border-t-primary-600 ${sizeClasses[size]} ${className}`\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\LoadingSpinner.js\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL0xvYWRpbmdTcGlubmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQSxlQUFlLEVBQUVDLE9BQU8sUUFBUSxFQUFFQyxZQUFZLEVBQUUsRUFBRTtJQUN4RSxNQUFNQyxjQUFjO1FBQ2xCQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsT0FBTztJQUNUO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlMLFdBQVcsQ0FBQyx3RUFBd0UsRUFBRUMsV0FBVyxDQUFDRixLQUFLLENBQUMsQ0FBQyxFQUFFQyxVQUFVLENBQUM7Ozs7OztBQUUvSCIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vY29tcG9uZW50cy9Mb2FkaW5nU3Bpbm5lci5qcz9lNjEwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmdTcGlubmVyKHsgc2l6ZSA9ICdtZWRpdW0nLCBjbGFzc05hbWUgPSAnJyB9KSB7XG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xuICAgIHNtYWxsOiAnaC00IHctNCcsXG4gICAgbWVkaXVtOiAnaC04IHctOCcsXG4gICAgbGFyZ2U6ICdoLTEyIHctMTInLFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGJvcmRlci0yIGJvcmRlci1ncmF5LTMwMCBib3JkZXItdC1wcmltYXJ5LTYwMCAke3NpemVDbGFzc2VzW3NpemVdfSAke2NsYXNzTmFtZX1gfSAvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkxvYWRpbmdTcGlubmVyIiwic2l6ZSIsImNsYXNzTmFtZSIsInNpemVDbGFzc2VzIiwic21hbGwiLCJtZWRpdW0iLCJsYXJnZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/LoadingSpinner.js\n");

/***/ }),

/***/ "./components/Sidebar.js":
/*!*******************************!*\
  !*** ./components/Sidebar.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArchiveBoxIcon,CalculatorIcon,ChevronLeftIcon,ChevronRightIcon,Cog6ToothIcon,CubeIcon,DocumentChartBarIcon,HomeIcon,ShoppingBagIcon,ShoppingCartIcon,UsersIcon,WrenchScrewdriverIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.HomeIcon\n    },\n    {\n        name: \"products\",\n        href: \"/products\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CubeIcon\n    },\n    {\n        name: \"customers\",\n        href: \"/customers\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.UsersIcon\n    },\n    {\n        name: \"sales\",\n        href: \"/sales\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShoppingCartIcon\n    },\n    {\n        name: \"purchases\",\n        href: \"/purchases\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ShoppingBagIcon\n    },\n    {\n        name: \"inventory\",\n        href: \"/inventory\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ArchiveBoxIcon\n    },\n    {\n        name: \"accounting\",\n        href: \"/accounting\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.CalculatorIcon\n    },\n    {\n        name: \"maintenance\",\n        href: \"/maintenance\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.WrenchScrewdriverIcon\n    },\n    {\n        name: \"reports\",\n        href: \"/reports\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.DocumentChartBarIcon\n    },\n    {\n        name: \"settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.Cog6ToothIcon\n    }\n];\nfunction Sidebar({ open, setOpen }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-y-0 left-0 rtl:left-auto rtl:right-0 z-50 ${open ? \"w-64\" : \"w-20\"} bg-white shadow-lg transition-all duration-300 hidden lg:block`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-bold\",\n                                                children: \"BMS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 46,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Business Management System\" || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setOpen(!open),\n                                    className: \"p-1.5 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: open ? router.locale === \"ar\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronRightIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 58,\n                                        columnNumber: 42\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronLeftIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 58,\n                                        columnNumber: 85\n                                    }, this) : router.locale === \"ar\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronLeftIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 60,\n                                        columnNumber: 42\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronRightIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 60,\n                                        columnNumber: 84\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-2 py-4 space-y-1 overflow-y-auto\",\n                            children: navigation.map((item)=>{\n                                const isActive = router.pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    className: `group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${isActive ? \"bg-primary-100 text-primary-900\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: `${open ? \"mr-3 rtl:mr-0 rtl:ml-3\" : \"mx-auto\"} h-6 w-6 flex-shrink-0 ${isActive ? \"text-primary-600\" : \"text-gray-400 group-hover:text-gray-500\"}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 79,\n                                            columnNumber: 19\n                                        }, this),\n                                        open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: t(`navigation.${item.name}`)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 85,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 70,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-y-0 left-0 rtl:left-auto rtl:right-0 z-50 w-64 bg-white shadow-lg transform ${open ? \"translate-x-0 rtl:-translate-x-0\" : \"-translate-x-full rtl:translate-x-full\"} transition-transform duration-300 lg:hidden`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-bold\",\n                                                children: \"BMS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold text-gray-900\",\n                                            children: \"Business Management System\" || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setOpen(false),\n                                    className: \"p-1.5 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CalculatorIcon_ChevronLeftIcon_ChevronRightIcon_Cog6ToothIcon_CubeIcon_DocumentChartBarIcon_HomeIcon_ShoppingBagIcon_ShoppingCartIcon_UsersIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__.ChevronLeftIcon, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-2 py-4 space-y-1 overflow-y-auto\",\n                            children: navigation.map((item)=>{\n                                const isActive = router.pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    onClick: ()=>setOpen(false),\n                                    className: `group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${isActive ? \"bg-primary-100 text-primary-900\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: `mr-3 rtl:mr-0 rtl:ml-3 h-6 w-6 flex-shrink-0 ${isActive ? \"text-primary-600\" : \"text-gray-400 group-hover:text-gray-500\"}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: t(`navigation.${item.name}`)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\Sidebar.js\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1NpZGViYXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBd0M7QUFDTTtBQUNqQjtBQWNRO0FBRXJDLE1BQU1lLGFBQWE7SUFDakI7UUFBRUMsTUFBTTtRQUFhQyxNQUFNO1FBQUtDLE1BQU1mLDRRQUFRQTtJQUFDO0lBQy9DO1FBQUVhLE1BQU07UUFBWUMsTUFBTTtRQUFhQyxNQUFNZCw0UUFBUUE7SUFBQztJQUN0RDtRQUFFWSxNQUFNO1FBQWFDLE1BQU07UUFBY0MsTUFBTWIsNlFBQVNBO0lBQUM7SUFDekQ7UUFBRVcsTUFBTTtRQUFTQyxNQUFNO1FBQVVDLE1BQU1aLG9SQUFnQkE7SUFBQztJQUN4RDtRQUFFVSxNQUFNO1FBQWFDLE1BQU07UUFBY0MsTUFBTVgsbVJBQWVBO0lBQUM7SUFDL0Q7UUFBRVMsTUFBTTtRQUFhQyxNQUFNO1FBQWNDLE1BQU1WLGtSQUFjQTtJQUFDO0lBQzlEO1FBQUVRLE1BQU07UUFBY0MsTUFBTTtRQUFlQyxNQUFNVCxrUkFBY0E7SUFBQztJQUNoRTtRQUFFTyxNQUFNO1FBQWVDLE1BQU07UUFBZ0JDLE1BQU1SLHlSQUFxQkE7SUFBQztJQUN6RTtRQUFFTSxNQUFNO1FBQVdDLE1BQU07UUFBWUMsTUFBTVAsd1JBQW9CQTtJQUFDO0lBQ2hFO1FBQUVLLE1BQU07UUFBWUMsTUFBTTtRQUFhQyxNQUFNTixpUkFBYUE7SUFBQztDQUM1RDtBQUVjLFNBQVNPLFFBQVEsRUFBRUMsSUFBSSxFQUFFQyxPQUFPLEVBQUU7SUFDL0MsTUFBTUMsU0FBU3RCLHNEQUFTQTtJQUN4QixNQUFNLEVBQUV1QixDQUFDLEVBQUUsR0FBR3RCLDREQUFjQSxDQUFDO0lBRTdCLHFCQUNFOzswQkFFRSw4REFBQ3VCO2dCQUFJQyxXQUFXLENBQUMsc0RBQXNELEVBQUVMLE9BQU8sU0FBUyxPQUFPLCtEQUErRCxDQUFDOzBCQUM5Siw0RUFBQ0k7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs0QkFBSUMsV0FBVTs7Z0NBQ1pMLHNCQUNDLDhEQUFDSTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDQztnREFBS0QsV0FBVTswREFBK0I7Ozs7Ozs7Ozs7O3NEQUVqRCw4REFBQ0M7NENBQUtELFdBQVU7c0RBQ2JFLDRCQUFvQyxJQUFJOzs7Ozs7Ozs7Ozs7OENBSS9DLDhEQUFDRztvQ0FDQ0MsU0FBUyxJQUFNVixRQUFRLENBQUNEO29DQUN4QkssV0FBVTs4Q0FFVEwsT0FDQ0UsT0FBT1UsTUFBTSxLQUFLLHFCQUFPLDhEQUFDbEIsb1JBQWdCQTt3Q0FBQ1csV0FBVTs7Ozs7NkRBQWUsOERBQUNaLG1SQUFlQTt3Q0FBQ1ksV0FBVTs7Ozs7K0NBRS9GSCxPQUFPVSxNQUFNLEtBQUsscUJBQU8sOERBQUNuQixtUkFBZUE7d0NBQUNZLFdBQVU7Ozs7OzZEQUFlLDhEQUFDWCxvUkFBZ0JBO3dDQUFDVyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNckcsOERBQUNROzRCQUFJUixXQUFVO3NDQUNaVixXQUFXbUIsR0FBRyxDQUFDLENBQUNDO2dDQUNmLE1BQU1DLFdBQVdkLE9BQU9lLFFBQVEsS0FBS0YsS0FBS2xCLElBQUk7Z0NBQzlDLHFCQUNFLDhEQUFDZixrREFBSUE7b0NBRUhlLE1BQU1rQixLQUFLbEIsSUFBSTtvQ0FDZlEsV0FBVyxDQUFDLG1GQUFtRixFQUM3RlcsV0FDSSxvQ0FDQSxxREFDTCxDQUFDOztzREFFRiw4REFBQ0QsS0FBS2pCLElBQUk7NENBQ1JPLFdBQVcsQ0FBQyxFQUFFTCxPQUFPLDJCQUEyQixVQUFVLHVCQUF1QixFQUMvRWdCLFdBQVcscUJBQXFCLDBDQUNqQyxDQUFDOzs7Ozs7d0NBRUhoQixzQkFDQyw4REFBQ007NENBQUtELFdBQVU7c0RBQ2JGLEVBQUUsQ0FBQyxXQUFXLEVBQUVZLEtBQUtuQixJQUFJLENBQUMsQ0FBQzs7Ozs7OzttQ0FmM0JtQixLQUFLbkIsSUFBSTs7Ozs7NEJBb0JwQjs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTU4sOERBQUNRO2dCQUFJQyxXQUFXLENBQUMsd0ZBQXdGLEVBQUVMLE9BQU8scUNBQXFDLHlDQUF5Qyw0Q0FBNEMsQ0FBQzswQkFDM08sNEVBQUNJO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDQztnREFBS0QsV0FBVTswREFBK0I7Ozs7Ozs7Ozs7O3NEQUVqRCw4REFBQ0M7NENBQUtELFdBQVU7c0RBQ2JFLDRCQUFvQyxJQUFJOzs7Ozs7Ozs7Ozs7OENBRzdDLDhEQUFDRztvQ0FDQ0MsU0FBUyxJQUFNVixRQUFRO29DQUN2QkksV0FBVTs4Q0FFViw0RUFBQ1osbVJBQWVBO3dDQUFDWSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLL0IsOERBQUNROzRCQUFJUixXQUFVO3NDQUNaVixXQUFXbUIsR0FBRyxDQUFDLENBQUNDO2dDQUNmLE1BQU1DLFdBQVdkLE9BQU9lLFFBQVEsS0FBS0YsS0FBS2xCLElBQUk7Z0NBQzlDLHFCQUNFLDhEQUFDZixrREFBSUE7b0NBRUhlLE1BQU1rQixLQUFLbEIsSUFBSTtvQ0FDZmMsU0FBUyxJQUFNVixRQUFRO29DQUN2QkksV0FBVyxDQUFDLG1GQUFtRixFQUM3RlcsV0FDSSxvQ0FDQSxxREFDTCxDQUFDOztzREFFRiw4REFBQ0QsS0FBS2pCLElBQUk7NENBQ1JPLFdBQVcsQ0FBQyw2Q0FBNkMsRUFDdkRXLFdBQVcscUJBQXFCLDBDQUNqQyxDQUFDOzs7Ozs7c0RBRUosOERBQUNWOzRDQUFLRCxXQUFVO3NEQUNiRixFQUFFLENBQUMsV0FBVyxFQUFFWSxLQUFLbkIsSUFBSSxDQUFDLENBQUM7Ozs7Ozs7bUNBZnpCbUIsS0FBS25CLElBQUk7Ozs7OzRCQW1CcEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNWiIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vY29tcG9uZW50cy9TaWRlYmFyLmpzPzNkYWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInO1xuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tICduZXh0LWkxOG5leHQnO1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCB7XG4gIEhvbWVJY29uLFxuICBDdWJlSWNvbixcbiAgVXNlcnNJY29uLFxuICBTaG9wcGluZ0NhcnRJY29uLFxuICBTaG9wcGluZ0JhZ0ljb24sXG4gIEFyY2hpdmVCb3hJY29uLFxuICBDYWxjdWxhdG9ySWNvbixcbiAgV3JlbmNoU2NyZXdkcml2ZXJJY29uLFxuICBEb2N1bWVudENoYXJ0QmFySWNvbixcbiAgQ29nNlRvb3RoSWNvbixcbiAgQ2hldnJvbkxlZnRJY29uLFxuICBDaGV2cm9uUmlnaHRJY29uLFxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuXG5jb25zdCBuYXZpZ2F0aW9uID0gW1xuICB7IG5hbWU6ICdkYXNoYm9hcmQnLCBocmVmOiAnLycsIGljb246IEhvbWVJY29uIH0sXG4gIHsgbmFtZTogJ3Byb2R1Y3RzJywgaHJlZjogJy9wcm9kdWN0cycsIGljb246IEN1YmVJY29uIH0sXG4gIHsgbmFtZTogJ2N1c3RvbWVycycsIGhyZWY6ICcvY3VzdG9tZXJzJywgaWNvbjogVXNlcnNJY29uIH0sXG4gIHsgbmFtZTogJ3NhbGVzJywgaHJlZjogJy9zYWxlcycsIGljb246IFNob3BwaW5nQ2FydEljb24gfSxcbiAgeyBuYW1lOiAncHVyY2hhc2VzJywgaHJlZjogJy9wdXJjaGFzZXMnLCBpY29uOiBTaG9wcGluZ0JhZ0ljb24gfSxcbiAgeyBuYW1lOiAnaW52ZW50b3J5JywgaHJlZjogJy9pbnZlbnRvcnknLCBpY29uOiBBcmNoaXZlQm94SWNvbiB9LFxuICB7IG5hbWU6ICdhY2NvdW50aW5nJywgaHJlZjogJy9hY2NvdW50aW5nJywgaWNvbjogQ2FsY3VsYXRvckljb24gfSxcbiAgeyBuYW1lOiAnbWFpbnRlbmFuY2UnLCBocmVmOiAnL21haW50ZW5hbmNlJywgaWNvbjogV3JlbmNoU2NyZXdkcml2ZXJJY29uIH0sXG4gIHsgbmFtZTogJ3JlcG9ydHMnLCBocmVmOiAnL3JlcG9ydHMnLCBpY29uOiBEb2N1bWVudENoYXJ0QmFySWNvbiB9LFxuICB7IG5hbWU6ICdzZXR0aW5ncycsIGhyZWY6ICcvc2V0dGluZ3MnLCBpY29uOiBDb2c2VG9vdGhJY29uIH0sXG5dO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTaWRlYmFyKHsgb3Blbiwgc2V0T3BlbiB9KSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKCdjb21tb24nKTtcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICB7LyogRGVza3RvcCBTaWRlYmFyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BmaXhlZCBpbnNldC15LTAgbGVmdC0wIHJ0bDpsZWZ0LWF1dG8gcnRsOnJpZ2h0LTAgei01MCAke29wZW4gPyAndy02NCcgOiAndy0yMCd9IGJnLXdoaXRlIHNoYWRvdy1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaGlkZGVuIGxnOmJsb2NrYH0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBoLWZ1bGxcIj5cbiAgICAgICAgICB7LyogTG9nbyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTQgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICB7b3BlbiAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHJ0bDpzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctcHJpbWFyeS02MDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtYm9sZFwiPkJNUzwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAge3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NPTVBBTllfTkFNRSB8fCAnQk1TJ31cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0T3Blbighb3Blbil9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMS41IHJvdW5kZWQtbWQgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIGhvdmVyOmJnLWdyYXktMTAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge29wZW4gPyAoXG4gICAgICAgICAgICAgICAgcm91dGVyLmxvY2FsZSA9PT0gJ2FyJyA/IDxDaGV2cm9uUmlnaHRJY29uIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPiA6IDxDaGV2cm9uTGVmdEljb24gY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgcm91dGVyLmxvY2FsZSA9PT0gJ2FyJyA/IDxDaGV2cm9uTGVmdEljb24gY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+IDogPENoZXZyb25SaWdodEljb24gY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uICovfVxuICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiZmxleC0xIHB4LTIgcHktNCBzcGFjZS15LTEgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICB7bmF2aWdhdGlvbi5tYXAoKGl0ZW0pID0+IHtcbiAgICAgICAgICAgICAgY29uc3QgaXNBY3RpdmUgPSByb3V0ZXIucGF0aG5hbWUgPT09IGl0ZW0uaHJlZjtcbiAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAga2V5PXtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bncm91cCBmbGV4IGl0ZW1zLWNlbnRlciBweC0yIHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLW1kIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIGlzQWN0aXZlXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctcHJpbWFyeS0xMDAgdGV4dC1wcmltYXJ5LTkwMCdcbiAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNTAgaG92ZXI6dGV4dC1ncmF5LTkwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxpdGVtLmljb25cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtvcGVuID8gJ21yLTMgcnRsOm1yLTAgcnRsOm1sLTMnIDogJ214LWF1dG8nfSBoLTYgdy02IGZsZXgtc2hyaW5rLTAgJHtcbiAgICAgICAgICAgICAgICAgICAgICBpc0FjdGl2ZSA/ICd0ZXh0LXByaW1hcnktNjAwJyA6ICd0ZXh0LWdyYXktNDAwIGdyb3VwLWhvdmVyOnRleHQtZ3JheS01MDAnXG4gICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIHtvcGVuICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7dChgbmF2aWdhdGlvbi4ke2l0ZW0ubmFtZX1gKX1cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICk7XG4gICAgICAgICAgICB9KX1cbiAgICAgICAgICA8L25hdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE1vYmlsZSBTaWRlYmFyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BmaXhlZCBpbnNldC15LTAgbGVmdC0wIHJ0bDpsZWZ0LWF1dG8gcnRsOnJpZ2h0LTAgei01MCB3LTY0IGJnLXdoaXRlIHNoYWRvdy1sZyB0cmFuc2Zvcm0gJHtvcGVuID8gJ3RyYW5zbGF0ZS14LTAgcnRsOi10cmFuc2xhdGUteC0wJyA6ICctdHJhbnNsYXRlLXgtZnVsbCBydGw6dHJhbnNsYXRlLXgtZnVsbCd9IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMCBsZzpoaWRkZW5gfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGgtZnVsbFwiPlxuICAgICAgICAgIHsvKiBMb2dvICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIHJ0bDpzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLXByaW1hcnktNjAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtc20gZm9udC1ib2xkXCI+Qk1TPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICB7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQ09NUEFOWV9OQU1FIHx8ICdCTVMnfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0T3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMS41IHJvdW5kZWQtbWQgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIGhvdmVyOmJnLWdyYXktMTAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPENoZXZyb25MZWZ0SWNvbiBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIE5hdmlnYXRpb24gKi99XG4gICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJmbGV4LTEgcHgtMiBweS00IHNwYWNlLXktMSBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgICAgIHtuYXZpZ2F0aW9uLm1hcCgoaXRlbSkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBpc0FjdGl2ZSA9IHJvdXRlci5wYXRobmFtZSA9PT0gaXRlbS5ocmVmO1xuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICBrZXk9e2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldE9wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZ3JvdXAgZmxleCBpdGVtcy1jZW50ZXIgcHgtMiBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICBpc0FjdGl2ZVxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXByaW1hcnktMTAwIHRleHQtcHJpbWFyeS05MDAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTYwMCBob3ZlcjpiZy1ncmF5LTUwIGhvdmVyOnRleHQtZ3JheS05MDAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8aXRlbS5pY29uXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YG1yLTMgcnRsOm1yLTAgcnRsOm1sLTMgaC02IHctNiBmbGV4LXNocmluay0wICR7XG4gICAgICAgICAgICAgICAgICAgICAgaXNBY3RpdmUgPyAndGV4dC1wcmltYXJ5LTYwMCcgOiAndGV4dC1ncmF5LTQwMCBncm91cC1ob3Zlcjp0ZXh0LWdyYXktNTAwJ1xuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICB7dChgbmF2aWdhdGlvbi4ke2l0ZW0ubmFtZX1gKX1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICk7XG4gICAgICAgICAgICB9KX1cbiAgICAgICAgICA8L25hdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8Lz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VSb3V0ZXIiLCJ1c2VUcmFuc2xhdGlvbiIsIkxpbmsiLCJIb21lSWNvbiIsIkN1YmVJY29uIiwiVXNlcnNJY29uIiwiU2hvcHBpbmdDYXJ0SWNvbiIsIlNob3BwaW5nQmFnSWNvbiIsIkFyY2hpdmVCb3hJY29uIiwiQ2FsY3VsYXRvckljb24iLCJXcmVuY2hTY3Jld2RyaXZlckljb24iLCJEb2N1bWVudENoYXJ0QmFySWNvbiIsIkNvZzZUb290aEljb24iLCJDaGV2cm9uTGVmdEljb24iLCJDaGV2cm9uUmlnaHRJY29uIiwibmF2aWdhdGlvbiIsIm5hbWUiLCJocmVmIiwiaWNvbiIsIlNpZGViYXIiLCJvcGVuIiwic2V0T3BlbiIsInJvdXRlciIsInQiLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0NPTVBBTllfTkFNRSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJsb2NhbGUiLCJuYXYiLCJtYXAiLCJpdGVtIiwiaXNBY3RpdmUiLCJwYXRobmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/Sidebar.js\n");

/***/ }),

/***/ "./contexts/AuthContext.js":
/*!*********************************!*\
  !*** ./contexts/AuthContext.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__]);\n([axios__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nconst initialState = {\n    user: null,\n    token: null,\n    isLoading: true,\n    isAuthenticated: false\n};\nfunction authReducer(state, action) {\n    switch(action.type){\n        case \"LOGIN_SUCCESS\":\n            return {\n                ...state,\n                user: action.payload.user,\n                token: action.payload.token,\n                isAuthenticated: true,\n                isLoading: false\n            };\n        case \"LOGOUT\":\n            return {\n                ...state,\n                user: null,\n                token: null,\n                isAuthenticated: false,\n                isLoading: false\n            };\n        case \"SET_LOADING\":\n            return {\n                ...state,\n                isLoading: action.payload\n            };\n        case \"UPDATE_USER\":\n            return {\n                ...state,\n                user: {\n                    ...state.user,\n                    ...action.payload\n                }\n            };\n        default:\n            return state;\n    }\n}\nfunction AuthProvider({ children }) {\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(authReducer, initialState);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Configure axios defaults\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const token = localStorage.getItem(\"token\");\n        if (token) {\n            axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n        }\n    }, []);\n    // Check for existing token on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            const token = localStorage.getItem(\"token\");\n            if (token) {\n                try {\n                    axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n                    const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(`${\"http://localhost:3000\"}/api/auth/profile`);\n                    dispatch({\n                        type: \"LOGIN_SUCCESS\",\n                        payload: {\n                            user: response.data.user,\n                            token: token\n                        }\n                    });\n                } catch (error) {\n                    console.error(\"Token validation failed:\", error);\n                    localStorage.removeItem(\"token\");\n                    delete axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"];\n                    dispatch({\n                        type: \"LOGOUT\"\n                    });\n                }\n            } else {\n                dispatch({\n                    type: \"SET_LOADING\",\n                    payload: false\n                });\n            }\n        };\n        checkAuth();\n    }, []);\n    const login = async (username, password)=>{\n        try {\n            dispatch({\n                type: \"SET_LOADING\",\n                payload: true\n            });\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(`${\"http://localhost:3000\"}/api/auth/login`, {\n                username,\n                password\n            });\n            const { token, user } = response.data;\n            // Store token in localStorage\n            localStorage.setItem(\"token\", token);\n            // Set axios default header\n            axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\n            dispatch({\n                type: \"LOGIN_SUCCESS\",\n                payload: {\n                    user,\n                    token\n                }\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(response.data.message || \"Login successful\");\n            // Redirect to dashboard\n            router.push(\"/\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            dispatch({\n                type: \"SET_LOADING\",\n                payload: false\n            });\n            const errorMessage = error.response?.data?.error || \"Login failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    };\n    const logout = ()=>{\n        // Remove token from localStorage\n        localStorage.removeItem(\"token\");\n        // Remove axios default header\n        delete axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].defaults.headers.common[\"Authorization\"];\n        dispatch({\n            type: \"LOGOUT\"\n        });\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(\"Logged out successfully\");\n        router.push(\"/login\");\n    };\n    const updateProfile = async (profileData)=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put(`${\"http://localhost:3000\"}/api/auth/profile`, profileData);\n            dispatch({\n                type: \"UPDATE_USER\",\n                payload: response.data.user\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(response.data.message || \"Profile updated successfully\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            const errorMessage = error.response?.data?.error || \"Profile update failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    };\n    const changePassword = async (currentPassword, newPassword)=>{\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put(`${\"http://localhost:3000\"}/api/auth/change-password`, {\n                currentPassword,\n                newPassword\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(response.data.message || \"Password changed successfully\");\n            return {\n                success: true\n            };\n        } catch (error) {\n            const errorMessage = error.response?.data?.error || \"Password change failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n    };\n    const value = {\n        ...state,\n        login,\n        logout,\n        updateProfile,\n        changePassword\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\contexts\\\\AuthContext.js\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthContext);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/AuthContext.js\n");

/***/ }),

/***/ "./contexts/SocketContext.js":
/*!***********************************!*\
  !*** ./contexts/SocketContext.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"socket.io-client\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([socket_io_client__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__]);\n([socket_io_client__WEBPACK_IMPORTED_MODULE_2__, _AuthContext__WEBPACK_IMPORTED_MODULE_3__, react_hot_toast__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst SocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction SocketProvider({ children }) {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { token, isAuthenticated } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isAuthenticated && token) {\n            // Initialize socket connection\n            const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(\"http://localhost:3000\", {\n                auth: {\n                    token: token\n                }\n            });\n            newSocket.on(\"connect\", ()=>{\n                console.log(\"Socket connected\");\n                setIsConnected(true);\n            });\n            newSocket.on(\"disconnect\", ()=>{\n                console.log(\"Socket disconnected\");\n                setIsConnected(false);\n            });\n            // Listen for real-time events\n            newSocket.on(\"inventory_updated\", (data)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(`Inventory updated: ${data.productName}`);\n            // You can dispatch events to update local state here\n            });\n            newSocket.on(\"order_created\", (data)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].info(`New order created: ${data.orderNumber}`);\n                // Add notification\n                setNotifications((prev)=>[\n                        {\n                            id: Date.now(),\n                            type: \"info\",\n                            title: \"New Order\",\n                            message: `Order ${data.orderNumber} has been created`,\n                            timestamp: new Date(),\n                            read: false\n                        },\n                        ...prev\n                    ]);\n            });\n            newSocket.on(\"maintenance_updated\", (data)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].info(`Maintenance order ${data.orderNumber} status updated`);\n                // Add notification\n                setNotifications((prev)=>[\n                        {\n                            id: Date.now(),\n                            type: \"info\",\n                            title: \"Maintenance Update\",\n                            message: `Order ${data.orderNumber} status: ${data.status}`,\n                            timestamp: new Date(),\n                            read: false\n                        },\n                        ...prev\n                    ]);\n            });\n            newSocket.on(\"low_stock_alert\", (data)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].warning(`Low stock alert: ${data.productName}`);\n                // Add notification\n                setNotifications((prev)=>[\n                        {\n                            id: Date.now(),\n                            type: \"warning\",\n                            title: \"Low Stock Alert\",\n                            message: `${data.productName} is running low (${data.currentStock} remaining)`,\n                            timestamp: new Date(),\n                            read: false\n                        },\n                        ...prev\n                    ]);\n            });\n            setSocket(newSocket);\n            return ()=>{\n                newSocket.close();\n            };\n        } else {\n            // Clean up socket when not authenticated\n            if (socket) {\n                socket.close();\n                setSocket(null);\n                setIsConnected(false);\n            }\n        }\n    }, [\n        isAuthenticated,\n        token\n    ]);\n    const emitEvent = (eventName, data)=>{\n        if (socket && isConnected) {\n            socket.emit(eventName, data);\n        }\n    };\n    const markNotificationAsRead = (notificationId)=>{\n        setNotifications((prev)=>prev.map((notification)=>notification.id === notificationId ? {\n                    ...notification,\n                    read: true\n                } : notification));\n    };\n    const clearNotifications = ()=>{\n        setNotifications([]);\n    };\n    const value = {\n        socket,\n        isConnected,\n        notifications,\n        emitEvent,\n        markNotificationAsRead,\n        clearNotifications\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocketContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\contexts\\\\SocketContext.js\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\nfunction useSocket() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SocketContext);\n    if (!context) {\n        throw new Error(\"useSocket must be used within a SocketProvider\");\n    }\n    return context;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SocketContext);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./contexts/SocketContext.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../contexts/SocketContext */ \"./contexts/SocketContext.js\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_9__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_5__, _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__, _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_7__, _components_Layout__WEBPACK_IMPORTED_MODULE_8__]);\n([react_hot_toast__WEBPACK_IMPORTED_MODULE_5__, _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__, _contexts_SocketContext__WEBPACK_IMPORTED_MODULE_7__, _components_Layout__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nconst queryClient = new react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n    defaultOptions: {\n        queries: {\n            retry: 1,\n            refetchOnWindowFocus: false\n        }\n    }\n});\nfunction MyApp({ Component, pageProps }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Set document direction based on locale\n        const direction = router.locale === \"ar\" ? \"rtl\" : \"ltr\";\n        document.documentElement.dir = direction;\n        document.documentElement.lang = router.locale;\n        // Set font family based on locale\n        const fontClass = router.locale === \"ar\" ? \"font-arabic\" : \"font-english\";\n        document.body.className = fontClass;\n    }, [\n        router.locale\n    ]);\n    // Check if it's a login page\n    const isLoginPage = router.pathname === \"/login\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_SocketContext__WEBPACK_IMPORTED_MODULE_7__.SocketProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `min-h-screen bg-gray-50 ${router.locale === \"ar\" ? \"font-arabic\" : \"font-english\"}`,\n                    children: [\n                        isLoginPage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...pageProps\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                            lineNumber: 43,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                ...pageProps\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                                lineNumber: 46,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                            lineNumber: 45,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                            position: router.locale === \"ar\" ? \"top-left\" : \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#363636\",\n                                    color: \"#fff\",\n                                    direction: router.locale === \"ar\" ? \"rtl\" : \"ltr\"\n                                },\n                                success: {\n                                    style: {\n                                        background: \"#10B981\"\n                                    }\n                                },\n                                error: {\n                                    style: {\n                                        background: \"#EF4444\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                    lineNumber: 41,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\_app.js\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.appWithTranslation)(MyApp));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next-i18next":
/*!*******************************!*\
  !*** external "next-i18next" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-i18next");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-query":
/*!******************************!*\
  !*** external "react-query" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-query");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "socket.io-client":
/*!***********************************!*\
  !*** external "socket.io-client" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("socket.io-client");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();