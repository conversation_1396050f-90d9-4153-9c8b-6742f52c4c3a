import { useState, useEffect } from 'react';
import { XMarkIcon, CogIcon } from '@heroicons/react/24/outline';

export default function ProductCustomizer({ isOpen, onClose, product, onSave }) {
  const [selectedOptions, setSelectedOptions] = useState({});
  const [totalPrice, setTotalPrice] = useState(0);

  useEffect(() => {
    if (isOpen && product) {
      // Initialize with default selections
      const defaultSelections = {};
      product.customizationOptions?.forEach(option => {
        if (option.required && option.options.length > 0) {
          defaultSelections[option.id] = option.options[0].id;
        }
      });
      setSelectedOptions(defaultSelections);
    }
  }, [isOpen, product]);

  useEffect(() => {
    calculateTotalPrice();
  }, [selectedOptions, product]);

  const calculateTotalPrice = () => {
    if (!product) return;
    
    let total = product.basePrice || 0;
    
    Object.entries(selectedOptions).forEach(([optionId, selectedId]) => {
      const option = product.customizationOptions?.find(opt => opt.id === optionId);
      const selectedOption = option?.options.find(opt => opt.id === selectedId);
      if (selectedOption) {
        total += selectedOption.price;
      }
    });
    
    setTotalPrice(total);
  };

  const handleOptionChange = (optionId, selectedId) => {
    setSelectedOptions(prev => ({
      ...prev,
      [optionId]: selectedId
    }));
  };

  const handleSave = () => {
    const customizedProduct = {
      ...product,
      customizations: selectedOptions,
      finalPrice: totalPrice,
      customizationDetails: getCustomizationDetails()
    };
    onSave(customizedProduct);
    onClose();
  };

  const getCustomizationDetails = () => {
    const details = [];
    Object.entries(selectedOptions).forEach(([optionId, selectedId]) => {
      const option = product.customizationOptions?.find(opt => opt.id === optionId);
      const selectedOption = option?.options.find(opt => opt.id === selectedId);
      if (selectedOption) {
        details.push({
          optionName: option.name,
          selectedName: selectedOption.name,
          price: selectedOption.price,
          componentId: selectedOption.componentId
        });
      }
    });
    return details;
  };

  const isValid = () => {
    if (!product?.customizationOptions) return true;
    
    return product.customizationOptions.every(option => {
      if (option.required) {
        return selectedOptions[option.id];
      }
      return true;
    });
  };

  if (!isOpen || !product) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center">
            <CogIcon className="h-6 w-6 text-blue-600 mr-3" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                تخصيص المنتج
              </h2>
              <p className="text-sm text-gray-600">{product.nameAr || product.name}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Product Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">معلومات المنتج الأساسي</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-blue-700">الكود:</span>
                <span className="font-medium mr-2">{product.code}</span>
              </div>
              <div>
                <span className="text-blue-700">السعر الأساسي:</span>
                <span className="font-medium mr-2">${product.basePrice?.toFixed(2)}</span>
              </div>
            </div>
          </div>

          {/* Customization Options */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">خيارات التخصيص</h3>
            
            {product.customizationOptions?.map((option) => (
              <div key={option.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900">
                    {option.name}
                    {option.required && <span className="text-red-500 mr-1">*</span>}
                  </h4>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    {option.required ? 'مطلوب' : 'اختياري'}
                  </span>
                </div>
                
                <div className="space-y-2">
                  {option.options.map((opt) => (
                    <label
                      key={opt.id}
                      className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedOptions[option.id] === opt.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center">
                        <input
                          type="radio"
                          name={option.id}
                          value={opt.id}
                          checked={selectedOptions[option.id] === opt.id}
                          onChange={() => handleOptionChange(option.id, opt.id)}
                          className="text-blue-600 focus:ring-blue-500"
                        />
                        <span className="mr-3 font-medium">{opt.name}</span>
                      </div>
                      <div className="text-right">
                        <span className="text-green-600 font-medium">
                          +${opt.price.toFixed(2)}
                        </span>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Price Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-3">ملخص السعر</h3>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>السعر الأساسي:</span>
                <span>${product.basePrice?.toFixed(2)}</span>
              </div>
              
              {Object.entries(selectedOptions).map(([optionId, selectedId]) => {
                const option = product.customizationOptions?.find(opt => opt.id === optionId);
                const selectedOption = option?.options.find(opt => opt.id === selectedId);
                if (!selectedOption) return null;
                
                return (
                  <div key={optionId} className="flex justify-between text-sm text-gray-600">
                    <span>{option.name}: {selectedOption.name}</span>
                    <span>+${selectedOption.price.toFixed(2)}</span>
                  </div>
                );
              })}
              
              <div className="border-t pt-2 flex justify-between font-bold text-lg">
                <span>الإجمالي:</span>
                <span className="text-green-600">${totalPrice.toFixed(2)}</span>
              </div>
            </div>
          </div>

          {/* Validation Message */}
          {!isValid() && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-700 text-sm">
                يرجى اختيار جميع الخيارات المطلوبة قبل الحفظ
              </p>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-4 p-6 border-t bg-gray-50">
          <button
            type="button"
            onClick={onClose}
            className="btn-secondary"
          >
            إلغاء
          </button>
          <button
            type="button"
            onClick={handleSave}
            disabled={!isValid()}
            className={`btn-primary ${!isValid() ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            حفظ التخصيص
          </button>
        </div>
      </div>
    </div>
  );
}
