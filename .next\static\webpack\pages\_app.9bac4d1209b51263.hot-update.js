"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: Inter, ui-sans-serif, system-ui, sans-serif; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n\\n[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  border-radius: 0px;\\n  padding-top: 0.5rem;\\n  padding-right: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  padding-left: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  border-color: #2563eb;\\n}\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\ninput::placeholder,textarea::placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\n::-webkit-datetime-edit-fields-wrapper {\\n  padding: 0;\\n}\\n\\n::-webkit-date-and-time-value {\\n  min-height: 1.5em;\\n  text-align: inherit;\\n}\\n\\n::-webkit-datetime-edit {\\n  display: inline-flex;\\n}\\n\\n::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n}\\n\\nselect {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\\\");\\n  background-position: right 0.5rem center;\\n  background-repeat: no-repeat;\\n  background-size: 1.5em 1.5em;\\n  padding-right: 2.5rem;\\n  -webkit-print-color-adjust: exact;\\n          print-color-adjust: exact;\\n}\\n\\n[multiple],[size]:where(select:not([size=\\\"1\\\"])) {\\n  background-image: initial;\\n  background-position: initial;\\n  background-repeat: unset;\\n  background-size: initial;\\n  padding-right: 0.75rem;\\n  -webkit-print-color-adjust: unset;\\n          print-color-adjust: unset;\\n}\\n\\n[type='checkbox'],[type='radio'] {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  padding: 0;\\n  -webkit-print-color-adjust: exact;\\n          print-color-adjust: exact;\\n  display: inline-block;\\n  vertical-align: middle;\\n  background-origin: border-box;\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n  flex-shrink: 0;\\n  height: 1rem;\\n  width: 1rem;\\n  color: #2563eb;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='checkbox'] {\\n  border-radius: 0px;\\n}\\n\\n[type='radio'] {\\n  border-radius: 100%;\\n}\\n\\n[type='checkbox']:focus,[type='radio']:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n}\\n\\n[type='checkbox']:checked,[type='radio']:checked {\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n[type='checkbox']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:checked {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='radio']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='radio']:checked {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='checkbox']:indeterminate {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e\\\");\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:indeterminate {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='file'] {\\n  background: unset;\\n  border-color: inherit;\\n  border-width: 0;\\n  border-radius: 0;\\n  padding: 0;\\n  font-size: unset;\\n  line-height: inherit;\\n}\\n\\n[type='file']:focus {\\n  outline: 1px solid ButtonText;\\n  outline: 1px auto -webkit-focus-ring-color;\\n}\\n.form-input,.form-textarea,.form-select,.form-multiselect {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  border-radius: 0px;\\n  padding-top: 0.5rem;\\n  padding-right: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  padding-left: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n  --tw-shadow: 0 0 #0000;\\n}\\n.form-input:focus, .form-textarea:focus, .form-select:focus, .form-multiselect:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  border-color: #2563eb;\\n}\\n.form-input::-moz-placeholder, .form-textarea::-moz-placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n.form-input::placeholder,.form-textarea::placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n.form-input::-webkit-datetime-edit-fields-wrapper {\\n  padding: 0;\\n}\\n.form-input::-webkit-date-and-time-value {\\n  min-height: 1.5em;\\n  text-align: inherit;\\n}\\n.form-input::-webkit-datetime-edit {\\n  display: inline-flex;\\n}\\n.form-input::-webkit-datetime-edit,.form-input::-webkit-datetime-edit-year-field,.form-input::-webkit-datetime-edit-month-field,.form-input::-webkit-datetime-edit-day-field,.form-input::-webkit-datetime-edit-hour-field,.form-input::-webkit-datetime-edit-minute-field,.form-input::-webkit-datetime-edit-second-field,.form-input::-webkit-datetime-edit-millisecond-field,.form-input::-webkit-datetime-edit-meridiem-field {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n}\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n.fixed {\\n  position: fixed;\\n}\\n.absolute {\\n  position: absolute;\\n}\\n.relative {\\n  position: relative;\\n}\\n.inset-0 {\\n  inset: 0px;\\n}\\n.inset-y-0 {\\n  top: 0px;\\n  bottom: 0px;\\n}\\n.-right-1 {\\n  right: -0.25rem;\\n}\\n.-top-1 {\\n  top: -0.25rem;\\n}\\n.left-0 {\\n  left: 0px;\\n}\\n.left-3 {\\n  left: 0.75rem;\\n}\\n.right-0 {\\n  right: 0px;\\n}\\n.top-1\\\\/2 {\\n  top: 50%;\\n}\\n.z-40 {\\n  z-index: 40;\\n}\\n.z-50 {\\n  z-index: 50;\\n}\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\n.ml-1 {\\n  margin-left: 0.25rem;\\n}\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\n.ml-4 {\\n  margin-left: 1rem;\\n}\\n.ml-5 {\\n  margin-left: 1.25rem;\\n}\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\n.mr-3 {\\n  margin-right: 0.75rem;\\n}\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\n.block {\\n  display: block;\\n}\\n.inline {\\n  display: inline;\\n}\\n.flex {\\n  display: flex;\\n}\\n.inline-flex {\\n  display: inline-flex;\\n}\\n.table {\\n  display: table;\\n}\\n.grid {\\n  display: grid;\\n}\\n.hidden {\\n  display: none;\\n}\\n.h-12 {\\n  height: 3rem;\\n}\\n.h-2 {\\n  height: 0.5rem;\\n}\\n.h-20 {\\n  height: 5rem;\\n}\\n.h-4 {\\n  height: 1rem;\\n}\\n.h-5 {\\n  height: 1.25rem;\\n}\\n.h-6 {\\n  height: 1.5rem;\\n}\\n.h-64 {\\n  height: 16rem;\\n}\\n.h-8 {\\n  height: 2rem;\\n}\\n.h-full {\\n  height: 100%;\\n}\\n.max-h-96 {\\n  max-height: 24rem;\\n}\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\n.w-0 {\\n  width: 0px;\\n}\\n.w-11 {\\n  width: 2.75rem;\\n}\\n.w-12 {\\n  width: 3rem;\\n}\\n.w-2 {\\n  width: 0.5rem;\\n}\\n.w-20 {\\n  width: 5rem;\\n}\\n.w-32 {\\n  width: 8rem;\\n}\\n.w-4 {\\n  width: 1rem;\\n}\\n.w-48 {\\n  width: 12rem;\\n}\\n.w-5 {\\n  width: 1.25rem;\\n}\\n.w-6 {\\n  width: 1.5rem;\\n}\\n.w-64 {\\n  width: 16rem;\\n}\\n.w-8 {\\n  width: 2rem;\\n}\\n.w-80 {\\n  width: 20rem;\\n}\\n.w-full {\\n  width: 100%;\\n}\\n.min-w-0 {\\n  min-width: 0px;\\n}\\n.max-w-7xl {\\n  max-width: 80rem;\\n}\\n.max-w-md {\\n  max-width: 28rem;\\n}\\n.max-w-xs {\\n  max-width: 20rem;\\n}\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\n.-translate-x-full {\\n  --tw-translate-x: -100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.-translate-y-1\\\\/2 {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-0 {\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\n.appearance-none {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n}\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.flex-col {\\n  flex-direction: column;\\n}\\n.items-start {\\n  align-items: flex-start;\\n}\\n.items-end {\\n  align-items: flex-end;\\n}\\n.items-center {\\n  align-items: center;\\n}\\n.justify-end {\\n  justify-content: flex-end;\\n}\\n.justify-center {\\n  justify-content: center;\\n}\\n.justify-between {\\n  justify-content: space-between;\\n}\\n.gap-4 {\\n  gap: 1rem;\\n}\\n.gap-5 {\\n  gap: 1.25rem;\\n}\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\n.overflow-x-auto {\\n  overflow-x: auto;\\n}\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\n.truncate {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\n.rounded-md {\\n  border-radius: 0.375rem;\\n}\\n.border {\\n  border-width: 1px;\\n}\\n.border-2 {\\n  border-width: 2px;\\n}\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\n.border-t {\\n  border-top-width: 1px;\\n}\\n.border-gray-100 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.border-primary-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\\n}\\n.border-red-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));\\n}\\n.border-transparent {\\n  border-color: transparent;\\n}\\n.border-t-primary-600 {\\n  --tw-border-opacity: 1;\\n  border-top-color: rgb(2 132 199 / var(--tw-border-opacity, 1));\\n}\\n.bg-black {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\\n}\\n.bg-pink-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\\n}\\n.bg-opacity-50 {\\n  --tw-bg-opacity: 0.5;\\n}\\n.p-1 {\\n  padding: 0.25rem;\\n}\\n.p-1\\\\.5 {\\n  padding: 0.375rem;\\n}\\n.p-12 {\\n  padding: 3rem;\\n}\\n.p-2 {\\n  padding: 0.5rem;\\n}\\n.p-3 {\\n  padding: 0.75rem;\\n}\\n.p-4 {\\n  padding: 1rem;\\n}\\n.p-5 {\\n  padding: 1.25rem;\\n}\\n.p-6 {\\n  padding: 1.5rem;\\n}\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-2\\\\.5 {\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n}\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\n.py-0\\\\.5 {\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n}\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-12 {\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\n.py-5 {\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\n.pl-10 {\\n  padding-left: 2.5rem;\\n}\\n.pr-10 {\\n  padding-right: 2.5rem;\\n}\\n.pr-3 {\\n  padding-right: 0.75rem;\\n}\\n.text-left {\\n  text-align: left;\\n}\\n.text-center {\\n  text-align: center;\\n}\\n.font-arabic {\\n  font-family: Cairo, ui-sans-serif, system-ui, sans-serif;\\n}\\n.font-english {\\n  font-family: Inter, ui-sans-serif, system-ui, sans-serif;\\n}\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold {\\n  font-weight: 700;\\n}\\n.font-extrabold {\\n  font-weight: 800;\\n}\\n.font-medium {\\n  font-weight: 500;\\n}\\n.font-semibold {\\n  font-weight: 600;\\n}\\n.capitalize {\\n  text-transform: capitalize;\\n}\\n.leading-6 {\\n  line-height: 1.5rem;\\n}\\n.text-blue-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-green-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n.text-green-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\\n}\\n.text-green-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n}\\n.text-green-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(20 83 45 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 48 163 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(3 105 161 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(12 74 110 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n.text-red-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n}\\n.placeholder-gray-500::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.placeholder-gray-500::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.shadow {\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.ring-1 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.ring-black {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));\\n}\\n.ring-opacity-5 {\\n  --tw-ring-opacity: 0.05;\\n}\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform {\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\n\\n/* Import fonts */\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\\n@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');\\n\\n/* Modern Minimalist Base Styles */\\nhtml {\\n  scroll-behavior: smooth;\\n}\\n\\nbody {\\n  font-family: 'Inter', 'ui-sans-serif', 'system-ui', sans-serif;\\n  line-height: 1.6;\\n  background-color: #fafafa; /* neutral-50 */\\n  color: #171717; /* text-primary */\\n  font-weight: 400;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n/* Arabic font */\\n.font-arabic {\\n  font-family: 'Cairo', sans-serif;\\n}\\n\\n.font-english {\\n  font-family: 'Inter', sans-serif;\\n}\\n\\n/* RTL support */\\n[dir=\\\"rtl\\\"] {\\n  text-align: right;\\n}\\n\\n[dir=\\\"rtl\\\"] .rtl\\\\:text-left {\\n  text-align: left;\\n}\\n\\n[dir=\\\"rtl\\\"] .rtl\\\\:text-right {\\n  text-align: right;\\n}\\n\\n/* Custom scrollbar */\\n::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n/* Form styles */\\n.form-input {\\n  display: block;\\n  width: 100%;\\n  border-radius: 0.375rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.form-input::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));\\n}\\n.form-input::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));\\n}\\n.form-input {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.form-input:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\\n}\\n@media (min-width: 640px) {\\n\\n  .form-input {\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n}\\n\\n.form-input:invalid {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));\\n}\\n\\n.form-input:invalid:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));\\n}\\n\\n.form-label {\\n  margin-bottom: 0.25rem;\\n  display: block;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n\\n.form-error {\\n  margin-top: 0.25rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n\\n/* Modern Button Styles */\\n.btn {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n.btn:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(14 165 233 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.btn-primary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-primary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.btn-primary:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(56 189 248 / var(--tw-ring-opacity, 1));\\n}\\n\\n.btn-secondary {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(64 64 64 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.btn-secondary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-secondary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.btn-secondary:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(56 189 248 / var(--tw-ring-opacity, 1));\\n}\\n\\n.btn-danger {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.btn-danger:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-danger:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.btn-danger:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));\\n}\\n\\n.btn-success {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.btn-success:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-success:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.btn-success:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(74 222 128 / var(--tw-ring-opacity, 1));\\n}\\n\\n.btn-sm {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n\\n.btn-lg {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n\\n/* Modern Card Styles */\\n.card {\\n  border-radius: 1rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.card-header {\\n  border-bottom-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\n\\n.card-body {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\n\\n.card-footer {\\n  border-bottom-right-radius: 1rem;\\n  border-bottom-left-radius: 1rem;\\n  border-top-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\n\\n/* Table styles */\\n.table {\\n  min-width: 100%;\\n}\\n.table > :not([hidden]) ~ :not([hidden]) {\\n  --tw-divide-y-reverse: 0;\\n  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\\n  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));\\n  --tw-divide-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));\\n}\\n\\n.table thead {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n\\n.table th {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  text-align: left;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n\\n.table td {\\n  white-space: nowrap;\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n\\n.table tbody tr:nth-child(even) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n\\n.table tbody tr:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n\\n/* Badge styles */\\n.badge {\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(7 89 133 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-secondary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-success {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-warning {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-danger {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-info {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n/* Alert styles */\\n.alert {\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n.alert-success {\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n.alert-warning {\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n.alert-danger {\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n.alert-info {\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n/* Animation utilities */\\n.fade-in {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n.slide-up {\\n  animation: slideUp 0.3s ease-out;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideUp {\\n  from {\\n    transform: translateY(10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n  \\n  .print-break {\\n    page-break-before: always;\\n  }\\n  \\n  body {\\n    font-size: 12pt;\\n    line-height: 1.4;\\n  }\\n  \\n  .card {\\n    box-shadow: none;\\n    border: 1px solid #ddd;\\n  }\\n}\\n\\n/* Dark mode support (if needed) */\\n@media (prefers-color-scheme: dark) {\\n  .dark-mode {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\\n    --tw-text-opacity: 1;\\n    color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  }\\n  \\n  .dark-mode .card {\\n    --tw-border-opacity: 1;\\n    border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n  }\\n  \\n  .dark-mode .form-input {\\n    --tw-border-opacity: 1;\\n    border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n    --tw-text-opacity: 1;\\n    color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  }\\n}\\n.after\\\\:absolute::after {\\n  content: var(--tw-content);\\n  position: absolute;\\n}\\n.after\\\\:left-\\\\[2px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 2px;\\n}\\n.after\\\\:top-\\\\[2px\\\\]::after {\\n  content: var(--tw-content);\\n  top: 2px;\\n}\\n.after\\\\:h-5::after {\\n  content: var(--tw-content);\\n  height: 1.25rem;\\n}\\n.after\\\\:w-5::after {\\n  content: var(--tw-content);\\n  width: 1.25rem;\\n}\\n.after\\\\:rounded-full::after {\\n  content: var(--tw-content);\\n  border-radius: 9999px;\\n}\\n.after\\\\:border::after {\\n  content: var(--tw-content);\\n  border-width: 1px;\\n}\\n.after\\\\:border-gray-300::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.after\\\\:bg-white::after {\\n  content: var(--tw-content);\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.after\\\\:transition-all::after {\\n  content: var(--tw-content);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.after\\\\:content-\\\\[\\\\'\\\\'\\\\]::after {\\n  --tw-content: '';\\n  content: var(--tw-content);\\n}\\n.hover\\\\:border-gray-300:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.hover\\\\:bg-gray-100:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-gray-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-primary-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(3 105 161 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:text-blue-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-gray-900:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-green-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-red-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n.focus\\\\:z-10:focus {\\n  z-index: 10;\\n}\\n.focus\\\\:border-primary-500:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\\n}\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.focus\\\\:ring-primary-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\n.disabled\\\\:cursor-not-allowed:disabled {\\n  cursor: not-allowed;\\n}\\n.disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\n.group:hover .group-hover\\\\:text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.peer:checked ~ .peer-checked\\\\:bg-primary-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\\n}\\n.peer:checked ~ .peer-checked\\\\:after\\\\:translate-x-full::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.peer:checked ~ .peer-checked\\\\:after\\\\:border-white::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\n.peer:focus ~ .peer-focus\\\\:outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.peer:focus ~ .peer-focus\\\\:ring-4 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.peer:focus ~ .peer-focus\\\\:ring-primary-300 {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(125 211 252 / var(--tw-ring-opacity, 1));\\n}\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:p-6 {\\n    padding: 1.5rem;\\n  }\\n\\n  .sm\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n\\n  .sm\\\\:text-sm {\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n}\\n@media (min-width: 768px) {\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:ml-20 {\\n    margin-left: 5rem;\\n  }\\n\\n  .lg\\\\:ml-64 {\\n    margin-left: 16rem;\\n  }\\n\\n  .lg\\\\:mr-20 {\\n    margin-right: 5rem;\\n  }\\n\\n  .lg\\\\:mr-64 {\\n    margin-right: 16rem;\\n  }\\n\\n  .lg\\\\:block {\\n    display: block;\\n  }\\n\\n  .lg\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .lg\\\\:w-1\\\\/4 {\\n    width: 25%;\\n  }\\n\\n  .lg\\\\:w-3\\\\/4 {\\n    width: 75%;\\n  }\\n\\n  .lg\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .lg\\\\:px-8 {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n}\\n.rtl\\\\:left-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  left: 0px;\\n}\\n.rtl\\\\:left-auto:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  left: auto;\\n}\\n.rtl\\\\:right-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  right: 0px;\\n}\\n.rtl\\\\:right-3:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  right: 0.75rem;\\n}\\n.rtl\\\\:right-auto:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  right: auto;\\n}\\n.rtl\\\\:ml-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-left: 0px;\\n}\\n.rtl\\\\:ml-1:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-left: 0.25rem;\\n}\\n.rtl\\\\:ml-2:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-left: 0.5rem;\\n}\\n.rtl\\\\:ml-3:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-left: 0.75rem;\\n}\\n.rtl\\\\:mr-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 0px;\\n}\\n.rtl\\\\:mr-1:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 0.25rem;\\n}\\n.rtl\\\\:mr-2:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 0.5rem;\\n}\\n.rtl\\\\:mr-4:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 1rem;\\n}\\n.rtl\\\\:mr-5:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 1.25rem;\\n}\\n.rtl\\\\:-translate-x-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  --tw-translate-x: -0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.rtl\\\\:translate-x-full:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.rtl\\\\:space-x-reverse:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 1;\\n}\\n.rtl\\\\:pl-10:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-left: 2.5rem;\\n}\\n.rtl\\\\:pl-3:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-left: 0.75rem;\\n}\\n.rtl\\\\:pr-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-right: 0px;\\n}\\n.rtl\\\\:pr-10:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-right: 2.5rem;\\n}\\n.rtl\\\\:pr-3:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-right: 0.75rem;\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAA0B;EAA1B,wBAA0B;EAA1B,mBAA0B;EAA1B,mBAA0B;EAA1B,cAA0B;EAA1B,cAA0B;EAA1B,cAA0B;EAA1B,eAA0B;EAA1B,eAA0B;EAA1B,aAA0B;EAA1B,aAA0B;EAA1B,kBAA0B;EAA1B,sCAA0B;EAA1B,8BAA0B;EAA1B,6BAA0B;EAA1B,4BAA0B;EAA1B,eAA0B;EAA1B,oBAA0B;EAA1B,sBAA0B;EAA1B,uBAA0B;EAA1B,wBAA0B;EAA1B,kBAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;EAA1B,sCAA0B;EAA1B,kCAA0B;EAA1B,2BAA0B;EAA1B,sBAA0B;EAA1B,8BAA0B;EAA1B,YAA0B;EAA1B,kBAA0B;EAA1B,gBAA0B;EAA1B,iBAA0B;EAA1B,kBAA0B;EAA1B,cAA0B;EAA1B,gBAA0B;EAA1B,aAA0B;EAA1B,mBAA0B;EAA1B,qBAA0B;EAA1B,2BAA0B;EAA1B,yBAA0B;EAA1B,0BAA0B;EAA1B,2BAA0B;EAA1B,uBAA0B;EAA1B,wBAA0B;EAA1B,yBAA0B;EAA1B,sBAA0B;EAA1B,oBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B;AAA0B;;AAA1B;EAAA,wBAA0B;EAA1B,wBAA0B;EAA1B,mBAA0B;EAA1B,mBAA0B;EAA1B,cAA0B;EAA1B,cAA0B;EAA1B,cAA0B;EAA1B,eAA0B;EAA1B,eAA0B;EAA1B,aAA0B;EAA1B,aAA0B;EAA1B,kBAA0B;EAA1B,sCAA0B;EAA1B,8BAA0B;EAA1B,6BAA0B;EAA1B,4BAA0B;EAA1B,eAA0B;EAA1B,oBAA0B;EAA1B,sBAA0B;EAA1B,uBAA0B;EAA1B,wBAA0B;EAA1B,kBAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;EAA1B,sCAA0B;EAA1B,kCAA0B;EAA1B,2BAA0B;EAA1B,sBAA0B;EAA1B,8BAA0B;EAA1B,YAA0B;EAA1B,kBAA0B;EAA1B,gBAA0B;EAA1B,iBAA0B;EAA1B,kBAA0B;EAA1B,cAA0B;EAA1B,gBAA0B;EAA1B,aAA0B;EAA1B,mBAA0B;EAA1B,qBAA0B;EAA1B,2BAA0B;EAA1B,yBAA0B;EAA1B,0BAA0B;EAA1B,2BAA0B;EAA1B,uBAA0B;EAA1B,wBAA0B;EAA1B,yBAA0B;EAA1B,sBAA0B;EAA1B,oBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B;AAA0B,CAA1B;;CAA0B,CAA1B;;;CAA0B;;AAA1B;;;EAAA,sBAA0B,EAA1B,MAA0B;EAA1B,eAA0B,EAA1B,MAA0B;EAA1B,mBAA0B,EAA1B,MAA0B;EAA1B,qBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;EAAA,gBAA0B;AAAA;;AAA1B;;;;;;;;CAA0B;;AAA1B;;EAAA,gBAA0B,EAA1B,MAA0B;EAA1B,8BAA0B,EAA1B,MAA0B;EAA1B,gBAA0B,EAA1B,MAA0B;EAA1B,cAA0B;KAA1B,WAA0B,EAA1B,MAA0B;EAA1B,wDAA0B,EAA1B,MAA0B;EAA1B,6BAA0B,EAA1B,MAA0B;EAA1B,+BAA0B,EAA1B,MAA0B;EAA1B,wCAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;EAAA,SAA0B,EAA1B,MAA0B;EAA1B,oBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;;;CAA0B;;AAA1B;EAAA,SAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;EAA1B,qBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,yCAA0B;UAA1B,iCAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;;;;;EAAA,kBAA0B;EAA1B,oBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,cAA0B;EAA1B,wBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,mBAA0B;AAAA;;AAA1B;;;;;CAA0B;;AAA1B;;;;EAAA,+GAA0B,EAA1B,MAA0B;EAA1B,6BAA0B,EAA1B,MAA0B;EAA1B,+BAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,cAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,cAA0B;EAA1B,cAA0B;EAA1B,kBAA0B;EAA1B,wBAA0B;AAAA;;AAA1B;EAAA,eAA0B;AAAA;;AAA1B;EAAA,WAA0B;AAAA;;AAA1B;;;;CAA0B;;AAA1B;EAAA,cAA0B,EAA1B,MAA0B;EAA1B,qBAA0B,EAA1B,MAA0B;EAA1B,yBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;;;CAA0B;;AAA1B;;;;;EAAA,oBAA0B,EAA1B,MAA0B;EAA1B,8BAA0B,EAA1B,MAA0B;EAA1B,gCAA0B,EAA1B,MAA0B;EAA1B,eAA0B,EAA1B,MAA0B;EAA1B,oBAA0B,EAA1B,MAA0B;EAA1B,oBAA0B,EAA1B,MAA0B;EAA1B,uBAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;EAA1B,SAA0B,EAA1B,MAA0B;EAA1B,UAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,oBAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;;;;EAAA,0BAA0B,EAA1B,MAA0B;EAA1B,6BAA0B,EAA1B,MAA0B;EAA1B,sBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,aAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,gBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,wBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,YAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;EAAA,6BAA0B,EAA1B,MAA0B;EAA1B,oBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,wBAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;EAAA,0BAA0B,EAA1B,MAA0B;EAA1B,aAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,kBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;;;;;;;;;;;;EAAA,SAA0B;AAAA;;AAA1B;EAAA,SAA0B;EAA1B,UAA0B;AAAA;;AAA1B;EAAA,UAA0B;AAAA;;AAA1B;;;EAAA,gBAA0B;EAA1B,SAA0B;EAA1B,UAA0B;AAAA;;AAA1B;;CAA0B;AAA1B;EAAA,UAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,gBAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;EAAA,UAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;EAAA,UAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,eAA0B;AAAA;;AAA1B;;CAA0B;AAA1B;EAAA,eAA0B;AAAA;;AAA1B;;;;CAA0B;;AAA1B;;;;;;;;EAAA,cAA0B,EAA1B,MAA0B;EAA1B,sBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,eAA0B;EAA1B,YAA0B;AAAA;;AAA1B,wEAA0B;AAA1B;EAAA,aAA0B;AAAA;;AAA1B;EAAA,wBAA0B;KAA1B,qBAA0B;UAA1B,gBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B,iBAA0B;EAA1B,kBAA0B;EAA1B,mBAA0B;EAA1B,sBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B,eAA0B;EAA1B,mBAA0B;EAA1B,sBAA0B;AAAA;;AAA1B;EAAA,8BAA0B;EAA1B,mBAA0B;EAA1B,4CAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;EAA1B,wBAA0B;EAA1B,2GAA0B;EAA1B,yGAA0B;EAA1B,iFAA0B;EAA1B;AAA0B;;AAA1B;EAAA,cAA0B;EAA1B;AAA0B;;AAA1B;EAAA,cAA0B;EAA1B;AAA0B;;AAA1B;EAAA;AAA0B;;AAA1B;EAAA,iBAA0B;EAA1B;AAA0B;;AAA1B;EAAA;AAA0B;;AAA1B;EAAA,cAA0B;EAA1B;AAA0B;;AAA1B;EAAA,mPAA0B;EAA1B,wCAA0B;EAA1B,4BAA0B;EAA1B,4BAA0B;EAA1B,qBAA0B;EAA1B,iCAA0B;UAA1B;AAA0B;;AAA1B;EAAA,yBAA0B;EAA1B,4BAA0B;EAA1B,wBAA0B;EAA1B,wBAA0B;EAA1B,sBAA0B;EAA1B,iCAA0B;UAA1B;AAA0B;;AAA1B;EAAA,wBAA0B;KAA1B,qBAA0B;UAA1B,gBAA0B;EAA1B,UAA0B;EAA1B,iCAA0B;UAA1B,yBAA0B;EAA1B,qBAA0B;EAA1B,sBAA0B;EAA1B,6BAA0B;EAA1B,yBAA0B;KAA1B,sBAA0B;UAA1B,iBAA0B;EAA1B,cAA0B;EAA1B,YAA0B;EAA1B,WAA0B;EAA1B,cAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B,iBAA0B;EAA1B;AAA0B;;AAA1B;EAAA;AAA0B;;AAA1B;EAAA;AAA0B;;AAA1B;EAAA,8BAA0B;EAA1B,mBAA0B;EAA1B,4CAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;EAA1B,wBAA0B;EAA1B,2GAA0B;EAA1B,yGAA0B;EAA1B;AAA0B;;AAA1B;EAAA,yBAA0B;EAA1B,8BAA0B;EAA1B,0BAA0B;EAA1B,2BAA0B;EAA1B;AAA0B;;AAA1B;EAAA,sQAA0B;AAAA;;AAA1B;;EAAA;IAAA,wBAA0B;OAA1B,qBAA0B;YAA1B;EAA0B;AAAA;;AAA1B;EAAA,oKAA0B;AAAA;;AAA1B;;EAAA;IAAA,wBAA0B;OAA1B,qBAA0B;YAA1B;EAA0B;AAAA;;AAA1B;EAAA,yBAA0B;EAA1B;AAA0B;;AAA1B;EAAA,uOAA0B;EAA1B,yBAA0B;EAA1B,8BAA0B;EAA1B,0BAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;AAAA;;AAA1B;;EAAA;IAAA,wBAA0B;OAA1B,qBAA0B;YAA1B;EAA0B;AAAA;;AAA1B;EAAA,yBAA0B;EAA1B;AAA0B;;AAA1B;EAAA,iBAA0B;EAA1B,qBAA0B;EAA1B,eAA0B;EAA1B,gBAA0B;EAA1B,UAA0B;EAA1B,gBAA0B;EAA1B;AAA0B;;AAA1B;EAAA,6BAA0B;EAA1B;AAA0B;AAC1B;EAAA,wBAAgC;KAAhC,qBAAgC;UAAhC,gBAAgC;EAAhC,sBAAgC;EAAhC,qBAAgC;EAAhC,iBAAgC;EAAhC,kBAAgC;EAAhC,mBAAgC;EAAhC,sBAAgC;EAAhC,sBAAgC;EAAhC,qBAAgC;EAAhC,eAAgC;EAAhC,mBAAgC;EAAhC,sBAAgC;AAAA;AAAhC;EAAA,8BAAgC;EAAhC,mBAAgC;EAAhC,4CAAgC;EAAhC,2BAAgC;EAAhC,4BAAgC;EAAhC,wBAAgC;EAAhC,2GAAgC;EAAhC,yGAAgC;EAAhC,iFAAgC;EAAhC;AAAgC;AAAhC;EAAA,cAAgC;EAAhC;AAAgC;AAAhC;EAAA,cAAgC;EAAhC;AAAgC;AAAhC;EAAA;AAAgC;AAAhC;EAAA,iBAAgC;EAAhC;AAAgC;AAAhC;EAAA;AAAgC;AAAhC;EAAA,cAAgC;EAAhC;AAAgC;AAChC;EAAA,kBAA+B;EAA/B,UAA+B;EAA/B,WAA+B;EAA/B,UAA+B;EAA/B,YAA+B;EAA/B,gBAA+B;EAA/B,sBAA+B;EAA/B,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,QAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,iBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,qBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;;EAAA;IAAA;EAA+B;AAAA;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,wBAA+B;KAA/B,qBAA+B;UAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,sDAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,uDAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,oDAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,+DAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,+DAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,4DAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,8DAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,4DAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,gBAA+B;EAA/B,uBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,qBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,qBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,iBAA+B;EAA/B;AAA+B;AAA/B;EAAA,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,iBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,iBAA+B;EAA/B;AAA+B;AAA/B;EAAA,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,2BAA+B;EAA/B;AAA+B;AAA/B;EAAA,2BAA+B;EAA/B;AAA+B;AAA/B;EAAA,0EAA+B;EAA/B,8FAA+B;EAA/B;AAA+B;AAA/B;EAAA,+EAA+B;EAA/B,mGAA+B;EAA/B;AAA+B;AAA/B;EAAA,0CAA+B;EAA/B,uDAA+B;EAA/B;AAA+B;AAA/B;EAAA,2GAA+B;EAA/B,yGAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,wBAA+B;EAA/B,wDAA+B;EAA/B;AAA+B;AAA/B;EAAA,+FAA+B;EAA/B,wDAA+B;EAA/B;AAA+B;AAA/B;EAAA,8BAA+B;EAA/B,wDAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;;AAE/B,iBAAiB;AACjB,mGAAmG;AACnG,mGAAmG;;AAEnG,kCAAkC;AAClC;EACE,uBAAuB;AACzB;;AAEA;EACE,8DAA8D;EAC9D,gBAAgB;EAChB,yBAAyB,EAAE,eAAe;EAC1C,cAAc,EAAE,iBAAiB;EACjC,gBAAgB;EAChB,mCAAmC;EACnC,kCAAkC;AACpC;;AAEA,gBAAgB;AAChB;EACE,gCAAgC;AAClC;;AAEA;EACE,gCAAgC;AAClC;;AAEA,gBAAgB;AAChB;EACE,iBAAiB;AACnB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,iBAAiB;AACnB;;AAEA,qBAAqB;AACrB;EACE,UAAU;EACV,WAAW;AACb;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;AACrB;;AAEA,gBAAgB;AAEd;EAAA,cAA4K;EAA5K,WAA4K;EAA5K,uBAA4K;EAA5K,iBAA4K;EAA5K,sBAA4K;EAA5K,4DAA4K;EAA5K,qBAA4K;EAA5K,sBAA4K;EAA5K,mBAA4K;EAA5K;AAA4K;AAA5K;EAAA,2BAA4K;EAA5K;AAA4K;AAA5K;EAAA,2BAA4K;EAA5K;AAA4K;AAA5K;EAAA,0CAA4K;EAA5K,uDAA4K;EAA5K;AAA4K;AAA5K;EAAA,sBAA4K;EAA5K,2DAA4K;EAA5K,8BAA4K;EAA5K,mBAA4K;EAA5K,oBAA4K;EAA5K;AAA4K;AAA5K;;EAAA;IAAA,mBAA4K;IAA5K;EAA4K;AAAA;;AAI5K;EAAA,sBAA6D;EAA7D;AAA6D;;AAA7D;EAAA,sBAA6D;EAA7D,0DAA6D;EAA7D,oBAA6D;EAA7D;AAA6D;;AAI7D;EAAA,sBAAmD;EAAnD,cAAmD;EAAnD,mBAAmD;EAAnD,oBAAmD;EAAnD,gBAAmD;EAAnD,oBAAmD;EAAnD;AAAmD;;AAInD;EAAA,mBAAgC;EAAhC,mBAAgC;EAAhC,oBAAgC;EAAhC,oBAAgC;EAAhC;AAAgC;;AAGlC,yBAAyB;AAEvB;EAAA,oBAAgO;EAAhO,mBAAgO;EAAhO,uBAAgO;EAAhO,sBAAgO;EAAhO,iBAAgO;EAAhO,yBAAgO;EAAhO,kBAAgO;EAAhO,mBAAgO;EAAhO,qBAAgO;EAAhO,wBAAgO;EAAhO,mBAAgO;EAAhO,oBAAgO;EAAhO,gBAAgO;EAAhO,wBAAgO;EAAhO,wDAAgO;EAAhO;AAAgO;AAAhO;EAAA,8BAAgO;EAAhO,mBAAgO;EAAhO,2GAAgO;EAAhO,yGAAgO;EAAhO,4FAAgO;EAAhO,2BAAgO;EAAhO;AAAgO;;AAIhO;EAAA,kBAAgH;EAAhH,2DAAgH;EAAhH,oBAAgH;EAAhH,mDAAgH;EAAhH,4CAAgH;EAAhH,uDAAgH;EAAhH,uGAAgH;EAAhH,oBAAgH;EAAhH,mBAAgH;EAAhH,uBAAgH;EAAhH,sBAAgH;EAAhH,iBAAgH;EAAhH,yBAAgH;EAAhH,kBAAgH;EAAhH,mBAAgH;EAAhH,qBAAgH;EAAhH,wBAAgH;EAAhH,mBAAgH;EAAhH,oBAAgH;EAAhH,gBAAgH;EAAhH,wBAAgH;EAAhH,wDAAgH;EAAhH;AAAgH;;AAAhH;EAAA,8BAAgH;EAAhH,mBAAgH;EAAhH,2GAAgH;EAAhH,yGAAgH;EAAhH,4FAAgH;EAAhH,2BAAgH;EAAhH;AAAgH;;AAAhH;EAAA,kBAAgH;EAAhH,0DAAgH;EAAhH,6CAAgH;EAAhH,wDAAgH;EAAhH;AAAgH;;AAAhH;EAAA,oBAAgH;EAAhH;AAAgH;;AAIhH;EAAA,sBAAkI;EAAlI,4DAAkI;EAAlI,kBAAkI;EAAlI,4DAAkI;EAAlI,oBAAkI;EAAlI,gDAAkI;EAAlI,4CAAkI;EAAlI,uDAAkI;EAAlI,uGAAkI;EAAlI,oBAAkI;EAAlI,mBAAkI;EAAlI,uBAAkI;EAAlI,sBAAkI;EAAlI,iBAAkI;EAAlI,yBAAkI;EAAlI,kBAAkI;EAAlI,mBAAkI;EAAlI,qBAAkI;EAAlI,wBAAkI;EAAlI,mBAAkI;EAAlI,oBAAkI;EAAlI,gBAAkI;EAAlI,wBAAkI;EAAlI,wDAAkI;EAAlI;AAAkI;;AAAlI;EAAA,8BAAkI;EAAlI,mBAAkI;EAAlI,2GAAkI;EAAlI,yGAAkI;EAAlI,4FAAkI;EAAlI,2BAAkI;EAAlI;AAAkI;;AAAlI;EAAA,kBAAkI;EAAlI,4DAAkI;EAAlI,6CAAkI;EAAlI,wDAAkI;EAAlI;AAAkI;;AAAlI;EAAA,oBAAkI;EAAlI;AAAkI;;AAIlI;EAAA,kBAAyG;EAAzG,0DAAyG;EAAzG,oBAAyG;EAAzG,mDAAyG;EAAzG,4CAAyG;EAAzG,uDAAyG;EAAzG,uGAAyG;EAAzG,oBAAyG;EAAzG,mBAAyG;EAAzG,uBAAyG;EAAzG,sBAAyG;EAAzG,iBAAyG;EAAzG,yBAAyG;EAAzG,kBAAyG;EAAzG,mBAAyG;EAAzG,qBAAyG;EAAzG,wBAAyG;EAAzG,mBAAyG;EAAzG,oBAAyG;EAAzG,gBAAyG;EAAzG,wBAAyG;EAAzG,wDAAyG;EAAzG;AAAyG;;AAAzG;EAAA,8BAAyG;EAAzG,mBAAyG;EAAzG,2GAAyG;EAAzG,yGAAyG;EAAzG,4FAAyG;EAAzG,2BAAyG;EAAzG;AAAyG;;AAAzG;EAAA,kBAAyG;EAAzG,0DAAyG;EAAzG,6CAAyG;EAAzG,wDAAyG;EAAzG;AAAyG;;AAAzG;EAAA,oBAAyG;EAAzG;AAAyG;;AAIzG;EAAA,kBAA6G;EAA7G,0DAA6G;EAA7G,oBAA6G;EAA7G,mDAA6G;EAA7G,4CAA6G;EAA7G,uDAA6G;EAA7G,uGAA6G;EAA7G,oBAA6G;EAA7G,mBAA6G;EAA7G,uBAA6G;EAA7G,sBAA6G;EAA7G,iBAA6G;EAA7G,yBAA6G;EAA7G,kBAA6G;EAA7G,mBAA6G;EAA7G,qBAA6G;EAA7G,wBAA6G;EAA7G,mBAA6G;EAA7G,oBAA6G;EAA7G,gBAA6G;EAA7G,wBAA6G;EAA7G,wDAA6G;EAA7G;AAA6G;;AAA7G;EAAA,8BAA6G;EAA7G,mBAA6G;EAA7G,2GAA6G;EAA7G,yGAA6G;EAA7G,4FAA6G;EAA7G,2BAA6G;EAA7G;AAA6G;;AAA7G;EAAA,kBAA6G;EAA7G,0DAA6G;EAA7G,6CAA6G;EAA7G,wDAA6G;EAA7G;AAA6G;;AAA7G;EAAA,oBAA6G;EAA7G;AAA6G;;AAI7G;EAAA,qBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B,wBAA0B;EAA1B,kBAA0B;EAA1B;AAA0B;;AAI1B;EAAA,oBAA0B;EAA1B,qBAA0B;EAA1B,oBAA0B;EAA1B,uBAA0B;EAA1B,eAA0B;EAA1B;AAA0B;;AAG5B,uBAAuB;AAErB;EAAA,mBAA2E;EAA3E,iBAA2E;EAA3E,sBAA2E;EAA3E,4DAA2E;EAA3E,kBAA2E;EAA3E,4DAA2E;EAA3E,4EAA2E;EAA3E,2FAA2E;EAA3E;AAA2E;;AAI3E;EAAA,wBAA4C;EAA5C,sBAA4C;EAA5C,4DAA4C;EAA5C,oBAA4C;EAA5C,qBAA4C;EAA5C,oBAA4C;EAA5C;AAA4C;;AAI5C;EAAA,oBAAgB;EAAhB,qBAAgB;EAAhB,oBAAgB;EAAhB;AAAgB;;AAIhB;EAAA,gCAA+E;EAA/E,+BAA+E;EAA/E,qBAA+E;EAA/E,sBAA+E;EAA/E,4DAA+E;EAA/E,kBAA+E;EAA/E,4DAA+E;EAA/E,oBAA+E;EAA/E,qBAA+E;EAA/E,oBAA+E;EAA/E;AAA+E;;AAGjF,iBAAiB;AAEf;EAAA;AAA0C;AAA1C;EAAA,wBAA0C;EAA1C,kEAA0C;EAA1C,2DAA0C;EAA1C,sBAA0C;EAA1C;AAA0C;;AAI1C;EAAA,kBAAiB;EAAjB;AAAiB;;AAIjB;EAAA,oBAAqF;EAArF,qBAAqF;EAArF,oBAAqF;EAArF,uBAAqF;EAArF,gBAAqF;EAArF,kBAAqF;EAArF,iBAAqF;EAArF,gBAAqF;EAArF,yBAAqF;EAArF,sBAAqF;EAArF,oBAAqF;EAArF;AAAqF;;AAIrF;EAAA,mBAAwD;EAAxD,oBAAwD;EAAxD,qBAAwD;EAAxD,iBAAwD;EAAxD,oBAAwD;EAAxD,mBAAwD;EAAxD,oBAAwD;EAAxD,oBAAwD;EAAxD;AAAwD;;AAIxD;EAAA,kBAAiB;EAAjB;AAAiB;;AAIjB;EAAA,kBAAkB;EAAlB;AAAkB;;AAGpB,iBAAiB;AAEf;EAAA,oBAA8E;EAA9E,mBAA8E;EAA9E,qBAA8E;EAA9E,sBAA8E;EAA9E,uBAA8E;EAA9E,qBAA8E;EAA9E,wBAA8E;EAA9E,kBAA8E;EAA9E,iBAA8E;EAA9E;AAA8E;;AAI9E;EAAA,kBAA4C;EAA5C,4DAA4C;EAA5C,oBAA4C;EAA5C,gDAA4C;EAA5C,oBAA4C;EAA5C,mBAA4C;EAA5C,qBAA4C;EAA5C,sBAA4C;EAA5C,uBAA4C;EAA5C,qBAA4C;EAA5C,wBAA4C;EAA5C,kBAA4C;EAA5C,iBAA4C;EAA5C;AAA4C;;AAI5C;EAAA,kBAAsC;EAAtC,4DAAsC;EAAtC,oBAAsC;EAAtC,gDAAsC;EAAtC,oBAAsC;EAAtC,mBAAsC;EAAtC,qBAAsC;EAAtC,sBAAsC;EAAtC,uBAAsC;EAAtC,qBAAsC;EAAtC,wBAAsC;EAAtC,kBAAsC;EAAtC,iBAAsC;EAAtC;AAAsC;;AAItC;EAAA,kBAAwC;EAAxC,4DAAwC;EAAxC,oBAAwC;EAAxC,iDAAwC;EAAxC,oBAAwC;EAAxC,mBAAwC;EAAxC,qBAAwC;EAAxC,sBAAwC;EAAxC,uBAAwC;EAAxC,qBAAwC;EAAxC,wBAAwC;EAAxC,kBAAwC;EAAxC,iBAAwC;EAAxC;AAAwC;;AAIxC;EAAA,kBAA0C;EAA1C,4DAA0C;EAA1C,oBAA0C;EAA1C,iDAA0C;EAA1C,oBAA0C;EAA1C,mBAA0C;EAA1C,qBAA0C;EAA1C,sBAA0C;EAA1C,uBAA0C;EAA1C,qBAA0C;EAA1C,wBAA0C;EAA1C,kBAA0C;EAA1C,iBAA0C;EAA1C;AAA0C;;AAI1C;EAAA,kBAAoC;EAApC,4DAAoC;EAApC,oBAAoC;EAApC,iDAAoC;EAApC,oBAAoC;EAApC,mBAAoC;EAApC,qBAAoC;EAApC,sBAAoC;EAApC,uBAAoC;EAApC,qBAAoC;EAApC,wBAAoC;EAApC,kBAAoC;EAApC,iBAAoC;EAApC;AAAoC;;AAIpC;EAAA,kBAAsC;EAAtC,4DAAsC;EAAtC,oBAAsC;EAAtC,iDAAsC;EAAtC,oBAAsC;EAAtC,mBAAsC;EAAtC,qBAAsC;EAAtC,sBAAsC;EAAtC,uBAAsC;EAAtC,qBAAsC;EAAtC,wBAAsC;EAAtC,kBAAsC;EAAtC,iBAAsC;EAAtC;AAAsC;;AAGxC,iBAAiB;AAEf;EAAA,uBAAqB;EAArB;AAAqB;;AAIrB;EAAA,iBAA+D;EAA/D,sBAA+D;EAA/D,4DAA+D;EAA/D,kBAA+D;EAA/D,4DAA+D;EAA/D,oBAA+D;EAA/D,iDAA+D;EAA/D,uBAA+D;EAA/D;AAA+D;;AAI/D;EAAA,iBAAkE;EAAlE,sBAAkE;EAAlE,4DAAkE;EAAlE,kBAAkE;EAAlE,4DAAkE;EAAlE,oBAAkE;EAAlE,iDAAkE;EAAlE,uBAAkE;EAAlE;AAAkE;;AAIlE;EAAA,iBAAyD;EAAzD,sBAAyD;EAAzD,4DAAyD;EAAzD,kBAAyD;EAAzD,4DAAyD;EAAzD,oBAAyD;EAAzD,iDAAyD;EAAzD,uBAAyD;EAAzD;AAAyD;;AAIzD;EAAA,iBAA4D;EAA5D,sBAA4D;EAA5D,4DAA4D;EAA5D,kBAA4D;EAA5D,4DAA4D;EAA5D,oBAA4D;EAA5D,iDAA4D;EAA5D,uBAA4D;EAA5D;AAA4D;;AAG9D,wBAAwB;AACxB;EACE,kCAAkC;AACpC;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE;IACE,2BAA2B;IAC3B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA,iBAAiB;AACjB;EACE;IACE,wBAAwB;EAC1B;;EAEA;IACE,yBAAyB;EAC3B;;EAEA;IACE,eAAe;IACf,gBAAgB;EAClB;;EAEA;IACE,gBAAgB;IAChB,sBAAsB;EACxB;AACF;;AAEA,kCAAkC;AAClC;EAEI;IAAA,kBAA6B;IAA7B,yDAA6B;IAA7B,oBAA6B;IAA7B;EAA6B;;EAI7B;IAAA,sBAAkC;IAAlC,yDAAkC;IAAlC,kBAAkC;IAAlC;EAAkC;;EAIlC;IAAA,sBAA6C;IAA7C,yDAA6C;IAA7C,kBAA6C;IAA7C,yDAA6C;IAA7C,oBAA6C;IAA7C;EAA6C;AAEjD;AA1QA;EAAA,0BA2QA;EA3QA;AA2QA;AA3QA;EAAA,0BA2QA;EA3QA;AA2QA;AA3QA;EAAA,0BA2QA;EA3QA;AA2QA;AA3QA;EAAA,0BA2QA;EA3QA;AA2QA;AA3QA;EAAA,0BA2QA;EA3QA;AA2QA;AA3QA;EAAA,0BA2QA;EA3QA;AA2QA;AA3QA;EAAA,0BA2QA;EA3QA;AA2QA;AA3QA;EAAA,0BA2QA;EA3QA,sBA2QA;EA3QA;AA2QA;AA3QA;EAAA,0BA2QA;EA3QA,kBA2QA;EA3QA;AA2QA;AA3QA;EAAA,0BA2QA;EA3QA,wBA2QA;EA3QA,wDA2QA;EA3QA;AA2QA;AA3QA;EAAA,gBA2QA;EA3QA;AA2QA;AA3QA;EAAA,sBA2QA;EA3QA;AA2QA;AA3QA;EAAA,kBA2QA;EA3QA;AA2QA;AA3QA;EAAA,kBA2QA;EA3QA;AA2QA;AA3QA;EAAA,kBA2QA;EA3QA;AA2QA;AA3QA;EAAA,oBA2QA;EA3QA;AA2QA;AA3QA;EAAA,oBA2QA;EA3QA;AA2QA;AA3QA;EAAA,oBA2QA;EA3QA;AA2QA;AA3QA;EAAA,oBA2QA;EA3QA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA,sBA2QA;EA3QA;AA2QA;AA3QA;EAAA,8BA2QA;EA3QA;AA2QA;AA3QA;EAAA,2GA2QA;EA3QA,yGA2QA;EA3QA;AA2QA;AA3QA;EAAA,oBA2QA;EA3QA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA,oBA2QA;EA3QA;AA2QA;AA3QA;EAAA,kBA2QA;EA3QA;AA2QA;AA3QA;EAAA,0BA2QA;EA3QA,sBA2QA;EA3QA;AA2QA;AA3QA;EAAA,0BA2QA;EA3QA,sBA2QA;EA3QA;AA2QA;AA3QA;EAAA,8BA2QA;EA3QA;AA2QA;AA3QA;EAAA,2GA2QA;EA3QA,yGA2QA;EA3QA;AA2QA;AA3QA;EAAA,oBA2QA;EA3QA;AA2QA;AA3QA;;EAAA;IAAA;EA2QA;;EA3QA;IAAA;EA2QA;;EA3QA;IAAA;EA2QA;;EA3QA;IAAA,oBA2QA;IA3QA;EA2QA;;EA3QA;IAAA,mBA2QA;IA3QA;EA2QA;AAAA;AA3QA;;EAAA;IAAA;EA2QA;AAAA;AA3QA;;EAAA;IAAA;EA2QA;;EA3QA;IAAA;EA2QA;;EA3QA;IAAA;EA2QA;;EA3QA;IAAA;EA2QA;;EA3QA;IAAA;EA2QA;;EA3QA;IAAA;EA2QA;;EA3QA;IAAA;EA2QA;;EA3QA;IAAA;EA2QA;;EA3QA;IAAA;EA2QA;;EA3QA;IAAA;EA2QA;;EA3QA;IAAA;EA2QA;;EA3QA;IAAA;EA2QA;;EA3QA;IAAA,kBA2QA;IA3QA;EA2QA;AAAA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA,sBA2QA;EA3QA;AA2QA;AA3QA;EAAA,sBA2QA;EA3QA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA;AA3QA;EAAA;AA2QA\",\"sourcesContent\":[\"@import 'tailwindcss/base';\\n@import 'tailwindcss/components';\\n@import 'tailwindcss/utilities';\\n\\n/* Import fonts */\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\\n@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');\\n\\n/* Modern Minimalist Base Styles */\\nhtml {\\n  scroll-behavior: smooth;\\n}\\n\\nbody {\\n  font-family: 'Inter', 'ui-sans-serif', 'system-ui', sans-serif;\\n  line-height: 1.6;\\n  background-color: #fafafa; /* neutral-50 */\\n  color: #171717; /* text-primary */\\n  font-weight: 400;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n/* Arabic font */\\n.font-arabic {\\n  font-family: 'Cairo', sans-serif;\\n}\\n\\n.font-english {\\n  font-family: 'Inter', sans-serif;\\n}\\n\\n/* RTL support */\\n[dir=\\\"rtl\\\"] {\\n  text-align: right;\\n}\\n\\n[dir=\\\"rtl\\\"] .rtl\\\\:text-left {\\n  text-align: left;\\n}\\n\\n[dir=\\\"rtl\\\"] .rtl\\\\:text-right {\\n  text-align: right;\\n}\\n\\n/* Custom scrollbar */\\n::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n/* Form styles */\\n.form-input {\\n  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;\\n}\\n\\n.form-input:invalid {\\n  @apply border-red-300 focus:ring-red-500 focus:border-red-500;\\n}\\n\\n.form-label {\\n  @apply block text-sm font-medium text-gray-700 mb-1;\\n}\\n\\n.form-error {\\n  @apply mt-1 text-sm text-red-600;\\n}\\n\\n/* Modern Button Styles */\\n.btn {\\n  @apply inline-flex items-center justify-center px-4 py-2.5 border border-transparent text-sm font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-50;\\n}\\n\\n.btn-primary {\\n  @apply btn text-white bg-primary-500 hover:bg-primary-600 focus:ring-primary-400 shadow-soft hover:shadow-medium;\\n}\\n\\n.btn-secondary {\\n  @apply btn text-neutral-700 bg-white border-neutral-200 hover:bg-neutral-50 focus:ring-primary-400 shadow-soft hover:shadow-medium;\\n}\\n\\n.btn-danger {\\n  @apply btn text-white bg-status-error hover:bg-red-600 focus:ring-red-400 shadow-soft hover:shadow-medium;\\n}\\n\\n.btn-success {\\n  @apply btn text-white bg-accent-500 hover:bg-accent-600 focus:ring-accent-400 shadow-soft hover:shadow-medium;\\n}\\n\\n.btn-sm {\\n  @apply px-3 py-1.5 text-xs;\\n}\\n\\n.btn-lg {\\n  @apply px-6 py-3 text-base;\\n}\\n\\n/* Modern Card Styles */\\n.card {\\n  @apply bg-surface-primary shadow-card rounded-2xl border border-neutral-100;\\n}\\n\\n.card-header {\\n  @apply px-6 py-5 border-b border-neutral-100;\\n}\\n\\n.card-body {\\n  @apply px-6 py-5;\\n}\\n\\n.card-footer {\\n  @apply px-6 py-5 border-t border-neutral-100 bg-surface-secondary rounded-b-2xl;\\n}\\n\\n/* Table styles */\\n.table {\\n  @apply min-w-full divide-y divide-gray-200;\\n}\\n\\n.table thead {\\n  @apply bg-gray-50;\\n}\\n\\n.table th {\\n  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;\\n}\\n\\n.table td {\\n  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;\\n}\\n\\n.table tbody tr:nth-child(even) {\\n  @apply bg-gray-50;\\n}\\n\\n.table tbody tr:hover {\\n  @apply bg-gray-100;\\n}\\n\\n/* Badge styles */\\n.badge {\\n  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;\\n}\\n\\n.badge-primary {\\n  @apply badge bg-primary-100 text-primary-800;\\n}\\n\\n.badge-secondary {\\n  @apply badge bg-gray-100 text-gray-800;\\n}\\n\\n.badge-success {\\n  @apply badge bg-green-100 text-green-800;\\n}\\n\\n.badge-warning {\\n  @apply badge bg-yellow-100 text-yellow-800;\\n}\\n\\n.badge-danger {\\n  @apply badge bg-red-100 text-red-800;\\n}\\n\\n.badge-info {\\n  @apply badge bg-blue-100 text-blue-800;\\n}\\n\\n/* Alert styles */\\n.alert {\\n  @apply p-4 rounded-md;\\n}\\n\\n.alert-success {\\n  @apply alert bg-green-50 border border-green-200 text-green-800;\\n}\\n\\n.alert-warning {\\n  @apply alert bg-yellow-50 border border-yellow-200 text-yellow-800;\\n}\\n\\n.alert-danger {\\n  @apply alert bg-red-50 border border-red-200 text-red-800;\\n}\\n\\n.alert-info {\\n  @apply alert bg-blue-50 border border-blue-200 text-blue-800;\\n}\\n\\n/* Animation utilities */\\n.fade-in {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n.slide-up {\\n  animation: slideUp 0.3s ease-out;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideUp {\\n  from {\\n    transform: translateY(10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n  \\n  .print-break {\\n    page-break-before: always;\\n  }\\n  \\n  body {\\n    font-size: 12pt;\\n    line-height: 1.4;\\n  }\\n  \\n  .card {\\n    box-shadow: none;\\n    border: 1px solid #ddd;\\n  }\\n}\\n\\n/* Dark mode support (if needed) */\\n@media (prefers-color-scheme: dark) {\\n  .dark-mode {\\n    @apply bg-gray-900 text-white;\\n  }\\n  \\n  .dark-mode .card {\\n    @apply bg-gray-800 border-gray-700;\\n  }\\n  \\n  .dark-mode .form-input {\\n    @apply bg-gray-700 border-gray-600 text-white;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});