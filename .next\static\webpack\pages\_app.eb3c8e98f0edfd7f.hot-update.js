"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: Inter, ui-sans-serif, system-ui, sans-serif; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n\\n[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  border-radius: 0px;\\n  padding-top: 0.5rem;\\n  padding-right: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  padding-left: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  border-color: #2563eb;\\n}\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\ninput::placeholder,textarea::placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\n::-webkit-datetime-edit-fields-wrapper {\\n  padding: 0;\\n}\\n\\n::-webkit-date-and-time-value {\\n  min-height: 1.5em;\\n  text-align: inherit;\\n}\\n\\n::-webkit-datetime-edit {\\n  display: inline-flex;\\n}\\n\\n::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n}\\n\\nselect {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\\\");\\n  background-position: right 0.5rem center;\\n  background-repeat: no-repeat;\\n  background-size: 1.5em 1.5em;\\n  padding-right: 2.5rem;\\n  -webkit-print-color-adjust: exact;\\n          print-color-adjust: exact;\\n}\\n\\n[multiple],[size]:where(select:not([size=\\\"1\\\"])) {\\n  background-image: initial;\\n  background-position: initial;\\n  background-repeat: unset;\\n  background-size: initial;\\n  padding-right: 0.75rem;\\n  -webkit-print-color-adjust: unset;\\n          print-color-adjust: unset;\\n}\\n\\n[type='checkbox'],[type='radio'] {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  padding: 0;\\n  -webkit-print-color-adjust: exact;\\n          print-color-adjust: exact;\\n  display: inline-block;\\n  vertical-align: middle;\\n  background-origin: border-box;\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n  flex-shrink: 0;\\n  height: 1rem;\\n  width: 1rem;\\n  color: #2563eb;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='checkbox'] {\\n  border-radius: 0px;\\n}\\n\\n[type='radio'] {\\n  border-radius: 100%;\\n}\\n\\n[type='checkbox']:focus,[type='radio']:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n}\\n\\n[type='checkbox']:checked,[type='radio']:checked {\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n[type='checkbox']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:checked {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='radio']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='radio']:checked {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='checkbox']:indeterminate {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e\\\");\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:indeterminate {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='file'] {\\n  background: unset;\\n  border-color: inherit;\\n  border-width: 0;\\n  border-radius: 0;\\n  padding: 0;\\n  font-size: unset;\\n  line-height: inherit;\\n}\\n\\n[type='file']:focus {\\n  outline: 1px solid ButtonText;\\n  outline: 1px auto -webkit-focus-ring-color;\\n}\\n.form-input,.form-textarea,.form-select,.form-multiselect {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  border-radius: 0px;\\n  padding-top: 0.5rem;\\n  padding-right: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  padding-left: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n  --tw-shadow: 0 0 #0000;\\n}\\n.form-input:focus, .form-textarea:focus, .form-select:focus, .form-multiselect:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  border-color: #2563eb;\\n}\\n.form-input::-moz-placeholder, .form-textarea::-moz-placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n.form-input::placeholder,.form-textarea::placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n.form-input::-webkit-datetime-edit-fields-wrapper {\\n  padding: 0;\\n}\\n.form-input::-webkit-date-and-time-value {\\n  min-height: 1.5em;\\n  text-align: inherit;\\n}\\n.form-input::-webkit-datetime-edit {\\n  display: inline-flex;\\n}\\n.form-input::-webkit-datetime-edit,.form-input::-webkit-datetime-edit-year-field,.form-input::-webkit-datetime-edit-month-field,.form-input::-webkit-datetime-edit-day-field,.form-input::-webkit-datetime-edit-hour-field,.form-input::-webkit-datetime-edit-minute-field,.form-input::-webkit-datetime-edit-second-field,.form-input::-webkit-datetime-edit-millisecond-field,.form-input::-webkit-datetime-edit-meridiem-field {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n}\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n.fixed {\\n  position: fixed;\\n}\\n.absolute {\\n  position: absolute;\\n}\\n.relative {\\n  position: relative;\\n}\\n.inset-0 {\\n  inset: 0px;\\n}\\n.inset-y-0 {\\n  top: 0px;\\n  bottom: 0px;\\n}\\n.-right-1 {\\n  right: -0.25rem;\\n}\\n.-top-1 {\\n  top: -0.25rem;\\n}\\n.left-0 {\\n  left: 0px;\\n}\\n.left-3 {\\n  left: 0.75rem;\\n}\\n.right-0 {\\n  right: 0px;\\n}\\n.top-1\\\\/2 {\\n  top: 50%;\\n}\\n.z-40 {\\n  z-index: 40;\\n}\\n.z-50 {\\n  z-index: 50;\\n}\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\n.ml-1 {\\n  margin-left: 0.25rem;\\n}\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\n.ml-4 {\\n  margin-left: 1rem;\\n}\\n.ml-5 {\\n  margin-left: 1.25rem;\\n}\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\n.mr-3 {\\n  margin-right: 0.75rem;\\n}\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\n.mt-3 {\\n  margin-top: 0.75rem;\\n}\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\n.block {\\n  display: block;\\n}\\n.inline-block {\\n  display: inline-block;\\n}\\n.inline {\\n  display: inline;\\n}\\n.flex {\\n  display: flex;\\n}\\n.inline-flex {\\n  display: inline-flex;\\n}\\n.table {\\n  display: table;\\n}\\n.grid {\\n  display: grid;\\n}\\n.hidden {\\n  display: none;\\n}\\n.h-12 {\\n  height: 3rem;\\n}\\n.h-2 {\\n  height: 0.5rem;\\n}\\n.h-20 {\\n  height: 5rem;\\n}\\n.h-4 {\\n  height: 1rem;\\n}\\n.h-5 {\\n  height: 1.25rem;\\n}\\n.h-6 {\\n  height: 1.5rem;\\n}\\n.h-64 {\\n  height: 16rem;\\n}\\n.h-8 {\\n  height: 2rem;\\n}\\n.h-full {\\n  height: 100%;\\n}\\n.max-h-96 {\\n  max-height: 24rem;\\n}\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\n.w-0 {\\n  width: 0px;\\n}\\n.w-11 {\\n  width: 2.75rem;\\n}\\n.w-12 {\\n  width: 3rem;\\n}\\n.w-2 {\\n  width: 0.5rem;\\n}\\n.w-20 {\\n  width: 5rem;\\n}\\n.w-32 {\\n  width: 8rem;\\n}\\n.w-4 {\\n  width: 1rem;\\n}\\n.w-48 {\\n  width: 12rem;\\n}\\n.w-5 {\\n  width: 1.25rem;\\n}\\n.w-6 {\\n  width: 1.5rem;\\n}\\n.w-64 {\\n  width: 16rem;\\n}\\n.w-8 {\\n  width: 2rem;\\n}\\n.w-80 {\\n  width: 20rem;\\n}\\n.w-full {\\n  width: 100%;\\n}\\n.min-w-0 {\\n  min-width: 0px;\\n}\\n.max-w-7xl {\\n  max-width: 80rem;\\n}\\n.max-w-md {\\n  max-width: 28rem;\\n}\\n.max-w-xs {\\n  max-width: 20rem;\\n}\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\n.-translate-x-full {\\n  --tw-translate-x: -100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.-translate-y-1\\\\/2 {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-0 {\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\n.appearance-none {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n}\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.flex-col {\\n  flex-direction: column;\\n}\\n.items-start {\\n  align-items: flex-start;\\n}\\n.items-end {\\n  align-items: flex-end;\\n}\\n.items-center {\\n  align-items: center;\\n}\\n.justify-end {\\n  justify-content: flex-end;\\n}\\n.justify-center {\\n  justify-content: center;\\n}\\n.justify-between {\\n  justify-content: space-between;\\n}\\n.gap-4 {\\n  gap: 1rem;\\n}\\n.gap-5 {\\n  gap: 1.25rem;\\n}\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\n.overflow-x-auto {\\n  overflow-x: auto;\\n}\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\n.truncate {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\n.rounded-lg {\\n  border-radius: 0.5rem;\\n}\\n.rounded-md {\\n  border-radius: 0.375rem;\\n}\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\n.rounded-2xl {\\n  border-radius: 1rem;\\n}\\n.border {\\n  border-width: 1px;\\n}\\n.border-2 {\\n  border-width: 2px;\\n}\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\n.border-t {\\n  border-top-width: 1px;\\n}\\n.border-gray-100 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.border-primary-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\\n}\\n.border-red-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));\\n}\\n.border-transparent {\\n  border-color: transparent;\\n}\\n.border-neutral-150 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(240 240 240 / var(--tw-border-opacity, 1));\\n}\\n.border-neutral-100 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));\\n}\\n.border-red-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\\n}\\n.border-t-primary-600 {\\n  --tw-border-opacity: 1;\\n  border-top-color: rgb(2 132 199 / var(--tw-border-opacity, 1));\\n}\\n.bg-black {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\\n}\\n.bg-pink-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-primary-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\\n}\\n.bg-neutral-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\\n}\\n.bg-surface-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-status-error {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-opacity-50 {\\n  --tw-bg-opacity: 0.5;\\n}\\n.bg-opacity-75 {\\n  --tw-bg-opacity: 0.75;\\n}\\n.p-1 {\\n  padding: 0.25rem;\\n}\\n.p-1\\\\.5 {\\n  padding: 0.375rem;\\n}\\n.p-12 {\\n  padding: 3rem;\\n}\\n.p-2 {\\n  padding: 0.5rem;\\n}\\n.p-3 {\\n  padding: 0.75rem;\\n}\\n.p-4 {\\n  padding: 1rem;\\n}\\n.p-5 {\\n  padding: 1.25rem;\\n}\\n.p-6 {\\n  padding: 1.5rem;\\n}\\n.p-2\\\\.5 {\\n  padding: 0.625rem;\\n}\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-2\\\\.5 {\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n}\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-6 {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n}\\n.py-0\\\\.5 {\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n}\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-12 {\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\n.py-5 {\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\n.pl-10 {\\n  padding-left: 2.5rem;\\n}\\n.pr-10 {\\n  padding-right: 2.5rem;\\n}\\n.pr-3 {\\n  padding-right: 0.75rem;\\n}\\n.pb-20 {\\n  padding-bottom: 5rem;\\n}\\n.pb-4 {\\n  padding-bottom: 1rem;\\n}\\n.pt-4 {\\n  padding-top: 1rem;\\n}\\n.pt-5 {\\n  padding-top: 1.25rem;\\n}\\n.text-left {\\n  text-align: left;\\n}\\n.text-center {\\n  text-align: center;\\n}\\n.align-bottom {\\n  vertical-align: bottom;\\n}\\n.font-arabic {\\n  font-family: Cairo, ui-sans-serif, system-ui, sans-serif;\\n}\\n.font-english {\\n  font-family: Inter, ui-sans-serif, system-ui, sans-serif;\\n}\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold {\\n  font-weight: 700;\\n}\\n.font-extrabold {\\n  font-weight: 800;\\n}\\n.font-medium {\\n  font-weight: 500;\\n}\\n.font-semibold {\\n  font-weight: 600;\\n}\\n.capitalize {\\n  text-transform: capitalize;\\n}\\n.leading-6 {\\n  line-height: 1.5rem;\\n}\\n.text-blue-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-green-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n.text-green-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\\n}\\n.text-green-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n}\\n.text-green-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(20 83 45 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 48 163 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(2 132 199 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(3 105 161 / var(--tw-text-opacity, 1));\\n}\\n.text-primary-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(12 74 110 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\\n}\\n.text-red-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n.text-red-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n}\\n.text-text-secondary {\\n  --tw-text-opacity: 1;\\n  color: rgb(82 82 82 / var(--tw-text-opacity, 1));\\n}\\n.text-text-primary {\\n  --tw-text-opacity: 1;\\n  color: rgb(23 23 23 / var(--tw-text-opacity, 1));\\n}\\n.placeholder-gray-500::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.placeholder-gray-500::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\\n}\\n.shadow {\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-soft {\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-large {\\n  --tw-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.12);\\n  --tw-shadow-colored: 0 8px 24px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-xl {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.ring-1 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.ring-black {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));\\n}\\n.ring-opacity-5 {\\n  --tw-ring-opacity: 0.05;\\n}\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform {\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity {\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\n\\n/* Import fonts */\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\\n@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');\\n\\n/* Modern Minimalist Base Styles */\\nhtml {\\n  scroll-behavior: smooth;\\n}\\n\\nbody {\\n  font-family: 'Inter', 'ui-sans-serif', 'system-ui', sans-serif;\\n  line-height: 1.6;\\n  background-color: #fafafa; /* neutral-50 */\\n  color: #171717; /* text-primary */\\n  font-weight: 400;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n/* Arabic font */\\n.font-arabic {\\n  font-family: 'Cairo', sans-serif;\\n}\\n\\n.font-english {\\n  font-family: 'Inter', sans-serif;\\n}\\n\\n/* RTL support */\\n[dir=\\\"rtl\\\"] {\\n  text-align: right;\\n}\\n\\n[dir=\\\"rtl\\\"] .rtl\\\\:text-left {\\n  text-align: left;\\n}\\n\\n[dir=\\\"rtl\\\"] .rtl\\\\:text-right {\\n  text-align: right;\\n}\\n\\n/* Custom scrollbar */\\n::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n/* Modern Form Styles */\\n.form-input {\\n  display: block;\\n  width: 100%;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.form-input::-moz-placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(115 115 115 / var(--tw-placeholder-opacity, 1));\\n}\\n.form-input::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(115 115 115 / var(--tw-placeholder-opacity, 1));\\n}\\n.form-input {\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n}\\n.form-input:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(56 189 248 / var(--tw-border-opacity, 1));\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(56 189 248 / var(--tw-ring-opacity, 1));\\n}\\n@media (min-width: 640px) {\\n\\n  .form-input {\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n}\\n\\n.form-input:invalid {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\\n}\\n\\n.form-input:invalid:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));\\n}\\n\\n.form-label {\\n  margin-bottom: 0.5rem;\\n  display: block;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(23 23 23 / var(--tw-text-opacity, 1));\\n}\\n\\n.form-error {\\n  margin-top: 0.25rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\\n}\\n\\n/* Modern Button Styles */\\n.btn {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n.btn:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(14 165 233 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.btn-primary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-primary {\\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n.btn-primary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.btn-primary:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(56 189 248 / var(--tw-ring-opacity, 1));\\n}\\n\\n.btn-secondary {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 229 229 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(64 64 64 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.btn-secondary:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-secondary {\\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n.btn-secondary:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.btn-secondary:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(56 189 248 / var(--tw-ring-opacity, 1));\\n}\\n\\n.btn-danger {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.btn-danger:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-danger {\\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n.btn-danger:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.btn-danger:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));\\n}\\n\\n.btn-success {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  --tw-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 2px 8px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 0.75rem;\\n  border-width: 1px;\\n  border-color: transparent;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.625rem;\\n  padding-bottom: 0.625rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.btn-success:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n.btn-success {\\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n.btn-success:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n  --tw-shadow-colored: 0 4px 12px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.btn-success:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(74 222 128 / var(--tw-ring-opacity, 1));\\n}\\n\\n.btn-sm {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n\\n.btn-lg {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n}\\n\\n/* Modern Card Styles */\\n.card {\\n  border-radius: 1rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n.card-header {\\n  border-bottom-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\n\\n.card-body {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\n\\n.card-footer {\\n  border-bottom-right-radius: 1rem;\\n  border-bottom-left-radius: 1rem;\\n  border-top-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 1.25rem;\\n  padding-bottom: 1.25rem;\\n}\\n\\n/* Modern Table Styles */\\n.table {\\n  min-width: 100%;\\n}\\n.table > :not([hidden]) ~ :not([hidden]) {\\n  --tw-divide-y-reverse: 0;\\n  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\\n  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));\\n  --tw-divide-opacity: 1;\\n  border-color: rgb(240 240 240 / var(--tw-divide-opacity, 1));\\n}\\n\\n.table thead {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\\n}\\n\\n.table th {\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n  text-align: left;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n  --tw-text-opacity: 1;\\n  color: rgb(82 82 82 / var(--tw-text-opacity, 1));\\n}\\n\\n.table td {\\n  white-space: nowrap;\\n  padding-left: 1.5rem;\\n  padding-right: 1.5rem;\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(23 23 23 / var(--tw-text-opacity, 1));\\n}\\n\\n.table tbody tr:nth-child(even) {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\\n}\\n\\n.table tbody tr:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(248 249 250 / var(--tw-bg-opacity, 1));\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n\\n/* Modern Badge Styles */\\n.badge {\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-primary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(3 105 161 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-secondary {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(64 64 64 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-success {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-warning {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(161 98 7 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-danger {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n.badge-info {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(3 105 161 / var(--tw-text-opacity, 1));\\n  display: inline-flex;\\n  align-items: center;\\n  border-radius: 9999px;\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  font-weight: 500;\\n}\\n\\n/* Alert styles */\\n.alert {\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n.alert-success {\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n.alert-warning {\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n.alert-danger {\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n.alert-info {\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n\\n/* Animation utilities */\\n.fade-in {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n.slide-up {\\n  animation: slideUp 0.3s ease-out;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideUp {\\n  from {\\n    transform: translateY(10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n  \\n  .print-break {\\n    page-break-before: always;\\n  }\\n  \\n  body {\\n    font-size: 12pt;\\n    line-height: 1.4;\\n  }\\n  \\n  .card {\\n    box-shadow: none;\\n    border: 1px solid #ddd;\\n  }\\n}\\n\\n/* Modern Layout Utilities */\\n.page-container {\\n  margin-left: auto;\\n  margin-right: auto;\\n  max-width: 80rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n@media (min-width: 640px) {\\n\\n  .page-container {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .page-container {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n}\\n\\n.section-spacing {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\n\\n@media (min-width: 1024px) {\\n\\n  .section-spacing {\\n    padding-top: 3rem;\\n    padding-bottom: 3rem;\\n  }\\n}\\n\\n.content-spacing > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n\\n@media (min-width: 1024px) {\\n\\n  .content-spacing > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n  }\\n}\\n\\n/* Modern Shadows */\\n.shadow-soft {\\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n.shadow-medium {\\n  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n}\\n\\n.shadow-large {\\n  box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.12);\\n}\\n\\n.shadow-card {\\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n/* Enhanced RTL Support */\\n[dir=\\\"rtl\\\"] .table th {\\n  text-align: right;\\n}\\n\\n[dir=\\\"rtl\\\"] .table td {\\n  text-align: right;\\n}\\n\\n/* Modern Focus States */\\n.focus-ring:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(56 189 248 / var(--tw-ring-opacity, 1));\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fafafa;\\n}\\n\\n/* Improved Scrollbar */\\n::-webkit-scrollbar {\\n  width: 8px;\\n  height: 8px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: #f5f5f5;\\n  border-radius: 4px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: #d4d4d4;\\n  border-radius: 4px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: #a3a3a3;\\n}\\n\\n/* Dark mode support (if needed) */\\n@media (prefers-color-scheme: dark) {\\n  .dark-mode {\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(23 23 23 / var(--tw-bg-opacity, 1));\\n    --tw-text-opacity: 1;\\n    color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  }\\n\\n  .dark-mode .card {\\n    --tw-border-opacity: 1;\\n    border-color: rgb(64 64 64 / var(--tw-border-opacity, 1));\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(38 38 38 / var(--tw-bg-opacity, 1));\\n  }\\n\\n  .dark-mode .form-input {\\n    --tw-border-opacity: 1;\\n    border-color: rgb(82 82 82 / var(--tw-border-opacity, 1));\\n    --tw-bg-opacity: 1;\\n    background-color: rgb(64 64 64 / var(--tw-bg-opacity, 1));\\n    --tw-text-opacity: 1;\\n    color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  }\\n}\\n.after\\\\:absolute::after {\\n  content: var(--tw-content);\\n  position: absolute;\\n}\\n.after\\\\:left-\\\\[2px\\\\]::after {\\n  content: var(--tw-content);\\n  left: 2px;\\n}\\n.after\\\\:top-\\\\[2px\\\\]::after {\\n  content: var(--tw-content);\\n  top: 2px;\\n}\\n.after\\\\:h-5::after {\\n  content: var(--tw-content);\\n  height: 1.25rem;\\n}\\n.after\\\\:w-5::after {\\n  content: var(--tw-content);\\n  width: 1.25rem;\\n}\\n.after\\\\:rounded-full::after {\\n  content: var(--tw-content);\\n  border-radius: 9999px;\\n}\\n.after\\\\:border::after {\\n  content: var(--tw-content);\\n  border-width: 1px;\\n}\\n.after\\\\:border-gray-300::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.after\\\\:bg-white::after {\\n  content: var(--tw-content);\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.after\\\\:transition-all::after {\\n  content: var(--tw-content);\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.after\\\\:content-\\\\[\\\\'\\\\'\\\\]::after {\\n  --tw-content: '';\\n  content: var(--tw-content);\\n}\\n.hover\\\\:border-gray-300:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.hover\\\\:bg-gray-100:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-gray-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-primary-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(3 105 161 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:bg-neutral-75:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(248 249 250 / var(--tw-bg-opacity, 1));\\n}\\n.hover\\\\:text-blue-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-gray-900:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-green-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-red-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-text-primary:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(23 23 23 / var(--tw-text-opacity, 1));\\n}\\n.hover\\\\:text-gray-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.focus\\\\:z-10:focus {\\n  z-index: 10;\\n}\\n.focus\\\\:border-primary-500:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));\\n}\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.focus\\\\:ring-primary-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(14 165 233 / var(--tw-ring-opacity, 1));\\n}\\n.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\n.disabled\\\\:cursor-not-allowed:disabled {\\n  cursor: not-allowed;\\n}\\n.disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\n.group:hover .group-hover\\\\:text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.peer:checked ~ .peer-checked\\\\:bg-primary-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(2 132 199 / var(--tw-bg-opacity, 1));\\n}\\n.peer:checked ~ .peer-checked\\\\:after\\\\:translate-x-full::after {\\n  content: var(--tw-content);\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.peer:checked ~ .peer-checked\\\\:after\\\\:border-white::after {\\n  content: var(--tw-content);\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\n.peer:focus ~ .peer-focus\\\\:outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.peer:focus ~ .peer-focus\\\\:ring-4 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.peer:focus ~ .peer-focus\\\\:ring-primary-300 {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(125 211 252 / var(--tw-ring-opacity, 1));\\n}\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:my-8 {\\n    margin-top: 2rem;\\n    margin-bottom: 2rem;\\n  }\\n\\n  .sm\\\\:ml-3 {\\n    margin-left: 0.75rem;\\n  }\\n\\n  .sm\\\\:mt-0 {\\n    margin-top: 0px;\\n  }\\n\\n  .sm\\\\:block {\\n    display: block;\\n  }\\n\\n  .sm\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .sm\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .sm\\\\:w-full {\\n    width: 100%;\\n  }\\n\\n  .sm\\\\:max-w-2xl {\\n    max-width: 42rem;\\n  }\\n\\n  .sm\\\\:max-w-lg {\\n    max-width: 32rem;\\n  }\\n\\n  .sm\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:flex-row-reverse {\\n    flex-direction: row-reverse;\\n  }\\n\\n  .sm\\\\:p-6 {\\n    padding: 1.5rem;\\n  }\\n\\n  .sm\\\\:p-0 {\\n    padding: 0px;\\n  }\\n\\n  .sm\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n\\n  .sm\\\\:pb-4 {\\n    padding-bottom: 1rem;\\n  }\\n\\n  .sm\\\\:align-middle {\\n    vertical-align: middle;\\n  }\\n\\n  .sm\\\\:text-sm {\\n    font-size: 0.875rem;\\n    line-height: 1.25rem;\\n  }\\n}\\n@media (min-width: 768px) {\\n\\n  .md\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n}\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:ml-20 {\\n    margin-left: 5rem;\\n  }\\n\\n  .lg\\\\:ml-64 {\\n    margin-left: 16rem;\\n  }\\n\\n  .lg\\\\:mr-20 {\\n    margin-right: 5rem;\\n  }\\n\\n  .lg\\\\:mr-64 {\\n    margin-right: 16rem;\\n  }\\n\\n  .lg\\\\:block {\\n    display: block;\\n  }\\n\\n  .lg\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .lg\\\\:w-1\\\\/4 {\\n    width: 25%;\\n  }\\n\\n  .lg\\\\:w-3\\\\/4 {\\n    width: 75%;\\n  }\\n\\n  .lg\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .lg\\\\:px-8 {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n}\\n.rtl\\\\:left-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  left: 0px;\\n}\\n.rtl\\\\:left-auto:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  left: auto;\\n}\\n.rtl\\\\:right-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  right: 0px;\\n}\\n.rtl\\\\:right-3:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  right: 0.75rem;\\n}\\n.rtl\\\\:right-auto:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  right: auto;\\n}\\n.rtl\\\\:ml-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-left: 0px;\\n}\\n.rtl\\\\:ml-1:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-left: 0.25rem;\\n}\\n.rtl\\\\:ml-2:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-left: 0.5rem;\\n}\\n.rtl\\\\:ml-3:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-left: 0.75rem;\\n}\\n.rtl\\\\:mr-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 0px;\\n}\\n.rtl\\\\:mr-1:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 0.25rem;\\n}\\n.rtl\\\\:mr-2:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 0.5rem;\\n}\\n.rtl\\\\:mr-4:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 1rem;\\n}\\n.rtl\\\\:mr-5:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  margin-right: 1.25rem;\\n}\\n.rtl\\\\:-translate-x-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  --tw-translate-x: -0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.rtl\\\\:translate-x-full:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  --tw-translate-x: 100%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.rtl\\\\:space-x-reverse:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 1;\\n}\\n.rtl\\\\:pl-10:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-left: 2.5rem;\\n}\\n.rtl\\\\:pl-3:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-left: 0.75rem;\\n}\\n.rtl\\\\:pr-0:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-right: 0px;\\n}\\n.rtl\\\\:pr-10:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-right: 2.5rem;\\n}\\n.rtl\\\\:pr-3:where([dir=\\\"rtl\\\"], [dir=\\\"rtl\\\"] *) {\\n  padding-right: 0.75rem;\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAA0B;EAA1B,wBAA0B;EAA1B,mBAA0B;EAA1B,mBAA0B;EAA1B,cAA0B;EAA1B,cAA0B;EAA1B,cAA0B;EAA1B,eAA0B;EAA1B,eAA0B;EAA1B,aAA0B;EAA1B,aAA0B;EAA1B,kBAA0B;EAA1B,sCAA0B;EAA1B,8BAA0B;EAA1B,6BAA0B;EAA1B,4BAA0B;EAA1B,eAA0B;EAA1B,oBAA0B;EAA1B,sBAA0B;EAA1B,uBAA0B;EAA1B,wBAA0B;EAA1B,kBAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;EAA1B,sCAA0B;EAA1B,kCAA0B;EAA1B,2BAA0B;EAA1B,sBAA0B;EAA1B,8BAA0B;EAA1B,YAA0B;EAA1B,kBAA0B;EAA1B,gBAA0B;EAA1B,iBAA0B;EAA1B,kBAA0B;EAA1B,cAA0B;EAA1B,gBAA0B;EAA1B,aAA0B;EAA1B,mBAA0B;EAA1B,qBAA0B;EAA1B,2BAA0B;EAA1B,yBAA0B;EAA1B,0BAA0B;EAA1B,2BAA0B;EAA1B,uBAA0B;EAA1B,wBAA0B;EAA1B,yBAA0B;EAA1B,sBAA0B;EAA1B,oBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B;AAA0B;;AAA1B;EAAA,wBAA0B;EAA1B,wBAA0B;EAA1B,mBAA0B;EAA1B,mBAA0B;EAA1B,cAA0B;EAA1B,cAA0B;EAA1B,cAA0B;EAA1B,eAA0B;EAA1B,eAA0B;EAA1B,aAA0B;EAA1B,aAA0B;EAA1B,kBAA0B;EAA1B,sCAA0B;EAA1B,8BAA0B;EAA1B,6BAA0B;EAA1B,4BAA0B;EAA1B,eAA0B;EAA1B,oBAA0B;EAA1B,sBAA0B;EAA1B,uBAA0B;EAA1B,wBAA0B;EAA1B,kBAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;EAA1B,sCAA0B;EAA1B,kCAA0B;EAA1B,2BAA0B;EAA1B,sBAA0B;EAA1B,8BAA0B;EAA1B,YAA0B;EAA1B,kBAA0B;EAA1B,gBAA0B;EAA1B,iBAA0B;EAA1B,kBAA0B;EAA1B,cAA0B;EAA1B,gBAA0B;EAA1B,aAA0B;EAA1B,mBAA0B;EAA1B,qBAA0B;EAA1B,2BAA0B;EAA1B,yBAA0B;EAA1B,0BAA0B;EAA1B,2BAA0B;EAA1B,uBAA0B;EAA1B,wBAA0B;EAA1B,yBAA0B;EAA1B,sBAA0B;EAA1B,oBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B;AAA0B,CAA1B;;CAA0B,CAA1B;;;CAA0B;;AAA1B;;;EAAA,sBAA0B,EAA1B,MAA0B;EAA1B,eAA0B,EAA1B,MAA0B;EAA1B,mBAA0B,EAA1B,MAA0B;EAA1B,qBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;EAAA,gBAA0B;AAAA;;AAA1B;;;;;;;;CAA0B;;AAA1B;;EAAA,gBAA0B,EAA1B,MAA0B;EAA1B,8BAA0B,EAA1B,MAA0B;EAA1B,gBAA0B,EAA1B,MAA0B;EAA1B,cAA0B;KAA1B,WAA0B,EAA1B,MAA0B;EAA1B,wDAA0B,EAA1B,MAA0B;EAA1B,6BAA0B,EAA1B,MAA0B;EAA1B,+BAA0B,EAA1B,MAA0B;EAA1B,wCAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;EAAA,SAA0B,EAA1B,MAA0B;EAA1B,oBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;;;CAA0B;;AAA1B;EAAA,SAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;EAA1B,qBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,yCAA0B;UAA1B,iCAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;;;;;EAAA,kBAA0B;EAA1B,oBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,cAA0B;EAA1B,wBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,mBAA0B;AAAA;;AAA1B;;;;;CAA0B;;AAA1B;;;;EAAA,+GAA0B,EAA1B,MAA0B;EAA1B,6BAA0B,EAA1B,MAA0B;EAA1B,+BAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,cAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,cAA0B;EAA1B,cAA0B;EAA1B,kBAA0B;EAA1B,wBAA0B;AAAA;;AAA1B;EAAA,eAA0B;AAAA;;AAA1B;EAAA,WAA0B;AAAA;;AAA1B;;;;CAA0B;;AAA1B;EAAA,cAA0B,EAA1B,MAA0B;EAA1B,qBAA0B,EAA1B,MAA0B;EAA1B,yBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;;;CAA0B;;AAA1B;;;;;EAAA,oBAA0B,EAA1B,MAA0B;EAA1B,8BAA0B,EAA1B,MAA0B;EAA1B,gCAA0B,EAA1B,MAA0B;EAA1B,eAA0B,EAA1B,MAA0B;EAA1B,oBAA0B,EAA1B,MAA0B;EAA1B,oBAA0B,EAA1B,MAA0B;EAA1B,uBAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;EAA1B,SAA0B,EAA1B,MAA0B;EAA1B,UAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,oBAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;;;;EAAA,0BAA0B,EAA1B,MAA0B;EAA1B,6BAA0B,EAA1B,MAA0B;EAA1B,sBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,aAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,gBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,wBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,YAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;EAAA,6BAA0B,EAA1B,MAA0B;EAA1B,oBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,wBAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;EAAA,0BAA0B,EAA1B,MAA0B;EAA1B,aAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,kBAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;;;;;;;;;;;;EAAA,SAA0B;AAAA;;AAA1B;EAAA,SAA0B;EAA1B,UAA0B;AAAA;;AAA1B;EAAA,UAA0B;AAAA;;AAA1B;;;EAAA,gBAA0B;EAA1B,SAA0B;EAA1B,UAA0B;AAAA;;AAA1B;;CAA0B;AAA1B;EAAA,UAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;EAAA,gBAA0B;AAAA;;AAA1B;;;CAA0B;;AAA1B;EAAA,UAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;EAAA,UAA0B,EAA1B,MAA0B;EAA1B,cAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,eAA0B;AAAA;;AAA1B;;CAA0B;AAA1B;EAAA,eAA0B;AAAA;;AAA1B;;;;CAA0B;;AAA1B;;;;;;;;EAAA,cAA0B,EAA1B,MAA0B;EAA1B,sBAA0B,EAA1B,MAA0B;AAAA;;AAA1B;;CAA0B;;AAA1B;;EAAA,eAA0B;EAA1B,YAA0B;AAAA;;AAA1B,wEAA0B;AAA1B;EAAA,aAA0B;AAAA;;AAA1B;EAAA,wBAA0B;KAA1B,qBAA0B;UAA1B,gBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B,iBAA0B;EAA1B,kBAA0B;EAA1B,mBAA0B;EAA1B,sBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B,eAA0B;EAA1B,mBAA0B;EAA1B,sBAA0B;AAAA;;AAA1B;EAAA,8BAA0B;EAA1B,mBAA0B;EAA1B,4CAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;EAA1B,wBAA0B;EAA1B,2GAA0B;EAA1B,yGAA0B;EAA1B,iFAA0B;EAA1B;AAA0B;;AAA1B;EAAA,cAA0B;EAA1B;AAA0B;;AAA1B;EAAA,cAA0B;EAA1B;AAA0B;;AAA1B;EAAA;AAA0B;;AAA1B;EAAA,iBAA0B;EAA1B;AAA0B;;AAA1B;EAAA;AAA0B;;AAA1B;EAAA,cAA0B;EAA1B;AAA0B;;AAA1B;EAAA,mPAA0B;EAA1B,wCAA0B;EAA1B,4BAA0B;EAA1B,4BAA0B;EAA1B,qBAA0B;EAA1B,iCAA0B;UAA1B;AAA0B;;AAA1B;EAAA,yBAA0B;EAA1B,4BAA0B;EAA1B,wBAA0B;EAA1B,wBAA0B;EAA1B,sBAA0B;EAA1B,iCAA0B;UAA1B;AAA0B;;AAA1B;EAAA,wBAA0B;KAA1B,qBAA0B;UAA1B,gBAA0B;EAA1B,UAA0B;EAA1B,iCAA0B;UAA1B,yBAA0B;EAA1B,qBAA0B;EAA1B,sBAA0B;EAA1B,6BAA0B;EAA1B,yBAA0B;KAA1B,sBAA0B;UAA1B,iBAA0B;EAA1B,cAA0B;EAA1B,YAA0B;EAA1B,WAA0B;EAA1B,cAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B,iBAA0B;EAA1B;AAA0B;;AAA1B;EAAA;AAA0B;;AAA1B;EAAA;AAA0B;;AAA1B;EAAA,8BAA0B;EAA1B,mBAA0B;EAA1B,4CAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;EAA1B,wBAA0B;EAA1B,2GAA0B;EAA1B,yGAA0B;EAA1B;AAA0B;;AAA1B;EAAA,yBAA0B;EAA1B,8BAA0B;EAA1B,0BAA0B;EAA1B,2BAA0B;EAA1B;AAA0B;;AAA1B;EAAA,sQAA0B;AAAA;;AAA1B;;EAAA;IAAA,wBAA0B;OAA1B,qBAA0B;YAA1B;EAA0B;AAAA;;AAA1B;EAAA,oKAA0B;AAAA;;AAA1B;;EAAA;IAAA,wBAA0B;OAA1B,qBAA0B;YAA1B;EAA0B;AAAA;;AAA1B;EAAA,yBAA0B;EAA1B;AAA0B;;AAA1B;EAAA,uOAA0B;EAA1B,yBAA0B;EAA1B,8BAA0B;EAA1B,0BAA0B;EAA1B,2BAA0B;EAA1B,4BAA0B;AAAA;;AAA1B;;EAAA;IAAA,wBAA0B;OAA1B,qBAA0B;YAA1B;EAA0B;AAAA;;AAA1B;EAAA,yBAA0B;EAA1B;AAA0B;;AAA1B;EAAA,iBAA0B;EAA1B,qBAA0B;EAA1B,eAA0B;EAA1B,gBAA0B;EAA1B,UAA0B;EAA1B,gBAA0B;EAA1B;AAA0B;;AAA1B;EAAA,6BAA0B;EAA1B;AAA0B;AAC1B;EAAA,wBAAgC;KAAhC,qBAAgC;UAAhC,gBAAgC;EAAhC,sBAAgC;EAAhC,qBAAgC;EAAhC,iBAAgC;EAAhC,kBAAgC;EAAhC,mBAAgC;EAAhC,sBAAgC;EAAhC,sBAAgC;EAAhC,qBAAgC;EAAhC,eAAgC;EAAhC,mBAAgC;EAAhC,sBAAgC;AAAA;AAAhC;EAAA,8BAAgC;EAAhC,mBAAgC;EAAhC,4CAAgC;EAAhC,2BAAgC;EAAhC,4BAAgC;EAAhC,wBAAgC;EAAhC,2GAAgC;EAAhC,yGAAgC;EAAhC,iFAAgC;EAAhC;AAAgC;AAAhC;EAAA,cAAgC;EAAhC;AAAgC;AAAhC;EAAA,cAAgC;EAAhC;AAAgC;AAAhC;EAAA;AAAgC;AAAhC;EAAA,iBAAgC;EAAhC;AAAgC;AAAhC;EAAA;AAAgC;AAAhC;EAAA,cAAgC;EAAhC;AAAgC;AAChC;EAAA,kBAA+B;EAA/B,UAA+B;EAA/B,WAA+B;EAA/B,UAA+B;EAA/B,YAA+B;EAA/B,gBAA+B;EAA/B,sBAA+B;EAA/B,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,QAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,iBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,qBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;;EAAA;IAAA;EAA+B;AAAA;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,wBAA+B;KAA/B,qBAA+B;UAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,sDAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,uDAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,oDAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,+DAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,+DAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,4DAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,8DAA+B;EAA/B;AAA+B;AAA/B;EAAA,uBAA+B;EAA/B,4DAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,gBAA+B;EAA/B,uBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,sBAA+B;EAA/B;AAA+B;AAA/B;EAAA,qBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,qBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,iBAA+B;EAA/B;AAA+B;AAA/B;EAAA,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,iBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,iBAA+B;EAA/B;AAA+B;AAA/B;EAAA,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA,mBAA+B;EAA/B;AAA+B;AAA/B;EAAA,kBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA,2BAA+B;EAA/B;AAA+B;AAA/B;EAAA,2BAA+B;EAA/B;AAA+B;AAA/B;EAAA,0EAA+B;EAA/B,8FAA+B;EAA/B;AAA+B;AAA/B;EAAA,+EAA+B;EAA/B,mGAA+B;EAA/B;AAA+B;AAA/B;EAAA,0CAA+B;EAA/B,uDAA+B;EAA/B;AAA+B;AAA/B;EAAA,4CAA+B;EAA/B,uDAA+B;EAA/B;AAA+B;AAA/B;EAAA,6CAA+B;EAA/B,wDAA+B;EAA/B;AAA+B;AAA/B;EAAA,gFAA+B;EAA/B,oGAA+B;EAA/B;AAA+B;AAA/B;EAAA,2GAA+B;EAA/B,yGAA+B;EAA/B;AAA+B;AAA/B;EAAA,oBAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA,wBAA+B;EAA/B,wDAA+B;EAA/B;AAA+B;AAA/B;EAAA,+FAA+B;EAA/B,wDAA+B;EAA/B;AAA+B;AAA/B;EAAA,8BAA+B;EAA/B,wDAA+B;EAA/B;AAA+B;AAA/B;EAAA,4BAA+B;EAA/B,wDAA+B;EAA/B;AAA+B;AAA/B;EAAA;AAA+B;AAA/B;EAAA;AAA+B;;AAE/B,iBAAiB;AACjB,mGAAmG;AACnG,mGAAmG;;AAEnG,kCAAkC;AAClC;EACE,uBAAuB;AACzB;;AAEA;EACE,8DAA8D;EAC9D,gBAAgB;EAChB,yBAAyB,EAAE,eAAe;EAC1C,cAAc,EAAE,iBAAiB;EACjC,gBAAgB;EAChB,mCAAmC;EACnC,kCAAkC;AACpC;;AAEA,gBAAgB;AAChB;EACE,gCAAgC;AAClC;;AAEA;EACE,gCAAgC;AAClC;;AAEA,gBAAgB;AAChB;EACE,iBAAiB;AACnB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,iBAAiB;AACnB;;AAEA,qBAAqB;AACrB;EACE,UAAU;EACV,WAAW;AACb;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;AACrB;;AAEA,uBAAuB;AAErB;EAAA,cAAkP;EAAlP,WAAkP;EAAlP,sBAAkP;EAAlP,iBAAkP;EAAlP,sBAAkP;EAAlP,4DAAkP;EAAlP,kBAAkP;EAAlP,4DAAkP;EAAlP,kBAAkP;EAAlP,mBAAkP;EAAlP,oBAAkP;EAAlP;AAAkP;AAAlP;EAAA,2BAAkP;EAAlP;AAAkP;AAAlP;EAAA,2BAAkP;EAAlP;AAAkP;AAAlP;EAAA,4CAAkP;EAAlP,uDAAkP;EAAlP,uGAAkP;EAAlP,wBAAkP;EAAlP,wDAAkP;EAAlP,0BAAkP;EAAlP;AAAkP;AAAlP;EAAA,sBAAkP;EAAlP,2DAAkP;EAAlP,8BAAkP;EAAlP,mBAAkP;EAAlP,2GAAkP;EAAlP,yGAAkP;EAAlP,4FAAkP;EAAlP,oBAAkP;EAAlP;AAAkP;AAAlP;;EAAA;IAAA,mBAAkP;IAAlP;EAAkP;AAAA;;AAIlP;EAAA,sBAA4E;EAA5E;AAA4E;;AAA5E;EAAA,sBAA4E;EAA5E,0DAA4E;EAA5E,oBAA4E;EAA5E;AAA4E;;AAI5E;EAAA,qBAAuD;EAAvD,cAAuD;EAAvD,mBAAuD;EAAvD,oBAAuD;EAAvD,gBAAuD;EAAvD,oBAAuD;EAAvD;AAAuD;;AAIvD;EAAA,mBAAqC;EAArC,mBAAqC;EAArC,oBAAqC;EAArC,oBAAqC;EAArC;AAAqC;;AAGvC,yBAAyB;AAEvB;EAAA,oBAAgO;EAAhO,mBAAgO;EAAhO,uBAAgO;EAAhO,sBAAgO;EAAhO,iBAAgO;EAAhO,yBAAgO;EAAhO,kBAAgO;EAAhO,mBAAgO;EAAhO,qBAAgO;EAAhO,wBAAgO;EAAhO,mBAAgO;EAAhO,oBAAgO;EAAhO,gBAAgO;EAAhO,wBAAgO;EAAhO,wDAAgO;EAAhO;AAAgO;AAAhO;EAAA,8BAAgO;EAAhO,mBAAgO;EAAhO,2GAAgO;EAAhO,yGAAgO;EAAhO,4FAAgO;EAAhO,2BAAgO;EAAhO;AAAgO;;AAIhO;EAAA,kBAAgH;EAAhH,2DAAgH;EAAhH,oBAAgH;EAAhH,mDAAgH;EAAhH,4CAAgH;EAAhH,uDAAgH;EAAhH,uGAAgH;EAAhH,oBAAgH;EAAhH,mBAAgH;EAAhH,uBAAgH;EAAhH,sBAAgH;EAAhH,iBAAgH;EAAhH,yBAAgH;EAAhH,kBAAgH;EAAhH,mBAAgH;EAAhH,qBAAgH;EAAhH,wBAAgH;EAAhH,mBAAgH;EAAhH,oBAAgH;EAAhH,gBAAgH;EAAhH,wBAAgH;EAAhH,wDAAgH;EAAhH;AAAgH;;AAAhH;EAAA,8BAAgH;EAAhH,mBAAgH;EAAhH,2GAAgH;EAAhH,yGAAgH;EAAhH,4FAAgH;EAAhH,2BAAgH;EAAhH;AAAgH;;AAAhH;EAAA,2CAAgH;AAAA;;AAAhH;EAAA,kBAAgH;EAAhH,0DAAgH;EAAhH,6CAAgH;EAAhH,wDAAgH;EAAhH;AAAgH;;AAAhH;EAAA,oBAAgH;EAAhH;AAAgH;;AAIhH;EAAA,sBAAkI;EAAlI,4DAAkI;EAAlI,kBAAkI;EAAlI,4DAAkI;EAAlI,oBAAkI;EAAlI,gDAAkI;EAAlI,4CAAkI;EAAlI,uDAAkI;EAAlI,uGAAkI;EAAlI,oBAAkI;EAAlI,mBAAkI;EAAlI,uBAAkI;EAAlI,sBAAkI;EAAlI,iBAAkI;EAAlI,yBAAkI;EAAlI,kBAAkI;EAAlI,mBAAkI;EAAlI,qBAAkI;EAAlI,wBAAkI;EAAlI,mBAAkI;EAAlI,oBAAkI;EAAlI,gBAAkI;EAAlI,wBAAkI;EAAlI,wDAAkI;EAAlI;AAAkI;;AAAlI;EAAA,8BAAkI;EAAlI,mBAAkI;EAAlI,2GAAkI;EAAlI,yGAAkI;EAAlI,4FAAkI;EAAlI,2BAAkI;EAAlI;AAAkI;;AAAlI;EAAA,2CAAkI;AAAA;;AAAlI;EAAA,kBAAkI;EAAlI,4DAAkI;EAAlI,6CAAkI;EAAlI,wDAAkI;EAAlI;AAAkI;;AAAlI;EAAA,oBAAkI;EAAlI;AAAkI;;AAIlI;EAAA,kBAAyG;EAAzG,0DAAyG;EAAzG,oBAAyG;EAAzG,mDAAyG;EAAzG,4CAAyG;EAAzG,uDAAyG;EAAzG,uGAAyG;EAAzG,oBAAyG;EAAzG,mBAAyG;EAAzG,uBAAyG;EAAzG,sBAAyG;EAAzG,iBAAyG;EAAzG,yBAAyG;EAAzG,kBAAyG;EAAzG,mBAAyG;EAAzG,qBAAyG;EAAzG,wBAAyG;EAAzG,mBAAyG;EAAzG,oBAAyG;EAAzG,gBAAyG;EAAzG,wBAAyG;EAAzG,wDAAyG;EAAzG;AAAyG;;AAAzG;EAAA,8BAAyG;EAAzG,mBAAyG;EAAzG,2GAAyG;EAAzG,yGAAyG;EAAzG,4FAAyG;EAAzG,2BAAyG;EAAzG;AAAyG;;AAAzG;EAAA,2CAAyG;AAAA;;AAAzG;EAAA,kBAAyG;EAAzG,0DAAyG;EAAzG,6CAAyG;EAAzG,wDAAyG;EAAzG;AAAyG;;AAAzG;EAAA,oBAAyG;EAAzG;AAAyG;;AAIzG;EAAA,kBAA6G;EAA7G,0DAA6G;EAA7G,oBAA6G;EAA7G,mDAA6G;EAA7G,4CAA6G;EAA7G,uDAA6G;EAA7G,uGAA6G;EAA7G,oBAA6G;EAA7G,mBAA6G;EAA7G,uBAA6G;EAA7G,sBAA6G;EAA7G,iBAA6G;EAA7G,yBAA6G;EAA7G,kBAA6G;EAA7G,mBAA6G;EAA7G,qBAA6G;EAA7G,wBAA6G;EAA7G,mBAA6G;EAA7G,oBAA6G;EAA7G,gBAA6G;EAA7G,wBAA6G;EAA7G,wDAA6G;EAA7G;AAA6G;;AAA7G;EAAA,8BAA6G;EAA7G,mBAA6G;EAA7G,2GAA6G;EAA7G,yGAA6G;EAA7G,4FAA6G;EAA7G,2BAA6G;EAA7G;AAA6G;;AAA7G;EAAA,2CAA6G;AAAA;;AAA7G;EAAA,kBAA6G;EAA7G,0DAA6G;EAA7G,6CAA6G;EAA7G,wDAA6G;EAA7G;AAA6G;;AAA7G;EAAA,oBAA6G;EAA7G;AAA6G;;AAI7G;EAAA,qBAA0B;EAA1B,sBAA0B;EAA1B,qBAA0B;EAA1B,wBAA0B;EAA1B,kBAA0B;EAA1B;AAA0B;;AAI1B;EAAA,oBAA0B;EAA1B,qBAA0B;EAA1B,oBAA0B;EAA1B,uBAA0B;EAA1B,eAA0B;EAA1B;AAA0B;;AAG5B,uBAAuB;AAErB;EAAA,mBAA2E;EAA3E,iBAA2E;EAA3E,sBAA2E;EAA3E,4DAA2E;EAA3E,kBAA2E;EAA3E,4DAA2E;EAA3E,4EAA2E;EAA3E,2FAA2E;EAA3E,uGAA2E;EAA3E;AAA2E;;AAI3E;EAAA,wBAA4C;EAA5C,sBAA4C;EAA5C,4DAA4C;EAA5C,oBAA4C;EAA5C,qBAA4C;EAA5C,oBAA4C;EAA5C;AAA4C;;AAI5C;EAAA,oBAAgB;EAAhB,qBAAgB;EAAhB,oBAAgB;EAAhB;AAAgB;;AAIhB;EAAA,gCAA+E;EAA/E,+BAA+E;EAA/E,qBAA+E;EAA/E,sBAA+E;EAA/E,4DAA+E;EAA/E,kBAA+E;EAA/E,4DAA+E;EAA/E,oBAA+E;EAA/E,qBAA+E;EAA/E,oBAA+E;EAA/E;AAA+E;;AAGjF,wBAAwB;AAEtB;EAAA;AAA6C;AAA7C;EAAA,wBAA6C;EAA7C,kEAA6C;EAA7C,2DAA6C;EAA7C,sBAA6C;EAA7C;AAA6C;;AAI7C;EAAA,kBAA2B;EAA3B;AAA2B;;AAI3B;EAAA,oBAA6F;EAA7F,qBAA6F;EAA7F,iBAA6F;EAA7F,oBAA6F;EAA7F,gBAA6F;EAA7F,kBAA6F;EAA7F,iBAA6F;EAA7F,gBAA6F;EAA7F,yBAA6F;EAA7F,sBAA6F;EAA7F,oBAA6F;EAA7F;AAA6F;;AAI7F;EAAA,mBAA4D;EAA5D,oBAA4D;EAA5D,qBAA4D;EAA5D,iBAA4D;EAA5D,oBAA4D;EAA5D,mBAA4D;EAA5D,oBAA4D;EAA5D,oBAA4D;EAA5D;AAA4D;;AAI5D;EAAA,kBAA2B;EAA3B;AAA2B;;AAI3B;EAAA,kBAAmD;EAAnD,4DAAmD;EAAnD,+FAAmD;EAAnD,wDAAmD;EAAnD;AAAmD;;AAGrD,wBAAwB;AAEtB;EAAA,oBAA0E;EAA1E,mBAA0E;EAA1E,qBAA0E;EAA1E,qBAA0E;EAA1E,sBAA0E;EAA1E,oBAA0E;EAA1E,uBAA0E;EAA1E,kBAA0E;EAA1E,iBAA0E;EAA1E;AAA0E;;AAI1E;EAAA,kBAA4C;EAA5C,4DAA4C;EAA5C,oBAA4C;EAA5C,iDAA4C;EAA5C,oBAA4C;EAA5C,mBAA4C;EAA5C,qBAA4C;EAA5C,qBAA4C;EAA5C,sBAA4C;EAA5C,oBAA4C;EAA5C,uBAA4C;EAA5C,kBAA4C;EAA5C,iBAA4C;EAA5C;AAA4C;;AAI5C;EAAA,kBAA4C;EAA5C,4DAA4C;EAA5C,oBAA4C;EAA5C,gDAA4C;EAA5C,oBAA4C;EAA5C,mBAA4C;EAA5C,qBAA4C;EAA5C,qBAA4C;EAA5C,sBAA4C;EAA5C,oBAA4C;EAA5C,uBAA4C;EAA5C,kBAA4C;EAA5C,iBAA4C;EAA5C;AAA4C;;AAI5C;EAAA,kBAA0C;EAA1C,4DAA0C;EAA1C,oBAA0C;EAA1C,iDAA0C;EAA1C,oBAA0C;EAA1C,mBAA0C;EAA1C,qBAA0C;EAA1C,qBAA0C;EAA1C,sBAA0C;EAA1C,oBAA0C;EAA1C,uBAA0C;EAA1C,kBAA0C;EAA1C,iBAA0C;EAA1C;AAA0C;;AAI1C;EAAA,kBAA0C;EAA1C,4DAA0C;EAA1C,oBAA0C;EAA1C,gDAA0C;EAA1C,oBAA0C;EAA1C,mBAA0C;EAA1C,qBAA0C;EAA1C,qBAA0C;EAA1C,sBAA0C;EAA1C,oBAA0C;EAA1C,uBAA0C;EAA1C,kBAA0C;EAA1C,iBAA0C;EAA1C;AAA0C;;AAI1C;EAAA,kBAAoC;EAApC,4DAAoC;EAApC,oBAAoC;EAApC,iDAAoC;EAApC,oBAAoC;EAApC,mBAAoC;EAApC,qBAAoC;EAApC,qBAAoC;EAApC,sBAAoC;EAApC,oBAAoC;EAApC,uBAAoC;EAApC,kBAAoC;EAApC,iBAAoC;EAApC;AAAoC;;AAIpC;EAAA,kBAA4C;EAA5C,4DAA4C;EAA5C,oBAA4C;EAA5C,iDAA4C;EAA5C,oBAA4C;EAA5C,mBAA4C;EAA5C,qBAA4C;EAA5C,qBAA4C;EAA5C,sBAA4C;EAA5C,oBAA4C;EAA5C,uBAA4C;EAA5C,kBAA4C;EAA5C,iBAA4C;EAA5C;AAA4C;;AAG9C,iBAAiB;AAEf;EAAA,uBAAqB;EAArB;AAAqB;;AAIrB;EAAA,iBAA+D;EAA/D,sBAA+D;EAA/D,4DAA+D;EAA/D,kBAA+D;EAA/D,4DAA+D;EAA/D,oBAA+D;EAA/D,iDAA+D;EAA/D,uBAA+D;EAA/D;AAA+D;;AAI/D;EAAA,iBAAkE;EAAlE,sBAAkE;EAAlE,4DAAkE;EAAlE,kBAAkE;EAAlE,4DAAkE;EAAlE,oBAAkE;EAAlE,iDAAkE;EAAlE,uBAAkE;EAAlE;AAAkE;;AAIlE;EAAA,iBAAyD;EAAzD,sBAAyD;EAAzD,4DAAyD;EAAzD,kBAAyD;EAAzD,4DAAyD;EAAzD,oBAAyD;EAAzD,iDAAyD;EAAzD,uBAAyD;EAAzD;AAAyD;;AAIzD;EAAA,iBAA4D;EAA5D,sBAA4D;EAA5D,4DAA4D;EAA5D,kBAA4D;EAA5D,4DAA4D;EAA5D,oBAA4D;EAA5D,iDAA4D;EAA5D,uBAA4D;EAA5D;AAA4D;;AAG9D,wBAAwB;AACxB;EACE,kCAAkC;AACpC;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE;IACE,2BAA2B;IAC3B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA,iBAAiB;AACjB;EACE;IACE,wBAAwB;EAC1B;;EAEA;IACE,yBAAyB;EAC3B;;EAEA;IACE,eAAe;IACf,gBAAgB;EAClB;;EAEA;IACE,gBAAgB;IAChB,sBAAsB;EACxB;AACF;;AAEA,4BAA4B;AAE1B;EAAA,iBAA6C;EAA7C,kBAA6C;EAA7C,gBAA6C;EAA7C,kBAA6C;EAA7C;AAA6C;AAA7C;;EAAA;IAAA,oBAA6C;IAA7C;EAA6C;AAAA;AAA7C;;EAAA;IAAA,kBAA6C;IAA7C;EAA6C;AAAA;;AAI7C;EAAA,iBAAoB;EAApB;AAAoB;;AAApB;;EAAA;IAAA,iBAAoB;IAApB;EAAoB;AAAA;;AAIpB;EAAA,uBAA6B;EAA7B,8DAA6B;EAA7B;AAA6B;;AAA7B;;EAAA;IAAA,uBAA6B;IAA7B,4DAA6B;IAA7B;EAA6B;AAAA;;AAG/B,mBAAmB;AACnB;EACE,2CAA2C;AAC7C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,2EAA2E;AAC7E;;AAEA,yBAAyB;AAEvB;EAAA;AAAiB;;AAIjB;EAAA;AAAiB;;AAGnB,wBAAwB;AAEtB;EAAA,8BAA8G;EAA9G,mBAA8G;EAA9G,2GAA8G;EAA9G,yGAA8G;EAA9G,4FAA8G;EAA9G,oBAA8G;EAA9G,4DAA8G;EAA9G,2BAA8G;EAA9G;AAA8G;;AAGhH,uBAAuB;AACvB;EACE,UAAU;EACV,WAAW;AACb;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;AACrB;;AAEA,kCAAkC;AAClC;EAEI;IAAA,kBAAuC;IAAvC,yDAAuC;IAAvC,oBAAuC;IAAvC;EAAuC;;EAIvC;IAAA,sBAAwC;IAAxC,yDAAwC;IAAxC,kBAAwC;IAAxC;EAAwC;;EAIxC;IAAA,sBAA0D;IAA1D,yDAA0D;IAA1D,kBAA0D;IAA1D,yDAA0D;IAA1D,oBAA0D;IAA1D;EAA0D;AAE9D;AA1UA;EAAA,0BA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA,sBA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA,kBA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA,wBA2UA;EA3UA,wDA2UA;EA3UA;AA2UA;AA3UA;EAAA,gBA2UA;EA3UA;AA2UA;AA3UA;EAAA,sBA2UA;EA3UA;AA2UA;AA3UA;EAAA,kBA2UA;EA3UA;AA2UA;AA3UA;EAAA,kBA2UA;EA3UA;AA2UA;AA3UA;EAAA,kBA2UA;EA3UA;AA2UA;AA3UA;EAAA,kBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA,sBA2UA;EA3UA;AA2UA;AA3UA;EAAA,8BA2UA;EA3UA;AA2UA;AA3UA;EAAA,2GA2UA;EA3UA,yGA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;EAAA,kBA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA,sBA2UA;EA3UA;AA2UA;AA3UA;EAAA,0BA2UA;EA3UA,sBA2UA;EA3UA;AA2UA;AA3UA;EAAA,8BA2UA;EA3UA;AA2UA;AA3UA;EAAA,2GA2UA;EA3UA,yGA2UA;EA3UA;AA2UA;AA3UA;EAAA,oBA2UA;EA3UA;AA2UA;AA3UA;;EAAA;IAAA,gBA2UA;IA3UA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA,oBA2UA;IA3UA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA,mBA2UA;IA3UA;EA2UA;AAAA;AA3UA;;EAAA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;AAAA;AA3UA;;EAAA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA;EA2UA;;EA3UA;IAAA,kBA2UA;IA3UA;EA2UA;AAAA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA,sBA2UA;EA3UA;AA2UA;AA3UA;EAAA,sBA2UA;EA3UA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA;AA3UA;EAAA;AA2UA\",\"sourcesContent\":[\"@import 'tailwindcss/base';\\n@import 'tailwindcss/components';\\n@import 'tailwindcss/utilities';\\n\\n/* Import fonts */\\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\\n@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');\\n\\n/* Modern Minimalist Base Styles */\\nhtml {\\n  scroll-behavior: smooth;\\n}\\n\\nbody {\\n  font-family: 'Inter', 'ui-sans-serif', 'system-ui', sans-serif;\\n  line-height: 1.6;\\n  background-color: #fafafa; /* neutral-50 */\\n  color: #171717; /* text-primary */\\n  font-weight: 400;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n/* Arabic font */\\n.font-arabic {\\n  font-family: 'Cairo', sans-serif;\\n}\\n\\n.font-english {\\n  font-family: 'Inter', sans-serif;\\n}\\n\\n/* RTL support */\\n[dir=\\\"rtl\\\"] {\\n  text-align: right;\\n}\\n\\n[dir=\\\"rtl\\\"] .rtl\\\\:text-left {\\n  text-align: left;\\n}\\n\\n[dir=\\\"rtl\\\"] .rtl\\\\:text-right {\\n  text-align: right;\\n}\\n\\n/* Custom scrollbar */\\n::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n/* Modern Form Styles */\\n.form-input {\\n  @apply block w-full px-4 py-3 border border-neutral-200 rounded-xl shadow-soft placeholder-text-tertiary focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-primary-400 sm:text-sm transition-all duration-200 bg-surface-primary;\\n}\\n\\n.form-input:invalid {\\n  @apply border-status-error focus:ring-status-error focus:border-status-error;\\n}\\n\\n.form-label {\\n  @apply block text-sm font-medium text-text-primary mb-2;\\n}\\n\\n.form-error {\\n  @apply mt-1 text-sm text-status-error;\\n}\\n\\n/* Modern Button Styles */\\n.btn {\\n  @apply inline-flex items-center justify-center px-4 py-2.5 border border-transparent text-sm font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-50;\\n}\\n\\n.btn-primary {\\n  @apply btn text-white bg-primary-500 hover:bg-primary-600 focus:ring-primary-400 shadow-soft hover:shadow-medium;\\n}\\n\\n.btn-secondary {\\n  @apply btn text-neutral-700 bg-white border-neutral-200 hover:bg-neutral-50 focus:ring-primary-400 shadow-soft hover:shadow-medium;\\n}\\n\\n.btn-danger {\\n  @apply btn text-white bg-status-error hover:bg-red-600 focus:ring-red-400 shadow-soft hover:shadow-medium;\\n}\\n\\n.btn-success {\\n  @apply btn text-white bg-accent-500 hover:bg-accent-600 focus:ring-accent-400 shadow-soft hover:shadow-medium;\\n}\\n\\n.btn-sm {\\n  @apply px-3 py-1.5 text-xs;\\n}\\n\\n.btn-lg {\\n  @apply px-6 py-3 text-base;\\n}\\n\\n/* Modern Card Styles */\\n.card {\\n  @apply bg-surface-primary shadow-card rounded-2xl border border-neutral-100;\\n}\\n\\n.card-header {\\n  @apply px-6 py-5 border-b border-neutral-100;\\n}\\n\\n.card-body {\\n  @apply px-6 py-5;\\n}\\n\\n.card-footer {\\n  @apply px-6 py-5 border-t border-neutral-100 bg-surface-secondary rounded-b-2xl;\\n}\\n\\n/* Modern Table Styles */\\n.table {\\n  @apply min-w-full divide-y divide-neutral-150;\\n}\\n\\n.table thead {\\n  @apply bg-surface-secondary;\\n}\\n\\n.table th {\\n  @apply px-6 py-4 text-left text-xs font-semibold text-text-secondary uppercase tracking-wider;\\n}\\n\\n.table td {\\n  @apply px-6 py-4 whitespace-nowrap text-sm text-text-primary;\\n}\\n\\n.table tbody tr:nth-child(even) {\\n  @apply bg-surface-secondary;\\n}\\n\\n.table tbody tr:hover {\\n  @apply bg-neutral-75 transition-colors duration-150;\\n}\\n\\n/* Modern Badge Styles */\\n.badge {\\n  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;\\n}\\n\\n.badge-primary {\\n  @apply badge bg-primary-100 text-primary-700;\\n}\\n\\n.badge-secondary {\\n  @apply badge bg-neutral-100 text-neutral-700;\\n}\\n\\n.badge-success {\\n  @apply badge bg-accent-100 text-accent-700;\\n}\\n\\n.badge-warning {\\n  @apply badge bg-yellow-100 text-yellow-700;\\n}\\n\\n.badge-danger {\\n  @apply badge bg-red-100 text-red-700;\\n}\\n\\n.badge-info {\\n  @apply badge bg-primary-100 text-primary-700;\\n}\\n\\n/* Alert styles */\\n.alert {\\n  @apply p-4 rounded-md;\\n}\\n\\n.alert-success {\\n  @apply alert bg-green-50 border border-green-200 text-green-800;\\n}\\n\\n.alert-warning {\\n  @apply alert bg-yellow-50 border border-yellow-200 text-yellow-800;\\n}\\n\\n.alert-danger {\\n  @apply alert bg-red-50 border border-red-200 text-red-800;\\n}\\n\\n.alert-info {\\n  @apply alert bg-blue-50 border border-blue-200 text-blue-800;\\n}\\n\\n/* Animation utilities */\\n.fade-in {\\n  animation: fadeIn 0.5s ease-in-out;\\n}\\n\\n.slide-up {\\n  animation: slideUp 0.3s ease-out;\\n}\\n\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes slideUp {\\n  from {\\n    transform: translateY(10px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n/* Print styles */\\n@media print {\\n  .no-print {\\n    display: none !important;\\n  }\\n  \\n  .print-break {\\n    page-break-before: always;\\n  }\\n  \\n  body {\\n    font-size: 12pt;\\n    line-height: 1.4;\\n  }\\n  \\n  .card {\\n    box-shadow: none;\\n    border: 1px solid #ddd;\\n  }\\n}\\n\\n/* Modern Layout Utilities */\\n.page-container {\\n  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;\\n}\\n\\n.section-spacing {\\n  @apply py-8 lg:py-12;\\n}\\n\\n.content-spacing {\\n  @apply space-y-6 lg:space-y-8;\\n}\\n\\n/* Modern Shadows */\\n.shadow-soft {\\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n.shadow-medium {\\n  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.08);\\n}\\n\\n.shadow-large {\\n  box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.12);\\n}\\n\\n.shadow-card {\\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\\n}\\n\\n/* Enhanced RTL Support */\\n[dir=\\\"rtl\\\"] .table th {\\n  @apply text-right;\\n}\\n\\n[dir=\\\"rtl\\\"] .table td {\\n  @apply text-right;\\n}\\n\\n/* Modern Focus States */\\n.focus-ring {\\n  @apply focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-2 focus:ring-offset-neutral-50;\\n}\\n\\n/* Improved Scrollbar */\\n::-webkit-scrollbar {\\n  width: 8px;\\n  height: 8px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: #f5f5f5;\\n  border-radius: 4px;\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: #d4d4d4;\\n  border-radius: 4px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: #a3a3a3;\\n}\\n\\n/* Dark mode support (if needed) */\\n@media (prefers-color-scheme: dark) {\\n  .dark-mode {\\n    @apply bg-neutral-900 text-text-inverse;\\n  }\\n\\n  .dark-mode .card {\\n    @apply bg-neutral-800 border-neutral-700;\\n  }\\n\\n  .dark-mode .form-input {\\n    @apply bg-neutral-700 border-neutral-600 text-text-inverse;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9jc3MtbG9hZGVyL3NyYy9pbmRleC5qcz8/cnVsZVNldFsxXS5ydWxlc1s3XS5vbmVPZlsxNF0udXNlWzFdIS4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvcG9zdGNzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzddLm9uZU9mWzE0XS51c2VbMl0hLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7O0FBQUE7QUFDd0g7QUFDeEgsOEJBQThCLGtIQUEyQjtBQUN6RDtBQUNBLGdFQUFnRSw2QkFBNkIsNkJBQTZCLHdCQUF3Qix3QkFBd0IsbUJBQW1CLG1CQUFtQixtQkFBbUIsb0JBQW9CLG9CQUFvQixrQkFBa0Isa0JBQWtCLHVCQUF1QiwyQ0FBMkMsbUNBQW1DLGtDQUFrQyxpQ0FBaUMsb0JBQW9CLHlCQUF5QiwyQkFBMkIsNEJBQTRCLDZCQUE2Qix1QkFBdUIsZ0NBQWdDLGlDQUFpQywyQ0FBMkMsdUNBQXVDLGdDQUFnQywyQkFBMkIsbUNBQW1DLGlCQUFpQix1QkFBdUIscUJBQXFCLHNCQUFzQix1QkFBdUIsbUJBQW1CLHFCQUFxQixrQkFBa0Isd0JBQXdCLDBCQUEwQixnQ0FBZ0MsOEJBQThCLCtCQUErQixnQ0FBZ0MsNEJBQTRCLDZCQUE2Qiw4QkFBOEIsMkJBQTJCLHlCQUF5QiwyQkFBMkIsMEJBQTBCLDBCQUEwQixHQUFHLGdCQUFnQiw2QkFBNkIsNkJBQTZCLHdCQUF3Qix3QkFBd0IsbUJBQW1CLG1CQUFtQixtQkFBbUIsb0JBQW9CLG9CQUFvQixrQkFBa0Isa0JBQWtCLHVCQUF1QiwyQ0FBMkMsbUNBQW1DLGtDQUFrQyxpQ0FBaUMsb0JBQW9CLHlCQUF5QiwyQkFBMkIsNEJBQTRCLDZCQUE2Qix1QkFBdUIsZ0NBQWdDLGlDQUFpQywyQ0FBMkMsdUNBQXVDLGdDQUFnQywyQkFBMkIsbUNBQW1DLGlCQUFpQix1QkFBdUIscUJBQXFCLHNCQUFzQix1QkFBdUIsbUJBQW1CLHFCQUFxQixrQkFBa0Isd0JBQXdCLDBCQUEwQixnQ0FBZ0MsOEJBQThCLCtCQUErQixnQ0FBZ0MsNEJBQTRCLDZCQUE2Qiw4QkFBOEIsMkJBQTJCLHlCQUF5QiwyQkFBMkIsMEJBQTBCLDBCQUEwQixHQUFHLGlWQUFpViw0QkFBNEIsNEJBQTRCLGdDQUFnQyxrQ0FBa0MsVUFBVSx3QkFBd0IscUJBQXFCLEdBQUcsK2FBQSthLHNCQUFzQiwyQ0FBMkMsNkJBQTZCLDBCQUEwQixvQkFBb0IscUVBQXFFLDBDQUEwQyw0Q0FBNEMscURBQXFELFVBQVUsZ0tBQWdLLGVBQWUsaUNBQWlDLFVBQVUsMk5BQTJOLGVBQWUsMkJBQTJCLGtDQUFrQyxVQUFVLGlHQUFpRyw4Q0FBOEMsOENBQThDLEdBQUcsa0dBQWtHLHVCQUF1Qix5QkFBeUIsR0FBRyxpRkFBaUYsbUJBQW1CLDZCQUE2QixHQUFHLDJFQUEyRSx3QkFBd0IsR0FBRywwU0FBMFMseUhBQXlILDBDQUEwQyw0Q0FBNEMsMkJBQTJCLFVBQVUsaUVBQWlFLG1CQUFtQixHQUFHLDJHQUEyRyxtQkFBbUIsbUJBQW1CLHVCQUF1Qiw2QkFBNkIsR0FBRyxTQUFTLG9CQUFvQixHQUFHLFNBQVMsZ0JBQWdCLEdBQUcsZ2JBQWdiLG9CQUFvQixrQ0FBa0Msc0NBQXNDLFVBQVUsa01BQWtNLDBCQUEwQiwyQ0FBMkMsNkNBQTZDLDRCQUE0QixpQ0FBaUMsaUNBQWlDLG9DQUFvQywyQkFBMkIsc0JBQXNCLHVCQUF1QixVQUFVLDhGQUE4Rix5QkFBeUIsR0FBRywwTkFBME4sZ0NBQWdDLDBDQUEwQyxtQ0FBbUMsVUFBVSwrRkFBK0Ysa0JBQWtCLEdBQUcsK01BQStNLHFCQUFxQixHQUFHLG1GQUFtRiw2QkFBNkIsR0FBRyxpSkFBaUosaUJBQWlCLEdBQUcsNkhBQTZILG1DQUFtQyxpQ0FBaUMsVUFBVSxvR0FBb0csNkJBQTZCLEdBQUcscUtBQXFLLGdDQUFnQywwQkFBMEIsVUFBVSxzRUFBc0UsdUJBQXVCLEdBQUcsNEpBQTRKLGNBQWMsR0FBRyxjQUFjLGNBQWMsZUFBZSxHQUFHLFlBQVksZUFBZSxHQUFHLG9CQUFvQixxQkFBcUIsY0FBYyxlQUFlLEdBQUcsd0RBQXdELGVBQWUsR0FBRyw2RUFBNkUscUJBQXFCLEdBQUcsa1FBQWtRLGdCQUFnQiwyQkFBMkIsVUFBVSxnREFBZ0QsZ0JBQWdCLDJCQUEyQixVQUFVLCtFQUErRSxvQkFBb0IsR0FBRyxpRkFBaUYsb0JBQW9CLEdBQUcsbWJBQW1iLG9CQUFvQixtQ0FBbUMsVUFBVSx3S0FBd0ssb0JBQW9CLGlCQUFpQixHQUFHLCtIQUErSCxrQkFBa0IsR0FBRyxvUEFBb1AsNkJBQTZCLDZCQUE2Qiw2QkFBNkIsMkJBQTJCLDBCQUEwQixzQkFBc0IsdUJBQXVCLHdCQUF3QiwyQkFBMkIsMkJBQTJCLDBCQUEwQixvQkFBb0Isd0JBQXdCLDJCQUEyQixHQUFHLG1XQUFtVyxtQ0FBbUMsd0JBQXdCLGlEQUFpRCxnQ0FBZ0MsaUNBQWlDLDZCQUE2QixnSEFBZ0gsOEdBQThHLHNGQUFzRiwwQkFBMEIsR0FBRyx5REFBeUQsbUJBQW1CLGVBQWUsR0FBRyw4Q0FBOEMsbUJBQW1CLGVBQWUsR0FBRyw0Q0FBNEMsZUFBZSxHQUFHLG1DQUFtQyxzQkFBc0Isd0JBQXdCLEdBQUcsNkJBQTZCLHlCQUF5QixHQUFHLG9VQUFvVSxtQkFBbUIsc0JBQXNCLEdBQUcsWUFBWSwwUEFBMFAsNkNBQTZDLGlDQUFpQyxpQ0FBaUMsMEJBQTBCLHNDQUFzQyxzQ0FBc0MsR0FBRyx1REFBdUQsOEJBQThCLGlDQUFpQyw2QkFBNkIsNkJBQTZCLDJCQUEyQixzQ0FBc0Msc0NBQXNDLEdBQUcsc0NBQXNDLDZCQUE2Qiw2QkFBNkIsNkJBQTZCLGVBQWUsc0NBQXNDLHNDQUFzQywwQkFBMEIsMkJBQTJCLGtDQUFrQyw4QkFBOEIsOEJBQThCLDhCQUE4QixtQkFBbUIsaUJBQWlCLGdCQUFnQixtQkFBbUIsMkJBQTJCLDBCQUEwQixzQkFBc0IsMkJBQTJCLEdBQUcsdUJBQXVCLHVCQUF1QixHQUFHLG9CQUFvQix3QkFBd0IsR0FBRyxrREFBa0QsbUNBQW1DLHdCQUF3QixpREFBaUQsZ0NBQWdDLGlDQUFpQyw2QkFBNkIsZ0hBQWdILDhHQUE4RyxzRkFBc0YsR0FBRyxzREFBc0QsOEJBQThCLG1DQUFtQywrQkFBK0IsZ0NBQWdDLGlDQUFpQyxHQUFHLCtCQUErQiw2UUFBNlEsR0FBRyxxQ0FBcUMsaUNBQWlDLCtCQUErQiwrQkFBK0IsK0JBQStCLEtBQUssR0FBRyw0QkFBNEIsMktBQTJLLEdBQUcscUNBQXFDLDhCQUE4QiwrQkFBK0IsK0JBQStCLCtCQUErQixLQUFLLEdBQUcsK0hBQStILDhCQUE4QixtQ0FBbUMsR0FBRyxxQ0FBcUMsOE9BQThPLDhCQUE4QixtQ0FBbUMsK0JBQStCLGdDQUFnQyxpQ0FBaUMsR0FBRyxxQ0FBcUMsdUNBQXVDLCtCQUErQiwrQkFBK0IsK0JBQStCLEtBQUssR0FBRyxpRkFBaUYsOEJBQThCLG1DQUFtQyxHQUFHLG1CQUFtQixzQkFBc0IsMEJBQTBCLG9CQUFvQixxQkFBcUIsZUFBZSxxQkFBcUIseUJBQXlCLEdBQUcseUJBQXlCLGtDQUFrQywrQ0FBK0MsR0FBRyw2REFBNkQsNkJBQTZCLDZCQUE2Qiw2QkFBNkIsMkJBQTJCLDBCQUEwQixzQkFBc0IsdUJBQXVCLHdCQUF3QiwyQkFBMkIsMkJBQTJCLDBCQUEwQixvQkFBb0Isd0JBQXdCLDJCQUEyQixHQUFHLHdGQUF3RixtQ0FBbUMsd0JBQXdCLGlEQUFpRCxnQ0FBZ0MsaUNBQWlDLDZCQUE2QixnSEFBZ0gsOEdBQThHLHNGQUFzRiwwQkFBMEIsR0FBRyxtRUFBbUUsbUJBQW1CLGVBQWUsR0FBRyx3REFBd0QsbUJBQW1CLGVBQWUsR0FBRyxxREFBcUQsZUFBZSxHQUFHLDRDQUE0QyxzQkFBc0Isd0JBQXdCLEdBQUcsc0NBQXNDLHlCQUF5QixHQUFHLHFhQUFxYSxtQkFBbUIsc0JBQXNCLEdBQUcsWUFBWSx1QkFBdUIsZUFBZSxnQkFBZ0IsZUFBZSxpQkFBaUIscUJBQXFCLDJCQUEyQix3QkFBd0Isb0JBQW9CLEdBQUcsVUFBVSxvQkFBb0IsR0FBRyxhQUFhLHVCQUF1QixHQUFHLGFBQWEsdUJBQXVCLEdBQUcsWUFBWSxlQUFlLEdBQUcsY0FBYyxhQUFhLGdCQUFnQixHQUFHLGFBQWEsb0JBQW9CLEdBQUcsV0FBVyxrQkFBa0IsR0FBRyxXQUFXLGNBQWMsR0FBRyxXQUFXLGtCQUFrQixHQUFHLFlBQVksZUFBZSxHQUFHLGNBQWMsYUFBYSxHQUFHLFNBQVMsZ0JBQWdCLEdBQUcsU0FBUyxnQkFBZ0IsR0FBRyxZQUFZLHNCQUFzQix1QkFBdUIsR0FBRyxTQUFTLDBCQUEwQixHQUFHLFNBQVMsd0JBQXdCLEdBQUcsU0FBUywwQkFBMEIsR0FBRyxTQUFTLHdCQUF3QixHQUFHLFNBQVMseUJBQXlCLEdBQUcsU0FBUyx3QkFBd0IsR0FBRyxTQUFTLHNCQUFzQixHQUFHLFNBQVMseUJBQXlCLEdBQUcsU0FBUywwQkFBMEIsR0FBRyxTQUFTLHlCQUF5QixHQUFHLFNBQVMsMEJBQTBCLEdBQUcsU0FBUyx3QkFBd0IsR0FBRyxTQUFTLHVCQUF1QixHQUFHLFNBQVMscUJBQXFCLEdBQUcsU0FBUyx1QkFBdUIsR0FBRyxTQUFTLHFCQUFxQixHQUFHLFNBQVMsd0JBQXdCLEdBQUcsYUFBYSx5QkFBeUIsR0FBRyxVQUFVLG1CQUFtQixHQUFHLGlCQUFpQiwwQkFBMEIsR0FBRyxXQUFXLG9CQUFvQixHQUFHLFNBQVMsa0JBQWtCLEdBQUcsZ0JBQWdCLHlCQUF5QixHQUFHLFVBQVUsbUJBQW1CLEdBQUcsU0FBUyxrQkFBa0IsR0FBRyxXQUFXLGtCQUFrQixHQUFHLFNBQVMsaUJBQWlCLEdBQUcsUUFBUSxtQkFBbUIsR0FBRyxTQUFTLGlCQUFpQixHQUFHLFFBQVEsaUJBQWlCLEdBQUcsUUFBUSxvQkFBb0IsR0FBRyxRQUFRLG1CQUFtQixHQUFHLFNBQVMsa0JBQWtCLEdBQUcsUUFBUSxpQkFBaUIsR0FBRyxXQUFXLGlCQUFpQixHQUFHLGFBQWEsc0JBQXNCLEdBQUcsaUJBQWlCLHNCQUFzQixHQUFHLFFBQVEsZUFBZSxHQUFHLFNBQVMsbUJBQW1CLEdBQUcsU0FBUyxnQkFBZ0IsR0FBRyxRQUFRLGtCQUFrQixHQUFHLFNBQVMsZ0JBQWdCLEdBQUcsU0FBUyxnQkFBZ0IsR0FBRyxRQUFRLGdCQUFnQixHQUFHLFNBQVMsaUJBQWlCLEdBQUcsUUFBUSxtQkFBbUIsR0FBRyxRQUFRLGtCQUFrQixHQUFHLFNBQVMsaUJBQWlCLEdBQUcsUUFBUSxnQkFBZ0IsR0FBRyxTQUFTLGlCQUFpQixHQUFHLFdBQVcsZ0JBQWdCLEdBQUcsWUFBWSxtQkFBbUIsR0FBRyxjQUFjLHFCQUFxQixHQUFHLGFBQWEscUJBQXFCLEdBQUcsYUFBYSxxQkFBcUIsR0FBRyxXQUFXLGlCQUFpQixHQUFHLGtCQUFrQixtQkFBbUIsR0FBRyxzQkFBc0IsNEJBQTRCLG9NQUFvTSxHQUFHLHVCQUF1QiwyQkFBMkIsb01BQW9NLEdBQUcsa0JBQWtCLDBCQUEwQixvTUFBb00sR0FBRyxjQUFjLG9NQUFvTSxHQUFHLG1CQUFtQixVQUFVLGdDQUFnQyxLQUFLLEdBQUcsaUJBQWlCLHVDQUF1QyxHQUFHLG1CQUFtQixvQkFBb0IsR0FBRyxvQkFBb0IsNkJBQTZCLDZCQUE2Qiw2QkFBNkIsR0FBRyxnQkFBZ0IscURBQXFELEdBQUcsYUFBYSwyQkFBMkIsR0FBRyxnQkFBZ0IsNEJBQTRCLEdBQUcsY0FBYywwQkFBMEIsR0FBRyxpQkFBaUIsd0JBQXdCLEdBQUcsZ0JBQWdCLDhCQUE4QixHQUFHLG1CQUFtQiw0QkFBNEIsR0FBRyxvQkFBb0IsbUNBQW1DLEdBQUcsVUFBVSxjQUFjLEdBQUcsVUFBVSxpQkFBaUIsR0FBRyxVQUFVLGdCQUFnQixHQUFHLGdEQUFnRCw0QkFBNEIsMkRBQTJELG9FQUFvRSxHQUFHLGdEQUFnRCw0QkFBNEIsNERBQTRELHFFQUFxRSxHQUFHLGdEQUFnRCw0QkFBNEIseURBQXlELGtFQUFrRSxHQUFHLGdEQUFnRCw0QkFBNEIsb0VBQW9FLDZEQUE2RCxHQUFHLGdEQUFnRCw0QkFBNEIsb0VBQW9FLDZEQUE2RCxHQUFHLGdEQUFnRCw0QkFBNEIsaUVBQWlFLDBEQUEwRCxHQUFHLGdEQUFnRCw0QkFBNEIsbUVBQW1FLDREQUE0RCxHQUFHLGdEQUFnRCw0QkFBNEIsaUVBQWlFLDBEQUEwRCxHQUFHLG9CQUFvQixxQkFBcUIsR0FBRyxvQkFBb0IscUJBQXFCLEdBQUcsb0JBQW9CLHFCQUFxQixHQUFHLGFBQWEscUJBQXFCLDRCQUE0Qix3QkFBd0IsR0FBRyxZQUFZLDJCQUEyQixHQUFHLGlCQUFpQiwwQkFBMEIsR0FBRyxlQUFlLDBCQUEwQixHQUFHLGVBQWUsNEJBQTRCLEdBQUcsZUFBZSwyQkFBMkIsR0FBRyxnQkFBZ0Isd0JBQXdCLEdBQUcsV0FBVyxzQkFBc0IsR0FBRyxhQUFhLHNCQUFzQixHQUFHLGFBQWEsNkJBQTZCLEdBQUcsYUFBYSwwQkFBMEIsR0FBRyxvQkFBb0IsMkJBQTJCLGlFQUFpRSxHQUFHLG9CQUFvQiwyQkFBMkIsaUVBQWlFLEdBQUcsb0JBQW9CLDJCQUEyQixpRUFBaUUsR0FBRyx1QkFBdUIsMkJBQTJCLGdFQUFnRSxHQUFHLG1CQUFtQiwyQkFBMkIsaUVBQWlFLEdBQUcsdUJBQXVCLDhCQUE4QixHQUFHLHVCQUF1QiwyQkFBMkIsaUVBQWlFLEdBQUcsdUJBQXVCLDJCQUEyQixpRUFBaUUsR0FBRyxtQkFBbUIsMkJBQTJCLCtEQUErRCxHQUFHLHlCQUF5QiwyQkFBMkIsbUVBQW1FLEdBQUcsYUFBYSx1QkFBdUIsMkRBQTJELEdBQUcsZ0JBQWdCLHVCQUF1QixpRUFBaUUsR0FBRyxlQUFlLHVCQUF1QixpRUFBaUUsR0FBRyxnQkFBZ0IsdUJBQXVCLGdFQUFnRSxHQUFHLG1CQUFtQix1QkFBdUIsZ0VBQWdFLEdBQUcsZ0JBQWdCLHVCQUF1QixpRUFBaUUsR0FBRyxnQkFBZ0IsdUJBQXVCLGlFQUFpRSxHQUFHLGVBQWUsdUJBQXVCLGlFQUFpRSxHQUFHLGlCQUFpQix1QkFBdUIsaUVBQWlFLEdBQUcsZ0JBQWdCLHVCQUF1QixpRUFBaUUsR0FBRyxpQkFBaUIsdUJBQXVCLCtEQUErRCxHQUFHLGtCQUFrQix1QkFBdUIsaUVBQWlFLEdBQUcsa0JBQWtCLHVCQUF1QixnRUFBZ0UsR0FBRyxrQkFBa0IsdUJBQXVCLGlFQUFpRSxHQUFHLGdCQUFnQix1QkFBdUIsZ0VBQWdFLEdBQUcsbUJBQW1CLHVCQUF1QixpRUFBaUUsR0FBRyxrQkFBa0IsdUJBQXVCLGlFQUFpRSxHQUFHLG1CQUFtQix1QkFBdUIsK0RBQStELEdBQUcsa0JBQWtCLHVCQUF1QixpRUFBaUUsR0FBRyxrQkFBa0IsdUJBQXVCLGdFQUFnRSxHQUFHLGVBQWUsdUJBQXVCLGlFQUFpRSxHQUFHLGVBQWUsdUJBQXVCLCtEQUErRCxHQUFHLGFBQWEsdUJBQXVCLGlFQUFpRSxHQUFHLGtCQUFrQix1QkFBdUIsaUVBQWlFLEdBQUcsa0JBQWtCLHVCQUF1QiwrREFBK0QsR0FBRyxrQkFBa0IsdUJBQXVCLGlFQUFpRSxHQUFHLHVCQUF1Qix1QkFBdUIsaUVBQWlFLEdBQUcsb0JBQW9CLHVCQUF1QiwrREFBK0QsR0FBRyxnQkFBZ0IsdUJBQXVCLGlFQUFpRSxHQUFHLGtCQUFrQix5QkFBeUIsR0FBRyxrQkFBa0IsMEJBQTBCLEdBQUcsUUFBUSxxQkFBcUIsR0FBRyxZQUFZLHNCQUFzQixHQUFHLFNBQVMsa0JBQWtCLEdBQUcsUUFBUSxvQkFBb0IsR0FBRyxRQUFRLHFCQUFxQixHQUFHLFFBQVEsa0JBQWtCLEdBQUcsUUFBUSxxQkFBcUIsR0FBRyxRQUFRLG9CQUFvQixHQUFHLFlBQVksc0JBQXNCLEdBQUcsU0FBUyx5QkFBeUIsMEJBQTBCLEdBQUcsYUFBYSwyQkFBMkIsNEJBQTRCLEdBQUcsU0FBUywwQkFBMEIsMkJBQTJCLEdBQUcsU0FBUyx1QkFBdUIsd0JBQXdCLEdBQUcsU0FBUyx5QkFBeUIsMEJBQTBCLEdBQUcsYUFBYSwwQkFBMEIsNkJBQTZCLEdBQUcsU0FBUyx5QkFBeUIsNEJBQTRCLEdBQUcsVUFBVSxzQkFBc0IseUJBQXlCLEdBQUcsU0FBUyx3QkFBd0IsMkJBQTJCLEdBQUcsU0FBUyx5QkFBeUIsNEJBQTRCLEdBQUcsU0FBUyxzQkFBc0IseUJBQXlCLEdBQUcsU0FBUyx5QkFBeUIsNEJBQTRCLEdBQUcsVUFBVSx5QkFBeUIsR0FBRyxVQUFVLDBCQUEwQixHQUFHLFNBQVMsMkJBQTJCLEdBQUcsVUFBVSx5QkFBeUIsR0FBRyxTQUFTLHlCQUF5QixHQUFHLFNBQVMsc0JBQXNCLEdBQUcsU0FBUyx5QkFBeUIsR0FBRyxjQUFjLHFCQUFxQixHQUFHLGdCQUFnQix1QkFBdUIsR0FBRyxpQkFBaUIsMkJBQTJCLEdBQUcsZ0JBQWdCLDZEQUE2RCxHQUFHLGlCQUFpQiw2REFBNkQsR0FBRyxhQUFhLHNCQUFzQixzQkFBc0IsR0FBRyxhQUFhLHdCQUF3Qix5QkFBeUIsR0FBRyxZQUFZLHdCQUF3Qix5QkFBeUIsR0FBRyxZQUFZLHdCQUF3Qix5QkFBeUIsR0FBRyxZQUFZLHVCQUF1QixzQkFBc0IsR0FBRyxjQUFjLHFCQUFxQixHQUFHLG1CQUFtQixxQkFBcUIsR0FBRyxnQkFBZ0IscUJBQXFCLEdBQUcsa0JBQWtCLHFCQUFxQixHQUFHLGVBQWUsK0JBQStCLEdBQUcsY0FBYyx3QkFBd0IsR0FBRyxrQkFBa0IseUJBQXlCLHVEQUF1RCxHQUFHLGtCQUFrQix5QkFBeUIsc0RBQXNELEdBQUcsa0JBQWtCLHlCQUF5QixzREFBc0QsR0FBRyxrQkFBa0IseUJBQXlCLHNEQUFzRCxHQUFHLGtCQUFrQix5QkFBeUIsc0RBQXNELEdBQUcsa0JBQWtCLHlCQUF5Qix3REFBd0QsR0FBRyxrQkFBa0IseUJBQXlCLHdEQUF3RCxHQUFHLGtCQUFrQix5QkFBeUIscURBQXFELEdBQUcsa0JBQWtCLHlCQUF5QixxREFBcUQsR0FBRyxrQkFBa0IseUJBQXlCLHFEQUFxRCxHQUFHLGtCQUFrQix5QkFBeUIscURBQXFELEdBQUcsbUJBQW1CLHlCQUF5Qix1REFBdUQsR0FBRyxtQkFBbUIseUJBQXlCLHNEQUFzRCxHQUFHLG1CQUFtQix5QkFBeUIsc0RBQXNELEdBQUcsbUJBQW1CLHlCQUF5QixzREFBc0QsR0FBRyxtQkFBbUIseUJBQXlCLHFEQUFxRCxHQUFHLG9CQUFvQix5QkFBeUIsc0RBQXNELEdBQUcsb0JBQW9CLHlCQUF5QixzREFBc0QsR0FBRyxvQkFBb0IseUJBQXlCLHNEQUFzRCxHQUFHLHFCQUFxQix5QkFBeUIsc0RBQXNELEdBQUcscUJBQXFCLHlCQUF5QixzREFBc0QsR0FBRyxxQkFBcUIseUJBQXlCLHNEQUFzRCxHQUFHLG9CQUFvQix5QkFBeUIsdURBQXVELEdBQUcsaUJBQWlCLHlCQUF5Qix3REFBd0QsR0FBRyxpQkFBaUIseUJBQXlCLHNEQUFzRCxHQUFHLGlCQUFpQix5QkFBeUIsc0RBQXNELEdBQUcsZUFBZSx5QkFBeUIsd0RBQXdELEdBQUcsb0JBQW9CLHlCQUF5Qix1REFBdUQsR0FBRyxvQkFBb0IseUJBQXlCLHNEQUFzRCxHQUFHLG9CQUFvQix5QkFBeUIsc0RBQXNELEdBQUcsd0JBQXdCLHlCQUF5QixxREFBcUQsR0FBRyxzQkFBc0IseUJBQXlCLHFEQUFxRCxHQUFHLDJDQUEyQyxnQ0FBZ0MsK0RBQStELEdBQUcsc0NBQXNDLGdDQUFnQywrREFBK0QsR0FBRyxXQUFXLCtFQUErRSxtR0FBbUcsNEdBQTRHLEdBQUcsY0FBYyxvRkFBb0Ysd0dBQXdHLDRHQUE0RyxHQUFHLGNBQWMsK0NBQStDLDREQUE0RCw0R0FBNEcsR0FBRyxnQkFBZ0IsaURBQWlELDREQUE0RCw0R0FBNEcsR0FBRyxpQkFBaUIsa0RBQWtELDZEQUE2RCw0R0FBNEcsR0FBRyxjQUFjLHFGQUFxRix5R0FBeUcsNEdBQTRHLEdBQUcsV0FBVyxnSEFBZ0gsOEdBQThHLGlHQUFpRyxHQUFHLGVBQWUseUJBQXlCLDREQUE0RCxHQUFHLG1CQUFtQiw0QkFBNEIsR0FBRyxXQUFXLHNMQUFzTCxHQUFHLG1CQUFtQiw2QkFBNkIsNkRBQTZELCtCQUErQixHQUFHLHNCQUFzQixvR0FBb0csNkRBQTZELCtCQUErQixHQUFHLHlCQUF5QixtQ0FBbUMsNkRBQTZELCtCQUErQixHQUFHLHVCQUF1QixpQ0FBaUMsNkRBQTZELCtCQUErQixHQUFHLGlCQUFpQiwrQkFBK0IsR0FBRyxpQkFBaUIsK0JBQStCLEdBQUcsNkZBQTZGLElBQUksSUFBSSxJQUFJLG1CQUFtQix1RUFBdUUsSUFBSSxJQUFJLElBQUksbUJBQW1CLCtDQUErQyw0QkFBNEIsR0FBRyxVQUFVLG1FQUFtRSxxQkFBcUIsK0JBQStCLG9DQUFvQyx1Q0FBdUMsd0NBQXdDLHVDQUF1QyxHQUFHLHFDQUFxQyxxQ0FBcUMsR0FBRyxtQkFBbUIscUNBQXFDLEdBQUcsc0NBQXNDLHNCQUFzQixHQUFHLG9DQUFvQyxxQkFBcUIsR0FBRyxxQ0FBcUMsc0JBQXNCLEdBQUcsaURBQWlELGVBQWUsZ0JBQWdCLEdBQUcsK0JBQStCLHdCQUF3Qix1QkFBdUIsR0FBRywrQkFBK0Isd0JBQXdCLHVCQUF1QixHQUFHLHFDQUFxQyx3QkFBd0IsR0FBRywyQ0FBMkMsbUJBQW1CLGdCQUFnQiwyQkFBMkIsc0JBQXNCLDJCQUEyQixpRUFBaUUsdUJBQXVCLGlFQUFpRSx1QkFBdUIsd0JBQXdCLHlCQUF5Qiw0QkFBNEIsR0FBRyxpQ0FBaUMsZ0NBQWdDLCtEQUErRCxHQUFHLDRCQUE0QixnQ0FBZ0MsK0RBQStELEdBQUcsZUFBZSxpREFBaUQsNERBQTRELDRHQUE0Ryw2QkFBNkIsNkRBQTZELCtCQUErQixnREFBZ0QsR0FBRyxxQkFBcUIsMkJBQTJCLGdFQUFnRSxtQ0FBbUMsd0JBQXdCLGdIQUFnSCw4R0FBOEcsaUdBQWlHLHlCQUF5QixpRUFBaUUsR0FBRyw2QkFBNkIsbUJBQW1CLDBCQUEwQiwyQkFBMkIsS0FBSyxHQUFHLHlCQUF5QiwyQkFBMkIsK0RBQStELEdBQUcsK0JBQStCLDJCQUEyQiwrREFBK0QseUJBQXlCLGdFQUFnRSxHQUFHLGlCQUFpQiwwQkFBMEIsbUJBQW1CLHdCQUF3Qix5QkFBeUIscUJBQXFCLHlCQUF5QixxREFBcUQsR0FBRyxpQkFBaUIsd0JBQXdCLHdCQUF3Qix5QkFBeUIseUJBQXlCLHNEQUFzRCxHQUFHLHNDQUFzQyx5QkFBeUIsd0JBQXdCLDRCQUE0QiwyQkFBMkIsc0JBQXNCLDhCQUE4Qix1QkFBdUIsd0JBQXdCLDBCQUEwQiw2QkFBNkIsd0JBQXdCLHlCQUF5QixxQkFBcUIsNkJBQTZCLDZEQUE2RCwrQkFBK0IsR0FBRyxjQUFjLG1DQUFtQyx3QkFBd0IsZ0hBQWdILDhHQUE4RyxpR0FBaUcsZ0NBQWdDLG9DQUFvQyxHQUFHLGtCQUFrQix1QkFBdUIsZ0VBQWdFLHlCQUF5Qix3REFBd0QsaURBQWlELDREQUE0RCw0R0FBNEcseUJBQXlCLHdCQUF3Qiw0QkFBNEIsMkJBQTJCLHNCQUFzQiw4QkFBOEIsdUJBQXVCLHdCQUF3QiwwQkFBMEIsNkJBQTZCLHdCQUF3Qix5QkFBeUIscUJBQXFCLDZCQUE2Qiw2REFBNkQsK0JBQStCLEdBQUcsd0JBQXdCLG1DQUFtQyx3QkFBd0IsZ0hBQWdILDhHQUE4RyxpR0FBaUcsZ0NBQWdDLG9DQUFvQyxHQUFHLGtCQUFrQixnREFBZ0QsR0FBRyx3QkFBd0IsdUJBQXVCLCtEQUErRCxrREFBa0QsNkRBQTZELDRHQUE0RyxHQUFHLHdCQUF3Qix5QkFBeUIsaUVBQWlFLEdBQUcsb0JBQW9CLDJCQUEyQixpRUFBaUUsdUJBQXVCLGlFQUFpRSx5QkFBeUIscURBQXFELGlEQUFpRCw0REFBNEQsNEdBQTRHLHlCQUF5Qix3QkFBd0IsNEJBQTRCLDJCQUEyQixzQkFBc0IsOEJBQThCLHVCQUF1Qix3QkFBd0IsMEJBQTBCLDZCQUE2Qix3QkFBd0IseUJBQXlCLHFCQUFxQiw2QkFBNkIsNkRBQTZELCtCQUErQixHQUFHLDBCQUEwQixtQ0FBbUMsd0JBQXdCLGdIQUFnSCw4R0FBOEcsaUdBQWlHLGdDQUFnQyxvQ0FBb0MsR0FBRyxvQkFBb0IsZ0RBQWdELEdBQUcsMEJBQTBCLHVCQUF1QixpRUFBaUUsa0RBQWtELDZEQUE2RCw0R0FBNEcsR0FBRywwQkFBMEIseUJBQXlCLGlFQUFpRSxHQUFHLGlCQUFpQix1QkFBdUIsK0RBQStELHlCQUF5Qix3REFBd0QsaURBQWlELDREQUE0RCw0R0FBNEcseUJBQXlCLHdCQUF3Qiw0QkFBNEIsMkJBQTJCLHNCQUFzQiw4QkFBOEIsdUJBQXVCLHdCQUF3QiwwQkFBMEIsNkJBQTZCLHdCQUF3Qix5QkFBeUIscUJBQXFCLDZCQUE2Qiw2REFBNkQsK0JBQStCLEdBQUcsdUJBQXVCLG1DQUFtQyx3QkFBd0IsZ0hBQWdILDhHQUE4RyxpR0FBaUcsZ0NBQWdDLG9DQUFvQyxHQUFHLGlCQUFpQixnREFBZ0QsR0FBRyx1QkFBdUIsdUJBQXVCLCtEQUErRCxrREFBa0QsNkRBQTZELDRHQUE0RyxHQUFHLHVCQUF1Qix5QkFBeUIsa0VBQWtFLEdBQUcsa0JBQWtCLHVCQUF1QiwrREFBK0QseUJBQXlCLHdEQUF3RCxpREFBaUQsNERBQTRELDRHQUE0Ryx5QkFBeUIsd0JBQXdCLDRCQUE0QiwyQkFBMkIsc0JBQXNCLDhCQUE4Qix1QkFBdUIsd0JBQXdCLDBCQUEwQiw2QkFBNkIsd0JBQXdCLHlCQUF5QixxQkFBcUIsNkJBQTZCLDZEQUE2RCwrQkFBK0IsR0FBRyx3QkFBd0IsbUNBQW1DLHdCQUF3QixnSEFBZ0gsOEdBQThHLGlHQUFpRyxnQ0FBZ0Msb0NBQW9DLEdBQUcsa0JBQWtCLGdEQUFnRCxHQUFHLHdCQUF3Qix1QkFBdUIsK0RBQStELGtEQUFrRCw2REFBNkQsNEdBQTRHLEdBQUcsd0JBQXdCLHlCQUF5QixpRUFBaUUsR0FBRyxhQUFhLDBCQUEwQiwyQkFBMkIsMEJBQTBCLDZCQUE2Qix1QkFBdUIsc0JBQXNCLEdBQUcsYUFBYSx5QkFBeUIsMEJBQTBCLHlCQUF5Qiw0QkFBNEIsb0JBQW9CLHdCQUF3QixHQUFHLHFDQUFxQyx3QkFBd0Isc0JBQXNCLDJCQUEyQixpRUFBaUUsdUJBQXVCLGlFQUFpRSxpRkFBaUYsZ0dBQWdHLDRHQUE0RyxnRkFBZ0YsR0FBRyxrQkFBa0IsNkJBQTZCLDJCQUEyQixpRUFBaUUseUJBQXlCLDBCQUEwQix5QkFBeUIsNEJBQTRCLEdBQUcsZ0JBQWdCLHlCQUF5QiwwQkFBMEIseUJBQXlCLDRCQUE0QixHQUFHLGtCQUFrQixxQ0FBcUMsb0NBQW9DLDBCQUEwQiwyQkFBMkIsaUVBQWlFLHVCQUF1QixpRUFBaUUseUJBQXlCLDBCQUEwQix5QkFBeUIsNEJBQTRCLEdBQUcsdUNBQXVDLG9CQUFvQixHQUFHLDRDQUE0Qyw2QkFBNkIsdUVBQXVFLGdFQUFnRSwyQkFBMkIsaUVBQWlFLEdBQUcsa0JBQWtCLHVCQUF1QixpRUFBaUUsR0FBRyxlQUFlLHlCQUF5QiwwQkFBMEIsc0JBQXNCLHlCQUF5QixxQkFBcUIsdUJBQXVCLHNCQUFzQixxQkFBcUIsOEJBQThCLDJCQUEyQix5QkFBeUIscURBQXFELEdBQUcsZUFBZSx3QkFBd0IseUJBQXlCLDBCQUEwQixzQkFBc0IseUJBQXlCLHdCQUF3Qix5QkFBeUIseUJBQXlCLHFEQUFxRCxHQUFHLHFDQUFxQyx1QkFBdUIsaUVBQWlFLEdBQUcsMkJBQTJCLHVCQUF1QixpRUFBaUUsb0dBQW9HLDZEQUE2RCwrQkFBK0IsR0FBRyx1Q0FBdUMseUJBQXlCLHdCQUF3QiwwQkFBMEIsMEJBQTBCLDJCQUEyQix5QkFBeUIsNEJBQTRCLHVCQUF1QixzQkFBc0IscUJBQXFCLEdBQUcsb0JBQW9CLHVCQUF1QixpRUFBaUUseUJBQXlCLHNEQUFzRCx5QkFBeUIsd0JBQXdCLDBCQUEwQiwwQkFBMEIsMkJBQTJCLHlCQUF5Qiw0QkFBNEIsdUJBQXVCLHNCQUFzQixxQkFBcUIsR0FBRyxzQkFBc0IsdUJBQXVCLGlFQUFpRSx5QkFBeUIscURBQXFELHlCQUF5Qix3QkFBd0IsMEJBQTBCLDBCQUEwQiwyQkFBMkIseUJBQXlCLDRCQUE0Qix1QkFBdUIsc0JBQXNCLHFCQUFxQixHQUFHLG9CQUFvQix1QkFBdUIsaUVBQWlFLHlCQUF5QixzREFBc0QseUJBQXlCLHdCQUF3QiwwQkFBMEIsMEJBQTBCLDJCQUEyQix5QkFBeUIsNEJBQTRCLHVCQUF1QixzQkFBc0IscUJBQXFCLEdBQUcsb0JBQW9CLHVCQUF1QixpRUFBaUUseUJBQXlCLHFEQUFxRCx5QkFBeUIsd0JBQXdCLDBCQUEwQiwwQkFBMEIsMkJBQTJCLHlCQUF5Qiw0QkFBNEIsdUJBQXVCLHNCQUFzQixxQkFBcUIsR0FBRyxtQkFBbUIsdUJBQXVCLGlFQUFpRSx5QkFBeUIsc0RBQXNELHlCQUF5Qix3QkFBd0IsMEJBQTBCLDBCQUEwQiwyQkFBMkIseUJBQXlCLDRCQUE0Qix1QkFBdUIsc0JBQXNCLHFCQUFxQixHQUFHLGlCQUFpQix1QkFBdUIsaUVBQWlFLHlCQUF5QixzREFBc0QseUJBQXlCLHdCQUF3QiwwQkFBMEIsMEJBQTBCLDJCQUEyQix5QkFBeUIsNEJBQTRCLHVCQUF1QixzQkFBc0IscUJBQXFCLEdBQUcsZ0NBQWdDLDRCQUE0QixrQkFBa0IsR0FBRyxvQkFBb0Isc0JBQXNCLDJCQUEyQixpRUFBaUUsdUJBQXVCLGlFQUFpRSx5QkFBeUIsc0RBQXNELDRCQUE0QixrQkFBa0IsR0FBRyxvQkFBb0Isc0JBQXNCLDJCQUEyQixpRUFBaUUsdUJBQXVCLGlFQUFpRSx5QkFBeUIsc0RBQXNELDRCQUE0QixrQkFBa0IsR0FBRyxtQkFBbUIsc0JBQXNCLDJCQUEyQixpRUFBaUUsdUJBQXVCLGlFQUFpRSx5QkFBeUIsc0RBQXNELDRCQUE0QixrQkFBa0IsR0FBRyxpQkFBaUIsc0JBQXNCLDJCQUEyQixpRUFBaUUsdUJBQXVCLGlFQUFpRSx5QkFBeUIsc0RBQXNELDRCQUE0QixrQkFBa0IsR0FBRyx5Q0FBeUMsdUNBQXVDLEdBQUcsZUFBZSxxQ0FBcUMsR0FBRyx1QkFBdUIsVUFBVSxpQkFBaUIsS0FBSyxRQUFRLGlCQUFpQixLQUFLLEdBQUcsd0JBQXdCLFVBQVUsa0NBQWtDLGlCQUFpQixLQUFLLFFBQVEsK0JBQStCLGlCQUFpQixLQUFLLEdBQUcsc0NBQXNDLGVBQWUsK0JBQStCLEtBQUssc0JBQXNCLGdDQUFnQyxLQUFLLGNBQWMsc0JBQXNCLHVCQUF1QixLQUFLLGVBQWUsdUJBQXVCLDZCQUE2QixLQUFLLEdBQUcsb0RBQW9ELHNCQUFzQix1QkFBdUIscUJBQXFCLHVCQUF1Qix3QkFBd0IsR0FBRyw2QkFBNkIsdUJBQXVCLDJCQUEyQiw0QkFBNEIsS0FBSyxHQUFHLDhCQUE4Qix1QkFBdUIseUJBQXlCLDBCQUEwQixLQUFLLEdBQUcsc0JBQXNCLHNCQUFzQix5QkFBeUIsR0FBRyxnQ0FBZ0Msd0JBQXdCLHdCQUF3QiwyQkFBMkIsS0FBSyxHQUFHLHdEQUF3RCw0QkFBNEIsbUVBQW1FLDREQUE0RCxHQUFHLGdDQUFnQywwREFBMEQsOEJBQThCLG1FQUFtRSw0REFBNEQsS0FBSyxHQUFHLHdDQUF3QyxnREFBZ0QsR0FBRyxvQkFBb0IsaURBQWlELEdBQUcsbUJBQW1CLGlEQUFpRCxHQUFHLGtCQUFrQixnRkFBZ0YsR0FBRyx5REFBeUQsc0JBQXNCLEdBQUcsNkJBQTZCLHNCQUFzQixHQUFHLGtEQUFrRCxtQ0FBbUMsd0JBQXdCLGdIQUFnSCw4R0FBOEcsaUdBQWlHLHlCQUF5QixpRUFBaUUsZ0NBQWdDLG9DQUFvQyxHQUFHLG1EQUFtRCxlQUFlLGdCQUFnQixHQUFHLCtCQUErQix3QkFBd0IsdUJBQXVCLEdBQUcsK0JBQStCLHdCQUF3Qix1QkFBdUIsR0FBRyxxQ0FBcUMsd0JBQXdCLEdBQUcsOEVBQThFLGdCQUFnQix5QkFBeUIsZ0VBQWdFLDJCQUEyQiwwREFBMEQsS0FBSyx3QkFBd0IsNkJBQTZCLGdFQUFnRSx5QkFBeUIsZ0VBQWdFLEtBQUssOEJBQThCLDZCQUE2QixnRUFBZ0UseUJBQXlCLGdFQUFnRSwyQkFBMkIsMERBQTBELEtBQUssR0FBRyw0QkFBNEIsK0JBQStCLHVCQUF1QixHQUFHLGtDQUFrQywrQkFBK0IsY0FBYyxHQUFHLGlDQUFpQywrQkFBK0IsYUFBYSxHQUFHLHVCQUF1QiwrQkFBK0Isb0JBQW9CLEdBQUcsdUJBQXVCLCtCQUErQixtQkFBbUIsR0FBRyxnQ0FBZ0MsK0JBQStCLDBCQUEwQixHQUFHLDBCQUEwQiwrQkFBK0Isc0JBQXNCLEdBQUcsbUNBQW1DLCtCQUErQiwyQkFBMkIsaUVBQWlFLEdBQUcsNEJBQTRCLCtCQUErQix1QkFBdUIsaUVBQWlFLEdBQUcsa0NBQWtDLCtCQUErQiw2QkFBNkIsNkRBQTZELCtCQUErQixHQUFHLHdDQUF3QyxxQkFBcUIsK0JBQStCLEdBQUcsa0NBQWtDLDJCQUEyQixpRUFBaUUsR0FBRyw4QkFBOEIsdUJBQXVCLGlFQUFpRSxHQUFHLDZCQUE2Qix1QkFBdUIsaUVBQWlFLEdBQUcsaUNBQWlDLHVCQUF1QiwrREFBK0QsR0FBRyxnQ0FBZ0MsdUJBQXVCLGlFQUFpRSxHQUFHLGdDQUFnQyx5QkFBeUIsc0RBQXNELEdBQUcsZ0NBQWdDLHlCQUF5QixxREFBcUQsR0FBRyxpQ0FBaUMseUJBQXlCLHNEQUFzRCxHQUFHLCtCQUErQix5QkFBeUIsc0RBQXNELEdBQUcsb0NBQW9DLHlCQUF5QixxREFBcUQsR0FBRyxnQ0FBZ0MseUJBQXlCLHFEQUFxRCxHQUFHLHVCQUF1QixnQkFBZ0IsR0FBRyxxQ0FBcUMsMkJBQTJCLGdFQUFnRSxHQUFHLCtCQUErQixtQ0FBbUMsd0JBQXdCLEdBQUcseUJBQXlCLGdIQUFnSCw4R0FBOEcsaUdBQWlHLEdBQUcsbUNBQW1DLHlCQUF5QixpRUFBaUUsR0FBRyxnQ0FBZ0MsZ0NBQWdDLEdBQUcsMkNBQTJDLHdCQUF3QixHQUFHLG1DQUFtQyxpQkFBaUIsR0FBRyw2Q0FBNkMseUJBQXlCLHdEQUF3RCxHQUFHLGtEQUFrRCx1QkFBdUIsK0RBQStELEdBQUcsbUVBQW1FLCtCQUErQiwyQkFBMkIsb01BQW9NLEdBQUcsK0RBQStELCtCQUErQiwyQkFBMkIsaUVBQWlFLEdBQUcsNENBQTRDLG1DQUFtQyx3QkFBd0IsR0FBRyxzQ0FBc0MsZ0hBQWdILDhHQUE4RyxpR0FBaUcsR0FBRyxnREFBZ0QseUJBQXlCLGtFQUFrRSxHQUFHLDZCQUE2QixrQkFBa0IsdUJBQXVCLDBCQUEwQixLQUFLLGtCQUFrQiwyQkFBMkIsS0FBSyxrQkFBa0Isc0JBQXNCLEtBQUssbUJBQW1CLHFCQUFxQixLQUFLLGtCQUFrQixvQkFBb0IsS0FBSyxvQkFBb0Isa0JBQWtCLEtBQUssb0JBQW9CLGtCQUFrQixLQUFLLHVCQUF1Qix1QkFBdUIsS0FBSyxzQkFBc0IsdUJBQXVCLEtBQUsseUJBQXlCLHVEQUF1RCxLQUFLLHNCQUFzQiwwQkFBMEIsS0FBSyw4QkFBOEIsa0NBQWtDLEtBQUssaUJBQWlCLHNCQUFzQixLQUFLLGlCQUFpQixtQkFBbUIsS0FBSyxrQkFBa0IsMkJBQTJCLDRCQUE0QixLQUFLLGtCQUFrQiwyQkFBMkIsS0FBSywwQkFBMEIsNkJBQTZCLEtBQUsscUJBQXFCLDBCQUEwQiwyQkFBMkIsS0FBSyxHQUFHLDZCQUE2Qix3QkFBd0IsbUNBQW1DLEtBQUsseUJBQXlCLHVEQUF1RCxLQUFLLEdBQUcsOEJBQThCLG1CQUFtQix3QkFBd0IsS0FBSyxtQkFBbUIseUJBQXlCLEtBQUssbUJBQW1CLHlCQUF5QixLQUFLLG1CQUFtQiwwQkFBMEIsS0FBSyxtQkFBbUIscUJBQXFCLEtBQUssb0JBQW9CLG9CQUFvQixLQUFLLHFCQUFxQixpQkFBaUIsS0FBSyxxQkFBcUIsaUJBQWlCLEtBQUsseUJBQXlCLHVEQUF1RCxLQUFLLHlCQUF5Qix1REFBdUQsS0FBSyx5QkFBeUIsdURBQXVELEtBQUssc0JBQXNCLDBCQUEwQixLQUFLLGtCQUFrQix5QkFBeUIsMEJBQTBCLEtBQUssR0FBRyx1REFBdUQsY0FBYyxHQUFHLDBEQUEwRCxlQUFlLEdBQUcsd0RBQXdELGVBQWUsR0FBRyx3REFBd0QsbUJBQW1CLEdBQUcsMkRBQTJELGdCQUFnQixHQUFHLHFEQUFxRCxxQkFBcUIsR0FBRyxxREFBcUQseUJBQXlCLEdBQUcscURBQXFELHdCQUF3QixHQUFHLHFEQUFxRCx5QkFBeUIsR0FBRyxxREFBcUQsc0JBQXNCLEdBQUcscURBQXFELDBCQUEwQixHQUFHLHFEQUFxRCx5QkFBeUIsR0FBRyxxREFBcUQsdUJBQXVCLEdBQUcscURBQXFELDBCQUEwQixHQUFHLCtEQUErRCwyQkFBMkIsb01BQW9NLEdBQUcsaUVBQWlFLDJCQUEyQixvTUFBb00sR0FBRyxrR0FBa0csNEJBQTRCLEdBQUcsc0RBQXNELHlCQUF5QixHQUFHLHFEQUFxRCwwQkFBMEIsR0FBRyxxREFBcUQsdUJBQXVCLEdBQUcsc0RBQXNELDBCQUEwQixHQUFHLHFEQUFxRCwyQkFBMkIsR0FBRyxTQUFTLG1GQUFtRixZQUFZLGFBQWEsYUFBYSxhQUFhLFlBQVksWUFBWSxZQUFZLFlBQVksWUFBWSxZQUFZLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLFlBQVksYUFBYSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxZQUFZLFlBQVksWUFBWSxZQUFZLFlBQVksWUFBWSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxZQUFZLGFBQWEsWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sYUFBYSxjQUFjLE9BQU8sUUFBUSx3QkFBd0Isd0JBQXdCLHlCQUF5Qix5QkFBeUIsTUFBTSxPQUFPLFlBQVksTUFBTSxhQUFhLE9BQU8sT0FBTyx3QkFBd0IseUJBQXlCLHlCQUF5QixZQUFZLHdCQUF3Qix5QkFBeUIseUJBQXlCLHlCQUF5Qix5QkFBeUIsTUFBTSxRQUFRLE9BQU8sTUFBTSx1QkFBdUIseUJBQXlCLE1BQU0sU0FBUyxPQUFPLE1BQU0sdUJBQXVCLHdCQUF3Qix5QkFBeUIsTUFBTSxPQUFPLE9BQU8sTUFBTSxZQUFZLGFBQWEsTUFBTSxPQUFPLE9BQU8sV0FBVyxZQUFZLGFBQWEsTUFBTSxPQUFPLE9BQU8sTUFBTSxXQUFXLGFBQWEsTUFBTSxPQUFPLE9BQU8sT0FBTyxZQUFZLE1BQU0sVUFBVSxPQUFPLFNBQVMsd0JBQXdCLHlCQUF5Qix5QkFBeUIsd0JBQXdCLE1BQU0sT0FBTyxPQUFPLE1BQU0sV0FBVyxNQUFNLE9BQU8sT0FBTyxPQUFPLFdBQVcsWUFBWSxhQUFhLGFBQWEsTUFBTSxNQUFNLFdBQVcsTUFBTSxNQUFNLFdBQVcsTUFBTSxTQUFTLE9BQU8sTUFBTSx1QkFBdUIseUJBQXlCLHlCQUF5QixNQUFNLFNBQVMsT0FBTyxVQUFVLHdCQUF3Qix5QkFBeUIseUJBQXlCLHdCQUF3Qix5QkFBeUIseUJBQXlCLHlCQUF5Qix3QkFBd0Isd0JBQXdCLHdCQUF3QixNQUFNLE9BQU8sT0FBTyxPQUFPLFlBQVksTUFBTSxRQUFRLE9BQU8sU0FBUyx3QkFBd0IseUJBQXlCLHlCQUF5QixNQUFNLE9BQU8sT0FBTyxNQUFNLFdBQVcsTUFBTSxPQUFPLE9BQU8sTUFBTSxZQUFZLE1BQU0sT0FBTyxPQUFPLE1BQU0sWUFBWSxNQUFNLE9BQU8sT0FBTyxPQUFPLFdBQVcsTUFBTSxRQUFRLE9BQU8sTUFBTSx3QkFBd0IseUJBQXlCLE1BQU0sT0FBTyxPQUFPLE1BQU0sWUFBWSxNQUFNLFFBQVEsT0FBTyxNQUFNLHdCQUF3Qix3QkFBd0IsTUFBTSxPQUFPLE9BQU8sTUFBTSxZQUFZLE1BQU0sT0FBTyxPQUFPLGtCQUFrQixXQUFXLE1BQU0sTUFBTSxXQUFXLFlBQVksTUFBTSxNQUFNLFdBQVcsTUFBTSxRQUFRLFlBQVksWUFBWSxZQUFZLE1BQU0sT0FBTyxNQUFNLE1BQU0sV0FBVyxNQUFNLE9BQU8sT0FBTyxNQUFNLFlBQVksTUFBTSxRQUFRLE9BQU8sTUFBTSx1QkFBdUIsd0JBQXdCLE1BQU0sT0FBTyx1QkFBdUIsd0JBQXdCLE1BQU0sT0FBTyxPQUFPLE9BQU8sV0FBVyxNQUFNLE9BQU8sTUFBTSxNQUFNLFdBQVcsTUFBTSxTQUFTLE9BQU8sYUFBYSx1QkFBdUIseUJBQXlCLE1BQU0sT0FBTyxPQUFPLE9BQU8sV0FBVyxZQUFZLE1BQU0sYUFBYSxNQUFNLFdBQVcsTUFBTSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxZQUFZLGFBQWEsYUFBYSxNQUFNLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sV0FBVyxNQUFNLE9BQU8sTUFBTSxXQUFXLE1BQU0sT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sV0FBVyxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxZQUFZLFlBQVksWUFBWSxZQUFZLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLEtBQUssWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE9BQU8sS0FBSyxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxLQUFLLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLFlBQVksYUFBYSxZQUFZLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsWUFBWSxhQUFhLGFBQWEsS0FBSyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sTUFBTSxNQUFNLFdBQVcsTUFBTSxNQUFNLE1BQU0sV0FBVyxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFdBQVcsTUFBTSxNQUFNLE1BQU0sWUFBWSxZQUFZLFlBQVksWUFBWSxZQUFZLGFBQWEsYUFBYSxhQUFhLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFdBQVcsTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sT0FBTyxLQUFLLEtBQUssTUFBTSxLQUFLLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxPQUFPLGFBQWEsYUFBYSxjQUFjLGFBQWEsTUFBTSxZQUFZLE9BQU8sS0FBSyxZQUFZLGFBQWEsdUJBQXVCLHVCQUF1QixhQUFhLGFBQWEsYUFBYSxPQUFPLFlBQVksTUFBTSxZQUFZLE9BQU8sS0FBSyxZQUFZLE9BQU8sWUFBWSxNQUFNLFlBQVksT0FBTyxLQUFLLFlBQVksT0FBTyxLQUFLLFlBQVksT0FBTyxZQUFZLE1BQU0sVUFBVSxVQUFVLE1BQU0sS0FBSyxZQUFZLGFBQWEsT0FBTyxLQUFLLFlBQVksYUFBYSxPQUFPLEtBQUssWUFBWSxPQUFPLFlBQVksTUFBTSxXQUFXLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE1BQU0sT0FBTyxLQUFLLFlBQVksTUFBTSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLGFBQWEsTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksTUFBTSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLE1BQU0sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLE1BQU0sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLE1BQU0sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsWUFBWSxNQUFNLE9BQU8sYUFBYSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLGFBQWEsTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLGFBQWEsTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sYUFBYSxLQUFLLFlBQVksTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxhQUFhLE1BQU0sWUFBWSxPQUFPLEtBQUssWUFBWSxPQUFPLEtBQUssS0FBSyxVQUFVLEtBQUssS0FBSyxVQUFVLEtBQUssTUFBTSxLQUFLLEtBQUssWUFBWSxXQUFXLEtBQUssS0FBSyxZQUFZLFdBQVcsS0FBSyxNQUFNLFlBQVksTUFBTSxLQUFLLFlBQVksT0FBTyxLQUFLLFlBQVksT0FBTyxLQUFLLFVBQVUsWUFBWSxPQUFPLEtBQUssWUFBWSxhQUFhLE1BQU0sTUFBTSxZQUFZLE1BQU0sWUFBWSxhQUFhLGFBQWEsYUFBYSxNQUFNLE1BQU0sT0FBTyxLQUFLLFlBQVksTUFBTSxNQUFNLEtBQUssT0FBTyxLQUFLLFlBQVksTUFBTSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sT0FBTyxPQUFPLEtBQUssWUFBWSxNQUFNLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE9BQU8sT0FBTyxLQUFLLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxhQUFhLE1BQU0sWUFBWSxPQUFPLEtBQUssWUFBWSxPQUFPLEtBQUssWUFBWSxPQUFPLEtBQUssWUFBWSxPQUFPLFlBQVksTUFBTSxLQUFLLE9BQU8sTUFBTSxLQUFLLE9BQU8sYUFBYSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE9BQU8sYUFBYSxNQUFNLFVBQVUsVUFBVSxNQUFNLEtBQUssWUFBWSxhQUFhLE9BQU8sS0FBSyxZQUFZLGFBQWEsT0FBTyxLQUFLLFlBQVksT0FBTyxZQUFZLE1BQU0sS0FBSyxZQUFZLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxNQUFNLE9BQU8sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE9BQU8sS0FBSyxZQUFZLE1BQU0sT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sWUFBWSxNQUFNLE1BQU0sS0FBSyxPQUFPLEtBQUssS0FBSyxPQUFPLE1BQU0sS0FBSyxNQUFNLEtBQUssT0FBTyxLQUFLLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLFlBQVksTUFBTSxNQUFNLEtBQUssTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxxREFBcUQsbUNBQW1DLGtDQUFrQyw2RkFBNkYsSUFBSSxJQUFJLElBQUksbUJBQW1CLHVFQUF1RSxJQUFJLElBQUksSUFBSSxtQkFBbUIsK0NBQStDLDRCQUE0QixHQUFHLFVBQVUsbUVBQW1FLHFCQUFxQiwrQkFBK0Isb0NBQW9DLHVDQUF1Qyx3Q0FBd0MsdUNBQXVDLEdBQUcscUNBQXFDLHFDQUFxQyxHQUFHLG1CQUFtQixxQ0FBcUMsR0FBRyxzQ0FBc0Msc0JBQXNCLEdBQUcsb0NBQW9DLHFCQUFxQixHQUFHLHFDQUFxQyxzQkFBc0IsR0FBRyxpREFBaUQsZUFBZSxnQkFBZ0IsR0FBRywrQkFBK0Isd0JBQXdCLHVCQUF1QixHQUFHLCtCQUErQix3QkFBd0IsdUJBQXVCLEdBQUcscUNBQXFDLHdCQUF3QixHQUFHLDJDQUEyQyx1UEFBdVAsR0FBRyx5QkFBeUIsaUZBQWlGLEdBQUcsaUJBQWlCLDREQUE0RCxHQUFHLGlCQUFpQiwwQ0FBMEMsR0FBRyxzQ0FBc0MscU9BQXFPLEdBQUcsa0JBQWtCLHFIQUFxSCxHQUFHLG9CQUFvQix1SUFBdUksR0FBRyxpQkFBaUIsOEdBQThHLEdBQUcsa0JBQWtCLGtIQUFrSCxHQUFHLGFBQWEsK0JBQStCLEdBQUcsYUFBYSwrQkFBK0IsR0FBRyxxQ0FBcUMsZ0ZBQWdGLEdBQUcsa0JBQWtCLGlEQUFpRCxHQUFHLGdCQUFnQixxQkFBcUIsR0FBRyxrQkFBa0Isb0ZBQW9GLEdBQUcsdUNBQXVDLGtEQUFrRCxHQUFHLGtCQUFrQixnQ0FBZ0MsR0FBRyxlQUFlLGtHQUFrRyxHQUFHLGVBQWUsaUVBQWlFLEdBQUcscUNBQXFDLGdDQUFnQyxHQUFHLDJCQUEyQix3REFBd0QsR0FBRyx1Q0FBdUMsK0VBQStFLEdBQUcsb0JBQW9CLGlEQUFpRCxHQUFHLHNCQUFzQixpREFBaUQsR0FBRyxvQkFBb0IsK0NBQStDLEdBQUcsb0JBQW9CLCtDQUErQyxHQUFHLG1CQUFtQix5Q0FBeUMsR0FBRyxpQkFBaUIsaURBQWlELEdBQUcsZ0NBQWdDLDBCQUEwQixHQUFHLG9CQUFvQixvRUFBb0UsR0FBRyxvQkFBb0IsdUVBQXVFLEdBQUcsbUJBQW1CLDhEQUE4RCxHQUFHLGlCQUFpQixpRUFBaUUsR0FBRyx5Q0FBeUMsdUNBQXVDLEdBQUcsZUFBZSxxQ0FBcUMsR0FBRyx1QkFBdUIsVUFBVSxpQkFBaUIsS0FBSyxRQUFRLGlCQUFpQixLQUFLLEdBQUcsd0JBQXdCLFVBQVUsa0NBQWtDLGlCQUFpQixLQUFLLFFBQVEsK0JBQStCLGlCQUFpQixLQUFLLEdBQUcsc0NBQXNDLGVBQWUsK0JBQStCLEtBQUssc0JBQXNCLGdDQUFnQyxLQUFLLGNBQWMsc0JBQXNCLHVCQUF1QixLQUFLLGVBQWUsdUJBQXVCLDZCQUE2QixLQUFLLEdBQUcsb0RBQW9ELGtEQUFrRCxHQUFHLHNCQUFzQix5QkFBeUIsR0FBRyxzQkFBc0Isa0NBQWtDLEdBQUcsd0NBQXdDLGdEQUFnRCxHQUFHLG9CQUFvQixpREFBaUQsR0FBRyxtQkFBbUIsaURBQWlELEdBQUcsa0JBQWtCLGdGQUFnRixHQUFHLHlEQUF5RCxzQkFBc0IsR0FBRyw2QkFBNkIsc0JBQXNCLEdBQUcsNENBQTRDLG1IQUFtSCxHQUFHLG1EQUFtRCxlQUFlLGdCQUFnQixHQUFHLCtCQUErQix3QkFBd0IsdUJBQXVCLEdBQUcsK0JBQStCLHdCQUF3Qix1QkFBdUIsR0FBRyxxQ0FBcUMsd0JBQXdCLEdBQUcsOEVBQThFLGdCQUFnQiw4Q0FBOEMsS0FBSyx3QkFBd0IsK0NBQStDLEtBQUssOEJBQThCLGlFQUFpRSxLQUFLLEdBQUcscUJBQXFCO0FBQzk5a0c7QUFDQSwrREFBZSx1QkFBdUIsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zdHlsZXMvZ2xvYmFscy5jc3M/OGM1MyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBJbXBvcnRzXG5pbXBvcnQgX19fQ1NTX0xPQURFUl9BUElfSU1QT1JUX19fIGZyb20gXCIuLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9jc3MtbG9hZGVyL3NyYy9ydW50aW1lL2FwaS5qc1wiO1xudmFyIF9fX0NTU19MT0FERVJfRVhQT1JUX19fID0gX19fQ1NTX0xPQURFUl9BUElfSU1QT1JUX19fKHRydWUpO1xuLy8gTW9kdWxlXG5fX19DU1NfTE9BREVSX0VYUE9SVF9fXy5wdXNoKFttb2R1bGUuaWQsIFwiKiwgOjpiZWZvcmUsIDo6YWZ0ZXIge1xcbiAgLS10dy1ib3JkZXItc3BhY2luZy14OiAwO1xcbiAgLS10dy1ib3JkZXItc3BhY2luZy15OiAwO1xcbiAgLS10dy10cmFuc2xhdGUteDogMDtcXG4gIC0tdHctdHJhbnNsYXRlLXk6IDA7XFxuICAtLXR3LXJvdGF0ZTogMDtcXG4gIC0tdHctc2tldy14OiAwO1xcbiAgLS10dy1za2V3LXk6IDA7XFxuICAtLXR3LXNjYWxlLXg6IDE7XFxuICAtLXR3LXNjYWxlLXk6IDE7XFxuICAtLXR3LXBhbi14OiAgO1xcbiAgLS10dy1wYW4teTogIDtcXG4gIC0tdHctcGluY2gtem9vbTogIDtcXG4gIC0tdHctc2Nyb2xsLXNuYXAtc3RyaWN0bmVzczogcHJveGltaXR5O1xcbiAgLS10dy1ncmFkaWVudC1mcm9tLXBvc2l0aW9uOiAgO1xcbiAgLS10dy1ncmFkaWVudC12aWEtcG9zaXRpb246ICA7XFxuICAtLXR3LWdyYWRpZW50LXRvLXBvc2l0aW9uOiAgO1xcbiAgLS10dy1vcmRpbmFsOiAgO1xcbiAgLS10dy1zbGFzaGVkLXplcm86ICA7XFxuICAtLXR3LW51bWVyaWMtZmlndXJlOiAgO1xcbiAgLS10dy1udW1lcmljLXNwYWNpbmc6ICA7XFxuICAtLXR3LW51bWVyaWMtZnJhY3Rpb246ICA7XFxuICAtLXR3LXJpbmctaW5zZXQ6ICA7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXdpZHRoOiAwcHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LWNvbG9yOiAjZmZmO1xcbiAgLS10dy1yaW5nLWNvbG9yOiByZ2IoNTkgMTMwIDI0NiAvIDAuNSk7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdzogMCAwICMwMDAwO1xcbiAgLS10dy1yaW5nLXNoYWRvdzogMCAwICMwMDAwO1xcbiAgLS10dy1zaGFkb3c6IDAgMCAjMDAwMDtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMCAjMDAwMDtcXG4gIC0tdHctYmx1cjogIDtcXG4gIC0tdHctYnJpZ2h0bmVzczogIDtcXG4gIC0tdHctY29udHJhc3Q6ICA7XFxuICAtLXR3LWdyYXlzY2FsZTogIDtcXG4gIC0tdHctaHVlLXJvdGF0ZTogIDtcXG4gIC0tdHctaW52ZXJ0OiAgO1xcbiAgLS10dy1zYXR1cmF0ZTogIDtcXG4gIC0tdHctc2VwaWE6ICA7XFxuICAtLXR3LWRyb3Atc2hhZG93OiAgO1xcbiAgLS10dy1iYWNrZHJvcC1ibHVyOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1icmlnaHRuZXNzOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1jb250cmFzdDogIDtcXG4gIC0tdHctYmFja2Ryb3AtZ3JheXNjYWxlOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1odWUtcm90YXRlOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1pbnZlcnQ6ICA7XFxuICAtLXR3LWJhY2tkcm9wLW9wYWNpdHk6ICA7XFxuICAtLXR3LWJhY2tkcm9wLXNhdHVyYXRlOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1zZXBpYTogIDtcXG4gIC0tdHctY29udGFpbi1zaXplOiAgO1xcbiAgLS10dy1jb250YWluLWxheW91dDogIDtcXG4gIC0tdHctY29udGFpbi1wYWludDogIDtcXG4gIC0tdHctY29udGFpbi1zdHlsZTogIDtcXG59XFxuXFxuOjpiYWNrZHJvcCB7XFxuICAtLXR3LWJvcmRlci1zcGFjaW5nLXg6IDA7XFxuICAtLXR3LWJvcmRlci1zcGFjaW5nLXk6IDA7XFxuICAtLXR3LXRyYW5zbGF0ZS14OiAwO1xcbiAgLS10dy10cmFuc2xhdGUteTogMDtcXG4gIC0tdHctcm90YXRlOiAwO1xcbiAgLS10dy1za2V3LXg6IDA7XFxuICAtLXR3LXNrZXcteTogMDtcXG4gIC0tdHctc2NhbGUteDogMTtcXG4gIC0tdHctc2NhbGUteTogMTtcXG4gIC0tdHctcGFuLXg6ICA7XFxuICAtLXR3LXBhbi15OiAgO1xcbiAgLS10dy1waW5jaC16b29tOiAgO1xcbiAgLS10dy1zY3JvbGwtc25hcC1zdHJpY3RuZXNzOiBwcm94aW1pdHk7XFxuICAtLXR3LWdyYWRpZW50LWZyb20tcG9zaXRpb246ICA7XFxuICAtLXR3LWdyYWRpZW50LXZpYS1wb3NpdGlvbjogIDtcXG4gIC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb246ICA7XFxuICAtLXR3LW9yZGluYWw6ICA7XFxuICAtLXR3LXNsYXNoZWQtemVybzogIDtcXG4gIC0tdHctbnVtZXJpYy1maWd1cmU6ICA7XFxuICAtLXR3LW51bWVyaWMtc3BhY2luZzogIDtcXG4gIC0tdHctbnVtZXJpYy1mcmFjdGlvbjogIDtcXG4gIC0tdHctcmluZy1pbnNldDogIDtcXG4gIC0tdHctcmluZy1vZmZzZXQtd2lkdGg6IDBweDtcXG4gIC0tdHctcmluZy1vZmZzZXQtY29sb3I6ICNmZmY7XFxuICAtLXR3LXJpbmctY29sb3I6IHJnYig1OSAxMzAgMjQ2IC8gMC41KTtcXG4gIC0tdHctcmluZy1vZmZzZXQtc2hhZG93OiAwIDAgIzAwMDA7XFxuICAtLXR3LXJpbmctc2hhZG93OiAwIDAgIzAwMDA7XFxuICAtLXR3LXNoYWRvdzogMCAwICMwMDAwO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCAwICMwMDAwO1xcbiAgLS10dy1ibHVyOiAgO1xcbiAgLS10dy1icmlnaHRuZXNzOiAgO1xcbiAgLS10dy1jb250cmFzdDogIDtcXG4gIC0tdHctZ3JheXNjYWxlOiAgO1xcbiAgLS10dy1odWUtcm90YXRlOiAgO1xcbiAgLS10dy1pbnZlcnQ6ICA7XFxuICAtLXR3LXNhdHVyYXRlOiAgO1xcbiAgLS10dy1zZXBpYTogIDtcXG4gIC0tdHctZHJvcC1zaGFkb3c6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWJsdXI6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWJyaWdodG5lc3M6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWNvbnRyYXN0OiAgO1xcbiAgLS10dy1iYWNrZHJvcC1ncmF5c2NhbGU6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWh1ZS1yb3RhdGU6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWludmVydDogIDtcXG4gIC0tdHctYmFja2Ryb3Atb3BhY2l0eTogIDtcXG4gIC0tdHctYmFja2Ryb3Atc2F0dXJhdGU6ICA7XFxuICAtLXR3LWJhY2tkcm9wLXNlcGlhOiAgO1xcbiAgLS10dy1jb250YWluLXNpemU6ICA7XFxuICAtLXR3LWNvbnRhaW4tbGF5b3V0OiAgO1xcbiAgLS10dy1jb250YWluLXBhaW50OiAgO1xcbiAgLS10dy1jb250YWluLXN0eWxlOiAgO1xcbn0vKlxcbiEgdGFpbHdpbmRjc3MgdjMuNC4xNyB8IE1JVCBMaWNlbnNlIHwgaHR0cHM6Ly90YWlsd2luZGNzcy5jb21cXG4qLy8qXFxuMS4gUHJldmVudCBwYWRkaW5nIGFuZCBib3JkZXIgZnJvbSBhZmZlY3RpbmcgZWxlbWVudCB3aWR0aC4gKGh0dHBzOi8vZ2l0aHViLmNvbS9tb3pkZXZzL2Nzc3JlbWVkeS9pc3N1ZXMvNClcXG4yLiBBbGxvdyBhZGRpbmcgYSBib3JkZXIgdG8gYW4gZWxlbWVudCBieSBqdXN0IGFkZGluZyBhIGJvcmRlci13aWR0aC4gKGh0dHBzOi8vZ2l0aHViLmNvbS90YWlsd2luZGNzcy90YWlsd2luZGNzcy9wdWxsLzExNilcXG4qL1xcblxcbiosXFxuOjpiZWZvcmUsXFxuOjphZnRlciB7XFxuICBib3gtc2l6aW5nOiBib3JkZXItYm94OyAvKiAxICovXFxuICBib3JkZXItd2lkdGg6IDA7IC8qIDIgKi9cXG4gIGJvcmRlci1zdHlsZTogc29saWQ7IC8qIDIgKi9cXG4gIGJvcmRlci1jb2xvcjogI2U1ZTdlYjsgLyogMiAqL1xcbn1cXG5cXG46OmJlZm9yZSxcXG46OmFmdGVyIHtcXG4gIC0tdHctY29udGVudDogJyc7XFxufVxcblxcbi8qXFxuMS4gVXNlIGEgY29uc2lzdGVudCBzZW5zaWJsZSBsaW5lLWhlaWdodCBpbiBhbGwgYnJvd3NlcnMuXFxuMi4gUHJldmVudCBhZGp1c3RtZW50cyBvZiBmb250IHNpemUgYWZ0ZXIgb3JpZW50YXRpb24gY2hhbmdlcyBpbiBpT1MuXFxuMy4gVXNlIGEgbW9yZSByZWFkYWJsZSB0YWIgc2l6ZS5cXG40LiBVc2UgdGhlIHVzZXIncyBjb25maWd1cmVkIGBzYW5zYCBmb250LWZhbWlseSBieSBkZWZhdWx0LlxcbjUuIFVzZSB0aGUgdXNlcidzIGNvbmZpZ3VyZWQgYHNhbnNgIGZvbnQtZmVhdHVyZS1zZXR0aW5ncyBieSBkZWZhdWx0LlxcbjYuIFVzZSB0aGUgdXNlcidzIGNvbmZpZ3VyZWQgYHNhbnNgIGZvbnQtdmFyaWF0aW9uLXNldHRpbmdzIGJ5IGRlZmF1bHQuXFxuNy4gRGlzYWJsZSB0YXAgaGlnaGxpZ2h0cyBvbiBpT1NcXG4qL1xcblxcbmh0bWwsXFxuOmhvc3Qge1xcbiAgbGluZS1oZWlnaHQ6IDEuNTsgLyogMSAqL1xcbiAgLXdlYmtpdC10ZXh0LXNpemUtYWRqdXN0OiAxMDAlOyAvKiAyICovXFxuICAtbW96LXRhYi1zaXplOiA0OyAvKiAzICovXFxuICAtby10YWItc2l6ZTogNDtcXG4gICAgIHRhYi1zaXplOiA0OyAvKiAzICovXFxuICBmb250LWZhbWlseTogSW50ZXIsIHVpLXNhbnMtc2VyaWYsIHN5c3RlbS11aSwgc2Fucy1zZXJpZjsgLyogNCAqL1xcbiAgZm9udC1mZWF0dXJlLXNldHRpbmdzOiBub3JtYWw7IC8qIDUgKi9cXG4gIGZvbnQtdmFyaWF0aW9uLXNldHRpbmdzOiBub3JtYWw7IC8qIDYgKi9cXG4gIC13ZWJraXQtdGFwLWhpZ2hsaWdodC1jb2xvcjogdHJhbnNwYXJlbnQ7IC8qIDcgKi9cXG59XFxuXFxuLypcXG4xLiBSZW1vdmUgdGhlIG1hcmdpbiBpbiBhbGwgYnJvd3NlcnMuXFxuMi4gSW5oZXJpdCBsaW5lLWhlaWdodCBmcm9tIGBodG1sYCBzbyB1c2VycyBjYW4gc2V0IHRoZW0gYXMgYSBjbGFzcyBkaXJlY3RseSBvbiB0aGUgYGh0bWxgIGVsZW1lbnQuXFxuKi9cXG5cXG5ib2R5IHtcXG4gIG1hcmdpbjogMDsgLyogMSAqL1xcbiAgbGluZS1oZWlnaHQ6IGluaGVyaXQ7IC8qIDIgKi9cXG59XFxuXFxuLypcXG4xLiBBZGQgdGhlIGNvcnJlY3QgaGVpZ2h0IGluIEZpcmVmb3guXFxuMi4gQ29ycmVjdCB0aGUgaW5oZXJpdGFuY2Ugb2YgYm9yZGVyIGNvbG9yIGluIEZpcmVmb3guIChodHRwczovL2J1Z3ppbGxhLm1vemlsbGEub3JnL3Nob3dfYnVnLmNnaT9pZD0xOTA2NTUpXFxuMy4gRW5zdXJlIGhvcml6b250YWwgcnVsZXMgYXJlIHZpc2libGUgYnkgZGVmYXVsdC5cXG4qL1xcblxcbmhyIHtcXG4gIGhlaWdodDogMDsgLyogMSAqL1xcbiAgY29sb3I6IGluaGVyaXQ7IC8qIDIgKi9cXG4gIGJvcmRlci10b3Atd2lkdGg6IDFweDsgLyogMyAqL1xcbn1cXG5cXG4vKlxcbkFkZCB0aGUgY29ycmVjdCB0ZXh0IGRlY29yYXRpb24gaW4gQ2hyb21lLCBFZGdlLCBhbmQgU2FmYXJpLlxcbiovXFxuXFxuYWJicjp3aGVyZShbdGl0bGVdKSB7XFxuICAtd2Via2l0LXRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lIGRvdHRlZDtcXG4gICAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmUgZG90dGVkO1xcbn1cXG5cXG4vKlxcblJlbW92ZSB0aGUgZGVmYXVsdCBmb250IHNpemUgYW5kIHdlaWdodCBmb3IgaGVhZGluZ3MuXFxuKi9cXG5cXG5oMSxcXG5oMixcXG5oMyxcXG5oNCxcXG5oNSxcXG5oNiB7XFxuICBmb250LXNpemU6IGluaGVyaXQ7XFxuICBmb250LXdlaWdodDogaW5oZXJpdDtcXG59XFxuXFxuLypcXG5SZXNldCBsaW5rcyB0byBvcHRpbWl6ZSBmb3Igb3B0LWluIHN0eWxpbmcgaW5zdGVhZCBvZiBvcHQtb3V0LlxcbiovXFxuXFxuYSB7XFxuICBjb2xvcjogaW5oZXJpdDtcXG4gIHRleHQtZGVjb3JhdGlvbjogaW5oZXJpdDtcXG59XFxuXFxuLypcXG5BZGQgdGhlIGNvcnJlY3QgZm9udCB3ZWlnaHQgaW4gRWRnZSBhbmQgU2FmYXJpLlxcbiovXFxuXFxuYixcXG5zdHJvbmcge1xcbiAgZm9udC13ZWlnaHQ6IGJvbGRlcjtcXG59XFxuXFxuLypcXG4xLiBVc2UgdGhlIHVzZXIncyBjb25maWd1cmVkIGBtb25vYCBmb250LWZhbWlseSBieSBkZWZhdWx0LlxcbjIuIFVzZSB0aGUgdXNlcidzIGNvbmZpZ3VyZWQgYG1vbm9gIGZvbnQtZmVhdHVyZS1zZXR0aW5ncyBieSBkZWZhdWx0LlxcbjMuIFVzZSB0aGUgdXNlcidzIGNvbmZpZ3VyZWQgYG1vbm9gIGZvbnQtdmFyaWF0aW9uLXNldHRpbmdzIGJ5IGRlZmF1bHQuXFxuNC4gQ29ycmVjdCB0aGUgb2RkIGBlbWAgZm9udCBzaXppbmcgaW4gYWxsIGJyb3dzZXJzLlxcbiovXFxuXFxuY29kZSxcXG5rYmQsXFxuc2FtcCxcXG5wcmUge1xcbiAgZm9udC1mYW1pbHk6IHVpLW1vbm9zcGFjZSwgU0ZNb25vLVJlZ3VsYXIsIE1lbmxvLCBNb25hY28sIENvbnNvbGFzLCBcXFwiTGliZXJhdGlvbiBNb25vXFxcIiwgXFxcIkNvdXJpZXIgTmV3XFxcIiwgbW9ub3NwYWNlOyAvKiAxICovXFxuICBmb250LWZlYXR1cmUtc2V0dGluZ3M6IG5vcm1hbDsgLyogMiAqL1xcbiAgZm9udC12YXJpYXRpb24tc2V0dGluZ3M6IG5vcm1hbDsgLyogMyAqL1xcbiAgZm9udC1zaXplOiAxZW07IC8qIDQgKi9cXG59XFxuXFxuLypcXG5BZGQgdGhlIGNvcnJlY3QgZm9udCBzaXplIGluIGFsbCBicm93c2Vycy5cXG4qL1xcblxcbnNtYWxsIHtcXG4gIGZvbnQtc2l6ZTogODAlO1xcbn1cXG5cXG4vKlxcblByZXZlbnQgYHN1YmAgYW5kIGBzdXBgIGVsZW1lbnRzIGZyb20gYWZmZWN0aW5nIHRoZSBsaW5lIGhlaWdodCBpbiBhbGwgYnJvd3NlcnMuXFxuKi9cXG5cXG5zdWIsXFxuc3VwIHtcXG4gIGZvbnQtc2l6ZTogNzUlO1xcbiAgbGluZS1oZWlnaHQ6IDA7XFxuICBwb3NpdGlvbjogcmVsYXRpdmU7XFxuICB2ZXJ0aWNhbC1hbGlnbjogYmFzZWxpbmU7XFxufVxcblxcbnN1YiB7XFxuICBib3R0b206IC0wLjI1ZW07XFxufVxcblxcbnN1cCB7XFxuICB0b3A6IC0wLjVlbTtcXG59XFxuXFxuLypcXG4xLiBSZW1vdmUgdGV4dCBpbmRlbnRhdGlvbiBmcm9tIHRhYmxlIGNvbnRlbnRzIGluIENocm9tZSBhbmQgU2FmYXJpLiAoaHR0cHM6Ly9idWdzLmNocm9taXVtLm9yZy9wL2Nocm9taXVtL2lzc3Vlcy9kZXRhaWw/aWQ9OTk5MDg4LCBodHRwczovL2J1Z3Mud2Via2l0Lm9yZy9zaG93X2J1Zy5jZ2k/aWQ9MjAxMjk3KVxcbjIuIENvcnJlY3QgdGFibGUgYm9yZGVyIGNvbG9yIGluaGVyaXRhbmNlIGluIGFsbCBDaHJvbWUgYW5kIFNhZmFyaS4gKGh0dHBzOi8vYnVncy5jaHJvbWl1bS5vcmcvcC9jaHJvbWl1bS9pc3N1ZXMvZGV0YWlsP2lkPTkzNTcyOSwgaHR0cHM6Ly9idWdzLndlYmtpdC5vcmcvc2hvd19idWcuY2dpP2lkPTE5NTAxNilcXG4zLiBSZW1vdmUgZ2FwcyBiZXR3ZWVuIHRhYmxlIGJvcmRlcnMgYnkgZGVmYXVsdC5cXG4qL1xcblxcbnRhYmxlIHtcXG4gIHRleHQtaW5kZW50OiAwOyAvKiAxICovXFxuICBib3JkZXItY29sb3I6IGluaGVyaXQ7IC8qIDIgKi9cXG4gIGJvcmRlci1jb2xsYXBzZTogY29sbGFwc2U7IC8qIDMgKi9cXG59XFxuXFxuLypcXG4xLiBDaGFuZ2UgdGhlIGZvbnQgc3R5bGVzIGluIGFsbCBicm93c2Vycy5cXG4yLiBSZW1vdmUgdGhlIG1hcmdpbiBpbiBGaXJlZm94IGFuZCBTYWZhcmkuXFxuMy4gUmVtb3ZlIGRlZmF1bHQgcGFkZGluZyBpbiBhbGwgYnJvd3NlcnMuXFxuKi9cXG5cXG5idXR0b24sXFxuaW5wdXQsXFxub3B0Z3JvdXAsXFxuc2VsZWN0LFxcbnRleHRhcmVhIHtcXG4gIGZvbnQtZmFtaWx5OiBpbmhlcml0OyAvKiAxICovXFxuICBmb250LWZlYXR1cmUtc2V0dGluZ3M6IGluaGVyaXQ7IC8qIDEgKi9cXG4gIGZvbnQtdmFyaWF0aW9uLXNldHRpbmdzOiBpbmhlcml0OyAvKiAxICovXFxuICBmb250LXNpemU6IDEwMCU7IC8qIDEgKi9cXG4gIGZvbnQtd2VpZ2h0OiBpbmhlcml0OyAvKiAxICovXFxuICBsaW5lLWhlaWdodDogaW5oZXJpdDsgLyogMSAqL1xcbiAgbGV0dGVyLXNwYWNpbmc6IGluaGVyaXQ7IC8qIDEgKi9cXG4gIGNvbG9yOiBpbmhlcml0OyAvKiAxICovXFxuICBtYXJnaW46IDA7IC8qIDIgKi9cXG4gIHBhZGRpbmc6IDA7IC8qIDMgKi9cXG59XFxuXFxuLypcXG5SZW1vdmUgdGhlIGluaGVyaXRhbmNlIG9mIHRleHQgdHJhbnNmb3JtIGluIEVkZ2UgYW5kIEZpcmVmb3guXFxuKi9cXG5cXG5idXR0b24sXFxuc2VsZWN0IHtcXG4gIHRleHQtdHJhbnNmb3JtOiBub25lO1xcbn1cXG5cXG4vKlxcbjEuIENvcnJlY3QgdGhlIGluYWJpbGl0eSB0byBzdHlsZSBjbGlja2FibGUgdHlwZXMgaW4gaU9TIGFuZCBTYWZhcmkuXFxuMi4gUmVtb3ZlIGRlZmF1bHQgYnV0dG9uIHN0eWxlcy5cXG4qL1xcblxcbmJ1dHRvbixcXG5pbnB1dDp3aGVyZShbdHlwZT0nYnV0dG9uJ10pLFxcbmlucHV0OndoZXJlKFt0eXBlPSdyZXNldCddKSxcXG5pbnB1dDp3aGVyZShbdHlwZT0nc3VibWl0J10pIHtcXG4gIC13ZWJraXQtYXBwZWFyYW5jZTogYnV0dG9uOyAvKiAxICovXFxuICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDsgLyogMiAqL1xcbiAgYmFja2dyb3VuZC1pbWFnZTogbm9uZTsgLyogMiAqL1xcbn1cXG5cXG4vKlxcblVzZSB0aGUgbW9kZXJuIEZpcmVmb3ggZm9jdXMgc3R5bGUgZm9yIGFsbCBmb2N1c2FibGUgZWxlbWVudHMuXFxuKi9cXG5cXG46LW1vei1mb2N1c3Jpbmcge1xcbiAgb3V0bGluZTogYXV0bztcXG59XFxuXFxuLypcXG5SZW1vdmUgdGhlIGFkZGl0aW9uYWwgYDppbnZhbGlkYCBzdHlsZXMgaW4gRmlyZWZveC4gKGh0dHBzOi8vZ2l0aHViLmNvbS9tb3ppbGxhL2dlY2tvLWRldi9ibG9iLzJmOWVhY2Q5ZDNkOTk1YzkzN2I0MjUxYTU1NTdkOTVkNDk0YzliZTEvbGF5b3V0L3N0eWxlL3Jlcy9mb3Jtcy5jc3MjTDcyOC1MNzM3KVxcbiovXFxuXFxuOi1tb3otdWktaW52YWxpZCB7XFxuICBib3gtc2hhZG93OiBub25lO1xcbn1cXG5cXG4vKlxcbkFkZCB0aGUgY29ycmVjdCB2ZXJ0aWNhbCBhbGlnbm1lbnQgaW4gQ2hyb21lIGFuZCBGaXJlZm94LlxcbiovXFxuXFxucHJvZ3Jlc3Mge1xcbiAgdmVydGljYWwtYWxpZ246IGJhc2VsaW5lO1xcbn1cXG5cXG4vKlxcbkNvcnJlY3QgdGhlIGN1cnNvciBzdHlsZSBvZiBpbmNyZW1lbnQgYW5kIGRlY3JlbWVudCBidXR0b25zIGluIFNhZmFyaS5cXG4qL1xcblxcbjo6LXdlYmtpdC1pbm5lci1zcGluLWJ1dHRvbixcXG46Oi13ZWJraXQtb3V0ZXItc3Bpbi1idXR0b24ge1xcbiAgaGVpZ2h0OiBhdXRvO1xcbn1cXG5cXG4vKlxcbjEuIENvcnJlY3QgdGhlIG9kZCBhcHBlYXJhbmNlIGluIENocm9tZSBhbmQgU2FmYXJpLlxcbjIuIENvcnJlY3QgdGhlIG91dGxpbmUgc3R5bGUgaW4gU2FmYXJpLlxcbiovXFxuXFxuW3R5cGU9J3NlYXJjaCddIHtcXG4gIC13ZWJraXQtYXBwZWFyYW5jZTogdGV4dGZpZWxkOyAvKiAxICovXFxuICBvdXRsaW5lLW9mZnNldDogLTJweDsgLyogMiAqL1xcbn1cXG5cXG4vKlxcblJlbW92ZSB0aGUgaW5uZXIgcGFkZGluZyBpbiBDaHJvbWUgYW5kIFNhZmFyaSBvbiBtYWNPUy5cXG4qL1xcblxcbjo6LXdlYmtpdC1zZWFyY2gtZGVjb3JhdGlvbiB7XFxuICAtd2Via2l0LWFwcGVhcmFuY2U6IG5vbmU7XFxufVxcblxcbi8qXFxuMS4gQ29ycmVjdCB0aGUgaW5hYmlsaXR5IHRvIHN0eWxlIGNsaWNrYWJsZSB0eXBlcyBpbiBpT1MgYW5kIFNhZmFyaS5cXG4yLiBDaGFuZ2UgZm9udCBwcm9wZXJ0aWVzIHRvIGBpbmhlcml0YCBpbiBTYWZhcmkuXFxuKi9cXG5cXG46Oi13ZWJraXQtZmlsZS11cGxvYWQtYnV0dG9uIHtcXG4gIC13ZWJraXQtYXBwZWFyYW5jZTogYnV0dG9uOyAvKiAxICovXFxuICBmb250OiBpbmhlcml0OyAvKiAyICovXFxufVxcblxcbi8qXFxuQWRkIHRoZSBjb3JyZWN0IGRpc3BsYXkgaW4gQ2hyb21lIGFuZCBTYWZhcmkuXFxuKi9cXG5cXG5zdW1tYXJ5IHtcXG4gIGRpc3BsYXk6IGxpc3QtaXRlbTtcXG59XFxuXFxuLypcXG5SZW1vdmVzIHRoZSBkZWZhdWx0IHNwYWNpbmcgYW5kIGJvcmRlciBmb3IgYXBwcm9wcmlhdGUgZWxlbWVudHMuXFxuKi9cXG5cXG5ibG9ja3F1b3RlLFxcbmRsLFxcbmRkLFxcbmgxLFxcbmgyLFxcbmgzLFxcbmg0LFxcbmg1LFxcbmg2LFxcbmhyLFxcbmZpZ3VyZSxcXG5wLFxcbnByZSB7XFxuICBtYXJnaW46IDA7XFxufVxcblxcbmZpZWxkc2V0IHtcXG4gIG1hcmdpbjogMDtcXG4gIHBhZGRpbmc6IDA7XFxufVxcblxcbmxlZ2VuZCB7XFxuICBwYWRkaW5nOiAwO1xcbn1cXG5cXG5vbCxcXG51bCxcXG5tZW51IHtcXG4gIGxpc3Qtc3R5bGU6IG5vbmU7XFxuICBtYXJnaW46IDA7XFxuICBwYWRkaW5nOiAwO1xcbn1cXG5cXG4vKlxcblJlc2V0IGRlZmF1bHQgc3R5bGluZyBmb3IgZGlhbG9ncy5cXG4qL1xcbmRpYWxvZyB7XFxuICBwYWRkaW5nOiAwO1xcbn1cXG5cXG4vKlxcblByZXZlbnQgcmVzaXppbmcgdGV4dGFyZWFzIGhvcml6b250YWxseSBieSBkZWZhdWx0LlxcbiovXFxuXFxudGV4dGFyZWEge1xcbiAgcmVzaXplOiB2ZXJ0aWNhbDtcXG59XFxuXFxuLypcXG4xLiBSZXNldCB0aGUgZGVmYXVsdCBwbGFjZWhvbGRlciBvcGFjaXR5IGluIEZpcmVmb3guIChodHRwczovL2dpdGh1Yi5jb20vdGFpbHdpbmRsYWJzL3RhaWx3aW5kY3NzL2lzc3Vlcy8zMzAwKVxcbjIuIFNldCB0aGUgZGVmYXVsdCBwbGFjZWhvbGRlciBjb2xvciB0byB0aGUgdXNlcidzIGNvbmZpZ3VyZWQgZ3JheSA0MDAgY29sb3IuXFxuKi9cXG5cXG5pbnB1dDo6LW1vei1wbGFjZWhvbGRlciwgdGV4dGFyZWE6Oi1tb3otcGxhY2Vob2xkZXIge1xcbiAgb3BhY2l0eTogMTsgLyogMSAqL1xcbiAgY29sb3I6ICM5Y2EzYWY7IC8qIDIgKi9cXG59XFxuXFxuaW5wdXQ6OnBsYWNlaG9sZGVyLFxcbnRleHRhcmVhOjpwbGFjZWhvbGRlciB7XFxuICBvcGFjaXR5OiAxOyAvKiAxICovXFxuICBjb2xvcjogIzljYTNhZjsgLyogMiAqL1xcbn1cXG5cXG4vKlxcblNldCB0aGUgZGVmYXVsdCBjdXJzb3IgZm9yIGJ1dHRvbnMuXFxuKi9cXG5cXG5idXR0b24sXFxuW3JvbGU9XFxcImJ1dHRvblxcXCJdIHtcXG4gIGN1cnNvcjogcG9pbnRlcjtcXG59XFxuXFxuLypcXG5NYWtlIHN1cmUgZGlzYWJsZWQgYnV0dG9ucyBkb24ndCBnZXQgdGhlIHBvaW50ZXIgY3Vyc29yLlxcbiovXFxuOmRpc2FibGVkIHtcXG4gIGN1cnNvcjogZGVmYXVsdDtcXG59XFxuXFxuLypcXG4xLiBNYWtlIHJlcGxhY2VkIGVsZW1lbnRzIGBkaXNwbGF5OiBibG9ja2AgYnkgZGVmYXVsdC4gKGh0dHBzOi8vZ2l0aHViLmNvbS9tb3pkZXZzL2Nzc3JlbWVkeS9pc3N1ZXMvMTQpXFxuMi4gQWRkIGB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlYCB0byBhbGlnbiByZXBsYWNlZCBlbGVtZW50cyBtb3JlIHNlbnNpYmx5IGJ5IGRlZmF1bHQuIChodHRwczovL2dpdGh1Yi5jb20vamVuc2ltbW9ucy9jc3NyZW1lZHkvaXNzdWVzLzE0I2lzc3VlY29tbWVudC02MzQ5MzQyMTApXFxuICAgVGhpcyBjYW4gdHJpZ2dlciBhIHBvb3JseSBjb25zaWRlcmVkIGxpbnQgZXJyb3IgaW4gc29tZSB0b29scyBidXQgaXMgaW5jbHVkZWQgYnkgZGVzaWduLlxcbiovXFxuXFxuaW1nLFxcbnN2ZyxcXG52aWRlbyxcXG5jYW52YXMsXFxuYXVkaW8sXFxuaWZyYW1lLFxcbmVtYmVkLFxcbm9iamVjdCB7XFxuICBkaXNwbGF5OiBibG9jazsgLyogMSAqL1xcbiAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsgLyogMiAqL1xcbn1cXG5cXG4vKlxcbkNvbnN0cmFpbiBpbWFnZXMgYW5kIHZpZGVvcyB0byB0aGUgcGFyZW50IHdpZHRoIGFuZCBwcmVzZXJ2ZSB0aGVpciBpbnRyaW5zaWMgYXNwZWN0IHJhdGlvLiAoaHR0cHM6Ly9naXRodWIuY29tL21vemRldnMvY3NzcmVtZWR5L2lzc3Vlcy8xNClcXG4qL1xcblxcbmltZyxcXG52aWRlbyB7XFxuICBtYXgtd2lkdGg6IDEwMCU7XFxuICBoZWlnaHQ6IGF1dG87XFxufVxcblxcbi8qIE1ha2UgZWxlbWVudHMgd2l0aCB0aGUgSFRNTCBoaWRkZW4gYXR0cmlidXRlIHN0YXkgaGlkZGVuIGJ5IGRlZmF1bHQgKi9cXG5baGlkZGVuXTp3aGVyZSg6bm90KFtoaWRkZW49XFxcInVudGlsLWZvdW5kXFxcIl0pKSB7XFxuICBkaXNwbGF5OiBub25lO1xcbn1cXG5cXG5bdHlwZT0ndGV4dCddLGlucHV0OndoZXJlKDpub3QoW3R5cGVdKSksW3R5cGU9J2VtYWlsJ10sW3R5cGU9J3VybCddLFt0eXBlPSdwYXNzd29yZCddLFt0eXBlPSdudW1iZXInXSxbdHlwZT0nZGF0ZSddLFt0eXBlPSdkYXRldGltZS1sb2NhbCddLFt0eXBlPSdtb250aCddLFt0eXBlPSdzZWFyY2gnXSxbdHlwZT0ndGVsJ10sW3R5cGU9J3RpbWUnXSxbdHlwZT0nd2VlayddLFttdWx0aXBsZV0sdGV4dGFyZWEsc2VsZWN0IHtcXG4gIC13ZWJraXQtYXBwZWFyYW5jZTogbm9uZTtcXG4gICAgIC1tb3otYXBwZWFyYW5jZTogbm9uZTtcXG4gICAgICAgICAgYXBwZWFyYW5jZTogbm9uZTtcXG4gIGJhY2tncm91bmQtY29sb3I6ICNmZmY7XFxuICBib3JkZXItY29sb3I6ICM2YjcyODA7XFxuICBib3JkZXItd2lkdGg6IDFweDtcXG4gIGJvcmRlci1yYWRpdXM6IDBweDtcXG4gIHBhZGRpbmctdG9wOiAwLjVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjc1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuNXJlbTtcXG4gIHBhZGRpbmctbGVmdDogMC43NXJlbTtcXG4gIGZvbnQtc2l6ZTogMXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxLjVyZW07XFxuICAtLXR3LXNoYWRvdzogMCAwICMwMDAwO1xcbn1cXG5cXG5bdHlwZT0ndGV4dCddOmZvY3VzLCBpbnB1dDp3aGVyZSg6bm90KFt0eXBlXSkpOmZvY3VzLCBbdHlwZT0nZW1haWwnXTpmb2N1cywgW3R5cGU9J3VybCddOmZvY3VzLCBbdHlwZT0ncGFzc3dvcmQnXTpmb2N1cywgW3R5cGU9J251bWJlciddOmZvY3VzLCBbdHlwZT0nZGF0ZSddOmZvY3VzLCBbdHlwZT0nZGF0ZXRpbWUtbG9jYWwnXTpmb2N1cywgW3R5cGU9J21vbnRoJ106Zm9jdXMsIFt0eXBlPSdzZWFyY2gnXTpmb2N1cywgW3R5cGU9J3RlbCddOmZvY3VzLCBbdHlwZT0ndGltZSddOmZvY3VzLCBbdHlwZT0nd2VlayddOmZvY3VzLCBbbXVsdGlwbGVdOmZvY3VzLCB0ZXh0YXJlYTpmb2N1cywgc2VsZWN0OmZvY3VzIHtcXG4gIG91dGxpbmU6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcXG4gIG91dGxpbmUtb2Zmc2V0OiAycHg7XFxuICAtLXR3LXJpbmctaW5zZXQ6IHZhcigtLXR3LWVtcHR5LC8qISovIC8qISovKTtcXG4gIC0tdHctcmluZy1vZmZzZXQtd2lkdGg6IDBweDtcXG4gIC0tdHctcmluZy1vZmZzZXQtY29sb3I6ICNmZmY7XFxuICAtLXR3LXJpbmctY29sb3I6ICMyNTYzZWI7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpIHZhcigtLXR3LXJpbmctb2Zmc2V0LWNvbG9yKTtcXG4gIC0tdHctcmluZy1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIGNhbGMoMXB4ICsgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpKSB2YXIoLS10dy1yaW5nLWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdyksIHZhcigtLXR3LXJpbmctc2hhZG93KSwgdmFyKC0tdHctc2hhZG93KTtcXG4gIGJvcmRlci1jb2xvcjogIzI1NjNlYjtcXG59XFxuXFxuaW5wdXQ6Oi1tb3otcGxhY2Vob2xkZXIsIHRleHRhcmVhOjotbW96LXBsYWNlaG9sZGVyIHtcXG4gIGNvbG9yOiAjNmI3MjgwO1xcbiAgb3BhY2l0eTogMTtcXG59XFxuXFxuaW5wdXQ6OnBsYWNlaG9sZGVyLHRleHRhcmVhOjpwbGFjZWhvbGRlciB7XFxuICBjb2xvcjogIzZiNzI4MDtcXG4gIG9wYWNpdHk6IDE7XFxufVxcblxcbjo6LXdlYmtpdC1kYXRldGltZS1lZGl0LWZpZWxkcy13cmFwcGVyIHtcXG4gIHBhZGRpbmc6IDA7XFxufVxcblxcbjo6LXdlYmtpdC1kYXRlLWFuZC10aW1lLXZhbHVlIHtcXG4gIG1pbi1oZWlnaHQ6IDEuNWVtO1xcbiAgdGV4dC1hbGlnbjogaW5oZXJpdDtcXG59XFxuXFxuOjotd2Via2l0LWRhdGV0aW1lLWVkaXQge1xcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XFxufVxcblxcbjo6LXdlYmtpdC1kYXRldGltZS1lZGl0LDo6LXdlYmtpdC1kYXRldGltZS1lZGl0LXllYXItZmllbGQsOjotd2Via2l0LWRhdGV0aW1lLWVkaXQtbW9udGgtZmllbGQsOjotd2Via2l0LWRhdGV0aW1lLWVkaXQtZGF5LWZpZWxkLDo6LXdlYmtpdC1kYXRldGltZS1lZGl0LWhvdXItZmllbGQsOjotd2Via2l0LWRhdGV0aW1lLWVkaXQtbWludXRlLWZpZWxkLDo6LXdlYmtpdC1kYXRldGltZS1lZGl0LXNlY29uZC1maWVsZCw6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1taWxsaXNlY29uZC1maWVsZCw6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1tZXJpZGllbS1maWVsZCB7XFxuICBwYWRkaW5nLXRvcDogMDtcXG4gIHBhZGRpbmctYm90dG9tOiAwO1xcbn1cXG5cXG5zZWxlY3Qge1xcbiAgYmFja2dyb3VuZC1pbWFnZTogdXJsKFxcXCJkYXRhOmltYWdlL3N2Zyt4bWwsJTNjc3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycgZmlsbD0nbm9uZScgdmlld0JveD0nMCAwIDIwIDIwJyUzZSUzY3BhdGggc3Ryb2tlPSclMjM2YjcyODAnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcgc3Ryb2tlLWxpbmVqb2luPSdyb3VuZCcgc3Ryb2tlLXdpZHRoPScxLjUnIGQ9J002IDhsNCA0IDQtNCcvJTNlJTNjL3N2ZyUzZVxcXCIpO1xcbiAgYmFja2dyb3VuZC1wb3NpdGlvbjogcmlnaHQgMC41cmVtIGNlbnRlcjtcXG4gIGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQ7XFxuICBiYWNrZ3JvdW5kLXNpemU6IDEuNWVtIDEuNWVtO1xcbiAgcGFkZGluZy1yaWdodDogMi41cmVtO1xcbiAgLXdlYmtpdC1wcmludC1jb2xvci1hZGp1c3Q6IGV4YWN0O1xcbiAgICAgICAgICBwcmludC1jb2xvci1hZGp1c3Q6IGV4YWN0O1xcbn1cXG5cXG5bbXVsdGlwbGVdLFtzaXplXTp3aGVyZShzZWxlY3Q6bm90KFtzaXplPVxcXCIxXFxcIl0pKSB7XFxuICBiYWNrZ3JvdW5kLWltYWdlOiBpbml0aWFsO1xcbiAgYmFja2dyb3VuZC1wb3NpdGlvbjogaW5pdGlhbDtcXG4gIGJhY2tncm91bmQtcmVwZWF0OiB1bnNldDtcXG4gIGJhY2tncm91bmQtc2l6ZTogaW5pdGlhbDtcXG4gIHBhZGRpbmctcmlnaHQ6IDAuNzVyZW07XFxuICAtd2Via2l0LXByaW50LWNvbG9yLWFkanVzdDogdW5zZXQ7XFxuICAgICAgICAgIHByaW50LWNvbG9yLWFkanVzdDogdW5zZXQ7XFxufVxcblxcblt0eXBlPSdjaGVja2JveCddLFt0eXBlPSdyYWRpbyddIHtcXG4gIC13ZWJraXQtYXBwZWFyYW5jZTogbm9uZTtcXG4gICAgIC1tb3otYXBwZWFyYW5jZTogbm9uZTtcXG4gICAgICAgICAgYXBwZWFyYW5jZTogbm9uZTtcXG4gIHBhZGRpbmc6IDA7XFxuICAtd2Via2l0LXByaW50LWNvbG9yLWFkanVzdDogZXhhY3Q7XFxuICAgICAgICAgIHByaW50LWNvbG9yLWFkanVzdDogZXhhY3Q7XFxuICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XFxuICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xcbiAgYmFja2dyb3VuZC1vcmlnaW46IGJvcmRlci1ib3g7XFxuICAtd2Via2l0LXVzZXItc2VsZWN0OiBub25lO1xcbiAgICAgLW1vei11c2VyLXNlbGVjdDogbm9uZTtcXG4gICAgICAgICAgdXNlci1zZWxlY3Q6IG5vbmU7XFxuICBmbGV4LXNocmluazogMDtcXG4gIGhlaWdodDogMXJlbTtcXG4gIHdpZHRoOiAxcmVtO1xcbiAgY29sb3I6ICMyNTYzZWI7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xcbiAgYm9yZGVyLWNvbG9yOiAjNmI3MjgwO1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxuICAtLXR3LXNoYWRvdzogMCAwICMwMDAwO1xcbn1cXG5cXG5bdHlwZT0nY2hlY2tib3gnXSB7XFxuICBib3JkZXItcmFkaXVzOiAwcHg7XFxufVxcblxcblt0eXBlPSdyYWRpbyddIHtcXG4gIGJvcmRlci1yYWRpdXM6IDEwMCU7XFxufVxcblxcblt0eXBlPSdjaGVja2JveCddOmZvY3VzLFt0eXBlPSdyYWRpbyddOmZvY3VzIHtcXG4gIG91dGxpbmU6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcXG4gIG91dGxpbmUtb2Zmc2V0OiAycHg7XFxuICAtLXR3LXJpbmctaW5zZXQ6IHZhcigtLXR3LWVtcHR5LC8qISovIC8qISovKTtcXG4gIC0tdHctcmluZy1vZmZzZXQtd2lkdGg6IDJweDtcXG4gIC0tdHctcmluZy1vZmZzZXQtY29sb3I6ICNmZmY7XFxuICAtLXR3LXJpbmctY29sb3I6ICMyNTYzZWI7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpIHZhcigtLXR3LXJpbmctb2Zmc2V0LWNvbG9yKTtcXG4gIC0tdHctcmluZy1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIGNhbGMoMnB4ICsgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpKSB2YXIoLS10dy1yaW5nLWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdyksIHZhcigtLXR3LXJpbmctc2hhZG93KSwgdmFyKC0tdHctc2hhZG93KTtcXG59XFxuXFxuW3R5cGU9J2NoZWNrYm94J106Y2hlY2tlZCxbdHlwZT0ncmFkaW8nXTpjaGVja2VkIHtcXG4gIGJvcmRlci1jb2xvcjogdHJhbnNwYXJlbnQ7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBjdXJyZW50Q29sb3I7XFxuICBiYWNrZ3JvdW5kLXNpemU6IDEwMCUgMTAwJTtcXG4gIGJhY2tncm91bmQtcG9zaXRpb246IGNlbnRlcjtcXG4gIGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQ7XFxufVxcblxcblt0eXBlPSdjaGVja2JveCddOmNoZWNrZWQge1xcbiAgYmFja2dyb3VuZC1pbWFnZTogdXJsKFxcXCJkYXRhOmltYWdlL3N2Zyt4bWwsJTNjc3ZnIHZpZXdCb3g9JzAgMCAxNiAxNicgZmlsbD0nd2hpdGUnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyclM2UlM2NwYXRoIGQ9J00xMi4yMDcgNC43OTNhMSAxIDAgMDEwIDEuNDE0bC01IDVhMSAxIDAgMDEtMS40MTQgMGwtMi0yYTEgMSAwIDAxMS40MTQtMS40MTRMNi41IDkuMDg2bDQuMjkzLTQuMjkzYTEgMSAwIDAxMS40MTQgMHonLyUzZSUzYy9zdmclM2VcXFwiKTtcXG59XFxuXFxuQG1lZGlhIChmb3JjZWQtY29sb3JzOiBhY3RpdmUpICB7XFxuXFxuICBbdHlwZT0nY2hlY2tib3gnXTpjaGVja2VkIHtcXG4gICAgLXdlYmtpdC1hcHBlYXJhbmNlOiBhdXRvO1xcbiAgICAgICAtbW96LWFwcGVhcmFuY2U6IGF1dG87XFxuICAgICAgICAgICAgYXBwZWFyYW5jZTogYXV0bztcXG4gIH1cXG59XFxuXFxuW3R5cGU9J3JhZGlvJ106Y2hlY2tlZCB7XFxuICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoXFxcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM2Nzdmcgdmlld0JveD0nMCAwIDE2IDE2JyBmaWxsPSd3aGl0ZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyUzZSUzY2NpcmNsZSBjeD0nOCcgY3k9JzgnIHI9JzMnLyUzZSUzYy9zdmclM2VcXFwiKTtcXG59XFxuXFxuQG1lZGlhIChmb3JjZWQtY29sb3JzOiBhY3RpdmUpICB7XFxuXFxuICBbdHlwZT0ncmFkaW8nXTpjaGVja2VkIHtcXG4gICAgLXdlYmtpdC1hcHBlYXJhbmNlOiBhdXRvO1xcbiAgICAgICAtbW96LWFwcGVhcmFuY2U6IGF1dG87XFxuICAgICAgICAgICAgYXBwZWFyYW5jZTogYXV0bztcXG4gIH1cXG59XFxuXFxuW3R5cGU9J2NoZWNrYm94J106Y2hlY2tlZDpob3ZlcixbdHlwZT0nY2hlY2tib3gnXTpjaGVja2VkOmZvY3VzLFt0eXBlPSdyYWRpbyddOmNoZWNrZWQ6aG92ZXIsW3R5cGU9J3JhZGlvJ106Y2hlY2tlZDpmb2N1cyB7XFxuICBib3JkZXItY29sb3I6IHRyYW5zcGFyZW50O1xcbiAgYmFja2dyb3VuZC1jb2xvcjogY3VycmVudENvbG9yO1xcbn1cXG5cXG5bdHlwZT0nY2hlY2tib3gnXTppbmRldGVybWluYXRlIHtcXG4gIGJhY2tncm91bmQtaW1hZ2U6IHVybChcXFwiZGF0YTppbWFnZS9zdmcreG1sLCUzY3N2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIGZpbGw9J25vbmUnIHZpZXdCb3g9JzAgMCAxNiAxNiclM2UlM2NwYXRoIHN0cm9rZT0nd2hpdGUnIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcgc3Ryb2tlLWxpbmVqb2luPSdyb3VuZCcgc3Ryb2tlLXdpZHRoPScyJyBkPSdNNCA4aDgnLyUzZSUzYy9zdmclM2VcXFwiKTtcXG4gIGJvcmRlci1jb2xvcjogdHJhbnNwYXJlbnQ7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBjdXJyZW50Q29sb3I7XFxuICBiYWNrZ3JvdW5kLXNpemU6IDEwMCUgMTAwJTtcXG4gIGJhY2tncm91bmQtcG9zaXRpb246IGNlbnRlcjtcXG4gIGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQ7XFxufVxcblxcbkBtZWRpYSAoZm9yY2VkLWNvbG9yczogYWN0aXZlKSAge1xcblxcbiAgW3R5cGU9J2NoZWNrYm94J106aW5kZXRlcm1pbmF0ZSB7XFxuICAgIC13ZWJraXQtYXBwZWFyYW5jZTogYXV0bztcXG4gICAgICAgLW1vei1hcHBlYXJhbmNlOiBhdXRvO1xcbiAgICAgICAgICAgIGFwcGVhcmFuY2U6IGF1dG87XFxuICB9XFxufVxcblxcblt0eXBlPSdjaGVja2JveCddOmluZGV0ZXJtaW5hdGU6aG92ZXIsW3R5cGU9J2NoZWNrYm94J106aW5kZXRlcm1pbmF0ZTpmb2N1cyB7XFxuICBib3JkZXItY29sb3I6IHRyYW5zcGFyZW50O1xcbiAgYmFja2dyb3VuZC1jb2xvcjogY3VycmVudENvbG9yO1xcbn1cXG5cXG5bdHlwZT0nZmlsZSddIHtcXG4gIGJhY2tncm91bmQ6IHVuc2V0O1xcbiAgYm9yZGVyLWNvbG9yOiBpbmhlcml0O1xcbiAgYm9yZGVyLXdpZHRoOiAwO1xcbiAgYm9yZGVyLXJhZGl1czogMDtcXG4gIHBhZGRpbmc6IDA7XFxuICBmb250LXNpemU6IHVuc2V0O1xcbiAgbGluZS1oZWlnaHQ6IGluaGVyaXQ7XFxufVxcblxcblt0eXBlPSdmaWxlJ106Zm9jdXMge1xcbiAgb3V0bGluZTogMXB4IHNvbGlkIEJ1dHRvblRleHQ7XFxuICBvdXRsaW5lOiAxcHggYXV0byAtd2Via2l0LWZvY3VzLXJpbmctY29sb3I7XFxufVxcbi5mb3JtLWlucHV0LC5mb3JtLXRleHRhcmVhLC5mb3JtLXNlbGVjdCwuZm9ybS1tdWx0aXNlbGVjdCB7XFxuICAtd2Via2l0LWFwcGVhcmFuY2U6IG5vbmU7XFxuICAgICAtbW96LWFwcGVhcmFuY2U6IG5vbmU7XFxuICAgICAgICAgIGFwcGVhcmFuY2U6IG5vbmU7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xcbiAgYm9yZGVyLWNvbG9yOiAjNmI3MjgwO1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxuICBib3JkZXItcmFkaXVzOiAwcHg7XFxuICBwYWRkaW5nLXRvcDogMC41cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMC43NXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjVyZW07XFxuICBwYWRkaW5nLWxlZnQ6IDAuNzVyZW07XFxuICBmb250LXNpemU6IDFyZW07XFxuICBsaW5lLWhlaWdodDogMS41cmVtO1xcbiAgLS10dy1zaGFkb3c6IDAgMCAjMDAwMDtcXG59XFxuLmZvcm0taW5wdXQ6Zm9jdXMsIC5mb3JtLXRleHRhcmVhOmZvY3VzLCAuZm9ybS1zZWxlY3Q6Zm9jdXMsIC5mb3JtLW11bHRpc2VsZWN0OmZvY3VzIHtcXG4gIG91dGxpbmU6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcXG4gIG91dGxpbmUtb2Zmc2V0OiAycHg7XFxuICAtLXR3LXJpbmctaW5zZXQ6IHZhcigtLXR3LWVtcHR5LC8qISovIC8qISovKTtcXG4gIC0tdHctcmluZy1vZmZzZXQtd2lkdGg6IDBweDtcXG4gIC0tdHctcmluZy1vZmZzZXQtY29sb3I6ICNmZmY7XFxuICAtLXR3LXJpbmctY29sb3I6ICMyNTYzZWI7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpIHZhcigtLXR3LXJpbmctb2Zmc2V0LWNvbG9yKTtcXG4gIC0tdHctcmluZy1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIGNhbGMoMXB4ICsgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpKSB2YXIoLS10dy1yaW5nLWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdyksIHZhcigtLXR3LXJpbmctc2hhZG93KSwgdmFyKC0tdHctc2hhZG93KTtcXG4gIGJvcmRlci1jb2xvcjogIzI1NjNlYjtcXG59XFxuLmZvcm0taW5wdXQ6Oi1tb3otcGxhY2Vob2xkZXIsIC5mb3JtLXRleHRhcmVhOjotbW96LXBsYWNlaG9sZGVyIHtcXG4gIGNvbG9yOiAjNmI3MjgwO1xcbiAgb3BhY2l0eTogMTtcXG59XFxuLmZvcm0taW5wdXQ6OnBsYWNlaG9sZGVyLC5mb3JtLXRleHRhcmVhOjpwbGFjZWhvbGRlciB7XFxuICBjb2xvcjogIzZiNzI4MDtcXG4gIG9wYWNpdHk6IDE7XFxufVxcbi5mb3JtLWlucHV0Ojotd2Via2l0LWRhdGV0aW1lLWVkaXQtZmllbGRzLXdyYXBwZXIge1xcbiAgcGFkZGluZzogMDtcXG59XFxuLmZvcm0taW5wdXQ6Oi13ZWJraXQtZGF0ZS1hbmQtdGltZS12YWx1ZSB7XFxuICBtaW4taGVpZ2h0OiAxLjVlbTtcXG4gIHRleHQtYWxpZ246IGluaGVyaXQ7XFxufVxcbi5mb3JtLWlucHV0Ojotd2Via2l0LWRhdGV0aW1lLWVkaXQge1xcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XFxufVxcbi5mb3JtLWlucHV0Ojotd2Via2l0LWRhdGV0aW1lLWVkaXQsLmZvcm0taW5wdXQ6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC15ZWFyLWZpZWxkLC5mb3JtLWlucHV0Ojotd2Via2l0LWRhdGV0aW1lLWVkaXQtbW9udGgtZmllbGQsLmZvcm0taW5wdXQ6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1kYXktZmllbGQsLmZvcm0taW5wdXQ6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1ob3VyLWZpZWxkLC5mb3JtLWlucHV0Ojotd2Via2l0LWRhdGV0aW1lLWVkaXQtbWludXRlLWZpZWxkLC5mb3JtLWlucHV0Ojotd2Via2l0LWRhdGV0aW1lLWVkaXQtc2Vjb25kLWZpZWxkLC5mb3JtLWlucHV0Ojotd2Via2l0LWRhdGV0aW1lLWVkaXQtbWlsbGlzZWNvbmQtZmllbGQsLmZvcm0taW5wdXQ6Oi13ZWJraXQtZGF0ZXRpbWUtZWRpdC1tZXJpZGllbS1maWVsZCB7XFxuICBwYWRkaW5nLXRvcDogMDtcXG4gIHBhZGRpbmctYm90dG9tOiAwO1xcbn1cXG4uc3Itb25seSB7XFxuICBwb3NpdGlvbjogYWJzb2x1dGU7XFxuICB3aWR0aDogMXB4O1xcbiAgaGVpZ2h0OiAxcHg7XFxuICBwYWRkaW5nOiAwO1xcbiAgbWFyZ2luOiAtMXB4O1xcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcXG4gIGNsaXA6IHJlY3QoMCwgMCwgMCwgMCk7XFxuICB3aGl0ZS1zcGFjZTogbm93cmFwO1xcbiAgYm9yZGVyLXdpZHRoOiAwO1xcbn1cXG4uZml4ZWQge1xcbiAgcG9zaXRpb246IGZpeGVkO1xcbn1cXG4uYWJzb2x1dGUge1xcbiAgcG9zaXRpb246IGFic29sdXRlO1xcbn1cXG4ucmVsYXRpdmUge1xcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xcbn1cXG4uaW5zZXQtMCB7XFxuICBpbnNldDogMHB4O1xcbn1cXG4uaW5zZXQteS0wIHtcXG4gIHRvcDogMHB4O1xcbiAgYm90dG9tOiAwcHg7XFxufVxcbi4tcmlnaHQtMSB7XFxuICByaWdodDogLTAuMjVyZW07XFxufVxcbi4tdG9wLTEge1xcbiAgdG9wOiAtMC4yNXJlbTtcXG59XFxuLmxlZnQtMCB7XFxuICBsZWZ0OiAwcHg7XFxufVxcbi5sZWZ0LTMge1xcbiAgbGVmdDogMC43NXJlbTtcXG59XFxuLnJpZ2h0LTAge1xcbiAgcmlnaHQ6IDBweDtcXG59XFxuLnRvcC0xXFxcXC8yIHtcXG4gIHRvcDogNTAlO1xcbn1cXG4uei00MCB7XFxuICB6LWluZGV4OiA0MDtcXG59XFxuLnotNTAge1xcbiAgei1pbmRleDogNTA7XFxufVxcbi5teC1hdXRvIHtcXG4gIG1hcmdpbi1sZWZ0OiBhdXRvO1xcbiAgbWFyZ2luLXJpZ2h0OiBhdXRvO1xcbn1cXG4ubWItMiB7XFxuICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XFxufVxcbi5tYi00IHtcXG4gIG1hcmdpbi1ib3R0b206IDFyZW07XFxufVxcbi5tYi02IHtcXG4gIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcXG59XFxuLm1iLTgge1xcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcXG59XFxuLm1sLTEge1xcbiAgbWFyZ2luLWxlZnQ6IDAuMjVyZW07XFxufVxcbi5tbC0yIHtcXG4gIG1hcmdpbi1sZWZ0OiAwLjVyZW07XFxufVxcbi5tbC00IHtcXG4gIG1hcmdpbi1sZWZ0OiAxcmVtO1xcbn1cXG4ubWwtNSB7XFxuICBtYXJnaW4tbGVmdDogMS4yNXJlbTtcXG59XFxuLm1yLTEge1xcbiAgbWFyZ2luLXJpZ2h0OiAwLjI1cmVtO1xcbn1cXG4ubXItMiB7XFxuICBtYXJnaW4tcmlnaHQ6IDAuNXJlbTtcXG59XFxuLm1yLTMge1xcbiAgbWFyZ2luLXJpZ2h0OiAwLjc1cmVtO1xcbn1cXG4ubXQtMSB7XFxuICBtYXJnaW4tdG9wOiAwLjI1cmVtO1xcbn1cXG4ubXQtMiB7XFxuICBtYXJnaW4tdG9wOiAwLjVyZW07XFxufVxcbi5tdC00IHtcXG4gIG1hcmdpbi10b3A6IDFyZW07XFxufVxcbi5tdC02IHtcXG4gIG1hcmdpbi10b3A6IDEuNXJlbTtcXG59XFxuLm10LTgge1xcbiAgbWFyZ2luLXRvcDogMnJlbTtcXG59XFxuLm10LTMge1xcbiAgbWFyZ2luLXRvcDogMC43NXJlbTtcXG59XFxuLm10LTBcXFxcLjUge1xcbiAgbWFyZ2luLXRvcDogMC4xMjVyZW07XFxufVxcbi5ibG9jayB7XFxuICBkaXNwbGF5OiBibG9jaztcXG59XFxuLmlubGluZS1ibG9jayB7XFxuICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XFxufVxcbi5pbmxpbmUge1xcbiAgZGlzcGxheTogaW5saW5lO1xcbn1cXG4uZmxleCB7XFxuICBkaXNwbGF5OiBmbGV4O1xcbn1cXG4uaW5saW5lLWZsZXgge1xcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XFxufVxcbi50YWJsZSB7XFxuICBkaXNwbGF5OiB0YWJsZTtcXG59XFxuLmdyaWQge1xcbiAgZGlzcGxheTogZ3JpZDtcXG59XFxuLmhpZGRlbiB7XFxuICBkaXNwbGF5OiBub25lO1xcbn1cXG4uaC0xMiB7XFxuICBoZWlnaHQ6IDNyZW07XFxufVxcbi5oLTIge1xcbiAgaGVpZ2h0OiAwLjVyZW07XFxufVxcbi5oLTIwIHtcXG4gIGhlaWdodDogNXJlbTtcXG59XFxuLmgtNCB7XFxuICBoZWlnaHQ6IDFyZW07XFxufVxcbi5oLTUge1xcbiAgaGVpZ2h0OiAxLjI1cmVtO1xcbn1cXG4uaC02IHtcXG4gIGhlaWdodDogMS41cmVtO1xcbn1cXG4uaC02NCB7XFxuICBoZWlnaHQ6IDE2cmVtO1xcbn1cXG4uaC04IHtcXG4gIGhlaWdodDogMnJlbTtcXG59XFxuLmgtZnVsbCB7XFxuICBoZWlnaHQ6IDEwMCU7XFxufVxcbi5tYXgtaC05NiB7XFxuICBtYXgtaGVpZ2h0OiAyNHJlbTtcXG59XFxuLm1pbi1oLXNjcmVlbiB7XFxuICBtaW4taGVpZ2h0OiAxMDB2aDtcXG59XFxuLnctMCB7XFxuICB3aWR0aDogMHB4O1xcbn1cXG4udy0xMSB7XFxuICB3aWR0aDogMi43NXJlbTtcXG59XFxuLnctMTIge1xcbiAgd2lkdGg6IDNyZW07XFxufVxcbi53LTIge1xcbiAgd2lkdGg6IDAuNXJlbTtcXG59XFxuLnctMjAge1xcbiAgd2lkdGg6IDVyZW07XFxufVxcbi53LTMyIHtcXG4gIHdpZHRoOiA4cmVtO1xcbn1cXG4udy00IHtcXG4gIHdpZHRoOiAxcmVtO1xcbn1cXG4udy00OCB7XFxuICB3aWR0aDogMTJyZW07XFxufVxcbi53LTUge1xcbiAgd2lkdGg6IDEuMjVyZW07XFxufVxcbi53LTYge1xcbiAgd2lkdGg6IDEuNXJlbTtcXG59XFxuLnctNjQge1xcbiAgd2lkdGg6IDE2cmVtO1xcbn1cXG4udy04IHtcXG4gIHdpZHRoOiAycmVtO1xcbn1cXG4udy04MCB7XFxuICB3aWR0aDogMjByZW07XFxufVxcbi53LWZ1bGwge1xcbiAgd2lkdGg6IDEwMCU7XFxufVxcbi5taW4tdy0wIHtcXG4gIG1pbi13aWR0aDogMHB4O1xcbn1cXG4ubWF4LXctN3hsIHtcXG4gIG1heC13aWR0aDogODByZW07XFxufVxcbi5tYXgtdy1tZCB7XFxuICBtYXgtd2lkdGg6IDI4cmVtO1xcbn1cXG4ubWF4LXcteHMge1xcbiAgbWF4LXdpZHRoOiAyMHJlbTtcXG59XFxuLmZsZXgtMSB7XFxuICBmbGV4OiAxIDEgMCU7XFxufVxcbi5mbGV4LXNocmluay0wIHtcXG4gIGZsZXgtc2hyaW5rOiAwO1xcbn1cXG4uLXRyYW5zbGF0ZS14LWZ1bGwge1xcbiAgLS10dy10cmFuc2xhdGUteDogLTEwMCU7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG59XFxuLi10cmFuc2xhdGUteS0xXFxcXC8yIHtcXG4gIC0tdHctdHJhbnNsYXRlLXk6IC01MCU7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG59XFxuLnRyYW5zbGF0ZS14LTAge1xcbiAgLS10dy10cmFuc2xhdGUteDogMHB4O1xcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUodmFyKC0tdHctdHJhbnNsYXRlLXgpLCB2YXIoLS10dy10cmFuc2xhdGUteSkpIHJvdGF0ZSh2YXIoLS10dy1yb3RhdGUpKSBza2V3WCh2YXIoLS10dy1za2V3LXgpKSBza2V3WSh2YXIoLS10dy1za2V3LXkpKSBzY2FsZVgodmFyKC0tdHctc2NhbGUteCkpIHNjYWxlWSh2YXIoLS10dy1zY2FsZS15KSk7XFxufVxcbi50cmFuc2Zvcm0ge1xcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUodmFyKC0tdHctdHJhbnNsYXRlLXgpLCB2YXIoLS10dy10cmFuc2xhdGUteSkpIHJvdGF0ZSh2YXIoLS10dy1yb3RhdGUpKSBza2V3WCh2YXIoLS10dy1za2V3LXgpKSBza2V3WSh2YXIoLS10dy1za2V3LXkpKSBzY2FsZVgodmFyKC0tdHctc2NhbGUteCkpIHNjYWxlWSh2YXIoLS10dy1zY2FsZS15KSk7XFxufVxcbkBrZXlmcmFtZXMgc3BpbiB7XFxuXFxuICB0byB7XFxuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XFxuICB9XFxufVxcbi5hbmltYXRlLXNwaW4ge1xcbiAgYW5pbWF0aW9uOiBzcGluIDFzIGxpbmVhciBpbmZpbml0ZTtcXG59XFxuLmN1cnNvci1wb2ludGVyIHtcXG4gIGN1cnNvcjogcG9pbnRlcjtcXG59XFxuLmFwcGVhcmFuY2Utbm9uZSB7XFxuICAtd2Via2l0LWFwcGVhcmFuY2U6IG5vbmU7XFxuICAgICAtbW96LWFwcGVhcmFuY2U6IG5vbmU7XFxuICAgICAgICAgIGFwcGVhcmFuY2U6IG5vbmU7XFxufVxcbi5ncmlkLWNvbHMtMSB7XFxuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgxLCBtaW5tYXgoMCwgMWZyKSk7XFxufVxcbi5mbGV4LWNvbCB7XFxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xcbn1cXG4uaXRlbXMtc3RhcnQge1xcbiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XFxufVxcbi5pdGVtcy1lbmQge1xcbiAgYWxpZ24taXRlbXM6IGZsZXgtZW5kO1xcbn1cXG4uaXRlbXMtY2VudGVyIHtcXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XFxufVxcbi5qdXN0aWZ5LWVuZCB7XFxuICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xcbn1cXG4uanVzdGlmeS1jZW50ZXIge1xcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XFxufVxcbi5qdXN0aWZ5LWJldHdlZW4ge1xcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xcbn1cXG4uZ2FwLTQge1xcbiAgZ2FwOiAxcmVtO1xcbn1cXG4uZ2FwLTUge1xcbiAgZ2FwOiAxLjI1cmVtO1xcbn1cXG4uZ2FwLTYge1xcbiAgZ2FwOiAxLjVyZW07XFxufVxcbi5zcGFjZS14LTIgPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteC1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXJpZ2h0OiBjYWxjKDAuNXJlbSAqIHZhcigtLXR3LXNwYWNlLXgtcmV2ZXJzZSkpO1xcbiAgbWFyZ2luLWxlZnQ6IGNhbGMoMC41cmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteC1yZXZlcnNlKSkpO1xcbn1cXG4uc3BhY2UteC0zID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXgtcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi1yaWdodDogY2FsYygwLjc1cmVtICogdmFyKC0tdHctc3BhY2UteC1yZXZlcnNlKSk7XFxuICBtYXJnaW4tbGVmdDogY2FsYygwLjc1cmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteC1yZXZlcnNlKSkpO1xcbn1cXG4uc3BhY2UteC00ID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXgtcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi1yaWdodDogY2FsYygxcmVtICogdmFyKC0tdHctc3BhY2UteC1yZXZlcnNlKSk7XFxuICBtYXJnaW4tbGVmdDogY2FsYygxcmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteC1yZXZlcnNlKSkpO1xcbn1cXG4uc3BhY2UteS0xID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXktcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi10b3A6IGNhbGMoMC4yNXJlbSAqIGNhbGMoMSAtIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpKTtcXG4gIG1hcmdpbi1ib3R0b206IGNhbGMoMC4yNXJlbSAqIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpO1xcbn1cXG4uc3BhY2UteS0zID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXktcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi10b3A6IGNhbGMoMC43NXJlbSAqIGNhbGMoMSAtIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpKTtcXG4gIG1hcmdpbi1ib3R0b206IGNhbGMoMC43NXJlbSAqIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpO1xcbn1cXG4uc3BhY2UteS00ID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXktcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi10b3A6IGNhbGMoMXJlbSAqIGNhbGMoMSAtIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpKTtcXG4gIG1hcmdpbi1ib3R0b206IGNhbGMoMXJlbSAqIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpO1xcbn1cXG4uc3BhY2UteS02ID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXktcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi10b3A6IGNhbGMoMS41cmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteS1yZXZlcnNlKSkpO1xcbiAgbWFyZ2luLWJvdHRvbTogY2FsYygxLjVyZW0gKiB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKTtcXG59XFxuLnNwYWNlLXktOCA+IDpub3QoW2hpZGRlbl0pIH4gOm5vdChbaGlkZGVuXSkge1xcbiAgLS10dy1zcGFjZS15LXJldmVyc2U6IDA7XFxuICBtYXJnaW4tdG9wOiBjYWxjKDJyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKSk7XFxuICBtYXJnaW4tYm90dG9tOiBjYWxjKDJyZW0gKiB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKTtcXG59XFxuLm92ZXJmbG93LWhpZGRlbiB7XFxuICBvdmVyZmxvdzogaGlkZGVuO1xcbn1cXG4ub3ZlcmZsb3cteC1hdXRvIHtcXG4gIG92ZXJmbG93LXg6IGF1dG87XFxufVxcbi5vdmVyZmxvdy15LWF1dG8ge1xcbiAgb3ZlcmZsb3cteTogYXV0bztcXG59XFxuLnRydW5jYXRlIHtcXG4gIG92ZXJmbG93OiBoaWRkZW47XFxuICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpcztcXG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7XFxufVxcbi5yb3VuZGVkIHtcXG4gIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XFxufVxcbi5yb3VuZGVkLWZ1bGwge1xcbiAgYm9yZGVyLXJhZGl1czogOTk5OXB4O1xcbn1cXG4ucm91bmRlZC1sZyB7XFxuICBib3JkZXItcmFkaXVzOiAwLjVyZW07XFxufVxcbi5yb3VuZGVkLW1kIHtcXG4gIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xcbn1cXG4ucm91bmRlZC14bCB7XFxuICBib3JkZXItcmFkaXVzOiAwLjc1cmVtO1xcbn1cXG4ucm91bmRlZC0yeGwge1xcbiAgYm9yZGVyLXJhZGl1czogMXJlbTtcXG59XFxuLmJvcmRlciB7XFxuICBib3JkZXItd2lkdGg6IDFweDtcXG59XFxuLmJvcmRlci0yIHtcXG4gIGJvcmRlci13aWR0aDogMnB4O1xcbn1cXG4uYm9yZGVyLWIge1xcbiAgYm9yZGVyLWJvdHRvbS13aWR0aDogMXB4O1xcbn1cXG4uYm9yZGVyLXQge1xcbiAgYm9yZGVyLXRvcC13aWR0aDogMXB4O1xcbn1cXG4uYm9yZGVyLWdyYXktMTAwIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigyNDMgMjQ0IDI0NiAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5ib3JkZXItZ3JheS0yMDAge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDIyOSAyMzEgMjM1IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLmJvcmRlci1ncmF5LTMwMCB7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjA5IDIxMyAyMTkgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbn1cXG4uYm9yZGVyLXByaW1hcnktNTAwIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigxNCAxNjUgMjMzIC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLmJvcmRlci1yZWQtMzAwIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigyNTIgMTY1IDE2NSAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5ib3JkZXItdHJhbnNwYXJlbnQge1xcbiAgYm9yZGVyLWNvbG9yOiB0cmFuc3BhcmVudDtcXG59XFxuLmJvcmRlci1uZXV0cmFsLTE1MCB7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjQwIDI0MCAyNDAgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbn1cXG4uYm9yZGVyLW5ldXRyYWwtMTAwIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigyNDUgMjQ1IDI0NSAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5ib3JkZXItcmVkLTUwMCB7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjM5IDY4IDY4IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLmJvcmRlci10LXByaW1hcnktNjAwIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItdG9wLWNvbG9yOiByZ2IoMiAxMzIgMTk5IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLWJsYWNrIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigwIDAgMCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLWJsdWUtMTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyMTkgMjM0IDI1NCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLWJsdWUtNTAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIzOSAyNDYgMjU1IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctYmx1ZS01MDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDU5IDEzMCAyNDYgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1lbWVyYWxkLTUwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMTYgMTg1IDEyOSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLWdyYXktMTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNDMgMjQ0IDI0NiAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLWdyYXktMjAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyMjkgMjMxIDIzNSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLWdyYXktNTAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0OSAyNTAgMjUxIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctZ3JlZW4tMTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyMjAgMjUyIDIzMSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLWdyZWVuLTUwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNDAgMjUzIDI0NCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLWdyZWVuLTUwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMzQgMTk3IDk0IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctaW5kaWdvLTEwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjI0IDIzMSAyNTUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1pbmRpZ28tNTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYig5OSAxMDIgMjQxIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctb3JhbmdlLTEwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU1IDIzNyAyMTMgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1waW5rLTUwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjM2IDcyIDE1MyAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLXByaW1hcnktMTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyMjQgMjQyIDI1NCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLXByaW1hcnktNTAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0MCAyNDkgMjU1IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctcHJpbWFyeS02MDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIgMTMyIDE5OSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLXB1cnBsZS0xMDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0MyAyMzIgMjU1IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctcHVycGxlLTUwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMTY4IDg1IDI0NyAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLXJlZC0xMDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NCAyMjYgMjI2IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctcmVkLTUwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjM5IDY4IDY4IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmctd2hpdGUge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uYmcteWVsbG93LTEwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU0IDI0OSAxOTUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy15ZWxsb3ctNTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyMzQgMTc5IDggLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1uZXV0cmFsLTUwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTAgMjUwIDI1MCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLXN1cmZhY2UtcHJpbWFyeSB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5iZy1zdGF0dXMtZXJyb3Ige1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIzOSA2OCA2OCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLWdyYXktNTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigxMDcgMTE0IDEyOCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLmJnLW9wYWNpdHktNTAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAwLjU7XFxufVxcbi5iZy1vcGFjaXR5LTc1IHtcXG4gIC0tdHctYmctb3BhY2l0eTogMC43NTtcXG59XFxuLnAtMSB7XFxuICBwYWRkaW5nOiAwLjI1cmVtO1xcbn1cXG4ucC0xXFxcXC41IHtcXG4gIHBhZGRpbmc6IDAuMzc1cmVtO1xcbn1cXG4ucC0xMiB7XFxuICBwYWRkaW5nOiAzcmVtO1xcbn1cXG4ucC0yIHtcXG4gIHBhZGRpbmc6IDAuNXJlbTtcXG59XFxuLnAtMyB7XFxuICBwYWRkaW5nOiAwLjc1cmVtO1xcbn1cXG4ucC00IHtcXG4gIHBhZGRpbmc6IDFyZW07XFxufVxcbi5wLTUge1xcbiAgcGFkZGluZzogMS4yNXJlbTtcXG59XFxuLnAtNiB7XFxuICBwYWRkaW5nOiAxLjVyZW07XFxufVxcbi5wLTJcXFxcLjUge1xcbiAgcGFkZGluZzogMC42MjVyZW07XFxufVxcbi5weC0yIHtcXG4gIHBhZGRpbmctbGVmdDogMC41cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMC41cmVtO1xcbn1cXG4ucHgtMlxcXFwuNSB7XFxuICBwYWRkaW5nLWxlZnQ6IDAuNjI1cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMC42MjVyZW07XFxufVxcbi5weC0zIHtcXG4gIHBhZGRpbmctbGVmdDogMC43NXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDAuNzVyZW07XFxufVxcbi5weC00IHtcXG4gIHBhZGRpbmctbGVmdDogMXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDFyZW07XFxufVxcbi5weC02IHtcXG4gIHBhZGRpbmctbGVmdDogMS41cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMS41cmVtO1xcbn1cXG4ucHktMFxcXFwuNSB7XFxuICBwYWRkaW5nLXRvcDogMC4xMjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC4xMjVyZW07XFxufVxcbi5weS0xIHtcXG4gIHBhZGRpbmctdG9wOiAwLjI1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuMjVyZW07XFxufVxcbi5weS0xMiB7XFxuICBwYWRkaW5nLXRvcDogM3JlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAzcmVtO1xcbn1cXG4ucHktMiB7XFxuICBwYWRkaW5nLXRvcDogMC41cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuNXJlbTtcXG59XFxuLnB5LTMge1xcbiAgcGFkZGluZy10b3A6IDAuNzVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC43NXJlbTtcXG59XFxuLnB5LTQge1xcbiAgcGFkZGluZy10b3A6IDFyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMXJlbTtcXG59XFxuLnB5LTUge1xcbiAgcGFkZGluZy10b3A6IDEuMjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMS4yNXJlbTtcXG59XFxuLnBsLTEwIHtcXG4gIHBhZGRpbmctbGVmdDogMi41cmVtO1xcbn1cXG4ucHItMTAge1xcbiAgcGFkZGluZy1yaWdodDogMi41cmVtO1xcbn1cXG4ucHItMyB7XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjc1cmVtO1xcbn1cXG4ucGItMjAge1xcbiAgcGFkZGluZy1ib3R0b206IDVyZW07XFxufVxcbi5wYi00IHtcXG4gIHBhZGRpbmctYm90dG9tOiAxcmVtO1xcbn1cXG4ucHQtNCB7XFxuICBwYWRkaW5nLXRvcDogMXJlbTtcXG59XFxuLnB0LTUge1xcbiAgcGFkZGluZy10b3A6IDEuMjVyZW07XFxufVxcbi50ZXh0LWxlZnQge1xcbiAgdGV4dC1hbGlnbjogbGVmdDtcXG59XFxuLnRleHQtY2VudGVyIHtcXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcXG59XFxuLmFsaWduLWJvdHRvbSB7XFxuICB2ZXJ0aWNhbC1hbGlnbjogYm90dG9tO1xcbn1cXG4uZm9udC1hcmFiaWMge1xcbiAgZm9udC1mYW1pbHk6IENhaXJvLCB1aS1zYW5zLXNlcmlmLCBzeXN0ZW0tdWksIHNhbnMtc2VyaWY7XFxufVxcbi5mb250LWVuZ2xpc2gge1xcbiAgZm9udC1mYW1pbHk6IEludGVyLCB1aS1zYW5zLXNlcmlmLCBzeXN0ZW0tdWksIHNhbnMtc2VyaWY7XFxufVxcbi50ZXh0LTJ4bCB7XFxuICBmb250LXNpemU6IDEuNXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAycmVtO1xcbn1cXG4udGV4dC0zeGwge1xcbiAgZm9udC1zaXplOiAxLjg3NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAyLjI1cmVtO1xcbn1cXG4udGV4dC1sZyB7XFxuICBmb250LXNpemU6IDEuMTI1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDEuNzVyZW07XFxufVxcbi50ZXh0LXNtIHtcXG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XFxuICBsaW5lLWhlaWdodDogMS4yNXJlbTtcXG59XFxuLnRleHQteHMge1xcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDFyZW07XFxufVxcbi5mb250LWJvbGQge1xcbiAgZm9udC13ZWlnaHQ6IDcwMDtcXG59XFxuLmZvbnQtZXh0cmFib2xkIHtcXG4gIGZvbnQtd2VpZ2h0OiA4MDA7XFxufVxcbi5mb250LW1lZGl1bSB7XFxuICBmb250LXdlaWdodDogNTAwO1xcbn1cXG4uZm9udC1zZW1pYm9sZCB7XFxuICBmb250LXdlaWdodDogNjAwO1xcbn1cXG4uY2FwaXRhbGl6ZSB7XFxuICB0ZXh0LXRyYW5zZm9ybTogY2FwaXRhbGl6ZTtcXG59XFxuLmxlYWRpbmctNiB7XFxuICBsaW5lLWhlaWdodDogMS41cmVtO1xcbn1cXG4udGV4dC1ibHVlLTQwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoOTYgMTY1IDI1MCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ibHVlLTYwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMzcgOTkgMjM1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LWJsdWUtNzAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyOSA3OCAyMTYgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtYmx1ZS04MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDMwIDY0IDE3NSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ibHVlLTkwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMzAgNTggMTM4IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LWdyYXktNDAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigxNTYgMTYzIDE3NSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ncmF5LTUwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTA3IDExNCAxMjggLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtZ3JheS02MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDc1IDg1IDk5IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LWdyYXktNzAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYig1NSA2NSA4MSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ncmF5LTgwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMzEgNDEgNTUgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtZ3JheS05MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDE3IDI0IDM5IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LWdyZWVuLTQwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoNzQgMjIyIDEyOCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ncmVlbi02MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIyIDE2MyA3NCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ncmVlbi03MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIxIDEyOCA2MSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ncmVlbi04MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIyIDEwMSA1MiAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1ncmVlbi05MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIwIDgzIDQ1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LWluZGlnby04MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDU1IDQ4IDE2MyAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1vcmFuZ2UtNjAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyMzQgODggMTIgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtb3JhbmdlLTgwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTU0IDUyIDE4IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LXByaW1hcnktNjAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyIDEzMiAxOTkgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtcHJpbWFyeS03MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDMgMTA1IDE2MSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC1wcmltYXJ5LTkwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTIgNzQgMTEwIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LXB1cnBsZS04MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDEwNyAzMyAxNjggLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtcmVkLTQwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjQ4IDExMyAxMTMgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQtcmVkLTYwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjIwIDM4IDM4IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LXJlZC04MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDE1MyAyNyAyNyAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC13aGl0ZSB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQteWVsbG93LTQwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjUwIDIwNCAyMSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC15ZWxsb3ctNjAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyMDIgMTM4IDQgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLnRleHQteWVsbG93LTgwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTMzIDc3IDE0IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi50ZXh0LXRleHQtc2Vjb25kYXJ5IHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYig4MiA4MiA4MiAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4udGV4dC10ZXh0LXByaW1hcnkge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIzIDIzIDIzIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi5wbGFjZWhvbGRlci1ncmF5LTUwMDo6LW1vei1wbGFjZWhvbGRlciB7XFxuICAtLXR3LXBsYWNlaG9sZGVyLW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDEwNyAxMTQgMTI4IC8gdmFyKC0tdHctcGxhY2Vob2xkZXItb3BhY2l0eSwgMSkpO1xcbn1cXG4ucGxhY2Vob2xkZXItZ3JheS01MDA6OnBsYWNlaG9sZGVyIHtcXG4gIC0tdHctcGxhY2Vob2xkZXItb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTA3IDExNCAxMjggLyB2YXIoLS10dy1wbGFjZWhvbGRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5zaGFkb3cge1xcbiAgLS10dy1zaGFkb3c6IDAgMXB4IDNweCAwIHJnYigwIDAgMCAvIDAuMSksIDAgMXB4IDJweCAtMXB4IHJnYigwIDAgMCAvIDAuMSk7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDFweCAzcHggMCB2YXIoLS10dy1zaGFkb3ctY29sb3IpLCAwIDFweCAycHggLTFweCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG59XFxuLnNoYWRvdy1sZyB7XFxuICAtLXR3LXNoYWRvdzogMCAxMHB4IDE1cHggLTNweCByZ2IoMCAwIDAgLyAwLjEpLCAwIDRweCA2cHggLTRweCByZ2IoMCAwIDAgLyAwLjEpO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCAxMHB4IDE1cHggLTNweCB2YXIoLS10dy1zaGFkb3ctY29sb3IpLCAwIDRweCA2cHggLTRweCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG59XFxuLnNoYWRvdy1zbSB7XFxuICAtLXR3LXNoYWRvdzogMCAxcHggMnB4IDAgcmdiKDAgMCAwIC8gMC4wNSk7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDFweCAycHggMCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG59XFxuLnNoYWRvdy1zb2Z0IHtcXG4gIC0tdHctc2hhZG93OiAwIDJweCA4cHggMCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCAycHggOHB4IDAgdmFyKC0tdHctc2hhZG93LWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctcmluZy1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXNoYWRvdyk7XFxufVxcbi5zaGFkb3ctbGFyZ2Uge1xcbiAgLS10dy1zaGFkb3c6IDAgOHB4IDI0cHggMCByZ2JhKDAsIDAsIDAsIDAuMTIpO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCA4cHggMjRweCAwIHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbn1cXG4uc2hhZG93LXhsIHtcXG4gIC0tdHctc2hhZG93OiAwIDIwcHggMjVweCAtNXB4IHJnYigwIDAgMCAvIDAuMSksIDAgOHB4IDEwcHggLTZweCByZ2IoMCAwIDAgLyAwLjEpO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCAyMHB4IDI1cHggLTVweCB2YXIoLS10dy1zaGFkb3ctY29sb3IpLCAwIDhweCAxMHB4IC02cHggdmFyKC0tdHctc2hhZG93LWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctcmluZy1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXNoYWRvdyk7XFxufVxcbi5yaW5nLTEge1xcbiAgLS10dy1yaW5nLW9mZnNldC1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSB2YXIoLS10dy1yaW5nLW9mZnNldC1jb2xvcik7XFxuICAtLXR3LXJpbmctc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCBjYWxjKDFweCArIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSkgdmFyKC0tdHctcmluZy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3cpLCB2YXIoLS10dy1yaW5nLXNoYWRvdyksIHZhcigtLXR3LXNoYWRvdywgMCAwICMwMDAwKTtcXG59XFxuLnJpbmctYmxhY2sge1xcbiAgLS10dy1yaW5nLW9wYWNpdHk6IDE7XFxuICAtLXR3LXJpbmctY29sb3I6IHJnYigwIDAgMCAvIHZhcigtLXR3LXJpbmctb3BhY2l0eSwgMSkpO1xcbn1cXG4ucmluZy1vcGFjaXR5LTUge1xcbiAgLS10dy1yaW5nLW9wYWNpdHk6IDAuMDU7XFxufVxcbi5maWx0ZXIge1xcbiAgZmlsdGVyOiB2YXIoLS10dy1ibHVyKSB2YXIoLS10dy1icmlnaHRuZXNzKSB2YXIoLS10dy1jb250cmFzdCkgdmFyKC0tdHctZ3JheXNjYWxlKSB2YXIoLS10dy1odWUtcm90YXRlKSB2YXIoLS10dy1pbnZlcnQpIHZhcigtLXR3LXNhdHVyYXRlKSB2YXIoLS10dy1zZXBpYSkgdmFyKC0tdHctZHJvcC1zaGFkb3cpO1xcbn1cXG4udHJhbnNpdGlvbi1hbGwge1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogYWxsO1xcbiAgdHJhbnNpdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAxNTBtcztcXG59XFxuLnRyYW5zaXRpb24tY29sb3JzIHtcXG4gIHRyYW5zaXRpb24tcHJvcGVydHk6IGNvbG9yLCBiYWNrZ3JvdW5kLWNvbG9yLCBib3JkZXItY29sb3IsIHRleHQtZGVjb3JhdGlvbi1jb2xvciwgZmlsbCwgc3Ryb2tlO1xcbiAgdHJhbnNpdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAxNTBtcztcXG59XFxuLnRyYW5zaXRpb24tdHJhbnNmb3JtIHtcXG4gIHRyYW5zaXRpb24tcHJvcGVydHk6IHRyYW5zZm9ybTtcXG4gIHRyYW5zaXRpb24tdGltaW5nLWZ1bmN0aW9uOiBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMTUwbXM7XFxufVxcbi50cmFuc2l0aW9uLW9wYWNpdHkge1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogb3BhY2l0eTtcXG4gIHRyYW5zaXRpb24tdGltaW5nLWZ1bmN0aW9uOiBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMTUwbXM7XFxufVxcbi5kdXJhdGlvbi0zMDAge1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMzAwbXM7XFxufVxcbi5kdXJhdGlvbi0yMDAge1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMjAwbXM7XFxufVxcblxcbi8qIEltcG9ydCBmb250cyAqL1xcbkBpbXBvcnQgdXJsKCdodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2NzczI/ZmFtaWx5PUludGVyOndnaHRAMzAwOzQwMDs1MDA7NjAwOzcwMCZkaXNwbGF5PXN3YXAnKTtcXG5AaW1wb3J0IHVybCgnaHR0cHM6Ly9mb250cy5nb29nbGVhcGlzLmNvbS9jc3MyP2ZhbWlseT1DYWlybzp3Z2h0QDMwMDs0MDA7NTAwOzYwMDs3MDAmZGlzcGxheT1zd2FwJyk7XFxuXFxuLyogTW9kZXJuIE1pbmltYWxpc3QgQmFzZSBTdHlsZXMgKi9cXG5odG1sIHtcXG4gIHNjcm9sbC1iZWhhdmlvcjogc21vb3RoO1xcbn1cXG5cXG5ib2R5IHtcXG4gIGZvbnQtZmFtaWx5OiAnSW50ZXInLCAndWktc2Fucy1zZXJpZicsICdzeXN0ZW0tdWknLCBzYW5zLXNlcmlmO1xcbiAgbGluZS1oZWlnaHQ6IDEuNjtcXG4gIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7IC8qIG5ldXRyYWwtNTAgKi9cXG4gIGNvbG9yOiAjMTcxNzE3OyAvKiB0ZXh0LXByaW1hcnkgKi9cXG4gIGZvbnQtd2VpZ2h0OiA0MDA7XFxuICAtd2Via2l0LWZvbnQtc21vb3RoaW5nOiBhbnRpYWxpYXNlZDtcXG4gIC1tb3otb3N4LWZvbnQtc21vb3RoaW5nOiBncmF5c2NhbGU7XFxufVxcblxcbi8qIEFyYWJpYyBmb250ICovXFxuLmZvbnQtYXJhYmljIHtcXG4gIGZvbnQtZmFtaWx5OiAnQ2Fpcm8nLCBzYW5zLXNlcmlmO1xcbn1cXG5cXG4uZm9udC1lbmdsaXNoIHtcXG4gIGZvbnQtZmFtaWx5OiAnSW50ZXInLCBzYW5zLXNlcmlmO1xcbn1cXG5cXG4vKiBSVEwgc3VwcG9ydCAqL1xcbltkaXI9XFxcInJ0bFxcXCJdIHtcXG4gIHRleHQtYWxpZ246IHJpZ2h0O1xcbn1cXG5cXG5bZGlyPVxcXCJydGxcXFwiXSAucnRsXFxcXDp0ZXh0LWxlZnQge1xcbiAgdGV4dC1hbGlnbjogbGVmdDtcXG59XFxuXFxuW2Rpcj1cXFwicnRsXFxcIl0gLnJ0bFxcXFw6dGV4dC1yaWdodCB7XFxuICB0ZXh0LWFsaWduOiByaWdodDtcXG59XFxuXFxuLyogQ3VzdG9tIHNjcm9sbGJhciAqL1xcbjo6LXdlYmtpdC1zY3JvbGxiYXIge1xcbiAgd2lkdGg6IDZweDtcXG4gIGhlaWdodDogNnB4O1xcbn1cXG5cXG46Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcXG4gIGJhY2tncm91bmQ6ICNmMWYxZjE7XFxuICBib3JkZXItcmFkaXVzOiAzcHg7XFxufVxcblxcbjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xcbiAgYmFja2dyb3VuZDogI2MxYzFjMTtcXG4gIGJvcmRlci1yYWRpdXM6IDNweDtcXG59XFxuXFxuOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7XFxuICBiYWNrZ3JvdW5kOiAjYThhOGE4O1xcbn1cXG5cXG4vKiBNb2Rlcm4gRm9ybSBTdHlsZXMgKi9cXG4uZm9ybS1pbnB1dCB7XFxuICBkaXNwbGF5OiBibG9jaztcXG4gIHdpZHRoOiAxMDAlO1xcbiAgYm9yZGVyLXJhZGl1czogMC43NXJlbTtcXG4gIGJvcmRlci13aWR0aDogMXB4O1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDIyOSAyMjkgMjI5IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gIHBhZGRpbmctbGVmdDogMXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDFyZW07XFxuICBwYWRkaW5nLXRvcDogMC43NXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjc1cmVtO1xcbn1cXG4uZm9ybS1pbnB1dDo6LW1vei1wbGFjZWhvbGRlciB7XFxuICAtLXR3LXBsYWNlaG9sZGVyLW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDExNSAxMTUgMTE1IC8gdmFyKC0tdHctcGxhY2Vob2xkZXItb3BhY2l0eSwgMSkpO1xcbn1cXG4uZm9ybS1pbnB1dDo6cGxhY2Vob2xkZXIge1xcbiAgLS10dy1wbGFjZWhvbGRlci1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigxMTUgMTE1IDExNSAvIHZhcigtLXR3LXBsYWNlaG9sZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLmZvcm0taW5wdXQge1xcbiAgLS10dy1zaGFkb3c6IDAgMnB4IDhweCAwIHJnYmEoMCwgMCwgMCwgMC4wNik7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDJweCA4cHggMCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG4gIHRyYW5zaXRpb24tcHJvcGVydHk6IGFsbDtcXG4gIHRyYW5zaXRpb24tdGltaW5nLWZ1bmN0aW9uOiBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMjAwbXM7XFxuICBib3gtc2hhZG93OiAwIDJweCA4cHggMCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xcbn1cXG4uZm9ybS1pbnB1dDpmb2N1cyB7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoNTYgMTg5IDI0OCAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxuICBvdXRsaW5lOiAycHggc29saWQgdHJhbnNwYXJlbnQ7XFxuICBvdXRsaW5lLW9mZnNldDogMnB4O1xcbiAgLS10dy1yaW5nLW9mZnNldC1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSB2YXIoLS10dy1yaW5nLW9mZnNldC1jb2xvcik7XFxuICAtLXR3LXJpbmctc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCBjYWxjKDJweCArIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSkgdmFyKC0tdHctcmluZy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3cpLCB2YXIoLS10dy1yaW5nLXNoYWRvdyksIHZhcigtLXR3LXNoYWRvdywgMCAwICMwMDAwKTtcXG4gIC0tdHctcmluZy1vcGFjaXR5OiAxO1xcbiAgLS10dy1yaW5nLWNvbG9yOiByZ2IoNTYgMTg5IDI0OCAvIHZhcigtLXR3LXJpbmctb3BhY2l0eSwgMSkpO1xcbn1cXG5AbWVkaWEgKG1pbi13aWR0aDogNjQwcHgpIHtcXG5cXG4gIC5mb3JtLWlucHV0IHtcXG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcXG4gICAgbGluZS1oZWlnaHQ6IDEuMjVyZW07XFxuICB9XFxufVxcblxcbi5mb3JtLWlucHV0OmludmFsaWQge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDIzOSA2OCA2OCAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcblxcbi5mb3JtLWlucHV0OmludmFsaWQ6Zm9jdXMge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDIzOSA2OCA2OCAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXJpbmctb3BhY2l0eTogMTtcXG4gIC0tdHctcmluZy1jb2xvcjogcmdiKDIzOSA2OCA2OCAvIHZhcigtLXR3LXJpbmctb3BhY2l0eSwgMSkpO1xcbn1cXG5cXG4uZm9ybS1sYWJlbCB7XFxuICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XFxuICBkaXNwbGF5OiBibG9jaztcXG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XFxuICBsaW5lLWhlaWdodDogMS4yNXJlbTtcXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjMgMjMgMjMgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuXFxuLmZvcm0tZXJyb3Ige1xcbiAgbWFyZ2luLXRvcDogMC4yNXJlbTtcXG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XFxuICBsaW5lLWhlaWdodDogMS4yNXJlbTtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyMzkgNjggNjggLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuXFxuLyogTW9kZXJuIEJ1dHRvbiBTdHlsZXMgKi9cXG4uYnRuIHtcXG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xcbiAgYm9yZGVyLXJhZGl1czogMC43NXJlbTtcXG4gIGJvcmRlci13aWR0aDogMXB4O1xcbiAgYm9yZGVyLWNvbG9yOiB0cmFuc3BhcmVudDtcXG4gIHBhZGRpbmctbGVmdDogMXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDFyZW07XFxuICBwYWRkaW5nLXRvcDogMC42MjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC42MjVyZW07XFxuICBmb250LXNpemU6IDAuODc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDEuMjVyZW07XFxuICBmb250LXdlaWdodDogNTAwO1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogYWxsO1xcbiAgdHJhbnNpdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAyMDBtcztcXG59XFxuLmJ0bjpmb2N1cyB7XFxuICBvdXRsaW5lOiAycHggc29saWQgdHJhbnNwYXJlbnQ7XFxuICBvdXRsaW5lLW9mZnNldDogMnB4O1xcbiAgLS10dy1yaW5nLW9mZnNldC1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSB2YXIoLS10dy1yaW5nLW9mZnNldC1jb2xvcik7XFxuICAtLXR3LXJpbmctc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCBjYWxjKDJweCArIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSkgdmFyKC0tdHctcmluZy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3cpLCB2YXIoLS10dy1yaW5nLXNoYWRvdyksIHZhcigtLXR3LXNoYWRvdywgMCAwICMwMDAwKTtcXG4gIC0tdHctcmluZy1vZmZzZXQtd2lkdGg6IDJweDtcXG4gIC0tdHctcmluZy1vZmZzZXQtY29sb3I6ICNmYWZhZmE7XFxufVxcblxcbi5idG4tcHJpbWFyeSB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMTQgMTY1IDIzMyAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1zaGFkb3c6IDAgMnB4IDhweCAwIHJnYmEoMCwgMCwgMCwgMC4wNik7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDJweCA4cHggMCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xcbiAgYm9yZGVyLXJhZGl1czogMC43NXJlbTtcXG4gIGJvcmRlci13aWR0aDogMXB4O1xcbiAgYm9yZGVyLWNvbG9yOiB0cmFuc3BhcmVudDtcXG4gIHBhZGRpbmctbGVmdDogMXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDFyZW07XFxuICBwYWRkaW5nLXRvcDogMC42MjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC42MjVyZW07XFxuICBmb250LXNpemU6IDAuODc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDEuMjVyZW07XFxuICBmb250LXdlaWdodDogNTAwO1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogYWxsO1xcbiAgdHJhbnNpdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAyMDBtcztcXG59XFxuXFxuLmJ0bi1wcmltYXJ5OmZvY3VzIHtcXG4gIG91dGxpbmU6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcXG4gIG91dGxpbmUtb2Zmc2V0OiAycHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpIHZhcigtLXR3LXJpbmctb2Zmc2V0LWNvbG9yKTtcXG4gIC0tdHctcmluZy1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIGNhbGMoMnB4ICsgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpKSB2YXIoLS10dy1yaW5nLWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdyksIHZhcigtLXR3LXJpbmctc2hhZG93KSwgdmFyKC0tdHctc2hhZG93LCAwIDAgIzAwMDApO1xcbiAgLS10dy1yaW5nLW9mZnNldC13aWR0aDogMnB4O1xcbiAgLS10dy1yaW5nLW9mZnNldC1jb2xvcjogI2ZhZmFmYTtcXG59XFxuXFxuLmJ0bi1wcmltYXJ5IHtcXG4gIGJveC1zaGFkb3c6IDAgMnB4IDhweCAwIHJnYmEoMCwgMCwgMCwgMC4wNik7XFxufVxcblxcbi5idG4tcHJpbWFyeTpob3ZlciB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMiAxMzIgMTk5IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1zaGFkb3c6IDAgNHB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCA0cHggMTJweCAwIHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbn1cXG5cXG4uYnRuLXByaW1hcnk6Zm9jdXMge1xcbiAgLS10dy1yaW5nLW9wYWNpdHk6IDE7XFxuICAtLXR3LXJpbmctY29sb3I6IHJnYig1NiAxODkgMjQ4IC8gdmFyKC0tdHctcmluZy1vcGFjaXR5LCAxKSk7XFxufVxcblxcbi5idG4tc2Vjb25kYXJ5IHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigyMjkgMjI5IDIyOSAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoNjQgNjQgNjQgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG4gIC0tdHctc2hhZG93OiAwIDJweCA4cHggMCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCAycHggOHB4IDAgdmFyKC0tdHctc2hhZG93LWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctcmluZy1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXNoYWRvdyk7XFxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XFxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcXG4gIGJvcmRlci1yYWRpdXM6IDAuNzVyZW07XFxuICBib3JkZXItd2lkdGg6IDFweDtcXG4gIGJvcmRlci1jb2xvcjogdHJhbnNwYXJlbnQ7XFxuICBwYWRkaW5nLWxlZnQ6IDFyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAxcmVtO1xcbiAgcGFkZGluZy10b3A6IDAuNjI1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuNjI1cmVtO1xcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxLjI1cmVtO1xcbiAgZm9udC13ZWlnaHQ6IDUwMDtcXG4gIHRyYW5zaXRpb24tcHJvcGVydHk6IGFsbDtcXG4gIHRyYW5zaXRpb24tdGltaW5nLWZ1bmN0aW9uOiBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMjAwbXM7XFxufVxcblxcbi5idG4tc2Vjb25kYXJ5OmZvY3VzIHtcXG4gIG91dGxpbmU6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcXG4gIG91dGxpbmUtb2Zmc2V0OiAycHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpIHZhcigtLXR3LXJpbmctb2Zmc2V0LWNvbG9yKTtcXG4gIC0tdHctcmluZy1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIGNhbGMoMnB4ICsgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpKSB2YXIoLS10dy1yaW5nLWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdyksIHZhcigtLXR3LXJpbmctc2hhZG93KSwgdmFyKC0tdHctc2hhZG93LCAwIDAgIzAwMDApO1xcbiAgLS10dy1yaW5nLW9mZnNldC13aWR0aDogMnB4O1xcbiAgLS10dy1yaW5nLW9mZnNldC1jb2xvcjogI2ZhZmFmYTtcXG59XFxuXFxuLmJ0bi1zZWNvbmRhcnkge1xcbiAgYm94LXNoYWRvdzogMCAycHggOHB4IDAgcmdiYSgwLCAwLCAwLCAwLjA2KTtcXG59XFxuXFxuLmJ0bi1zZWNvbmRhcnk6aG92ZXIge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1MCAyNTAgMjUwIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1zaGFkb3c6IDAgNHB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCA0cHggMTJweCAwIHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbn1cXG5cXG4uYnRuLXNlY29uZGFyeTpmb2N1cyB7XFxuICAtLXR3LXJpbmctb3BhY2l0eTogMTtcXG4gIC0tdHctcmluZy1jb2xvcjogcmdiKDU2IDE4OSAyNDggLyB2YXIoLS10dy1yaW5nLW9wYWNpdHksIDEpKTtcXG59XFxuXFxuLmJ0bi1kYW5nZXIge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIzOSA2OCA2OCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1zaGFkb3c6IDAgMnB4IDhweCAwIHJnYmEoMCwgMCwgMCwgMC4wNik7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDJweCA4cHggMCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xcbiAgYm9yZGVyLXJhZGl1czogMC43NXJlbTtcXG4gIGJvcmRlci13aWR0aDogMXB4O1xcbiAgYm9yZGVyLWNvbG9yOiB0cmFuc3BhcmVudDtcXG4gIHBhZGRpbmctbGVmdDogMXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDFyZW07XFxuICBwYWRkaW5nLXRvcDogMC42MjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC42MjVyZW07XFxuICBmb250LXNpemU6IDAuODc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDEuMjVyZW07XFxuICBmb250LXdlaWdodDogNTAwO1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogYWxsO1xcbiAgdHJhbnNpdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAyMDBtcztcXG59XFxuXFxuLmJ0bi1kYW5nZXI6Zm9jdXMge1xcbiAgb3V0bGluZTogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xcbiAgb3V0bGluZS1vZmZzZXQ6IDJweDtcXG4gIC0tdHctcmluZy1vZmZzZXQtc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkgdmFyKC0tdHctcmluZy1vZmZzZXQtY29sb3IpO1xcbiAgLS10dy1yaW5nLXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgY2FsYygycHggKyB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkpIHZhcigtLXR3LXJpbmctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93KSwgdmFyKC0tdHctcmluZy1zaGFkb3cpLCB2YXIoLS10dy1zaGFkb3csIDAgMCAjMDAwMCk7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXdpZHRoOiAycHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LWNvbG9yOiAjZmFmYWZhO1xcbn1cXG5cXG4uYnRuLWRhbmdlciB7XFxuICBib3gtc2hhZG93OiAwIDJweCA4cHggMCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xcbn1cXG5cXG4uYnRuLWRhbmdlcjpob3ZlciB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjIwIDM4IDM4IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1zaGFkb3c6IDAgNHB4IDEycHggMCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCA0cHggMTJweCAwIHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbn1cXG5cXG4uYnRuLWRhbmdlcjpmb2N1cyB7XFxuICAtLXR3LXJpbmctb3BhY2l0eTogMTtcXG4gIC0tdHctcmluZy1jb2xvcjogcmdiKDI0OCAxMTMgMTEzIC8gdmFyKC0tdHctcmluZy1vcGFjaXR5LCAxKSk7XFxufVxcblxcbi5idG4tc3VjY2VzcyB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMzQgMTk3IDk0IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXNoYWRvdzogMCAycHggOHB4IDAgcmdiYSgwLCAwLCAwLCAwLjA2KTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMnB4IDhweCAwIHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XFxuICBhbGlnbi1pdGVtczogY2VudGVyO1xcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XFxuICBib3JkZXItcmFkaXVzOiAwLjc1cmVtO1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxuICBib3JkZXItY29sb3I6IHRyYW5zcGFyZW50O1xcbiAgcGFkZGluZy1sZWZ0OiAxcmVtO1xcbiAgcGFkZGluZy1yaWdodDogMXJlbTtcXG4gIHBhZGRpbmctdG9wOiAwLjYyNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjYyNXJlbTtcXG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XFxuICBsaW5lLWhlaWdodDogMS4yNXJlbTtcXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XFxuICB0cmFuc2l0aW9uLXByb3BlcnR5OiBhbGw7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDIwMG1zO1xcbn1cXG5cXG4uYnRuLXN1Y2Nlc3M6Zm9jdXMge1xcbiAgb3V0bGluZTogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xcbiAgb3V0bGluZS1vZmZzZXQ6IDJweDtcXG4gIC0tdHctcmluZy1vZmZzZXQtc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkgdmFyKC0tdHctcmluZy1vZmZzZXQtY29sb3IpO1xcbiAgLS10dy1yaW5nLXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgY2FsYygycHggKyB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkpIHZhcigtLXR3LXJpbmctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93KSwgdmFyKC0tdHctcmluZy1zaGFkb3cpLCB2YXIoLS10dy1zaGFkb3csIDAgMCAjMDAwMCk7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXdpZHRoOiAycHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LWNvbG9yOiAjZmFmYWZhO1xcbn1cXG5cXG4uYnRuLXN1Y2Nlc3Mge1xcbiAgYm94LXNoYWRvdzogMCAycHggOHB4IDAgcmdiYSgwLCAwLCAwLCAwLjA2KTtcXG59XFxuXFxuLmJ0bi1zdWNjZXNzOmhvdmVyIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyMiAxNjMgNzQgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXNoYWRvdzogMCA0cHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4wOCk7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDRweCAxMnB4IDAgdmFyKC0tdHctc2hhZG93LWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctcmluZy1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXNoYWRvdyk7XFxufVxcblxcbi5idG4tc3VjY2Vzczpmb2N1cyB7XFxuICAtLXR3LXJpbmctb3BhY2l0eTogMTtcXG4gIC0tdHctcmluZy1jb2xvcjogcmdiKDc0IDIyMiAxMjggLyB2YXIoLS10dy1yaW5nLW9wYWNpdHksIDEpKTtcXG59XFxuXFxuLmJ0bi1zbSB7XFxuICBwYWRkaW5nLWxlZnQ6IDAuNzVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjc1cmVtO1xcbiAgcGFkZGluZy10b3A6IDAuMzc1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuMzc1cmVtO1xcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDFyZW07XFxufVxcblxcbi5idG4tbGcge1xcbiAgcGFkZGluZy1sZWZ0OiAxLjVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAxLjVyZW07XFxuICBwYWRkaW5nLXRvcDogMC43NXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjc1cmVtO1xcbiAgZm9udC1zaXplOiAxcmVtO1xcbiAgbGluZS1oZWlnaHQ6IDEuNXJlbTtcXG59XFxuXFxuLyogTW9kZXJuIENhcmQgU3R5bGVzICovXFxuLmNhcmQge1xcbiAgYm9yZGVyLXJhZGl1czogMXJlbTtcXG4gIGJvcmRlci13aWR0aDogMXB4O1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDI0NSAyNDUgMjQ1IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctc2hhZG93OiAwIDFweCAzcHggMCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgMXB4IDJweCAwIHJnYmEoMCwgMCwgMCwgMC4wNik7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDFweCAzcHggMCB2YXIoLS10dy1zaGFkb3ctY29sb3IpLCAwIDFweCAycHggMCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG4gIGJveC1zaGFkb3c6IDAgMXB4IDNweCAwIHJnYmEoMCwgMCwgMCwgMC4xKSwgMCAxcHggMnB4IDAgcmdiYSgwLCAwLCAwLCAwLjA2KTtcXG59XFxuXFxuLmNhcmQtaGVhZGVyIHtcXG4gIGJvcmRlci1ib3R0b20td2lkdGg6IDFweDtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigyNDUgMjQ1IDI0NSAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxuICBwYWRkaW5nLWxlZnQ6IDEuNXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDEuNXJlbTtcXG4gIHBhZGRpbmctdG9wOiAxLjI1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDEuMjVyZW07XFxufVxcblxcbi5jYXJkLWJvZHkge1xcbiAgcGFkZGluZy1sZWZ0OiAxLjVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAxLjVyZW07XFxuICBwYWRkaW5nLXRvcDogMS4yNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAxLjI1cmVtO1xcbn1cXG5cXG4uY2FyZC1mb290ZXIge1xcbiAgYm9yZGVyLWJvdHRvbS1yaWdodC1yYWRpdXM6IDFyZW07XFxuICBib3JkZXItYm90dG9tLWxlZnQtcmFkaXVzOiAxcmVtO1xcbiAgYm9yZGVyLXRvcC13aWR0aDogMXB4O1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDI0NSAyNDUgMjQ1IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTAgMjUwIDI1MCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gIHBhZGRpbmctbGVmdDogMS41cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMS41cmVtO1xcbiAgcGFkZGluZy10b3A6IDEuMjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMS4yNXJlbTtcXG59XFxuXFxuLyogTW9kZXJuIFRhYmxlIFN0eWxlcyAqL1xcbi50YWJsZSB7XFxuICBtaW4td2lkdGg6IDEwMCU7XFxufVxcbi50YWJsZSA+IDpub3QoW2hpZGRlbl0pIH4gOm5vdChbaGlkZGVuXSkge1xcbiAgLS10dy1kaXZpZGUteS1yZXZlcnNlOiAwO1xcbiAgYm9yZGVyLXRvcC13aWR0aDogY2FsYygxcHggKiBjYWxjKDEgLSB2YXIoLS10dy1kaXZpZGUteS1yZXZlcnNlKSkpO1xcbiAgYm9yZGVyLWJvdHRvbS13aWR0aDogY2FsYygxcHggKiB2YXIoLS10dy1kaXZpZGUteS1yZXZlcnNlKSk7XFxuICAtLXR3LWRpdmlkZS1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjQwIDI0MCAyNDAgLyB2YXIoLS10dy1kaXZpZGUtb3BhY2l0eSwgMSkpO1xcbn1cXG5cXG4udGFibGUgdGhlYWQge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1MCAyNTAgMjUwIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG5cXG4udGFibGUgdGgge1xcbiAgcGFkZGluZy1sZWZ0OiAxLjVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAxLjVyZW07XFxuICBwYWRkaW5nLXRvcDogMXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAxcmVtO1xcbiAgdGV4dC1hbGlnbjogbGVmdDtcXG4gIGZvbnQtc2l6ZTogMC43NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxcmVtO1xcbiAgZm9udC13ZWlnaHQ6IDYwMDtcXG4gIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XFxuICBsZXR0ZXItc3BhY2luZzogMC4wNWVtO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDgyIDgyIDgyIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcblxcbi50YWJsZSB0ZCB7XFxuICB3aGl0ZS1zcGFjZTogbm93cmFwO1xcbiAgcGFkZGluZy1sZWZ0OiAxLjVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAxLjVyZW07XFxuICBwYWRkaW5nLXRvcDogMXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAxcmVtO1xcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxLjI1cmVtO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIzIDIzIDIzIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcblxcbi50YWJsZSB0Ym9keSB0cjpudGgtY2hpbGQoZXZlbikge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1MCAyNTAgMjUwIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG5cXG4udGFibGUgdGJvZHkgdHI6aG92ZXIge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0OCAyNDkgMjUwIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogY29sb3IsIGJhY2tncm91bmQtY29sb3IsIGJvcmRlci1jb2xvciwgdGV4dC1kZWNvcmF0aW9uLWNvbG9yLCBmaWxsLCBzdHJva2U7XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDE1MG1zO1xcbn1cXG5cXG4vKiBNb2Rlcm4gQmFkZ2UgU3R5bGVzICovXFxuLmJhZGdlIHtcXG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcXG4gIGJvcmRlci1yYWRpdXM6IDk5OTlweDtcXG4gIHBhZGRpbmctbGVmdDogMC43NXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDAuNzVyZW07XFxuICBwYWRkaW5nLXRvcDogMC4yNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjI1cmVtO1xcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDFyZW07XFxuICBmb250LXdlaWdodDogNTAwO1xcbn1cXG5cXG4uYmFkZ2UtcHJpbWFyeSB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjI0IDI0MiAyNTQgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMyAxMDUgMTYxIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XFxuICBib3JkZXItcmFkaXVzOiA5OTk5cHg7XFxuICBwYWRkaW5nLWxlZnQ6IDAuNzVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjc1cmVtO1xcbiAgcGFkZGluZy10b3A6IDAuMjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC4yNXJlbTtcXG4gIGZvbnQtc2l6ZTogMC43NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxcmVtO1xcbiAgZm9udC13ZWlnaHQ6IDUwMDtcXG59XFxuXFxuLmJhZGdlLXNlY29uZGFyeSB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjQ1IDI0NSAyNDUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoNjQgNjQgNjQgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcXG4gIGJvcmRlci1yYWRpdXM6IDk5OTlweDtcXG4gIHBhZGRpbmctbGVmdDogMC43NXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDAuNzVyZW07XFxuICBwYWRkaW5nLXRvcDogMC4yNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAwLjI1cmVtO1xcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDFyZW07XFxuICBmb250LXdlaWdodDogNTAwO1xcbn1cXG5cXG4uYmFkZ2Utc3VjY2VzcyB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjIwIDI1MiAyMzEgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjEgMTI4IDYxIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XFxuICBib3JkZXItcmFkaXVzOiA5OTk5cHg7XFxuICBwYWRkaW5nLWxlZnQ6IDAuNzVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjc1cmVtO1xcbiAgcGFkZGluZy10b3A6IDAuMjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC4yNXJlbTtcXG4gIGZvbnQtc2l6ZTogMC43NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxcmVtO1xcbiAgZm9udC13ZWlnaHQ6IDUwMDtcXG59XFxuXFxuLmJhZGdlLXdhcm5pbmcge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NCAyNDkgMTk1IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDE2MSA5OCA3IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XFxuICBib3JkZXItcmFkaXVzOiA5OTk5cHg7XFxuICBwYWRkaW5nLWxlZnQ6IDAuNzVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjc1cmVtO1xcbiAgcGFkZGluZy10b3A6IDAuMjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC4yNXJlbTtcXG4gIGZvbnQtc2l6ZTogMC43NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxcmVtO1xcbiAgZm9udC13ZWlnaHQ6IDUwMDtcXG59XFxuXFxuLmJhZGdlLWRhbmdlciB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU0IDIyNiAyMjYgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTg1IDI4IDI4IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XFxuICBib3JkZXItcmFkaXVzOiA5OTk5cHg7XFxuICBwYWRkaW5nLWxlZnQ6IDAuNzVyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjc1cmVtO1xcbiAgcGFkZGluZy10b3A6IDAuMjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC4yNXJlbTtcXG4gIGZvbnQtc2l6ZTogMC43NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxcmVtO1xcbiAgZm9udC13ZWlnaHQ6IDUwMDtcXG59XFxuXFxuLmJhZGdlLWluZm8ge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIyNCAyNDIgMjU0IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDMgMTA1IDE2MSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XFxuICBhbGlnbi1pdGVtczogY2VudGVyO1xcbiAgYm9yZGVyLXJhZGl1czogOTk5OXB4O1xcbiAgcGFkZGluZy1sZWZ0OiAwLjc1cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMC43NXJlbTtcXG4gIHBhZGRpbmctdG9wOiAwLjI1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuMjVyZW07XFxuICBmb250LXNpemU6IDAuNzVyZW07XFxuICBsaW5lLWhlaWdodDogMXJlbTtcXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XFxufVxcblxcbi8qIEFsZXJ0IHN0eWxlcyAqL1xcbi5hbGVydCB7XFxuICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcXG4gIHBhZGRpbmc6IDFyZW07XFxufVxcblxcbi5hbGVydC1zdWNjZXNzIHtcXG4gIGJvcmRlci13aWR0aDogMXB4O1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDE4NyAyNDcgMjA4IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNDAgMjUzIDI0NCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyMiAxMDEgNTIgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG4gIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xcbiAgcGFkZGluZzogMXJlbTtcXG59XFxuXFxuLmFsZXJ0LXdhcm5pbmcge1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjU0IDI0MCAxMzggLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NCAyNTIgMjMyIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDEzMyA3NyAxNCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbiAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XFxuICBwYWRkaW5nOiAxcmVtO1xcbn1cXG5cXG4uYWxlcnQtZGFuZ2VyIHtcXG4gIGJvcmRlci13aWR0aDogMXB4O1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDI1NCAyMDIgMjAyIC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTQgMjQyIDI0MiAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigxNTMgMjcgMjcgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG4gIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xcbiAgcGFkZGluZzogMXJlbTtcXG59XFxuXFxuLmFsZXJ0LWluZm8ge1xcbiAgYm9yZGVyLXdpZHRoOiAxcHg7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMTkxIDIxOSAyNTQgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIzOSAyNDYgMjU1IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDMwIDY0IDE3NSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbiAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XFxuICBwYWRkaW5nOiAxcmVtO1xcbn1cXG5cXG4vKiBBbmltYXRpb24gdXRpbGl0aWVzICovXFxuLmZhZGUtaW4ge1xcbiAgYW5pbWF0aW9uOiBmYWRlSW4gMC41cyBlYXNlLWluLW91dDtcXG59XFxuXFxuLnNsaWRlLXVwIHtcXG4gIGFuaW1hdGlvbjogc2xpZGVVcCAwLjNzIGVhc2Utb3V0O1xcbn1cXG5cXG5Aa2V5ZnJhbWVzIGZhZGVJbiB7XFxuICBmcm9tIHtcXG4gICAgb3BhY2l0eTogMDtcXG4gIH1cXG4gIHRvIHtcXG4gICAgb3BhY2l0eTogMTtcXG4gIH1cXG59XFxuXFxuQGtleWZyYW1lcyBzbGlkZVVwIHtcXG4gIGZyb20ge1xcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMTBweCk7XFxuICAgIG9wYWNpdHk6IDA7XFxuICB9XFxuICB0byB7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcXG4gICAgb3BhY2l0eTogMTtcXG4gIH1cXG59XFxuXFxuLyogUHJpbnQgc3R5bGVzICovXFxuQG1lZGlhIHByaW50IHtcXG4gIC5uby1wcmludCB7XFxuICAgIGRpc3BsYXk6IG5vbmUgIWltcG9ydGFudDtcXG4gIH1cXG4gIFxcbiAgLnByaW50LWJyZWFrIHtcXG4gICAgcGFnZS1icmVhay1iZWZvcmU6IGFsd2F5cztcXG4gIH1cXG4gIFxcbiAgYm9keSB7XFxuICAgIGZvbnQtc2l6ZTogMTJwdDtcXG4gICAgbGluZS1oZWlnaHQ6IDEuNDtcXG4gIH1cXG4gIFxcbiAgLmNhcmQge1xcbiAgICBib3gtc2hhZG93OiBub25lO1xcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZGRkO1xcbiAgfVxcbn1cXG5cXG4vKiBNb2Rlcm4gTGF5b3V0IFV0aWxpdGllcyAqL1xcbi5wYWdlLWNvbnRhaW5lciB7XFxuICBtYXJnaW4tbGVmdDogYXV0bztcXG4gIG1hcmdpbi1yaWdodDogYXV0bztcXG4gIG1heC13aWR0aDogODByZW07XFxuICBwYWRkaW5nLWxlZnQ6IDFyZW07XFxuICBwYWRkaW5nLXJpZ2h0OiAxcmVtO1xcbn1cXG5AbWVkaWEgKG1pbi13aWR0aDogNjQwcHgpIHtcXG5cXG4gIC5wYWdlLWNvbnRhaW5lciB7XFxuICAgIHBhZGRpbmctbGVmdDogMS41cmVtO1xcbiAgICBwYWRkaW5nLXJpZ2h0OiAxLjVyZW07XFxuICB9XFxufVxcbkBtZWRpYSAobWluLXdpZHRoOiAxMDI0cHgpIHtcXG5cXG4gIC5wYWdlLWNvbnRhaW5lciB7XFxuICAgIHBhZGRpbmctbGVmdDogMnJlbTtcXG4gICAgcGFkZGluZy1yaWdodDogMnJlbTtcXG4gIH1cXG59XFxuXFxuLnNlY3Rpb24tc3BhY2luZyB7XFxuICBwYWRkaW5nLXRvcDogMnJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAycmVtO1xcbn1cXG5cXG5AbWVkaWEgKG1pbi13aWR0aDogMTAyNHB4KSB7XFxuXFxuICAuc2VjdGlvbi1zcGFjaW5nIHtcXG4gICAgcGFkZGluZy10b3A6IDNyZW07XFxuICAgIHBhZGRpbmctYm90dG9tOiAzcmVtO1xcbiAgfVxcbn1cXG5cXG4uY29udGVudC1zcGFjaW5nID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXktcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi10b3A6IGNhbGMoMS41cmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteS1yZXZlcnNlKSkpO1xcbiAgbWFyZ2luLWJvdHRvbTogY2FsYygxLjVyZW0gKiB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKTtcXG59XFxuXFxuQG1lZGlhIChtaW4td2lkdGg6IDEwMjRweCkge1xcblxcbiAgLmNvbnRlbnQtc3BhY2luZyA+IDpub3QoW2hpZGRlbl0pIH4gOm5vdChbaGlkZGVuXSkge1xcbiAgICAtLXR3LXNwYWNlLXktcmV2ZXJzZTogMDtcXG4gICAgbWFyZ2luLXRvcDogY2FsYygycmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteS1yZXZlcnNlKSkpO1xcbiAgICBtYXJnaW4tYm90dG9tOiBjYWxjKDJyZW0gKiB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKTtcXG4gIH1cXG59XFxuXFxuLyogTW9kZXJuIFNoYWRvd3MgKi9cXG4uc2hhZG93LXNvZnQge1xcbiAgYm94LXNoYWRvdzogMCAycHggOHB4IDAgcmdiYSgwLCAwLCAwLCAwLjA2KTtcXG59XFxuXFxuLnNoYWRvdy1tZWRpdW0ge1xcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4wOCk7XFxufVxcblxcbi5zaGFkb3ctbGFyZ2Uge1xcbiAgYm94LXNoYWRvdzogMCA4cHggMjRweCAwIHJnYmEoMCwgMCwgMCwgMC4xMik7XFxufVxcblxcbi5zaGFkb3ctY2FyZCB7XFxuICBib3gtc2hhZG93OiAwIDFweCAzcHggMCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgMXB4IDJweCAwIHJnYmEoMCwgMCwgMCwgMC4wNik7XFxufVxcblxcbi8qIEVuaGFuY2VkIFJUTCBTdXBwb3J0ICovXFxuW2Rpcj1cXFwicnRsXFxcIl0gLnRhYmxlIHRoIHtcXG4gIHRleHQtYWxpZ246IHJpZ2h0O1xcbn1cXG5cXG5bZGlyPVxcXCJydGxcXFwiXSAudGFibGUgdGQge1xcbiAgdGV4dC1hbGlnbjogcmlnaHQ7XFxufVxcblxcbi8qIE1vZGVybiBGb2N1cyBTdGF0ZXMgKi9cXG4uZm9jdXMtcmluZzpmb2N1cyB7XFxuICBvdXRsaW5lOiAycHggc29saWQgdHJhbnNwYXJlbnQ7XFxuICBvdXRsaW5lLW9mZnNldDogMnB4O1xcbiAgLS10dy1yaW5nLW9mZnNldC1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSB2YXIoLS10dy1yaW5nLW9mZnNldC1jb2xvcik7XFxuICAtLXR3LXJpbmctc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCBjYWxjKDJweCArIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSkgdmFyKC0tdHctcmluZy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3cpLCB2YXIoLS10dy1yaW5nLXNoYWRvdyksIHZhcigtLXR3LXNoYWRvdywgMCAwICMwMDAwKTtcXG4gIC0tdHctcmluZy1vcGFjaXR5OiAxO1xcbiAgLS10dy1yaW5nLWNvbG9yOiByZ2IoNTYgMTg5IDI0OCAvIHZhcigtLXR3LXJpbmctb3BhY2l0eSwgMSkpO1xcbiAgLS10dy1yaW5nLW9mZnNldC13aWR0aDogMnB4O1xcbiAgLS10dy1yaW5nLW9mZnNldC1jb2xvcjogI2ZhZmFmYTtcXG59XFxuXFxuLyogSW1wcm92ZWQgU2Nyb2xsYmFyICovXFxuOjotd2Via2l0LXNjcm9sbGJhciB7XFxuICB3aWR0aDogOHB4O1xcbiAgaGVpZ2h0OiA4cHg7XFxufVxcblxcbjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sge1xcbiAgYmFja2dyb3VuZDogI2Y1ZjVmNTtcXG4gIGJvcmRlci1yYWRpdXM6IDRweDtcXG59XFxuXFxuOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XFxuICBiYWNrZ3JvdW5kOiAjZDRkNGQ0O1xcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xcbn1cXG5cXG46Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHtcXG4gIGJhY2tncm91bmQ6ICNhM2EzYTM7XFxufVxcblxcbi8qIERhcmsgbW9kZSBzdXBwb3J0IChpZiBuZWVkZWQpICovXFxuQG1lZGlhIChwcmVmZXJzLWNvbG9yLXNjaGVtZTogZGFyaykge1xcbiAgLmRhcmstbW9kZSB7XFxuICAgIC0tdHctYmctb3BhY2l0eTogMTtcXG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIzIDIzIDIzIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbiAgICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gICAgY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbiAgfVxcblxcbiAgLmRhcmstbW9kZSAuY2FyZCB7XFxuICAgIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICAgIGJvcmRlci1jb2xvcjogcmdiKDY0IDY0IDY0IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG4gICAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMzggMzggMzggLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxuICB9XFxuXFxuICAuZGFyay1tb2RlIC5mb3JtLWlucHV0IHtcXG4gICAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gICAgYm9yZGVyLWNvbG9yOiByZ2IoODIgODIgODIgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbiAgICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICAgIGJhY2tncm91bmQtY29sb3I6IHJnYig2NCA2NCA2NCAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG4gICAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICAgIGNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG4gIH1cXG59XFxuLmFmdGVyXFxcXDphYnNvbHV0ZTo6YWZ0ZXIge1xcbiAgY29udGVudDogdmFyKC0tdHctY29udGVudCk7XFxuICBwb3NpdGlvbjogYWJzb2x1dGU7XFxufVxcbi5hZnRlclxcXFw6bGVmdC1cXFxcWzJweFxcXFxdOjphZnRlciB7XFxuICBjb250ZW50OiB2YXIoLS10dy1jb250ZW50KTtcXG4gIGxlZnQ6IDJweDtcXG59XFxuLmFmdGVyXFxcXDp0b3AtXFxcXFsycHhcXFxcXTo6YWZ0ZXIge1xcbiAgY29udGVudDogdmFyKC0tdHctY29udGVudCk7XFxuICB0b3A6IDJweDtcXG59XFxuLmFmdGVyXFxcXDpoLTU6OmFmdGVyIHtcXG4gIGNvbnRlbnQ6IHZhcigtLXR3LWNvbnRlbnQpO1xcbiAgaGVpZ2h0OiAxLjI1cmVtO1xcbn1cXG4uYWZ0ZXJcXFxcOnctNTo6YWZ0ZXIge1xcbiAgY29udGVudDogdmFyKC0tdHctY29udGVudCk7XFxuICB3aWR0aDogMS4yNXJlbTtcXG59XFxuLmFmdGVyXFxcXDpyb3VuZGVkLWZ1bGw6OmFmdGVyIHtcXG4gIGNvbnRlbnQ6IHZhcigtLXR3LWNvbnRlbnQpO1xcbiAgYm9yZGVyLXJhZGl1czogOTk5OXB4O1xcbn1cXG4uYWZ0ZXJcXFxcOmJvcmRlcjo6YWZ0ZXIge1xcbiAgY29udGVudDogdmFyKC0tdHctY29udGVudCk7XFxuICBib3JkZXItd2lkdGg6IDFweDtcXG59XFxuLmFmdGVyXFxcXDpib3JkZXItZ3JheS0zMDA6OmFmdGVyIHtcXG4gIGNvbnRlbnQ6IHZhcigtLXR3LWNvbnRlbnQpO1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDIwOSAyMTMgMjE5IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLmFmdGVyXFxcXDpiZy13aGl0ZTo6YWZ0ZXIge1xcbiAgY29udGVudDogdmFyKC0tdHctY29udGVudCk7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5hZnRlclxcXFw6dHJhbnNpdGlvbi1hbGw6OmFmdGVyIHtcXG4gIGNvbnRlbnQ6IHZhcigtLXR3LWNvbnRlbnQpO1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogYWxsO1xcbiAgdHJhbnNpdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAxNTBtcztcXG59XFxuLmFmdGVyXFxcXDpjb250ZW50LVxcXFxbXFxcXCdcXFxcJ1xcXFxdOjphZnRlciB7XFxuICAtLXR3LWNvbnRlbnQ6ICcnO1xcbiAgY29udGVudDogdmFyKC0tdHctY29udGVudCk7XFxufVxcbi5ob3ZlclxcXFw6Ym9yZGVyLWdyYXktMzAwOmhvdmVyIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigyMDkgMjEzIDIxOSAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6YmctZ3JheS0xMDA6aG92ZXIge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0MyAyNDQgMjQ2IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uaG92ZXJcXFxcOmJnLWdyYXktNTA6aG92ZXIge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0OSAyNTAgMjUxIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uaG92ZXJcXFxcOmJnLXByaW1hcnktNzAwOmhvdmVyIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigzIDEwNSAxNjEgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6YmctbmV1dHJhbC03NTpob3ZlciB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjQ4IDI0OSAyNTAgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6dGV4dC1ibHVlLTYwMDpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMzcgOTkgMjM1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6dGV4dC1ncmF5LTkwMDpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTcgMjQgMzkgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLmhvdmVyXFxcXDp0ZXh0LWdyZWVuLTYwMDpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjIgMTYzIDc0IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcbi5ob3ZlclxcXFw6dGV4dC1yZWQtNjAwOmhvdmVyIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyMjAgMzggMzggLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLmhvdmVyXFxcXDp0ZXh0LXRleHQtcHJpbWFyeTpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjMgMjMgMjMgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxuLmhvdmVyXFxcXDp0ZXh0LWdyYXktNjAwOmhvdmVyIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYig3NSA4NSA5OSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4uZm9jdXNcXFxcOnotMTA6Zm9jdXMge1xcbiAgei1pbmRleDogMTA7XFxufVxcbi5mb2N1c1xcXFw6Ym9yZGVyLXByaW1hcnktNTAwOmZvY3VzIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigxNCAxNjUgMjMzIC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLmZvY3VzXFxcXDpvdXRsaW5lLW5vbmU6Zm9jdXMge1xcbiAgb3V0bGluZTogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xcbiAgb3V0bGluZS1vZmZzZXQ6IDJweDtcXG59XFxuLmZvY3VzXFxcXDpyaW5nLTI6Zm9jdXMge1xcbiAgLS10dy1yaW5nLW9mZnNldC1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSB2YXIoLS10dy1yaW5nLW9mZnNldC1jb2xvcik7XFxuICAtLXR3LXJpbmctc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCBjYWxjKDJweCArIHZhcigtLXR3LXJpbmctb2Zmc2V0LXdpZHRoKSkgdmFyKC0tdHctcmluZy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3cpLCB2YXIoLS10dy1yaW5nLXNoYWRvdyksIHZhcigtLXR3LXNoYWRvdywgMCAwICMwMDAwKTtcXG59XFxuLmZvY3VzXFxcXDpyaW5nLXByaW1hcnktNTAwOmZvY3VzIHtcXG4gIC0tdHctcmluZy1vcGFjaXR5OiAxO1xcbiAgLS10dy1yaW5nLWNvbG9yOiByZ2IoMTQgMTY1IDIzMyAvIHZhcigtLXR3LXJpbmctb3BhY2l0eSwgMSkpO1xcbn1cXG4uZm9jdXNcXFxcOnJpbmctb2Zmc2V0LTI6Zm9jdXMge1xcbiAgLS10dy1yaW5nLW9mZnNldC13aWR0aDogMnB4O1xcbn1cXG4uZGlzYWJsZWRcXFxcOmN1cnNvci1ub3QtYWxsb3dlZDpkaXNhYmxlZCB7XFxuICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xcbn1cXG4uZGlzYWJsZWRcXFxcOm9wYWNpdHktNTA6ZGlzYWJsZWQge1xcbiAgb3BhY2l0eTogMC41O1xcbn1cXG4uZ3JvdXA6aG92ZXIgLmdyb3VwLWhvdmVyXFxcXDp0ZXh0LWdyYXktNTAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigxMDcgMTE0IDEyOCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXG4ucGVlcjpjaGVja2VkIH4gLnBlZXItY2hlY2tlZFxcXFw6YmctcHJpbWFyeS02MDAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIgMTMyIDE5OSAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxuLnBlZXI6Y2hlY2tlZCB+IC5wZWVyLWNoZWNrZWRcXFxcOmFmdGVyXFxcXDp0cmFuc2xhdGUteC1mdWxsOjphZnRlciB7XFxuICBjb250ZW50OiB2YXIoLS10dy1jb250ZW50KTtcXG4gIC0tdHctdHJhbnNsYXRlLXg6IDEwMCU7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG59XFxuLnBlZXI6Y2hlY2tlZCB+IC5wZWVyLWNoZWNrZWRcXFxcOmFmdGVyXFxcXDpib3JkZXItd2hpdGU6OmFmdGVyIHtcXG4gIGNvbnRlbnQ6IHZhcigtLXR3LWNvbnRlbnQpO1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxuLnBlZXI6Zm9jdXMgfiAucGVlci1mb2N1c1xcXFw6b3V0bGluZS1ub25lIHtcXG4gIG91dGxpbmU6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcXG4gIG91dGxpbmUtb2Zmc2V0OiAycHg7XFxufVxcbi5wZWVyOmZvY3VzIH4gLnBlZXItZm9jdXNcXFxcOnJpbmctNCB7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpIHZhcigtLXR3LXJpbmctb2Zmc2V0LWNvbG9yKTtcXG4gIC0tdHctcmluZy1zaGFkb3c6IHZhcigtLXR3LXJpbmctaW5zZXQpIDAgMCAwIGNhbGMoNHB4ICsgdmFyKC0tdHctcmluZy1vZmZzZXQtd2lkdGgpKSB2YXIoLS10dy1yaW5nLWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdyksIHZhcigtLXR3LXJpbmctc2hhZG93KSwgdmFyKC0tdHctc2hhZG93LCAwIDAgIzAwMDApO1xcbn1cXG4ucGVlcjpmb2N1cyB+IC5wZWVyLWZvY3VzXFxcXDpyaW5nLXByaW1hcnktMzAwIHtcXG4gIC0tdHctcmluZy1vcGFjaXR5OiAxO1xcbiAgLS10dy1yaW5nLWNvbG9yOiByZ2IoMTI1IDIxMSAyNTIgLyB2YXIoLS10dy1yaW5nLW9wYWNpdHksIDEpKTtcXG59XFxuQG1lZGlhIChtaW4td2lkdGg6IDY0MHB4KSB7XFxuXFxuICAuc21cXFxcOm15LTgge1xcbiAgICBtYXJnaW4tdG9wOiAycmVtO1xcbiAgICBtYXJnaW4tYm90dG9tOiAycmVtO1xcbiAgfVxcblxcbiAgLnNtXFxcXDptbC0zIHtcXG4gICAgbWFyZ2luLWxlZnQ6IDAuNzVyZW07XFxuICB9XFxuXFxuICAuc21cXFxcOm10LTAge1xcbiAgICBtYXJnaW4tdG9wOiAwcHg7XFxuICB9XFxuXFxuICAuc21cXFxcOmJsb2NrIHtcXG4gICAgZGlzcGxheTogYmxvY2s7XFxuICB9XFxuXFxuICAuc21cXFxcOmZsZXgge1xcbiAgICBkaXNwbGF5OiBmbGV4O1xcbiAgfVxcblxcbiAgLnNtXFxcXDp3LWF1dG8ge1xcbiAgICB3aWR0aDogYXV0bztcXG4gIH1cXG5cXG4gIC5zbVxcXFw6dy1mdWxsIHtcXG4gICAgd2lkdGg6IDEwMCU7XFxuICB9XFxuXFxuICAuc21cXFxcOm1heC13LTJ4bCB7XFxuICAgIG1heC13aWR0aDogNDJyZW07XFxuICB9XFxuXFxuICAuc21cXFxcOm1heC13LWxnIHtcXG4gICAgbWF4LXdpZHRoOiAzMnJlbTtcXG4gIH1cXG5cXG4gIC5zbVxcXFw6Z3JpZC1jb2xzLTIge1xcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCBtaW5tYXgoMCwgMWZyKSk7XFxuICB9XFxuXFxuICAuc21cXFxcOmZsZXgtcm93IHtcXG4gICAgZmxleC1kaXJlY3Rpb246IHJvdztcXG4gIH1cXG5cXG4gIC5zbVxcXFw6ZmxleC1yb3ctcmV2ZXJzZSB7XFxuICAgIGZsZXgtZGlyZWN0aW9uOiByb3ctcmV2ZXJzZTtcXG4gIH1cXG5cXG4gIC5zbVxcXFw6cC02IHtcXG4gICAgcGFkZGluZzogMS41cmVtO1xcbiAgfVxcblxcbiAgLnNtXFxcXDpwLTAge1xcbiAgICBwYWRkaW5nOiAwcHg7XFxuICB9XFxuXFxuICAuc21cXFxcOnB4LTYge1xcbiAgICBwYWRkaW5nLWxlZnQ6IDEuNXJlbTtcXG4gICAgcGFkZGluZy1yaWdodDogMS41cmVtO1xcbiAgfVxcblxcbiAgLnNtXFxcXDpwYi00IHtcXG4gICAgcGFkZGluZy1ib3R0b206IDFyZW07XFxuICB9XFxuXFxuICAuc21cXFxcOmFsaWduLW1pZGRsZSB7XFxuICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7XFxuICB9XFxuXFxuICAuc21cXFxcOnRleHQtc20ge1xcbiAgICBmb250LXNpemU6IDAuODc1cmVtO1xcbiAgICBsaW5lLWhlaWdodDogMS4yNXJlbTtcXG4gIH1cXG59XFxuQG1lZGlhIChtaW4td2lkdGg6IDc2OHB4KSB7XFxuXFxuICAubWRcXFxcOmNvbC1zcGFuLTIge1xcbiAgICBncmlkLWNvbHVtbjogc3BhbiAyIC8gc3BhbiAyO1xcbiAgfVxcblxcbiAgLm1kXFxcXDpncmlkLWNvbHMtMiB7XFxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIG1pbm1heCgwLCAxZnIpKTtcXG4gIH1cXG59XFxuQG1lZGlhIChtaW4td2lkdGg6IDEwMjRweCkge1xcblxcbiAgLmxnXFxcXDptbC0yMCB7XFxuICAgIG1hcmdpbi1sZWZ0OiA1cmVtO1xcbiAgfVxcblxcbiAgLmxnXFxcXDptbC02NCB7XFxuICAgIG1hcmdpbi1sZWZ0OiAxNnJlbTtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6bXItMjAge1xcbiAgICBtYXJnaW4tcmlnaHQ6IDVyZW07XFxuICB9XFxuXFxuICAubGdcXFxcOm1yLTY0IHtcXG4gICAgbWFyZ2luLXJpZ2h0OiAxNnJlbTtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6YmxvY2sge1xcbiAgICBkaXNwbGF5OiBibG9jaztcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6aGlkZGVuIHtcXG4gICAgZGlzcGxheTogbm9uZTtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6dy0xXFxcXC80IHtcXG4gICAgd2lkdGg6IDI1JTtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6dy0zXFxcXC80IHtcXG4gICAgd2lkdGg6IDc1JTtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6Z3JpZC1jb2xzLTIge1xcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCBtaW5tYXgoMCwgMWZyKSk7XFxuICB9XFxuXFxuICAubGdcXFxcOmdyaWQtY29scy0zIHtcXG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMywgbWlubWF4KDAsIDFmcikpO1xcbiAgfVxcblxcbiAgLmxnXFxcXDpncmlkLWNvbHMtNCB7XFxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDQsIG1pbm1heCgwLCAxZnIpKTtcXG4gIH1cXG5cXG4gIC5sZ1xcXFw6ZmxleC1yb3cge1xcbiAgICBmbGV4LWRpcmVjdGlvbjogcm93O1xcbiAgfVxcblxcbiAgLmxnXFxcXDpweC04IHtcXG4gICAgcGFkZGluZy1sZWZ0OiAycmVtO1xcbiAgICBwYWRkaW5nLXJpZ2h0OiAycmVtO1xcbiAgfVxcbn1cXG4ucnRsXFxcXDpsZWZ0LTA6d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopIHtcXG4gIGxlZnQ6IDBweDtcXG59XFxuLnJ0bFxcXFw6bGVmdC1hdXRvOndoZXJlKFtkaXI9XFxcInJ0bFxcXCJdLCBbZGlyPVxcXCJydGxcXFwiXSAqKSB7XFxuICBsZWZ0OiBhdXRvO1xcbn1cXG4ucnRsXFxcXDpyaWdodC0wOndoZXJlKFtkaXI9XFxcInJ0bFxcXCJdLCBbZGlyPVxcXCJydGxcXFwiXSAqKSB7XFxuICByaWdodDogMHB4O1xcbn1cXG4ucnRsXFxcXDpyaWdodC0zOndoZXJlKFtkaXI9XFxcInJ0bFxcXCJdLCBbZGlyPVxcXCJydGxcXFwiXSAqKSB7XFxuICByaWdodDogMC43NXJlbTtcXG59XFxuLnJ0bFxcXFw6cmlnaHQtYXV0bzp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgcmlnaHQ6IGF1dG87XFxufVxcbi5ydGxcXFxcOm1sLTA6d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopIHtcXG4gIG1hcmdpbi1sZWZ0OiAwcHg7XFxufVxcbi5ydGxcXFxcOm1sLTE6d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopIHtcXG4gIG1hcmdpbi1sZWZ0OiAwLjI1cmVtO1xcbn1cXG4ucnRsXFxcXDptbC0yOndoZXJlKFtkaXI9XFxcInJ0bFxcXCJdLCBbZGlyPVxcXCJydGxcXFwiXSAqKSB7XFxuICBtYXJnaW4tbGVmdDogMC41cmVtO1xcbn1cXG4ucnRsXFxcXDptbC0zOndoZXJlKFtkaXI9XFxcInJ0bFxcXCJdLCBbZGlyPVxcXCJydGxcXFwiXSAqKSB7XFxuICBtYXJnaW4tbGVmdDogMC43NXJlbTtcXG59XFxuLnJ0bFxcXFw6bXItMDp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgbWFyZ2luLXJpZ2h0OiAwcHg7XFxufVxcbi5ydGxcXFxcOm1yLTE6d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopIHtcXG4gIG1hcmdpbi1yaWdodDogMC4yNXJlbTtcXG59XFxuLnJ0bFxcXFw6bXItMjp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgbWFyZ2luLXJpZ2h0OiAwLjVyZW07XFxufVxcbi5ydGxcXFxcOm1yLTQ6d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopIHtcXG4gIG1hcmdpbi1yaWdodDogMXJlbTtcXG59XFxuLnJ0bFxcXFw6bXItNTp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgbWFyZ2luLXJpZ2h0OiAxLjI1cmVtO1xcbn1cXG4ucnRsXFxcXDotdHJhbnNsYXRlLXgtMDp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgLS10dy10cmFuc2xhdGUteDogLTBweDtcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKHZhcigtLXR3LXRyYW5zbGF0ZS14KSwgdmFyKC0tdHctdHJhbnNsYXRlLXkpKSByb3RhdGUodmFyKC0tdHctcm90YXRlKSkgc2tld1godmFyKC0tdHctc2tldy14KSkgc2tld1kodmFyKC0tdHctc2tldy15KSkgc2NhbGVYKHZhcigtLXR3LXNjYWxlLXgpKSBzY2FsZVkodmFyKC0tdHctc2NhbGUteSkpO1xcbn1cXG4ucnRsXFxcXDp0cmFuc2xhdGUteC1mdWxsOndoZXJlKFtkaXI9XFxcInJ0bFxcXCJdLCBbZGlyPVxcXCJydGxcXFwiXSAqKSB7XFxuICAtLXR3LXRyYW5zbGF0ZS14OiAxMDAlO1xcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUodmFyKC0tdHctdHJhbnNsYXRlLXgpLCB2YXIoLS10dy10cmFuc2xhdGUteSkpIHJvdGF0ZSh2YXIoLS10dy1yb3RhdGUpKSBza2V3WCh2YXIoLS10dy1za2V3LXgpKSBza2V3WSh2YXIoLS10dy1za2V3LXkpKSBzY2FsZVgodmFyKC0tdHctc2NhbGUteCkpIHNjYWxlWSh2YXIoLS10dy1zY2FsZS15KSk7XFxufVxcbi5ydGxcXFxcOnNwYWNlLXgtcmV2ZXJzZTp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikgPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteC1yZXZlcnNlOiAxO1xcbn1cXG4ucnRsXFxcXDpwbC0xMDp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgcGFkZGluZy1sZWZ0OiAyLjVyZW07XFxufVxcbi5ydGxcXFxcOnBsLTM6d2hlcmUoW2Rpcj1cXFwicnRsXFxcIl0sIFtkaXI9XFxcInJ0bFxcXCJdICopIHtcXG4gIHBhZGRpbmctbGVmdDogMC43NXJlbTtcXG59XFxuLnJ0bFxcXFw6cHItMDp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgcGFkZGluZy1yaWdodDogMHB4O1xcbn1cXG4ucnRsXFxcXDpwci0xMDp3aGVyZShbZGlyPVxcXCJydGxcXFwiXSwgW2Rpcj1cXFwicnRsXFxcIl0gKikge1xcbiAgcGFkZGluZy1yaWdodDogMi41cmVtO1xcbn1cXG4ucnRsXFxcXDpwci0zOndoZXJlKFtkaXI9XFxcInJ0bFxcXCJdLCBbZGlyPVxcXCJydGxcXFwiXSAqKSB7XFxuICBwYWRkaW5nLXJpZ2h0OiAwLjc1cmVtO1xcbn1cXG5cIiwgXCJcIix7XCJ2ZXJzaW9uXCI6MyxcInNvdXJjZXNcIjpbXCJ3ZWJwYWNrOi8vc3R5bGVzL2dsb2JhbHMuY3NzXCJdLFwibmFtZXNcIjpbXSxcIm1hcHBpbmdzXCI6XCJBQUFBO0VBQUEsd0JBQTBCO0VBQTFCLHdCQUEwQjtFQUExQixtQkFBMEI7RUFBMUIsbUJBQTBCO0VBQTFCLGNBQTBCO0VBQTFCLGNBQTBCO0VBQTFCLGNBQTBCO0VBQTFCLGVBQTBCO0VBQTFCLGVBQTBCO0VBQTFCLGFBQTBCO0VBQTFCLGFBQTBCO0VBQTFCLGtCQUEwQjtFQUExQixzQ0FBMEI7RUFBMUIsOEJBQTBCO0VBQTFCLDZCQUEwQjtFQUExQiw0QkFBMEI7RUFBMUIsZUFBMEI7RUFBMUIsb0JBQTBCO0VBQTFCLHNCQUEwQjtFQUExQix1QkFBMEI7RUFBMUIsd0JBQTBCO0VBQTFCLGtCQUEwQjtFQUExQiwyQkFBMEI7RUFBMUIsNEJBQTBCO0VBQTFCLHNDQUEwQjtFQUExQixrQ0FBMEI7RUFBMUIsMkJBQTBCO0VBQTFCLHNCQUEwQjtFQUExQiw4QkFBMEI7RUFBMUIsWUFBMEI7RUFBMUIsa0JBQTBCO0VBQTFCLGdCQUEwQjtFQUExQixpQkFBMEI7RUFBMUIsa0JBQTBCO0VBQTFCLGNBQTBCO0VBQTFCLGdCQUEwQjtFQUExQixhQUEwQjtFQUExQixtQkFBMEI7RUFBMUIscUJBQTBCO0VBQTFCLDJCQUEwQjtFQUExQix5QkFBMEI7RUFBMUIsMEJBQTBCO0VBQTFCLDJCQUEwQjtFQUExQix1QkFBMEI7RUFBMUIsd0JBQTBCO0VBQTFCLHlCQUEwQjtFQUExQixzQkFBMEI7RUFBMUIsb0JBQTBCO0VBQTFCLHNCQUEwQjtFQUExQixxQkFBMEI7RUFBMUI7QUFBMEI7O0FBQTFCO0VBQUEsd0JBQTBCO0VBQTFCLHdCQUEwQjtFQUExQixtQkFBMEI7RUFBMUIsbUJBQTBCO0VBQTFCLGNBQTBCO0VBQTFCLGNBQTBCO0VBQTFCLGNBQTBCO0VBQTFCLGVBQTBCO0VBQTFCLGVBQTBCO0VBQTFCLGFBQTBCO0VBQTFCLGFBQTBCO0VBQTFCLGtCQUEwQjtFQUExQixzQ0FBMEI7RUFBMUIsOEJBQTBCO0VBQTFCLDZCQUEwQjtFQUExQiw0QkFBMEI7RUFBMUIsZUFBMEI7RUFBMUIsb0JBQTBCO0VBQTFCLHNCQUEwQjtFQUExQix1QkFBMEI7RUFBMUIsd0JBQTBCO0VBQTFCLGtCQUEwQjtFQUExQiwyQkFBMEI7RUFBMUIsNEJBQTBCO0VBQTFCLHNDQUEwQjtFQUExQixrQ0FBMEI7RUFBMUIsMkJBQTBCO0VBQTFCLHNCQUEwQjtFQUExQiw4QkFBMEI7RUFBMUIsWUFBMEI7RUFBMUIsa0JBQTBCO0VBQTFCLGdCQUEwQjtFQUExQixpQkFBMEI7RUFBMUIsa0JBQTBCO0VBQTFCLGNBQTBCO0VBQTFCLGdCQUEwQjtFQUExQixhQUEwQjtFQUExQixtQkFBMEI7RUFBMUIscUJBQTBCO0VBQTFCLDJCQUEwQjtFQUExQix5QkFBMEI7RUFBMUIsMEJBQTBCO0VBQTFCLDJCQUEwQjtFQUExQix1QkFBMEI7RUFBMUIsd0JBQTBCO0VBQTFCLHlCQUEwQjtFQUExQixzQkFBMEI7RUFBMUIsb0JBQTBCO0VBQTFCLHNCQUEwQjtFQUExQixxQkFBMEI7RUFBMUI7QUFBMEIsQ0FBMUI7O0NBQTBCLENBQTFCOzs7Q0FBMEI7O0FBQTFCOzs7RUFBQSxzQkFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsZUFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsbUJBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLHFCQUEwQixFQUExQixNQUEwQjtBQUFBOztBQUExQjs7RUFBQSxnQkFBMEI7QUFBQTs7QUFBMUI7Ozs7Ozs7O0NBQTBCOztBQUExQjs7RUFBQSxnQkFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsOEJBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLGdCQUEwQixFQUExQixNQUEwQjtFQUExQixjQUEwQjtLQUExQixXQUEwQixFQUExQixNQUEwQjtFQUExQix3REFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsNkJBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLCtCQUEwQixFQUExQixNQUEwQjtFQUExQix3Q0FBMEIsRUFBMUIsTUFBMEI7QUFBQTs7QUFBMUI7OztDQUEwQjs7QUFBMUI7RUFBQSxTQUEwQixFQUExQixNQUEwQjtFQUExQixvQkFBMEIsRUFBMUIsTUFBMEI7QUFBQTs7QUFBMUI7Ozs7Q0FBMEI7O0FBQTFCO0VBQUEsU0FBMEIsRUFBMUIsTUFBMEI7RUFBMUIsY0FBMEIsRUFBMUIsTUFBMEI7RUFBMUIscUJBQTBCLEVBQTFCLE1BQTBCO0FBQUE7O0FBQTFCOztDQUEwQjs7QUFBMUI7RUFBQSx5Q0FBMEI7VUFBMUIsaUNBQTBCO0FBQUE7O0FBQTFCOztDQUEwQjs7QUFBMUI7Ozs7OztFQUFBLGtCQUEwQjtFQUExQixvQkFBMEI7QUFBQTs7QUFBMUI7O0NBQTBCOztBQUExQjtFQUFBLGNBQTBCO0VBQTFCLHdCQUEwQjtBQUFBOztBQUExQjs7Q0FBMEI7O0FBQTFCOztFQUFBLG1CQUEwQjtBQUFBOztBQUExQjs7Ozs7Q0FBMEI7O0FBQTFCOzs7O0VBQUEsK0dBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLDZCQUEwQixFQUExQixNQUEwQjtFQUExQiwrQkFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsY0FBMEIsRUFBMUIsTUFBMEI7QUFBQTs7QUFBMUI7O0NBQTBCOztBQUExQjtFQUFBLGNBQTBCO0FBQUE7O0FBQTFCOztDQUEwQjs7QUFBMUI7O0VBQUEsY0FBMEI7RUFBMUIsY0FBMEI7RUFBMUIsa0JBQTBCO0VBQTFCLHdCQUEwQjtBQUFBOztBQUExQjtFQUFBLGVBQTBCO0FBQUE7O0FBQTFCO0VBQUEsV0FBMEI7QUFBQTs7QUFBMUI7Ozs7Q0FBMEI7O0FBQTFCO0VBQUEsY0FBMEIsRUFBMUIsTUFBMEI7RUFBMUIscUJBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLHlCQUEwQixFQUExQixNQUEwQjtBQUFBOztBQUExQjs7OztDQUEwQjs7QUFBMUI7Ozs7O0VBQUEsb0JBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLDhCQUEwQixFQUExQixNQUEwQjtFQUExQixnQ0FBMEIsRUFBMUIsTUFBMEI7RUFBMUIsZUFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsb0JBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLG9CQUEwQixFQUExQixNQUEwQjtFQUExQix1QkFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsY0FBMEIsRUFBMUIsTUFBMEI7RUFBMUIsU0FBMEIsRUFBMUIsTUFBMEI7RUFBMUIsVUFBMEIsRUFBMUIsTUFBMEI7QUFBQTs7QUFBMUI7O0NBQTBCOztBQUExQjs7RUFBQSxvQkFBMEI7QUFBQTs7QUFBMUI7OztDQUEwQjs7QUFBMUI7Ozs7RUFBQSwwQkFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsNkJBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLHNCQUEwQixFQUExQixNQUEwQjtBQUFBOztBQUExQjs7Q0FBMEI7O0FBQTFCO0VBQUEsYUFBMEI7QUFBQTs7QUFBMUI7O0NBQTBCOztBQUExQjtFQUFBLGdCQUEwQjtBQUFBOztBQUExQjs7Q0FBMEI7O0FBQTFCO0VBQUEsd0JBQTBCO0FBQUE7O0FBQTFCOztDQUEwQjs7QUFBMUI7O0VBQUEsWUFBMEI7QUFBQTs7QUFBMUI7OztDQUEwQjs7QUFBMUI7RUFBQSw2QkFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsb0JBQTBCLEVBQTFCLE1BQTBCO0FBQUE7O0FBQTFCOztDQUEwQjs7QUFBMUI7RUFBQSx3QkFBMEI7QUFBQTs7QUFBMUI7OztDQUEwQjs7QUFBMUI7RUFBQSwwQkFBMEIsRUFBMUIsTUFBMEI7RUFBMUIsYUFBMEIsRUFBMUIsTUFBMEI7QUFBQTs7QUFBMUI7O0NBQTBCOztBQUExQjtFQUFBLGtCQUEwQjtBQUFBOztBQUExQjs7Q0FBMEI7O0FBQTFCOzs7Ozs7Ozs7Ozs7O0VBQUEsU0FBMEI7QUFBQTs7QUFBMUI7RUFBQSxTQUEwQjtFQUExQixVQUEwQjtBQUFBOztBQUExQjtFQUFBLFVBQTBCO0FBQUE7O0FBQTFCOzs7RUFBQSxnQkFBMEI7RUFBMUIsU0FBMEI7RUFBMUIsVUFBMEI7QUFBQTs7QUFBMUI7O0NBQTBCO0FBQTFCO0VBQUEsVUFBMEI7QUFBQTs7QUFBMUI7O0NBQTBCOztBQUExQjtFQUFBLGdCQUEwQjtBQUFBOztBQUExQjs7O0NBQTBCOztBQUExQjtFQUFBLFVBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLGNBQTBCLEVBQTFCLE1BQTBCO0FBQUE7O0FBQTFCOztFQUFBLFVBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLGNBQTBCLEVBQTFCLE1BQTBCO0FBQUE7O0FBQTFCOztDQUEwQjs7QUFBMUI7O0VBQUEsZUFBMEI7QUFBQTs7QUFBMUI7O0NBQTBCO0FBQTFCO0VBQUEsZUFBMEI7QUFBQTs7QUFBMUI7Ozs7Q0FBMEI7O0FBQTFCOzs7Ozs7OztFQUFBLGNBQTBCLEVBQTFCLE1BQTBCO0VBQTFCLHNCQUEwQixFQUExQixNQUEwQjtBQUFBOztBQUExQjs7Q0FBMEI7O0FBQTFCOztFQUFBLGVBQTBCO0VBQTFCLFlBQTBCO0FBQUE7O0FBQTFCLHdFQUEwQjtBQUExQjtFQUFBLGFBQTBCO0FBQUE7O0FBQTFCO0VBQUEsd0JBQTBCO0tBQTFCLHFCQUEwQjtVQUExQixnQkFBMEI7RUFBMUIsc0JBQTBCO0VBQTFCLHFCQUEwQjtFQUExQixpQkFBMEI7RUFBMUIsa0JBQTBCO0VBQTFCLG1CQUEwQjtFQUExQixzQkFBMEI7RUFBMUIsc0JBQTBCO0VBQTFCLHFCQUEwQjtFQUExQixlQUEwQjtFQUExQixtQkFBMEI7RUFBMUIsc0JBQTBCO0FBQUE7O0FBQTFCO0VBQUEsOEJBQTBCO0VBQTFCLG1CQUEwQjtFQUExQiw0Q0FBMEI7RUFBMUIsMkJBQTBCO0VBQTFCLDRCQUEwQjtFQUExQix3QkFBMEI7RUFBMUIsMkdBQTBCO0VBQTFCLHlHQUEwQjtFQUExQixpRkFBMEI7RUFBMUI7QUFBMEI7O0FBQTFCO0VBQUEsY0FBMEI7RUFBMUI7QUFBMEI7O0FBQTFCO0VBQUEsY0FBMEI7RUFBMUI7QUFBMEI7O0FBQTFCO0VBQUE7QUFBMEI7O0FBQTFCO0VBQUEsaUJBQTBCO0VBQTFCO0FBQTBCOztBQUExQjtFQUFBO0FBQTBCOztBQUExQjtFQUFBLGNBQTBCO0VBQTFCO0FBQTBCOztBQUExQjtFQUFBLG1QQUEwQjtFQUExQix3Q0FBMEI7RUFBMUIsNEJBQTBCO0VBQTFCLDRCQUEwQjtFQUExQixxQkFBMEI7RUFBMUIsaUNBQTBCO1VBQTFCO0FBQTBCOztBQUExQjtFQUFBLHlCQUEwQjtFQUExQiw0QkFBMEI7RUFBMUIsd0JBQTBCO0VBQTFCLHdCQUEwQjtFQUExQixzQkFBMEI7RUFBMUIsaUNBQTBCO1VBQTFCO0FBQTBCOztBQUExQjtFQUFBLHdCQUEwQjtLQUExQixxQkFBMEI7VUFBMUIsZ0JBQTBCO0VBQTFCLFVBQTBCO0VBQTFCLGlDQUEwQjtVQUExQix5QkFBMEI7RUFBMUIscUJBQTBCO0VBQTFCLHNCQUEwQjtFQUExQiw2QkFBMEI7RUFBMUIseUJBQTBCO0tBQTFCLHNCQUEwQjtVQUExQixpQkFBMEI7RUFBMUIsY0FBMEI7RUFBMUIsWUFBMEI7RUFBMUIsV0FBMEI7RUFBMUIsY0FBMEI7RUFBMUIsc0JBQTBCO0VBQTFCLHFCQUEwQjtFQUExQixpQkFBMEI7RUFBMUI7QUFBMEI7O0FBQTFCO0VBQUE7QUFBMEI7O0FBQTFCO0VBQUE7QUFBMEI7O0FBQTFCO0VBQUEsOEJBQTBCO0VBQTFCLG1CQUEwQjtFQUExQiw0Q0FBMEI7RUFBMUIsMkJBQTBCO0VBQTFCLDRCQUEwQjtFQUExQix3QkFBMEI7RUFBMUIsMkdBQTBCO0VBQTFCLHlHQUEwQjtFQUExQjtBQUEwQjs7QUFBMUI7RUFBQSx5QkFBMEI7RUFBMUIsOEJBQTBCO0VBQTFCLDBCQUEwQjtFQUExQiwyQkFBMEI7RUFBMUI7QUFBMEI7O0FBQTFCO0VBQUEsc1FBQTBCO0FBQUE7O0FBQTFCOztFQUFBO0lBQUEsd0JBQTBCO09BQTFCLHFCQUEwQjtZQUExQjtFQUEwQjtBQUFBOztBQUExQjtFQUFBLG9LQUEwQjtBQUFBOztBQUExQjs7RUFBQTtJQUFBLHdCQUEwQjtPQUExQixxQkFBMEI7WUFBMUI7RUFBMEI7QUFBQTs7QUFBMUI7RUFBQSx5QkFBMEI7RUFBMUI7QUFBMEI7O0FBQTFCO0VBQUEsdU9BQTBCO0VBQTFCLHlCQUEwQjtFQUExQiw4QkFBMEI7RUFBMUIsMEJBQTBCO0VBQTFCLDJCQUEwQjtFQUExQiw0QkFBMEI7QUFBQTs7QUFBMUI7O0VBQUE7SUFBQSx3QkFBMEI7T0FBMUIscUJBQTBCO1lBQTFCO0VBQTBCO0FBQUE7O0FBQTFCO0VBQUEseUJBQTBCO0VBQTFCO0FBQTBCOztBQUExQjtFQUFBLGlCQUEwQjtFQUExQixxQkFBMEI7RUFBMUIsZUFBMEI7RUFBMUIsZ0JBQTBCO0VBQTFCLFVBQTBCO0VBQTFCLGdCQUEwQjtFQUExQjtBQUEwQjs7QUFBMUI7RUFBQSw2QkFBMEI7RUFBMUI7QUFBMEI7QUFDMUI7RUFBQSx3QkFBZ0M7S0FBaEMscUJBQWdDO1VBQWhDLGdCQUFnQztFQUFoQyxzQkFBZ0M7RUFBaEMscUJBQWdDO0VBQWhDLGlCQUFnQztFQUFoQyxrQkFBZ0M7RUFBaEMsbUJBQWdDO0VBQWhDLHNCQUFnQztFQUFoQyxzQkFBZ0M7RUFBaEMscUJBQWdDO0VBQWhDLGVBQWdDO0VBQWhDLG1CQUFnQztFQUFoQyxzQkFBZ0M7QUFBQTtBQUFoQztFQUFBLDhCQUFnQztFQUFoQyxtQkFBZ0M7RUFBaEMsNENBQWdDO0VBQWhDLDJCQUFnQztFQUFoQyw0QkFBZ0M7RUFBaEMsd0JBQWdDO0VBQWhDLDJHQUFnQztFQUFoQyx5R0FBZ0M7RUFBaEMsaUZBQWdDO0VBQWhDO0FBQWdDO0FBQWhDO0VBQUEsY0FBZ0M7RUFBaEM7QUFBZ0M7QUFBaEM7RUFBQSxjQUFnQztFQUFoQztBQUFnQztBQUFoQztFQUFBO0FBQWdDO0FBQWhDO0VBQUEsaUJBQWdDO0VBQWhDO0FBQWdDO0FBQWhDO0VBQUE7QUFBZ0M7QUFBaEM7RUFBQSxjQUFnQztFQUFoQztBQUFnQztBQUNoQztFQUFBLGtCQUErQjtFQUEvQixVQUErQjtFQUEvQixXQUErQjtFQUEvQixVQUErQjtFQUEvQixZQUErQjtFQUEvQixnQkFBK0I7RUFBL0Isc0JBQStCO0VBQS9CLG1CQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUEsUUFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUEsaUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUEsdUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsc0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEscUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7O0VBQUE7SUFBQTtFQUErQjtBQUFBO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBLHdCQUErQjtLQUEvQixxQkFBK0I7VUFBL0I7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUEsdUJBQStCO0VBQS9CLHNEQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHVCQUErQjtFQUEvQix1REFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSx1QkFBK0I7RUFBL0Isb0RBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsdUJBQStCO0VBQS9CLCtEQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHVCQUErQjtFQUEvQiwrREFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSx1QkFBK0I7RUFBL0IsNERBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsdUJBQStCO0VBQS9CLDhEQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHVCQUErQjtFQUEvQiw0REFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQSxnQkFBK0I7RUFBL0IsdUJBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQSxzQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxzQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxzQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxzQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxzQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLHNCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGtCQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxzQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxxQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxrQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxxQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxpQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxtQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxpQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQSxpQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxtQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxtQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxtQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxrQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSxvQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSwyQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSwyQkFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSwwRUFBK0I7RUFBL0IsOEZBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsK0VBQStCO0VBQS9CLG1HQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLDBDQUErQjtFQUEvQix1REFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSw0Q0FBK0I7RUFBL0IsdURBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsNkNBQStCO0VBQS9CLHdEQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLGdGQUErQjtFQUEvQixvR0FBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSwyR0FBK0I7RUFBL0IseUdBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsb0JBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUE7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBLHdCQUErQjtFQUEvQix3REFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQSwrRkFBK0I7RUFBL0Isd0RBQStCO0VBQS9CO0FBQStCO0FBQS9CO0VBQUEsOEJBQStCO0VBQS9CLHdEQUErQjtFQUEvQjtBQUErQjtBQUEvQjtFQUFBLDRCQUErQjtFQUEvQix3REFBK0I7RUFBL0I7QUFBK0I7QUFBL0I7RUFBQTtBQUErQjtBQUEvQjtFQUFBO0FBQStCOztBQUUvQixpQkFBaUI7QUFDakIsbUdBQW1HO0FBQ25HLG1HQUFtRzs7QUFFbkcsa0NBQWtDO0FBQ2xDO0VBQ0UsdUJBQXVCO0FBQ3pCOztBQUVBO0VBQ0UsOERBQThEO0VBQzlELGdCQUFnQjtFQUNoQix5QkFBeUIsRUFBRSxlQUFlO0VBQzFDLGNBQWMsRUFBRSxpQkFBaUI7RUFDakMsZ0JBQWdCO0VBQ2hCLG1DQUFtQztFQUNuQyxrQ0FBa0M7QUFDcEM7O0FBRUEsZ0JBQWdCO0FBQ2hCO0VBQ0UsZ0NBQWdDO0FBQ2xDOztBQUVBO0VBQ0UsZ0NBQWdDO0FBQ2xDOztBQUVBLGdCQUFnQjtBQUNoQjtFQUNFLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGlCQUFpQjtBQUNuQjs7QUFFQSxxQkFBcUI7QUFDckI7RUFDRSxVQUFVO0VBQ1YsV0FBVztBQUNiOztBQUVBO0VBQ0UsbUJBQW1CO0VBQ25CLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxtQkFBbUI7QUFDckI7O0FBRUEsdUJBQXVCO0FBRXJCO0VBQUEsY0FBa1A7RUFBbFAsV0FBa1A7RUFBbFAsc0JBQWtQO0VBQWxQLGlCQUFrUDtFQUFsUCxzQkFBa1A7RUFBbFAsNERBQWtQO0VBQWxQLGtCQUFrUDtFQUFsUCw0REFBa1A7RUFBbFAsa0JBQWtQO0VBQWxQLG1CQUFrUDtFQUFsUCxvQkFBa1A7RUFBbFA7QUFBa1A7QUFBbFA7RUFBQSwyQkFBa1A7RUFBbFA7QUFBa1A7QUFBbFA7RUFBQSwyQkFBa1A7RUFBbFA7QUFBa1A7QUFBbFA7RUFBQSw0Q0FBa1A7RUFBbFAsdURBQWtQO0VBQWxQLHVHQUFrUDtFQUFsUCx3QkFBa1A7RUFBbFAsd0RBQWtQO0VBQWxQLDBCQUFrUDtFQUFsUDtBQUFrUDtBQUFsUDtFQUFBLHNCQUFrUDtFQUFsUCwyREFBa1A7RUFBbFAsOEJBQWtQO0VBQWxQLG1CQUFrUDtFQUFsUCwyR0FBa1A7RUFBbFAseUdBQWtQO0VBQWxQLDRGQUFrUDtFQUFsUCxvQkFBa1A7RUFBbFA7QUFBa1A7QUFBbFA7O0VBQUE7SUFBQSxtQkFBa1A7SUFBbFA7RUFBa1A7QUFBQTs7QUFJbFA7RUFBQSxzQkFBNEU7RUFBNUU7QUFBNEU7O0FBQTVFO0VBQUEsc0JBQTRFO0VBQTVFLDBEQUE0RTtFQUE1RSxvQkFBNEU7RUFBNUU7QUFBNEU7O0FBSTVFO0VBQUEscUJBQXVEO0VBQXZELGNBQXVEO0VBQXZELG1CQUF1RDtFQUF2RCxvQkFBdUQ7RUFBdkQsZ0JBQXVEO0VBQXZELG9CQUF1RDtFQUF2RDtBQUF1RDs7QUFJdkQ7RUFBQSxtQkFBcUM7RUFBckMsbUJBQXFDO0VBQXJDLG9CQUFxQztFQUFyQyxvQkFBcUM7RUFBckM7QUFBcUM7O0FBR3ZDLHlCQUF5QjtBQUV2QjtFQUFBLG9CQUFnTztFQUFoTyxtQkFBZ087RUFBaE8sdUJBQWdPO0VBQWhPLHNCQUFnTztFQUFoTyxpQkFBZ087RUFBaE8seUJBQWdPO0VBQWhPLGtCQUFnTztFQUFoTyxtQkFBZ087RUFBaE8scUJBQWdPO0VBQWhPLHdCQUFnTztFQUFoTyxtQkFBZ087RUFBaE8sb0JBQWdPO0VBQWhPLGdCQUFnTztFQUFoTyx3QkFBZ087RUFBaE8sd0RBQWdPO0VBQWhPO0FBQWdPO0FBQWhPO0VBQUEsOEJBQWdPO0VBQWhPLG1CQUFnTztFQUFoTywyR0FBZ087RUFBaE8seUdBQWdPO0VBQWhPLDRGQUFnTztFQUFoTywyQkFBZ087RUFBaE87QUFBZ087O0FBSWhPO0VBQUEsa0JBQWdIO0VBQWhILDJEQUFnSDtFQUFoSCxvQkFBZ0g7RUFBaEgsbURBQWdIO0VBQWhILDRDQUFnSDtFQUFoSCx1REFBZ0g7RUFBaEgsdUdBQWdIO0VBQWhILG9CQUFnSDtFQUFoSCxtQkFBZ0g7RUFBaEgsdUJBQWdIO0VBQWhILHNCQUFnSDtFQUFoSCxpQkFBZ0g7RUFBaEgseUJBQWdIO0VBQWhILGtCQUFnSDtFQUFoSCxtQkFBZ0g7RUFBaEgscUJBQWdIO0VBQWhILHdCQUFnSDtFQUFoSCxtQkFBZ0g7RUFBaEgsb0JBQWdIO0VBQWhILGdCQUFnSDtFQUFoSCx3QkFBZ0g7RUFBaEgsd0RBQWdIO0VBQWhIO0FBQWdIOztBQUFoSDtFQUFBLDhCQUFnSDtFQUFoSCxtQkFBZ0g7RUFBaEgsMkdBQWdIO0VBQWhILHlHQUFnSDtFQUFoSCw0RkFBZ0g7RUFBaEgsMkJBQWdIO0VBQWhIO0FBQWdIOztBQUFoSDtFQUFBLDJDQUFnSDtBQUFBOztBQUFoSDtFQUFBLGtCQUFnSDtFQUFoSCwwREFBZ0g7RUFBaEgsNkNBQWdIO0VBQWhILHdEQUFnSDtFQUFoSDtBQUFnSDs7QUFBaEg7RUFBQSxvQkFBZ0g7RUFBaEg7QUFBZ0g7O0FBSWhIO0VBQUEsc0JBQWtJO0VBQWxJLDREQUFrSTtFQUFsSSxrQkFBa0k7RUFBbEksNERBQWtJO0VBQWxJLG9CQUFrSTtFQUFsSSxnREFBa0k7RUFBbEksNENBQWtJO0VBQWxJLHVEQUFrSTtFQUFsSSx1R0FBa0k7RUFBbEksb0JBQWtJO0VBQWxJLG1CQUFrSTtFQUFsSSx1QkFBa0k7RUFBbEksc0JBQWtJO0VBQWxJLGlCQUFrSTtFQUFsSSx5QkFBa0k7RUFBbEksa0JBQWtJO0VBQWxJLG1CQUFrSTtFQUFsSSxxQkFBa0k7RUFBbEksd0JBQWtJO0VBQWxJLG1CQUFrSTtFQUFsSSxvQkFBa0k7RUFBbEksZ0JBQWtJO0VBQWxJLHdCQUFrSTtFQUFsSSx3REFBa0k7RUFBbEk7QUFBa0k7O0FBQWxJO0VBQUEsOEJBQWtJO0VBQWxJLG1CQUFrSTtFQUFsSSwyR0FBa0k7RUFBbEkseUdBQWtJO0VBQWxJLDRGQUFrSTtFQUFsSSwyQkFBa0k7RUFBbEk7QUFBa0k7O0FBQWxJO0VBQUEsMkNBQWtJO0FBQUE7O0FBQWxJO0VBQUEsa0JBQWtJO0VBQWxJLDREQUFrSTtFQUFsSSw2Q0FBa0k7RUFBbEksd0RBQWtJO0VBQWxJO0FBQWtJOztBQUFsSTtFQUFBLG9CQUFrSTtFQUFsSTtBQUFrSTs7QUFJbEk7RUFBQSxrQkFBeUc7RUFBekcsMERBQXlHO0VBQXpHLG9CQUF5RztFQUF6RyxtREFBeUc7RUFBekcsNENBQXlHO0VBQXpHLHVEQUF5RztFQUF6Ryx1R0FBeUc7RUFBekcsb0JBQXlHO0VBQXpHLG1CQUF5RztFQUF6Ryx1QkFBeUc7RUFBekcsc0JBQXlHO0VBQXpHLGlCQUF5RztFQUF6Ryx5QkFBeUc7RUFBekcsa0JBQXlHO0VBQXpHLG1CQUF5RztFQUF6RyxxQkFBeUc7RUFBekcsd0JBQXlHO0VBQXpHLG1CQUF5RztFQUF6RyxvQkFBeUc7RUFBekcsZ0JBQXlHO0VBQXpHLHdCQUF5RztFQUF6Ryx3REFBeUc7RUFBekc7QUFBeUc7O0FBQXpHO0VBQUEsOEJBQXlHO0VBQXpHLG1CQUF5RztFQUF6RywyR0FBeUc7RUFBekcseUdBQXlHO0VBQXpHLDRGQUF5RztFQUF6RywyQkFBeUc7RUFBekc7QUFBeUc7O0FBQXpHO0VBQUEsMkNBQXlHO0FBQUE7O0FBQXpHO0VBQUEsa0JBQXlHO0VBQXpHLDBEQUF5RztFQUF6Ryw2Q0FBeUc7RUFBekcsd0RBQXlHO0VBQXpHO0FBQXlHOztBQUF6RztFQUFBLG9CQUF5RztFQUF6RztBQUF5Rzs7QUFJekc7RUFBQSxrQkFBNkc7RUFBN0csMERBQTZHO0VBQTdHLG9CQUE2RztFQUE3RyxtREFBNkc7RUFBN0csNENBQTZHO0VBQTdHLHVEQUE2RztFQUE3Ryx1R0FBNkc7RUFBN0csb0JBQTZHO0VBQTdHLG1CQUE2RztFQUE3Ryx1QkFBNkc7RUFBN0csc0JBQTZHO0VBQTdHLGlCQUE2RztFQUE3Ryx5QkFBNkc7RUFBN0csa0JBQTZHO0VBQTdHLG1CQUE2RztFQUE3RyxxQkFBNkc7RUFBN0csd0JBQTZHO0VBQTdHLG1CQUE2RztFQUE3RyxvQkFBNkc7RUFBN0csZ0JBQTZHO0VBQTdHLHdCQUE2RztFQUE3Ryx3REFBNkc7RUFBN0c7QUFBNkc7O0FBQTdHO0VBQUEsOEJBQTZHO0VBQTdHLG1CQUE2RztFQUE3RywyR0FBNkc7RUFBN0cseUdBQTZHO0VBQTdHLDRGQUE2RztFQUE3RywyQkFBNkc7RUFBN0c7QUFBNkc7O0FBQTdHO0VBQUEsMkNBQTZHO0FBQUE7O0FBQTdHO0VBQUEsa0JBQTZHO0VBQTdHLDBEQUE2RztFQUE3Ryw2Q0FBNkc7RUFBN0csd0RBQTZHO0VBQTdHO0FBQTZHOztBQUE3RztFQUFBLG9CQUE2RztFQUE3RztBQUE2Rzs7QUFJN0c7RUFBQSxxQkFBMEI7RUFBMUIsc0JBQTBCO0VBQTFCLHFCQUEwQjtFQUExQix3QkFBMEI7RUFBMUIsa0JBQTBCO0VBQTFCO0FBQTBCOztBQUkxQjtFQUFBLG9CQUEwQjtFQUExQixxQkFBMEI7RUFBMUIsb0JBQTBCO0VBQTFCLHVCQUEwQjtFQUExQixlQUEwQjtFQUExQjtBQUEwQjs7QUFHNUIsdUJBQXVCO0FBRXJCO0VBQUEsbUJBQTJFO0VBQTNFLGlCQUEyRTtFQUEzRSxzQkFBMkU7RUFBM0UsNERBQTJFO0VBQTNFLGtCQUEyRTtFQUEzRSw0REFBMkU7RUFBM0UsNEVBQTJFO0VBQTNFLDJGQUEyRTtFQUEzRSx1R0FBMkU7RUFBM0U7QUFBMkU7O0FBSTNFO0VBQUEsd0JBQTRDO0VBQTVDLHNCQUE0QztFQUE1Qyw0REFBNEM7RUFBNUMsb0JBQTRDO0VBQTVDLHFCQUE0QztFQUE1QyxvQkFBNEM7RUFBNUM7QUFBNEM7O0FBSTVDO0VBQUEsb0JBQWdCO0VBQWhCLHFCQUFnQjtFQUFoQixvQkFBZ0I7RUFBaEI7QUFBZ0I7O0FBSWhCO0VBQUEsZ0NBQStFO0VBQS9FLCtCQUErRTtFQUEvRSxxQkFBK0U7RUFBL0Usc0JBQStFO0VBQS9FLDREQUErRTtFQUEvRSxrQkFBK0U7RUFBL0UsNERBQStFO0VBQS9FLG9CQUErRTtFQUEvRSxxQkFBK0U7RUFBL0Usb0JBQStFO0VBQS9FO0FBQStFOztBQUdqRix3QkFBd0I7QUFFdEI7RUFBQTtBQUE2QztBQUE3QztFQUFBLHdCQUE2QztFQUE3QyxrRUFBNkM7RUFBN0MsMkRBQTZDO0VBQTdDLHNCQUE2QztFQUE3QztBQUE2Qzs7QUFJN0M7RUFBQSxrQkFBMkI7RUFBM0I7QUFBMkI7O0FBSTNCO0VBQUEsb0JBQTZGO0VBQTdGLHFCQUE2RjtFQUE3RixpQkFBNkY7RUFBN0Ysb0JBQTZGO0VBQTdGLGdCQUE2RjtFQUE3RixrQkFBNkY7RUFBN0YsaUJBQTZGO0VBQTdGLGdCQUE2RjtFQUE3Rix5QkFBNkY7RUFBN0Ysc0JBQTZGO0VBQTdGLG9CQUE2RjtFQUE3RjtBQUE2Rjs7QUFJN0Y7RUFBQSxtQkFBNEQ7RUFBNUQsb0JBQTREO0VBQTVELHFCQUE0RDtFQUE1RCxpQkFBNEQ7RUFBNUQsb0JBQTREO0VBQTVELG1CQUE0RDtFQUE1RCxvQkFBNEQ7RUFBNUQsb0JBQTREO0VBQTVEO0FBQTREOztBQUk1RDtFQUFBLGtCQUEyQjtFQUEzQjtBQUEyQjs7QUFJM0I7RUFBQSxrQkFBbUQ7RUFBbkQsNERBQW1EO0VBQW5ELCtGQUFtRDtFQUFuRCx3REFBbUQ7RUFBbkQ7QUFBbUQ7O0FBR3JELHdCQUF3QjtBQUV0QjtFQUFBLG9CQUEwRTtFQUExRSxtQkFBMEU7RUFBMUUscUJBQTBFO0VBQTFFLHFCQUEwRTtFQUExRSxzQkFBMEU7RUFBMUUsb0JBQTBFO0VBQTFFLHVCQUEwRTtFQUExRSxrQkFBMEU7RUFBMUUsaUJBQTBFO0VBQTFFO0FBQTBFOztBQUkxRTtFQUFBLGtCQUE0QztFQUE1Qyw0REFBNEM7RUFBNUMsb0JBQTRDO0VBQTVDLGlEQUE0QztFQUE1QyxvQkFBNEM7RUFBNUMsbUJBQTRDO0VBQTVDLHFCQUE0QztFQUE1QyxxQkFBNEM7RUFBNUMsc0JBQTRDO0VBQTVDLG9CQUE0QztFQUE1Qyx1QkFBNEM7RUFBNUMsa0JBQTRDO0VBQTVDLGlCQUE0QztFQUE1QztBQUE0Qzs7QUFJNUM7RUFBQSxrQkFBNEM7RUFBNUMsNERBQTRDO0VBQTVDLG9CQUE0QztFQUE1QyxnREFBNEM7RUFBNUMsb0JBQTRDO0VBQTVDLG1CQUE0QztFQUE1QyxxQkFBNEM7RUFBNUMscUJBQTRDO0VBQTVDLHNCQUE0QztFQUE1QyxvQkFBNEM7RUFBNUMsdUJBQTRDO0VBQTVDLGtCQUE0QztFQUE1QyxpQkFBNEM7RUFBNUM7QUFBNEM7O0FBSTVDO0VBQUEsa0JBQTBDO0VBQTFDLDREQUEwQztFQUExQyxvQkFBMEM7RUFBMUMsaURBQTBDO0VBQTFDLG9CQUEwQztFQUExQyxtQkFBMEM7RUFBMUMscUJBQTBDO0VBQTFDLHFCQUEwQztFQUExQyxzQkFBMEM7RUFBMUMsb0JBQTBDO0VBQTFDLHVCQUEwQztFQUExQyxrQkFBMEM7RUFBMUMsaUJBQTBDO0VBQTFDO0FBQTBDOztBQUkxQztFQUFBLGtCQUEwQztFQUExQyw0REFBMEM7RUFBMUMsb0JBQTBDO0VBQTFDLGdEQUEwQztFQUExQyxvQkFBMEM7RUFBMUMsbUJBQTBDO0VBQTFDLHFCQUEwQztFQUExQyxxQkFBMEM7RUFBMUMsc0JBQTBDO0VBQTFDLG9CQUEwQztFQUExQyx1QkFBMEM7RUFBMUMsa0JBQTBDO0VBQTFDLGlCQUEwQztFQUExQztBQUEwQzs7QUFJMUM7RUFBQSxrQkFBb0M7RUFBcEMsNERBQW9DO0VBQXBDLG9CQUFvQztFQUFwQyxpREFBb0M7RUFBcEMsb0JBQW9DO0VBQXBDLG1CQUFvQztFQUFwQyxxQkFBb0M7RUFBcEMscUJBQW9DO0VBQXBDLHNCQUFvQztFQUFwQyxvQkFBb0M7RUFBcEMsdUJBQW9DO0VBQXBDLGtCQUFvQztFQUFwQyxpQkFBb0M7RUFBcEM7QUFBb0M7O0FBSXBDO0VBQUEsa0JBQTRDO0VBQTVDLDREQUE0QztFQUE1QyxvQkFBNEM7RUFBNUMsaURBQTRDO0VBQTVDLG9CQUE0QztFQUE1QyxtQkFBNEM7RUFBNUMscUJBQTRDO0VBQTVDLHFCQUE0QztFQUE1QyxzQkFBNEM7RUFBNUMsb0JBQTRDO0VBQTVDLHVCQUE0QztFQUE1QyxrQkFBNEM7RUFBNUMsaUJBQTRDO0VBQTVDO0FBQTRDOztBQUc5QyxpQkFBaUI7QUFFZjtFQUFBLHVCQUFxQjtFQUFyQjtBQUFxQjs7QUFJckI7RUFBQSxpQkFBK0Q7RUFBL0Qsc0JBQStEO0VBQS9ELDREQUErRDtFQUEvRCxrQkFBK0Q7RUFBL0QsNERBQStEO0VBQS9ELG9CQUErRDtFQUEvRCxpREFBK0Q7RUFBL0QsdUJBQStEO0VBQS9EO0FBQStEOztBQUkvRDtFQUFBLGlCQUFrRTtFQUFsRSxzQkFBa0U7RUFBbEUsNERBQWtFO0VBQWxFLGtCQUFrRTtFQUFsRSw0REFBa0U7RUFBbEUsb0JBQWtFO0VBQWxFLGlEQUFrRTtFQUFsRSx1QkFBa0U7RUFBbEU7QUFBa0U7O0FBSWxFO0VBQUEsaUJBQXlEO0VBQXpELHNCQUF5RDtFQUF6RCw0REFBeUQ7RUFBekQsa0JBQXlEO0VBQXpELDREQUF5RDtFQUF6RCxvQkFBeUQ7RUFBekQsaURBQXlEO0VBQXpELHVCQUF5RDtFQUF6RDtBQUF5RDs7QUFJekQ7RUFBQSxpQkFBNEQ7RUFBNUQsc0JBQTREO0VBQTVELDREQUE0RDtFQUE1RCxrQkFBNEQ7RUFBNUQsNERBQTREO0VBQTVELG9CQUE0RDtFQUE1RCxpREFBNEQ7RUFBNUQsdUJBQTREO0VBQTVEO0FBQTREOztBQUc5RCx3QkFBd0I7QUFDeEI7RUFDRSxrQ0FBa0M7QUFDcEM7O0FBRUE7RUFDRSxnQ0FBZ0M7QUFDbEM7O0FBRUE7RUFDRTtJQUNFLFVBQVU7RUFDWjtFQUNBO0lBQ0UsVUFBVTtFQUNaO0FBQ0Y7O0FBRUE7RUFDRTtJQUNFLDJCQUEyQjtJQUMzQixVQUFVO0VBQ1o7RUFDQTtJQUNFLHdCQUF3QjtJQUN4QixVQUFVO0VBQ1o7QUFDRjs7QUFFQSxpQkFBaUI7QUFDakI7RUFDRTtJQUNFLHdCQUF3QjtFQUMxQjs7RUFFQTtJQUNFLHlCQUF5QjtFQUMzQjs7RUFFQTtJQUNFLGVBQWU7SUFDZixnQkFBZ0I7RUFDbEI7O0VBRUE7SUFDRSxnQkFBZ0I7SUFDaEIsc0JBQXNCO0VBQ3hCO0FBQ0Y7O0FBRUEsNEJBQTRCO0FBRTFCO0VBQUEsaUJBQTZDO0VBQTdDLGtCQUE2QztFQUE3QyxnQkFBNkM7RUFBN0Msa0JBQTZDO0VBQTdDO0FBQTZDO0FBQTdDOztFQUFBO0lBQUEsb0JBQTZDO0lBQTdDO0VBQTZDO0FBQUE7QUFBN0M7O0VBQUE7SUFBQSxrQkFBNkM7SUFBN0M7RUFBNkM7QUFBQTs7QUFJN0M7RUFBQSxpQkFBb0I7RUFBcEI7QUFBb0I7O0FBQXBCOztFQUFBO0lBQUEsaUJBQW9CO0lBQXBCO0VBQW9CO0FBQUE7O0FBSXBCO0VBQUEsdUJBQTZCO0VBQTdCLDhEQUE2QjtFQUE3QjtBQUE2Qjs7QUFBN0I7O0VBQUE7SUFBQSx1QkFBNkI7SUFBN0IsNERBQTZCO0lBQTdCO0VBQTZCO0FBQUE7O0FBRy9CLG1CQUFtQjtBQUNuQjtFQUNFLDJDQUEyQztBQUM3Qzs7QUFFQTtFQUNFLDRDQUE0QztBQUM5Qzs7QUFFQTtFQUNFLDRDQUE0QztBQUM5Qzs7QUFFQTtFQUNFLDJFQUEyRTtBQUM3RTs7QUFFQSx5QkFBeUI7QUFFdkI7RUFBQTtBQUFpQjs7QUFJakI7RUFBQTtBQUFpQjs7QUFHbkIsd0JBQXdCO0FBRXRCO0VBQUEsOEJBQThHO0VBQTlHLG1CQUE4RztFQUE5RywyR0FBOEc7RUFBOUcseUdBQThHO0VBQTlHLDRGQUE4RztFQUE5RyxvQkFBOEc7RUFBOUcsNERBQThHO0VBQTlHLDJCQUE4RztFQUE5RztBQUE4Rzs7QUFHaEgsdUJBQXVCO0FBQ3ZCO0VBQ0UsVUFBVTtFQUNWLFdBQVc7QUFDYjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIsa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBLGtDQUFrQztBQUNsQztFQUVJO0lBQUEsa0JBQXVDO0lBQXZDLHlEQUF1QztJQUF2QyxvQkFBdUM7SUFBdkM7RUFBdUM7O0VBSXZDO0lBQUEsc0JBQXdDO0lBQXhDLHlEQUF3QztJQUF4QyxrQkFBd0M7SUFBeEM7RUFBd0M7O0VBSXhDO0lBQUEsc0JBQTBEO0lBQTFELHlEQUEwRDtJQUExRCxrQkFBMEQ7SUFBMUQseURBQTBEO0lBQTFELG9CQUEwRDtJQUExRDtFQUEwRDtBQUU5RDtBQTFVQTtFQUFBLDBCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDBCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDBCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDBCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDBCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDBCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDBCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDBCQTJVQTtFQTNVQSxzQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSwwQkEyVUE7RUEzVUEsa0JBMlVBO0VBM1VBO0FBMlVBO0FBM1VBO0VBQUEsMEJBMlVBO0VBM1VBLHdCQTJVQTtFQTNVQSx3REEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxnQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxzQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxrQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxrQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxrQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxrQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBLHNCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDhCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDJHQTJVQTtFQTNVQSx5R0EyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBO0FBMlVBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxrQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSwwQkEyVUE7RUEzVUEsc0JBMlVBO0VBM1VBO0FBMlVBO0FBM1VBO0VBQUEsMEJBMlVBO0VBM1VBLHNCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDhCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLDJHQTJVQTtFQTNVQSx5R0EyVUE7RUEzVUE7QUEyVUE7QUEzVUE7RUFBQSxvQkEyVUE7RUEzVUE7QUEyVUE7QUEzVUE7O0VBQUE7SUFBQSxnQkEyVUE7SUEzVUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUEsb0JBMlVBO0lBM1VBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBLG1CQTJVQTtJQTNVQTtFQTJVQTtBQUFBO0FBM1VBOztFQUFBO0lBQUE7RUEyVUE7O0VBM1VBO0lBQUE7RUEyVUE7QUFBQTtBQTNVQTs7RUFBQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBO0VBMlVBOztFQTNVQTtJQUFBLGtCQTJVQTtJQTNVQTtFQTJVQTtBQUFBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBO0FBMlVBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBO0FBMlVBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBO0FBMlVBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBO0FBMlVBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBLHNCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBLHNCQTJVQTtFQTNVQTtBQTJVQTtBQTNVQTtFQUFBO0FBMlVBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQTtBQTNVQTtFQUFBO0FBMlVBO0FBM1VBO0VBQUE7QUEyVUE7QUEzVUE7RUFBQTtBQTJVQVwiLFwic291cmNlc0NvbnRlbnRcIjpbXCJAaW1wb3J0ICd0YWlsd2luZGNzcy9iYXNlJztcXG5AaW1wb3J0ICd0YWlsd2luZGNzcy9jb21wb25lbnRzJztcXG5AaW1wb3J0ICd0YWlsd2luZGNzcy91dGlsaXRpZXMnO1xcblxcbi8qIEltcG9ydCBmb250cyAqL1xcbkBpbXBvcnQgdXJsKCdodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2NzczI/ZmFtaWx5PUludGVyOndnaHRAMzAwOzQwMDs1MDA7NjAwOzcwMCZkaXNwbGF5PXN3YXAnKTtcXG5AaW1wb3J0IHVybCgnaHR0cHM6Ly9mb250cy5nb29nbGVhcGlzLmNvbS9jc3MyP2ZhbWlseT1DYWlybzp3Z2h0QDMwMDs0MDA7NTAwOzYwMDs3MDAmZGlzcGxheT1zd2FwJyk7XFxuXFxuLyogTW9kZXJuIE1pbmltYWxpc3QgQmFzZSBTdHlsZXMgKi9cXG5odG1sIHtcXG4gIHNjcm9sbC1iZWhhdmlvcjogc21vb3RoO1xcbn1cXG5cXG5ib2R5IHtcXG4gIGZvbnQtZmFtaWx5OiAnSW50ZXInLCAndWktc2Fucy1zZXJpZicsICdzeXN0ZW0tdWknLCBzYW5zLXNlcmlmO1xcbiAgbGluZS1oZWlnaHQ6IDEuNjtcXG4gIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7IC8qIG5ldXRyYWwtNTAgKi9cXG4gIGNvbG9yOiAjMTcxNzE3OyAvKiB0ZXh0LXByaW1hcnkgKi9cXG4gIGZvbnQtd2VpZ2h0OiA0MDA7XFxuICAtd2Via2l0LWZvbnQtc21vb3RoaW5nOiBhbnRpYWxpYXNlZDtcXG4gIC1tb3otb3N4LWZvbnQtc21vb3RoaW5nOiBncmF5c2NhbGU7XFxufVxcblxcbi8qIEFyYWJpYyBmb250ICovXFxuLmZvbnQtYXJhYmljIHtcXG4gIGZvbnQtZmFtaWx5OiAnQ2Fpcm8nLCBzYW5zLXNlcmlmO1xcbn1cXG5cXG4uZm9udC1lbmdsaXNoIHtcXG4gIGZvbnQtZmFtaWx5OiAnSW50ZXInLCBzYW5zLXNlcmlmO1xcbn1cXG5cXG4vKiBSVEwgc3VwcG9ydCAqL1xcbltkaXI9XFxcInJ0bFxcXCJdIHtcXG4gIHRleHQtYWxpZ246IHJpZ2h0O1xcbn1cXG5cXG5bZGlyPVxcXCJydGxcXFwiXSAucnRsXFxcXDp0ZXh0LWxlZnQge1xcbiAgdGV4dC1hbGlnbjogbGVmdDtcXG59XFxuXFxuW2Rpcj1cXFwicnRsXFxcIl0gLnJ0bFxcXFw6dGV4dC1yaWdodCB7XFxuICB0ZXh0LWFsaWduOiByaWdodDtcXG59XFxuXFxuLyogQ3VzdG9tIHNjcm9sbGJhciAqL1xcbjo6LXdlYmtpdC1zY3JvbGxiYXIge1xcbiAgd2lkdGg6IDZweDtcXG4gIGhlaWdodDogNnB4O1xcbn1cXG5cXG46Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcXG4gIGJhY2tncm91bmQ6ICNmMWYxZjE7XFxuICBib3JkZXItcmFkaXVzOiAzcHg7XFxufVxcblxcbjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xcbiAgYmFja2dyb3VuZDogI2MxYzFjMTtcXG4gIGJvcmRlci1yYWRpdXM6IDNweDtcXG59XFxuXFxuOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7XFxuICBiYWNrZ3JvdW5kOiAjYThhOGE4O1xcbn1cXG5cXG4vKiBNb2Rlcm4gRm9ybSBTdHlsZXMgKi9cXG4uZm9ybS1pbnB1dCB7XFxuICBAYXBwbHkgYmxvY2sgdy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMjAwIHJvdW5kZWQteGwgc2hhZG93LXNvZnQgcGxhY2Vob2xkZXItdGV4dC10ZXJ0aWFyeSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeS00MDAgZm9jdXM6Ym9yZGVyLXByaW1hcnktNDAwIHNtOnRleHQtc20gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGJnLXN1cmZhY2UtcHJpbWFyeTtcXG59XFxuXFxuLmZvcm0taW5wdXQ6aW52YWxpZCB7XFxuICBAYXBwbHkgYm9yZGVyLXN0YXR1cy1lcnJvciBmb2N1czpyaW5nLXN0YXR1cy1lcnJvciBmb2N1czpib3JkZXItc3RhdHVzLWVycm9yO1xcbn1cXG5cXG4uZm9ybS1sYWJlbCB7XFxuICBAYXBwbHkgYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXRleHQtcHJpbWFyeSBtYi0yO1xcbn1cXG5cXG4uZm9ybS1lcnJvciB7XFxuICBAYXBwbHkgbXQtMSB0ZXh0LXNtIHRleHQtc3RhdHVzLWVycm9yO1xcbn1cXG5cXG4vKiBNb2Rlcm4gQnV0dG9uIFN0eWxlcyAqL1xcbi5idG4ge1xcbiAgQGFwcGx5IGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweC00IHB5LTIuNSBib3JkZXIgYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtc20gZm9udC1tZWRpdW0gcm91bmRlZC14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctb2Zmc2V0LW5ldXRyYWwtNTA7XFxufVxcblxcbi5idG4tcHJpbWFyeSB7XFxuICBAYXBwbHkgYnRuIHRleHQtd2hpdGUgYmctcHJpbWFyeS01MDAgaG92ZXI6YmctcHJpbWFyeS02MDAgZm9jdXM6cmluZy1wcmltYXJ5LTQwMCBzaGFkb3ctc29mdCBob3ZlcjpzaGFkb3ctbWVkaXVtO1xcbn1cXG5cXG4uYnRuLXNlY29uZGFyeSB7XFxuICBAYXBwbHkgYnRuIHRleHQtbmV1dHJhbC03MDAgYmctd2hpdGUgYm9yZGVyLW5ldXRyYWwtMjAwIGhvdmVyOmJnLW5ldXRyYWwtNTAgZm9jdXM6cmluZy1wcmltYXJ5LTQwMCBzaGFkb3ctc29mdCBob3ZlcjpzaGFkb3ctbWVkaXVtO1xcbn1cXG5cXG4uYnRuLWRhbmdlciB7XFxuICBAYXBwbHkgYnRuIHRleHQtd2hpdGUgYmctc3RhdHVzLWVycm9yIGhvdmVyOmJnLXJlZC02MDAgZm9jdXM6cmluZy1yZWQtNDAwIHNoYWRvdy1zb2Z0IGhvdmVyOnNoYWRvdy1tZWRpdW07XFxufVxcblxcbi5idG4tc3VjY2VzcyB7XFxuICBAYXBwbHkgYnRuIHRleHQtd2hpdGUgYmctYWNjZW50LTUwMCBob3ZlcjpiZy1hY2NlbnQtNjAwIGZvY3VzOnJpbmctYWNjZW50LTQwMCBzaGFkb3ctc29mdCBob3ZlcjpzaGFkb3ctbWVkaXVtO1xcbn1cXG5cXG4uYnRuLXNtIHtcXG4gIEBhcHBseSBweC0zIHB5LTEuNSB0ZXh0LXhzO1xcbn1cXG5cXG4uYnRuLWxnIHtcXG4gIEBhcHBseSBweC02IHB5LTMgdGV4dC1iYXNlO1xcbn1cXG5cXG4vKiBNb2Rlcm4gQ2FyZCBTdHlsZXMgKi9cXG4uY2FyZCB7XFxuICBAYXBwbHkgYmctc3VyZmFjZS1wcmltYXJ5IHNoYWRvdy1jYXJkIHJvdW5kZWQtMnhsIGJvcmRlciBib3JkZXItbmV1dHJhbC0xMDA7XFxufVxcblxcbi5jYXJkLWhlYWRlciB7XFxuICBAYXBwbHkgcHgtNiBweS01IGJvcmRlci1iIGJvcmRlci1uZXV0cmFsLTEwMDtcXG59XFxuXFxuLmNhcmQtYm9keSB7XFxuICBAYXBwbHkgcHgtNiBweS01O1xcbn1cXG5cXG4uY2FyZC1mb290ZXIge1xcbiAgQGFwcGx5IHB4LTYgcHktNSBib3JkZXItdCBib3JkZXItbmV1dHJhbC0xMDAgYmctc3VyZmFjZS1zZWNvbmRhcnkgcm91bmRlZC1iLTJ4bDtcXG59XFxuXFxuLyogTW9kZXJuIFRhYmxlIFN0eWxlcyAqL1xcbi50YWJsZSB7XFxuICBAYXBwbHkgbWluLXctZnVsbCBkaXZpZGUteSBkaXZpZGUtbmV1dHJhbC0xNTA7XFxufVxcblxcbi50YWJsZSB0aGVhZCB7XFxuICBAYXBwbHkgYmctc3VyZmFjZS1zZWNvbmRhcnk7XFxufVxcblxcbi50YWJsZSB0aCB7XFxuICBAYXBwbHkgcHgtNiBweS00IHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC10ZXh0LXNlY29uZGFyeSB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXI7XFxufVxcblxcbi50YWJsZSB0ZCB7XFxuICBAYXBwbHkgcHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC10ZXh0LXByaW1hcnk7XFxufVxcblxcbi50YWJsZSB0Ym9keSB0cjpudGgtY2hpbGQoZXZlbikge1xcbiAgQGFwcGx5IGJnLXN1cmZhY2Utc2Vjb25kYXJ5O1xcbn1cXG5cXG4udGFibGUgdGJvZHkgdHI6aG92ZXIge1xcbiAgQGFwcGx5IGJnLW5ldXRyYWwtNzUgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMTUwO1xcbn1cXG5cXG4vKiBNb2Rlcm4gQmFkZ2UgU3R5bGVzICovXFxuLmJhZGdlIHtcXG4gIEBhcHBseSBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtO1xcbn1cXG5cXG4uYmFkZ2UtcHJpbWFyeSB7XFxuICBAYXBwbHkgYmFkZ2UgYmctcHJpbWFyeS0xMDAgdGV4dC1wcmltYXJ5LTcwMDtcXG59XFxuXFxuLmJhZGdlLXNlY29uZGFyeSB7XFxuICBAYXBwbHkgYmFkZ2UgYmctbmV1dHJhbC0xMDAgdGV4dC1uZXV0cmFsLTcwMDtcXG59XFxuXFxuLmJhZGdlLXN1Y2Nlc3Mge1xcbiAgQGFwcGx5IGJhZGdlIGJnLWFjY2VudC0xMDAgdGV4dC1hY2NlbnQtNzAwO1xcbn1cXG5cXG4uYmFkZ2Utd2FybmluZyB7XFxuICBAYXBwbHkgYmFkZ2UgYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy03MDA7XFxufVxcblxcbi5iYWRnZS1kYW5nZXIge1xcbiAgQGFwcGx5IGJhZGdlIGJnLXJlZC0xMDAgdGV4dC1yZWQtNzAwO1xcbn1cXG5cXG4uYmFkZ2UtaW5mbyB7XFxuICBAYXBwbHkgYmFkZ2UgYmctcHJpbWFyeS0xMDAgdGV4dC1wcmltYXJ5LTcwMDtcXG59XFxuXFxuLyogQWxlcnQgc3R5bGVzICovXFxuLmFsZXJ0IHtcXG4gIEBhcHBseSBwLTQgcm91bmRlZC1tZDtcXG59XFxuXFxuLmFsZXJ0LXN1Y2Nlc3Mge1xcbiAgQGFwcGx5IGFsZXJ0IGJnLWdyZWVuLTUwIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIHRleHQtZ3JlZW4tODAwO1xcbn1cXG5cXG4uYWxlcnQtd2FybmluZyB7XFxuICBAYXBwbHkgYWxlcnQgYmcteWVsbG93LTUwIGJvcmRlciBib3JkZXIteWVsbG93LTIwMCB0ZXh0LXllbGxvdy04MDA7XFxufVxcblxcbi5hbGVydC1kYW5nZXIge1xcbiAgQGFwcGx5IGFsZXJ0IGJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgdGV4dC1yZWQtODAwO1xcbn1cXG5cXG4uYWxlcnQtaW5mbyB7XFxuICBAYXBwbHkgYWxlcnQgYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHRleHQtYmx1ZS04MDA7XFxufVxcblxcbi8qIEFuaW1hdGlvbiB1dGlsaXRpZXMgKi9cXG4uZmFkZS1pbiB7XFxuICBhbmltYXRpb246IGZhZGVJbiAwLjVzIGVhc2UtaW4tb3V0O1xcbn1cXG5cXG4uc2xpZGUtdXAge1xcbiAgYW5pbWF0aW9uOiBzbGlkZVVwIDAuM3MgZWFzZS1vdXQ7XFxufVxcblxcbkBrZXlmcmFtZXMgZmFkZUluIHtcXG4gIGZyb20ge1xcbiAgICBvcGFjaXR5OiAwO1xcbiAgfVxcbiAgdG8ge1xcbiAgICBvcGFjaXR5OiAxO1xcbiAgfVxcbn1cXG5cXG5Aa2V5ZnJhbWVzIHNsaWRlVXAge1xcbiAgZnJvbSB7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgxMHB4KTtcXG4gICAgb3BhY2l0eTogMDtcXG4gIH1cXG4gIHRvIHtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xcbiAgICBvcGFjaXR5OiAxO1xcbiAgfVxcbn1cXG5cXG4vKiBQcmludCBzdHlsZXMgKi9cXG5AbWVkaWEgcHJpbnQge1xcbiAgLm5vLXByaW50IHtcXG4gICAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50O1xcbiAgfVxcbiAgXFxuICAucHJpbnQtYnJlYWsge1xcbiAgICBwYWdlLWJyZWFrLWJlZm9yZTogYWx3YXlzO1xcbiAgfVxcbiAgXFxuICBib2R5IHtcXG4gICAgZm9udC1zaXplOiAxMnB0O1xcbiAgICBsaW5lLWhlaWdodDogMS40O1xcbiAgfVxcbiAgXFxuICAuY2FyZCB7XFxuICAgIGJveC1zaGFkb3c6IG5vbmU7XFxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7XFxuICB9XFxufVxcblxcbi8qIE1vZGVybiBMYXlvdXQgVXRpbGl0aWVzICovXFxuLnBhZ2UtY29udGFpbmVyIHtcXG4gIEBhcHBseSBtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtODtcXG59XFxuXFxuLnNlY3Rpb24tc3BhY2luZyB7XFxuICBAYXBwbHkgcHktOCBsZzpweS0xMjtcXG59XFxuXFxuLmNvbnRlbnQtc3BhY2luZyB7XFxuICBAYXBwbHkgc3BhY2UteS02IGxnOnNwYWNlLXktODtcXG59XFxuXFxuLyogTW9kZXJuIFNoYWRvd3MgKi9cXG4uc2hhZG93LXNvZnQge1xcbiAgYm94LXNoYWRvdzogMCAycHggOHB4IDAgcmdiYSgwLCAwLCAwLCAwLjA2KTtcXG59XFxuXFxuLnNoYWRvdy1tZWRpdW0ge1xcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCAwIHJnYmEoMCwgMCwgMCwgMC4wOCk7XFxufVxcblxcbi5zaGFkb3ctbGFyZ2Uge1xcbiAgYm94LXNoYWRvdzogMCA4cHggMjRweCAwIHJnYmEoMCwgMCwgMCwgMC4xMik7XFxufVxcblxcbi5zaGFkb3ctY2FyZCB7XFxuICBib3gtc2hhZG93OiAwIDFweCAzcHggMCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgMXB4IDJweCAwIHJnYmEoMCwgMCwgMCwgMC4wNik7XFxufVxcblxcbi8qIEVuaGFuY2VkIFJUTCBTdXBwb3J0ICovXFxuW2Rpcj1cXFwicnRsXFxcIl0gLnRhYmxlIHRoIHtcXG4gIEBhcHBseSB0ZXh0LXJpZ2h0O1xcbn1cXG5cXG5bZGlyPVxcXCJydGxcXFwiXSAudGFibGUgdGQge1xcbiAgQGFwcGx5IHRleHQtcmlnaHQ7XFxufVxcblxcbi8qIE1vZGVybiBGb2N1cyBTdGF0ZXMgKi9cXG4uZm9jdXMtcmluZyB7XFxuICBAYXBwbHkgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnktNDAwIGZvY3VzOnJpbmctb2Zmc2V0LTIgZm9jdXM6cmluZy1vZmZzZXQtbmV1dHJhbC01MDtcXG59XFxuXFxuLyogSW1wcm92ZWQgU2Nyb2xsYmFyICovXFxuOjotd2Via2l0LXNjcm9sbGJhciB7XFxuICB3aWR0aDogOHB4O1xcbiAgaGVpZ2h0OiA4cHg7XFxufVxcblxcbjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sge1xcbiAgYmFja2dyb3VuZDogI2Y1ZjVmNTtcXG4gIGJvcmRlci1yYWRpdXM6IDRweDtcXG59XFxuXFxuOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XFxuICBiYWNrZ3JvdW5kOiAjZDRkNGQ0O1xcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xcbn1cXG5cXG46Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHtcXG4gIGJhY2tncm91bmQ6ICNhM2EzYTM7XFxufVxcblxcbi8qIERhcmsgbW9kZSBzdXBwb3J0IChpZiBuZWVkZWQpICovXFxuQG1lZGlhIChwcmVmZXJzLWNvbG9yLXNjaGVtZTogZGFyaykge1xcbiAgLmRhcmstbW9kZSB7XFxuICAgIEBhcHBseSBiZy1uZXV0cmFsLTkwMCB0ZXh0LXRleHQtaW52ZXJzZTtcXG4gIH1cXG5cXG4gIC5kYXJrLW1vZGUgLmNhcmQge1xcbiAgICBAYXBwbHkgYmctbmV1dHJhbC04MDAgYm9yZGVyLW5ldXRyYWwtNzAwO1xcbiAgfVxcblxcbiAgLmRhcmstbW9kZSAuZm9ybS1pbnB1dCB7XFxuICAgIEBhcHBseSBiZy1uZXV0cmFsLTcwMCBib3JkZXItbmV1dHJhbC02MDAgdGV4dC10ZXh0LWludmVyc2U7XFxuICB9XFxufVxcblwiXSxcInNvdXJjZVJvb3RcIjpcIlwifV0pO1xuLy8gRXhwb3J0c1xuZXhwb3J0IGRlZmF1bHQgX19fQ1NTX0xPQURFUl9FWFBPUlRfX187XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});