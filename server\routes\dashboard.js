const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Get dashboard statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const startOfYear = new Date(today.getFullYear(), 0, 1);

    // Get basic counts
    const [
      totalProducts,
      totalCustomers,
      totalSuppliers,
      lowStockProducts,
      pendingSalesOrders,
      pendingPurchaseOrders,
      pendingMaintenanceOrders,
      monthlySales,
      monthlyPurchases,
      yearlySales
    ] = await Promise.all([
      // Total products
      prisma.product.count({
        where: { isActive: true }
      }),
      
      // Total customers
      prisma.customer.count({
        where: { 
          isActive: true,
          type: { in: ['CUSTOMER', 'BOTH'] }
        }
      }),
      
      // Total suppliers
      prisma.customer.count({
        where: { 
          isActive: true,
          type: { in: ['SUPPLIER', 'BOTH'] }
        }
      }),
      
      // Low stock products
      prisma.product.count({
        where: {
          isActive: true,
          currentStock: {
            lte: prisma.product.fields.minStock
          }
        }
      }),
      
      // Pending sales orders
      prisma.salesOrder.count({
        where: {
          status: { in: ['PENDING', 'CONFIRMED'] }
        }
      }),
      
      // Pending purchase orders
      prisma.purchaseOrder.count({
        where: {
          status: { in: ['PENDING', 'CONFIRMED'] }
        }
      }),
      
      // Pending maintenance orders
      prisma.maintenanceOrder.count({
        where: {
          status: { in: ['RECEIVED', 'IN_PROGRESS', 'WAITING_PARTS'] }
        }
      }),
      
      // Monthly sales total
      prisma.salesOrder.aggregate({
        where: {
          orderDate: { gte: startOfMonth },
          status: { not: 'CANCELLED' }
        },
        _sum: { total: true }
      }),
      
      // Monthly purchases total
      prisma.purchaseOrder.aggregate({
        where: {
          orderDate: { gte: startOfMonth },
          status: { not: 'CANCELLED' }
        },
        _sum: { total: true }
      }),
      
      // Yearly sales total
      prisma.salesOrder.aggregate({
        where: {
          orderDate: { gte: startOfYear },
          status: { not: 'CANCELLED' }
        },
        _sum: { total: true }
      })
    ]);

    // Get recent activities
    const recentSalesOrders = await prisma.salesOrder.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: {
          select: { name: true, nameAr: true }
        }
      }
    });

    const recentMaintenanceOrders = await prisma.maintenanceOrder.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: {
          select: { name: true, nameAr: true }
        }
      }
    });

    // Get sales chart data (last 12 months)
    const salesChartData = await prisma.$queryRaw`
      SELECT 
        DATE_TRUNC('month', "orderDate") as month,
        SUM(total) as total,
        COUNT(*) as count
      FROM sales_orders 
      WHERE "orderDate" >= ${new Date(today.getFullYear() - 1, today.getMonth(), 1)}
        AND status != 'CANCELLED'
      GROUP BY DATE_TRUNC('month', "orderDate")
      ORDER BY month
    `;

    // Get top selling products
    const topProducts = await prisma.$queryRaw`
      SELECT 
        p.id,
        p.name,
        p."nameAr",
        SUM(soi.quantity) as total_sold,
        SUM(soi.total) as total_revenue
      FROM products p
      JOIN sales_order_items soi ON p.id = soi."productId"
      JOIN sales_orders so ON soi."salesOrderId" = so.id
      WHERE so."orderDate" >= ${startOfMonth}
        AND so.status != 'CANCELLED'
      GROUP BY p.id, p.name, p."nameAr"
      ORDER BY total_sold DESC
      LIMIT 10
    `;

    res.json({
      stats: {
        totalProducts,
        totalCustomers,
        totalSuppliers,
        lowStockProducts,
        pendingSalesOrders,
        pendingPurchaseOrders,
        pendingMaintenanceOrders,
        monthlySales: monthlySales._sum.total || 0,
        monthlyPurchases: monthlyPurchases._sum.total || 0,
        yearlySales: yearlySales._sum.total || 0
      },
      recentActivities: {
        salesOrders: recentSalesOrders,
        maintenanceOrders: recentMaintenanceOrders
      },
      charts: {
        salesData: salesChartData,
        topProducts
      }
    });

  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get real-time notifications
router.get('/notifications', authenticateToken, async (req, res) => {
  try {
    const notifications = [];

    // Low stock alerts
    const lowStockProducts = await prisma.product.findMany({
      where: {
        isActive: true,
        currentStock: {
          lte: prisma.product.fields.minStock
        }
      },
      select: {
        id: true,
        name: true,
        nameAr: true,
        currentStock: true,
        minStock: true
      }
    });

    lowStockProducts.forEach(product => {
      notifications.push({
        id: `low-stock-${product.id}`,
        type: 'warning',
        title: 'Low Stock Alert',
        titleAr: 'تنبيه نفاد المخزون',
        message: `${product.name} is running low (${product.currentStock} remaining)`,
        messageAr: `${product.nameAr} على وشك النفاد (${product.currentStock} متبقي)`,
        timestamp: new Date(),
        read: false
      });
    });

    // Overdue orders
    const overdueOrders = await prisma.salesOrder.findMany({
      where: {
        dueDate: { lt: new Date() },
        status: { in: ['PENDING', 'CONFIRMED'] }
      },
      include: {
        customer: {
          select: { name: true, nameAr: true }
        }
      }
    });

    overdueOrders.forEach(order => {
      notifications.push({
        id: `overdue-${order.id}`,
        type: 'error',
        title: 'Overdue Order',
        titleAr: 'طلب متأخر',
        message: `Order ${order.orderNumber} for ${order.customer.name} is overdue`,
        messageAr: `الطلب ${order.orderNumber} للعميل ${order.customer.nameAr} متأخر`,
        timestamp: order.dueDate,
        read: false
      });
    });

    // Pending maintenance orders
    const pendingMaintenance = await prisma.maintenanceOrder.findMany({
      where: {
        status: 'RECEIVED',
        receivedDate: { lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Older than 24 hours
      },
      include: {
        customer: {
          select: { name: true, nameAr: true }
        }
      }
    });

    pendingMaintenance.forEach(order => {
      notifications.push({
        id: `pending-maintenance-${order.id}`,
        type: 'info',
        title: 'Pending Maintenance',
        titleAr: 'صيانة معلقة',
        message: `Maintenance order ${order.orderNumber} needs attention`,
        messageAr: `طلب الصيانة ${order.orderNumber} يحتاج متابعة`,
        timestamp: order.receivedDate,
        read: false
      });
    });

    // Sort by timestamp (newest first)
    notifications.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    res.json({ notifications });

  } catch (error) {
    console.error('Notifications error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
