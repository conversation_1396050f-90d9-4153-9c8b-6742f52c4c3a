"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "./pages/sales/index.js":
/*!******************************!*\
  !*** ./pages/sales/index.js ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SalesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/Layout */ \"./components/Layout.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"./contexts/AuthContext.js\");\n/* harmony import */ var _components_sales_QuoteModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/sales/QuoteModal */ \"./components/sales/QuoteModal.js\");\n/* harmony import */ var _components_sales_SalesOrderModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/sales/SalesOrderModal */ \"./components/sales/SalesOrderModal.js\");\n/* harmony import */ var _components_sales_InvoiceModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../components/sales/InvoiceModal */ \"./components/sales/InvoiceModal.js\");\n/* harmony import */ var _components_sales_SalesReturnModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../components/sales/SalesReturnModal */ \"./components/sales/SalesReturnModal.js\");\n/* harmony import */ var _components_sales_ProductCustomizer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../components/sales/ProductCustomizer */ \"./components/sales/ProductCustomizer.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUturnLeftIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,ReceiptPercentIcon,ShoppingCartIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArrowRightIcon,ArrowUturnLeftIcon,CheckCircleIcon,ClockIcon,DocumentTextIcon,EyeIcon,PlusIcon,ReceiptPercentIcon,ShoppingCartIcon,XCircleIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SalesPage() {\n    _s();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"quotes\");\n    // Modal states\n    const [quoteModal, setQuoteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        quote: null\n    });\n    const [salesOrderModal, setSalesOrderModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        salesOrder: null,\n        fromQuote: null\n    });\n    const [invoiceModal, setInvoiceModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        invoice: null,\n        fromSalesOrder: null\n    });\n    const [returnModal, setReturnModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        salesReturn: null\n    });\n    // Data states\n    const [quotes, setQuotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [salesOrders, setSalesOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [salesReturns, setSalesReturns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // التحقق من تسجيل الدخول\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && !user) {\n            router.push(\"/login\");\n        }\n    }, [\n        user,\n        isLoading,\n        router\n    ]);\n    // عرض شاشة التحميل أثناء التحقق من المصادقة\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 57,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this);\n    }\n    // إذا لم يكن المستخدم مسجل دخول، لا تعرض شيء (سيتم التوجيه)\n    if (!user) {\n        return null;\n    }\n    // Load data when tab changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            loadData();\n        }\n    }, [\n        activeTab,\n        user\n    ]);\n    const loadData = async ()=>{\n        setLoading(true);\n        try {\n            if (activeTab === \"quotes\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/quotes\"));\n                setQuotes(response.data.quotes || []);\n            } else if (activeTab === \"orders\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/sales-orders\"));\n                setSalesOrders(response.data.salesOrders || []);\n            } else if (activeTab === \"invoices\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/invoices\"));\n                setInvoices(response.data.invoices || []);\n            } else if (activeTab === \"returns\") {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_12__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/sales-returns\"));\n                setSalesReturns(response.data.salesReturns || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_11__[\"default\"].error(\"خطأ في تحميل البيانات\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Modal handlers\n    const handleQuoteCreate = ()=>{\n        setQuoteModal({\n            isOpen: true,\n            quote: null\n        });\n    };\n    const handleQuoteEdit = (quote)=>{\n        setQuoteModal({\n            isOpen: true,\n            quote\n        });\n    };\n    const handleQuoteConvert = (quote)=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder: null,\n            fromQuote: quote\n        });\n    };\n    const handleSalesOrderCreate = ()=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder: null,\n            fromQuote: null\n        });\n    };\n    const handleSalesOrderEdit = (salesOrder)=>{\n        setSalesOrderModal({\n            isOpen: true,\n            salesOrder,\n            fromQuote: null\n        });\n    };\n    const handleSalesOrderConvert = (salesOrder)=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice: null,\n            fromSalesOrder: salesOrder\n        });\n    };\n    const handleInvoiceCreate = ()=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice: null,\n            fromSalesOrder: null\n        });\n    };\n    const handleInvoiceEdit = (invoice)=>{\n        setInvoiceModal({\n            isOpen: true,\n            invoice,\n            fromSalesOrder: null\n        });\n    };\n    const handleReturnCreate = ()=>{\n        setReturnModal({\n            isOpen: true,\n            salesReturn: null\n        });\n    };\n    const handleReturnEdit = (salesReturn)=>{\n        setReturnModal({\n            isOpen: true,\n            salesReturn\n        });\n    };\n    // Save handlers\n    const handleQuoteSave = (quote)=>{\n        loadData();\n    };\n    const handleSalesOrderSave = (salesOrder)=>{\n        loadData();\n    };\n    const handleInvoiceSave = (invoice)=>{\n        loadData();\n    };\n    const handleReturnSave = (salesReturn)=>{\n        loadData();\n    };\n    const tabs = [\n        {\n            id: \"quotes\",\n            name: \"عروض الأسعار\",\n            nameEn: \"Quotes\",\n            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.DocumentTextIcon,\n            color: \"blue\",\n            description: \"لا تؤثر على المخزون - صالحة لمدة محددة\"\n        },\n        {\n            id: \"orders\",\n            name: \"أوامر البيع\",\n            nameEn: \"Sales Orders\",\n            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ShoppingCartIcon,\n            color: \"orange\",\n            description: \"تحجز من المخزون - لها مدة صلاحية\"\n        },\n        {\n            id: \"invoices\",\n            name: \"الفواتير\",\n            nameEn: \"Invoices\",\n            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ReceiptPercentIcon,\n            color: \"green\",\n            description: \"تخصم من المخزون نهائياً - تحسب في المبيعات\"\n        },\n        {\n            id: \"returns\",\n            name: \"مرتجع المبيعات\",\n            nameEn: \"Sales Returns\",\n            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowUturnLeftIcon,\n            color: \"red\",\n            description: \"إرجاع المنتجات وإضافة للمخزون\"\n        }\n    ];\n    const getStatusColor = (status)=>{\n        const colors = {\n            \"DRAFT\": \"gray\",\n            \"PENDING\": \"yellow\",\n            \"APPROVED\": \"green\",\n            \"REJECTED\": \"red\",\n            \"EXPIRED\": \"red\",\n            \"CONFIRMED\": \"blue\",\n            \"SHIPPED\": \"purple\",\n            \"DELIVERED\": \"green\",\n            \"CANCELLED\": \"red\",\n            \"PAID\": \"green\",\n            \"PARTIALLY_PAID\": \"yellow\",\n            \"OVERDUE\": \"red\"\n        };\n        return colors[status] || \"gray\";\n    };\n    const getStatusText = (status)=>{\n        const statusTexts = {\n            \"DRAFT\": \"مسودة\",\n            \"PENDING\": \"في الانتظار\",\n            \"APPROVED\": \"موافق عليه\",\n            \"REJECTED\": \"مرفوض\",\n            \"EXPIRED\": \"منتهي الصلاحية\",\n            \"CONFIRMED\": \"مؤكد\",\n            \"SHIPPED\": \"تم الشحن\",\n            \"DELIVERED\": \"تم التسليم\",\n            \"CANCELLED\": \"ملغي\",\n            \"PAID\": \"مدفوع\",\n            \"PARTIALLY_PAID\": \"مدفوع جزئياً\",\n            \"OVERDUE\": \"متأخر\"\n        };\n        return statusTexts[status] || status;\n    };\n    const renderQuotes = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"عروض الأسعار\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 233,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleQuoteCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 238,\n                                    columnNumber: 11\n                                }, this),\n                                \"عرض سعر جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 234,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 232,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم العرض\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"صالح حتى\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: quotes.map((quote)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: quote.quoteNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: quote.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(quote.status), \"-100 text-\").concat(getStatusColor(quote.status), \"-800\"),\n                                                    children: getStatusText(quote.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    quote.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: quote.validUntil\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuoteEdit(quote),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    quote.status === \"APPROVED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleQuoteConvert(quote),\n                                                        className: \"text-green-600 hover:text-green-900 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                                className: \"h-5 w-5 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"تحويل لأمر\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, quote.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 243,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 231,\n            columnNumber: 5\n        }, this);\n    const renderOrders = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"أوامر البيع\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 315,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSalesOrderCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 320,\n                                    columnNumber: 11\n                                }, this),\n                                \"أمر بيع جديد\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 316,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 314,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم الأمر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المخزون\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: salesOrders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: order.orderNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: order.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(order.status), \"-100 text-\").concat(getStatusColor(order.status), \"-800\"),\n                                                    children: getStatusText(order.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    order.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: order.reservedStock ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ClockIcon, {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"محجوز\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                                    children: \"غير محجوز\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSalesOrderEdit(order),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    order.status === \"CONFIRMED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleSalesOrderConvert(order),\n                                                        className: \"text-green-600 hover:text-green-900 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                                className: \"h-5 w-5 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"تحويل لفاتورة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, order.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 325,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 313,\n            columnNumber: 5\n        }, this);\n    const renderInvoices = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900\",\n                            children: \"الفواتير\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 406,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleInvoiceCreate,\n                            className: \"btn-primary flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.PlusIcon, {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 411,\n                                    columnNumber: 11\n                                }, this),\n                                \"فاتورة جديدة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                            lineNumber: 407,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 405,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"رقم الفاتورة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"العميل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المبلغ الإجمالي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"المدفوع\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"الإجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 435,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 418,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: invoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                                children: invoice.invoiceNumber\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: invoice.customerName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-\".concat(getStatusColor(invoice.status), \"-100 text-\").concat(getStatusColor(invoice.status), \"-800\"),\n                                                    children: getStatusText(invoice.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 449,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    invoice.total.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                                children: [\n                                                    \"$\",\n                                                    invoice.paidAmount.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleInvoiceEdit(invoice),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.EyeIcon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-green-600 hover:text-green-900\",\n                                                        children: \"طباعة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, invoice.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 417,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                    lineNumber: 416,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n            lineNumber: 404,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                children: \"إدارة المبيعات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 484,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"نظام المبيعات بثلاث مراحل: عروض الأسعار → أوامر البيع → الفواتير\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 487,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-8 mb-6\",\n                                children: tabs.map((tab, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center p-4 rounded-lg border-2 \".concat(activeTab === tab.id ? \"border-\".concat(tab.color, \"-500 bg-\").concat(tab.color, \"-50\") : \"border-gray-200 bg-gray-50\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                        className: \"h-8 w-8 mb-2 \".concat(activeTab === tab.id ? \"text-\".concat(tab.color, \"-600\") : \"text-gray-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium \".concat(activeTab === tab.id ? \"text-\".concat(tab.color, \"-900\") : \"text-gray-600\"),\n                                                        children: tab.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 text-center mt-1\",\n                                                        children: tab.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, this),\n                                            index < tabs.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUturnLeftIcon_CheckCircleIcon_ClockIcon_DocumentTextIcon_EyeIcon_PlusIcon_ReceiptPercentIcon_ShoppingCartIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__.ArrowRightIcon, {\n                                                className: \"h-6 w-6 text-gray-400 mx-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                lineNumber: 513,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 492,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 483,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white shadow rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8 px-6\",\n                                    children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: \"py-4 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab.id ? \"border-\".concat(tab.color, \"-500 text-\").concat(tab.color, \"-600\") : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                    className: \"h-5 w-5 inline-block ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 19\n                                                }, this),\n                                                tab.name\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                            lineNumber: 525,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 522,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    activeTab === \"quotes\" && renderQuotes(),\n                                    activeTab === \"orders\" && renderOrders(),\n                                    activeTab === \"invoices\" && renderInvoices()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                                lineNumber: 541,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 481,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_QuoteModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: quoteModal.isOpen,\n                onClose: ()=>setQuoteModal({\n                        isOpen: false,\n                        quote: null\n                    }),\n                onSave: handleQuoteSave,\n                quote: quoteModal.quote\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 550,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_SalesOrderModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: salesOrderModal.isOpen,\n                onClose: ()=>setSalesOrderModal({\n                        isOpen: false,\n                        salesOrder: null,\n                        fromQuote: null\n                    }),\n                onSave: handleSalesOrderSave,\n                salesOrder: salesOrderModal.salesOrder,\n                fromQuote: salesOrderModal.fromQuote\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 557,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sales_InvoiceModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: invoiceModal.isOpen,\n                onClose: ()=>setInvoiceModal({\n                        isOpen: false,\n                        invoice: null,\n                        fromSalesOrder: null\n                    }),\n                onSave: handleInvoiceSave,\n                invoice: invoiceModal.invoice,\n                fromSalesOrder: invoiceModal.fromSalesOrder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n                lineNumber: 565,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\sales\\\\index.js\",\n        lineNumber: 480,\n        columnNumber: 5\n    }, this);\n}\n_s(SalesPage, \"RRWhhBSYkrjK1fRa3K47H2b2dW4=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = SalesPage;\nvar _c;\n$RefreshReg$(_c, \"SalesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/sales/index.js\n"));

/***/ })

});