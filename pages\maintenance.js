import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useQuery } from 'react-query';
import axios from 'axios';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  EyeIcon,
  WrenchScrewdriverIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/LoadingSpinner';

export default function Maintenance() {
  const { t } = useTranslation('common');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedStatus, setSelectedStatus] = useState('all');

  // Fetch maintenance orders
  const { data: maintenanceData, isLoading, error, refetch } = useQuery(
    ['maintenance', currentPage, searchTerm, selectedStatus],
    async () => {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        search: searchTerm,
        status: selectedStatus,
      });

      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/maintenance?${params}`);
      return response.data;
    },
    {
      keepPreviousData: true,
    }
  );

  const orders = maintenanceData?.orders || [];
  const pagination = maintenanceData?.pagination || {};

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    refetch();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'RECEIVED':
        return 'bg-blue-100 text-blue-800';
      case 'IN_PROGRESS':
        return 'bg-yellow-100 text-yellow-800';
      case 'WAITING_PARTS':
        return 'bg-orange-100 text-orange-800';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'DELIVERED':
        return 'bg-purple-100 text-purple-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getDeviceIcon = (deviceType) => {
    if (deviceType.toLowerCase().includes('phone') || deviceType.toLowerCase().includes('mobile')) {
      return <DevicePhoneMobileIcon className="h-4 w-4" />;
    } else if (deviceType.toLowerCase().includes('laptop') || deviceType.toLowerCase().includes('computer')) {
      return <ComputerDesktopIcon className="h-4 w-4" />;
    }
    return <WrenchScrewdriverIcon className="h-4 w-4" />;
  };

  if (isLoading && !orders.length) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h3 className="mt-2 text-sm font-medium text-gray-900">{t('common.error')}</h3>
        <p className="mt-1 text-sm text-gray-500">Failed to load maintenance orders</p>
        <button
          onClick={() => refetch()}
          className="mt-4 btn-primary"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{t('maintenance.title')}</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage device repairs and maintenance services
          </p>
        </div>
        <button className="btn-primary">
          <PlusIcon className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2" />
          {t('maintenance.newOrder')}
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder={t('common.search')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input pl-10 rtl:pl-3 rtl:pr-10"
              />
            </div>
          </div>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="form-input"
          >
            <option value="all">All Status</option>
            <option value="received">{t('maintenance.received')}</option>
            <option value="in_progress">{t('maintenance.inProgress')}</option>
            <option value="waiting_parts">{t('maintenance.waitingParts')}</option>
            <option value="completed">{t('maintenance.completed')}</option>
            <option value="delivered">{t('maintenance.delivered')}</option>
          </select>
          <button type="submit" className="btn-primary">
            {t('common.search')}
          </button>
        </form>
      </div>

      {/* Maintenance Orders Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="table">
            <thead>
              <tr>
                <th>{t('maintenance.orderNumber')}</th>
                <th>{t('maintenance.customer')}</th>
                <th>{t('maintenance.deviceType')}</th>
                <th>{t('maintenance.problem')}</th>
                <th>{t('maintenance.receivedDate')}</th>
                <th>{t('maintenance.estimatedCost')}</th>
                <th>{t('maintenance.status')}</th>
                <th>{t('common.actions')}</th>
              </tr>
            </thead>
            <tbody>
              {orders.map((order) => (
                <tr key={order.id}>
                  <td className="font-medium">{order.orderNumber}</td>
                  <td>
                    <div>
                      <div className="font-medium text-gray-900">{order.customer?.name}</div>
                      <div className="text-sm text-gray-500">{order.customer?.phone}</div>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center">
                      {getDeviceIcon(order.deviceType)}
                      <div className="ml-2 rtl:ml-0 rtl:mr-2">
                        <div className="text-sm font-medium text-gray-900">{order.deviceType}</div>
                        <div className="text-xs text-gray-500">{order.deviceModel}</div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="max-w-xs">
                      <div className="text-sm text-gray-900 truncate">{order.problem}</div>
                      <div className="text-xs text-gray-500 truncate">{order.problemAr}</div>
                    </div>
                  </td>
                  <td>
                    {new Date(order.receivedDate).toLocaleDateString()}
                  </td>
                  <td>
                    {order.estimatedCost ? (
                      <span className="font-medium">${(parseFloat(order.estimatedCost) || 0).toFixed(2)}</span>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </td>
                  <td>
                    <span className={`badge ${getStatusColor(order.status)}`}>
                      {t(`maintenance.${order.status.toLowerCase()}`)}
                    </span>
                  </td>
                  <td>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <button
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title={t('common.view')}
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        className="p-1 text-gray-400 hover:text-green-600"
                        title={t('common.edit')}
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Empty State */}
        {orders.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <WrenchScrewdriverIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No maintenance orders found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by receiving your first device for maintenance.
            </p>
            <div className="mt-6">
              <button className="btn-primary">
                <PlusIcon className="h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2" />
                {t('maintenance.newOrder')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  };
}
