# Business Management System (BMS)

A comprehensive business management system with Arabic and English language support, built with modern web technologies.

## Features

### Core Modules
- **Dashboard** - Real-time overview with statistics and charts
- **Product Management** - Complete product catalog with inventory tracking
- **Customer Management** - Customer and supplier database
- **Sales Management** - Sales orders, quotations, and invoicing
- **Purchase Management** - Purchase orders and supplier management
- **Inventory Management** - Real-time stock tracking and alerts
- **Accounting** - Basic financial tracking and reporting
- **Technical Maintenance** - Service order management with WhatsApp notifications
- **Reports** - Comprehensive business reports with export capabilities
- **Settings** - System configuration and user management

### Key Features
- **Bilingual Support** - Full Arabic and English interface
- **Real-time Updates** - Live notifications and data synchronization
- **Role-based Access** - Multiple user roles with different permissions
- **Responsive Design** - Works on desktop, tablet, and mobile devices
- **Modern UI** - Clean, intuitive interface with dark mode support
- **Export Capabilities** - PDF and Excel export for reports
- **Security** - JWT authentication with optional 2FA

## Technology Stack

### Frontend
- **Next.js 14** - React framework with SSR and i18n
- **React 18** - Modern React with hooks
- **Tailwind CSS** - Utility-first CSS framework
- **next-i18next** - Internationalization
- **React Query** - Data fetching and caching
- **React Hook Form** - Form management
- **Recharts** - Data visualization
- **Socket.io Client** - Real-time communication

### Backend
- **Node.js** - JavaScript runtime
- **Express.js** - Web framework
- **Prisma** - Database ORM
- **PostgreSQL** - Primary database
- **Socket.io** - Real-time communication
- **JWT** - Authentication
- **bcryptjs** - Password hashing

## Installation

### Prerequisites
- Node.js 18+ 
- PostgreSQL 12+
- npm or yarn

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd business-management-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your database and configuration details:
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/business_management_db"
   JWT_SECRET="your-super-secret-jwt-key-here"
   PORT=3001
   NEXT_PUBLIC_API_URL="http://localhost:3001"
   ```

4. **Database Setup**
   ```bash
   # Generate Prisma client
   npm run db:generate
   
   # Push database schema
   npm run db:push
   
   # Or run migrations
   npm run db:migrate
   ```

5. **Start Development Servers**
   
   Terminal 1 - Backend:
   ```bash
   npm run server:dev
   ```
   
   Terminal 2 - Frontend:
   ```bash
   npm run dev
   ```

6. **Access the Application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - Database Studio: `npm run db:studio`

## Default Users

The system comes with demo credentials:

- **Admin**: admin / admin123
- **Manager**: manager / manager123  
- **Sales**: sales / sales123

## Usage

### Language Switching
- Click the language icon in the header to switch between Arabic and English
- The interface automatically adjusts text direction (RTL for Arabic)

### Dashboard
- View real-time business statistics
- Monitor low stock alerts
- Track pending orders and maintenance requests
- Analyze sales trends with interactive charts

### Product Management
- Add/edit products with bilingual names and descriptions
- Set minimum stock levels for automatic alerts
- Track current inventory levels
- Manage product categories

### Sales Process
1. Create quotations for customers
2. Convert quotations to sales orders
3. Process orders and update inventory
4. Generate invoices and delivery notes
5. Handle returns if needed

### Maintenance Workflow
1. Receive maintenance requests
2. Diagnose and estimate costs
3. Get customer approval
4. Perform maintenance work
5. Complete and deliver with notifications

## API Documentation

### Authentication
```bash
POST /api/auth/login
POST /api/auth/logout
GET /api/auth/profile
PUT /api/auth/profile
PUT /api/auth/change-password
```

### Products
```bash
GET /api/products
POST /api/products
GET /api/products/:id
PUT /api/products/:id
DELETE /api/products/:id
```

### Sales
```bash
GET /api/sales
POST /api/sales
GET /api/sales/:id
PUT /api/sales/:id
DELETE /api/sales/:id
```

## Deployment

### Production Build
```bash
npm run build
npm start
```

### Environment Variables
Set the following for production:
```env
NODE_ENV=production
DATABASE_URL=<production-database-url>
JWT_SECRET=<secure-random-string>
NEXT_PUBLIC_API_URL=<production-api-url>
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: [Link to docs]
- Issues: [GitHub Issues]

## Roadmap

- [ ] Advanced reporting with custom filters
- [ ] Mobile app (React Native)
- [ ] Advanced inventory management
- [ ] Integration with accounting software
- [ ] Multi-company support
- [ ] Advanced user permissions
- [ ] API rate limiting
- [ ] Automated backups
- [ ] Email notifications
- [ ] Barcode scanning
