/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/products"],{

/***/ "__barrel_optimize__?names=ArchiveBoxIcon,CubeIcon,CurrencyDollarIcon,TagIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!********************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ArchiveBoxIcon,CubeIcon,CurrencyDollarIcon,TagIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \********************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArchiveBoxIcon: function() { return /* reexport safe */ _ArchiveBoxIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   CubeIcon: function() { return /* reexport safe */ _CubeIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   CurrencyDollarIcon: function() { return /* reexport safe */ _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   TagIcon: function() { return /* reexport safe */ _TagIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   XMarkIcon: function() { return /* reexport safe */ _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _ArchiveBoxIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ArchiveBoxIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/ArchiveBoxIcon.js\");\n/* harmony import */ var _CubeIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CubeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CubeIcon.js\");\n/* harmony import */ var _CurrencyDollarIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CurrencyDollarIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _TagIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TagIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/TagIcon.js\");\n/* harmony import */ var _XMarkIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./XMarkIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BcmNoaXZlQm94SWNvbixDdWJlSWNvbixDdXJyZW5jeURvbGxhckljb24sVGFnSWNvbixYTWFya0ljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQytEO0FBQ1o7QUFDb0I7QUFDdEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/NWQwZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXJjaGl2ZUJveEljb24gfSBmcm9tIFwiLi9BcmNoaXZlQm94SWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEN1YmVJY29uIH0gZnJvbSBcIi4vQ3ViZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDdXJyZW5jeURvbGxhckljb24gfSBmcm9tIFwiLi9DdXJyZW5jeURvbGxhckljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUYWdJY29uIH0gZnJvbSBcIi4vVGFnSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFhNYXJrSWNvbiB9IGZyb20gXCIuL1hNYXJrSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ArchiveBoxIcon,CubeIcon,CurrencyDollarIcon,TagIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "__barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js":
/*!*****************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js ***!
  \*****************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EyeIcon: function() { return /* reexport safe */ _EyeIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   MagnifyingGlassIcon: function() { return /* reexport safe */ _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   PencilIcon: function() { return /* reexport safe */ _PencilIcon_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   PlusIcon: function() { return /* reexport safe */ _PlusIcon_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; },\n/* harmony export */   TrashIcon: function() { return /* reexport safe */ _TrashIcon_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _EyeIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EyeIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _MagnifyingGlassIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MagnifyingGlassIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _PencilIcon_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./PencilIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _PlusIcon_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PlusIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _TrashIcon_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TrashIcon.js */ \"./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1FeWVJY29uLE1hZ25pZnlpbmdHbGFzc0ljb24sUGVuY2lsSWNvbixQbHVzSWNvbixUcmFzaEljb24hPSEuL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQ2lEO0FBQ3dCO0FBQ2xCO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vaW5kZXguanM/Mzc5ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXllSWNvbiB9IGZyb20gXCIuL0V5ZUljb24uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNYWduaWZ5aW5nR2xhc3NJY29uIH0gZnJvbSBcIi4vTWFnbmlmeWluZ0dsYXNzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFBlbmNpbEljb24gfSBmcm9tIFwiLi9QZW5jaWxJY29uLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGx1c0ljb24gfSBmcm9tIFwiLi9QbHVzSWNvbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFRyYXNoSWNvbiB9IGZyb20gXCIuL1RyYXNoSWNvbi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Cproducts.js&page=%2Fproducts!":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Cproducts.js&page=%2Fproducts! ***!
  \******************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/products\",\n      function () {\n        return __webpack_require__(/*! ./pages/products.js */ \"./pages/products.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/products\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDVXNlcnMlNUNWaWN0b3IlNUNEZXNrdG9wJTVDTmV3JTIwZm9sZGVyJTVDcGFnZXMlNUNwcm9kdWN0cy5qcyZwYWdlPSUyRnByb2R1Y3RzISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLGdEQUFxQjtBQUM1QztBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/ZDg5ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL3Byb2R1Y3RzXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwiLi9wYWdlcy9wcm9kdWN0cy5qc1wiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvcHJvZHVjdHNcIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Cproducts.js&page=%2Fproducts!\n"));

/***/ }),

/***/ "./components/ProductModals.js":
/*!*************************************!*\
  !*** ./components/ProductModals.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductFormModal: function() { return /* binding */ ProductFormModal; },\n/* harmony export */   ViewProductModal: function() { return /* binding */ ViewProductModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArchiveBoxIcon_CubeIcon_CurrencyDollarIcon_TagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArchiveBoxIcon,CubeIcon,CurrencyDollarIcon,TagIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=ArchiveBoxIcon,CubeIcon,CurrencyDollarIcon,TagIcon,XMarkIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n// View Product Modal\nfunction ViewProductModal(param) {\n    let { product, isOpen, onClose } = param;\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    if (!isOpen || !product) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: t(\"products.viewProduct\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                            lineNumber: 28,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CubeIcon_CurrencyDollarIcon_TagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.XMarkIcon, {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 35,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                            lineNumber: 31,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CubeIcon_CurrencyDollarIcon_TagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CubeIcon, {\n                                                    className: \"h-5 w-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 41,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                            lineNumber: 43,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: product.nameAr\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                            lineNumber: 44,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CubeIcon_CurrencyDollarIcon_TagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.TagIcon, {\n                                                    className: \"h-5 w-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: product.code\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium text-gray-500\",\n                                                        children: \"CAT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 55,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: product.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CubeIcon_CurrencyDollarIcon_TagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.CurrencyDollarIcon, {\n                                                    className: \"h-5 w-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: [\n                                                                \"Unit Price: $\",\n                                                                (parseFloat(product.unitPrice) || 0).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                            lineNumber: 63,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                \"Cost Price: $\",\n                                                                (parseFloat(product.costPrice) || 0).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CubeIcon_CurrencyDollarIcon_TagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.ArchiveBoxIcon, {\n                                                    className: \"h-5 w-5 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: [\n                                                                \"Current Stock: \",\n                                                                product.currentStock || 0,\n                                                                \" \",\n                                                                product.unit\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                            lineNumber: 75,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                \"Min Stock: \",\n                                                                product.minStock || 0,\n                                                                \" \",\n                                                                product.unit\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                            lineNumber: 78,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this),\n                                        product.barcode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium text-gray-500\",\n                                                        children: \"BAR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 87,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-900\",\n                                                    children: product.barcode\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, this),\n                                        product.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 flex items-center justify-center mt-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium text-gray-500\",\n                                                        children: \"DESC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-900\",\n                                                            children: product.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.descriptionAr && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: product.descriptionAr\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-5 h-5 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium text-gray-500\",\n                                                        children: \"STATUS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"badge \".concat(product.isActive ? \"badge-success\" : \"badge-secondary\"),\n                                                    children: product.isActive ? \"Active\" : \"Inactive\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"btn-secondary w-full sm:w-auto sm:ml-3\",\n                                children: t(\"common.close\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(ViewProductModal, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = ViewProductModal;\n// Create/Edit Product Modal\nfunction ProductFormModal(param) {\n    let { product, isOpen, onClose, onSuccess } = param;\n    _s1();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const isEdit = !!product;\n    var _product_hasInventory, _product_isActive;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        code: (product === null || product === void 0 ? void 0 : product.code) || \"\",\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        nameAr: (product === null || product === void 0 ? void 0 : product.nameAr) || \"\",\n        categoryId: (product === null || product === void 0 ? void 0 : product.categoryId) || \"\",\n        productType: (product === null || product === void 0 ? void 0 : product.productType) || \"COMPONENT\",\n        unitPrice: (product === null || product === void 0 ? void 0 : product.unitPrice) || \"\",\n        costPrice: (product === null || product === void 0 ? void 0 : product.costPrice) || \"\",\n        unit: (product === null || product === void 0 ? void 0 : product.unit) || \"piece\",\n        unitAr: (product === null || product === void 0 ? void 0 : product.unitAr) || \"قطعة\",\n        barcode: (product === null || product === void 0 ? void 0 : product.barcode) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        descriptionAr: (product === null || product === void 0 ? void 0 : product.descriptionAr) || \"\",\n        minStock: (product === null || product === void 0 ? void 0 : product.minStock) || 0,\n        currentStock: (product === null || product === void 0 ? void 0 : product.currentStock) || 0,\n        hasInventory: (_product_hasInventory = product === null || product === void 0 ? void 0 : product.hasInventory) !== null && _product_hasInventory !== void 0 ? _product_hasInventory : true,\n        customizationOptions: (product === null || product === void 0 ? void 0 : product.customizationOptions) || {},\n        compatibleComponents: (product === null || product === void 0 ? void 0 : product.compatibleComponents) || [],\n        assemblyInstructions: (product === null || product === void 0 ? void 0 : product.assemblyInstructions) || \"\",\n        assemblyInstructionsAr: (product === null || product === void 0 ? void 0 : product.assemblyInstructionsAr) || \"\",\n        isActive: (_product_isActive = product === null || product === void 0 ? void 0 : product.isActive) !== null && _product_isActive !== void 0 ? _product_isActive : true\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const mutation = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)(async (data)=>{\n        if (isEdit) {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].put(\"\".concat(\"http://localhost:3070\", \"/api/products/\").concat(product.id), data);\n            return response.data;\n        } else {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_6__[\"default\"].post(\"\".concat(\"http://localhost:3070\", \"/api/products\"), data);\n            return response.data;\n        }\n    }, {\n        onSuccess: ()=>{\n            queryClient.invalidateQueries([\n                \"products\"\n            ]);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(isEdit ? \"Product updated successfully\" : \"Product created successfully\");\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n            onClose();\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response, _error_response_data1, _error_response1;\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"Operation failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(errorMessage);\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.errors) {\n                setErrors(error.response.data.errors);\n            }\n        }\n    });\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        setErrors({});\n        // Basic validation\n        const newErrors = {};\n        if (!formData.code) newErrors.code = \"Code is required\";\n        if (!formData.name) newErrors.name = \"Name is required\";\n        if (!formData.nameAr) newErrors.nameAr = \"Arabic name is required\";\n        if (!formData.categoryId) newErrors.categoryId = \"Category is required\";\n        if (!formData.productType) newErrors.productType = \"Product type is required\";\n        if (!formData.unitPrice || parseFloat(formData.unitPrice) <= 0) {\n            newErrors.unitPrice = \"Unit price is required and must be greater than 0\";\n        }\n        if (!formData.costPrice || parseFloat(formData.costPrice) <= 0) {\n            newErrors.costPrice = \"Cost price is required and must be greater than 0\";\n        }\n        if (Object.keys(newErrors).length > 0) {\n            setErrors(newErrors);\n            return;\n        }\n        mutation.mutate({\n            ...formData,\n            unitPrice: parseFloat(formData.unitPrice),\n            costPrice: parseFloat(formData.costPrice),\n            minStock: parseInt(formData.minStock) || 0,\n            currentStock: parseInt(formData.currentStock) || 0,\n            customizationOptions: formData.productType === \"CUSTOMIZABLE\" ? formData.customizationOptions : null,\n            compatibleComponents: formData.productType === \"CUSTOMIZABLE\" ? formData.compatibleComponents : [],\n            assemblyInstructions: formData.productType === \"CUSTOMIZABLE\" ? formData.assemblyInstructions : null,\n            assemblyInstructionsAr: formData.productType === \"CUSTOMIZABLE\" ? formData.assemblyInstructionsAr : null\n        });\n    };\n    const handleChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        let newData = {\n            ...formData,\n            [name]: type === \"checkbox\" ? checked : value\n        };\n        // Auto-update hasInventory based on productType\n        if (name === \"productType\") {\n            newData.hasInventory = value !== \"SERVICE\";\n            if (value === \"SERVICE\") {\n                newData.currentStock = 0;\n                newData.minStock = 0;\n            }\n        }\n        setFormData(newData);\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 overflow-y-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: isEdit ? t(\"products.editProduct\") : t(\"products.addProduct\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: onClose,\n                                                className: \"text-gray-400 hover:text-gray-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArchiveBoxIcon_CubeIcon_CurrencyDollarIcon_TagIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__.XMarkIcon, {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: [\n                                                            t(\"products.productCode\"),\n                                                            \" *\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"code\",\n                                                        value: formData.code,\n                                                        onChange: handleChange,\n                                                        className: \"form-input \".concat(errors.code ? \"border-red-500\" : \"\"),\n                                                        placeholder: \"Enter product code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.code\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 35\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Product Type *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"productType\",\n                                                        value: formData.productType,\n                                                        onChange: handleChange,\n                                                        className: \"form-input \".concat(errors.productType ? \"border-red-500\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"COMPONENT\",\n                                                                children: \"Component (مكون)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"CUSTOMIZABLE\",\n                                                                children: \"Customizable Product (منتج قابل للتخصيص)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERVICE\",\n                                                                children: \"Service (خدمة)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.productType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.productType\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 42\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: [\n                                                            t(\"products.category\"),\n                                                            \" *\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"categoryId\",\n                                                        value: formData.categoryId,\n                                                        onChange: handleChange,\n                                                        className: \"form-input \".concat(errors.categoryId ? \"border-red-500\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"Select Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"electronics\",\n                                                                children: \"Electronics\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"computers\",\n                                                                children: \"Computers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"mobile\",\n                                                                children: \"Mobile Devices\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"services\",\n                                                                children: \"Services\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.categoryId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.categoryId\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Name (English) *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"name\",\n                                                        value: formData.name,\n                                                        onChange: handleChange,\n                                                        className: \"form-input \".concat(errors.name ? \"border-red-500\" : \"\"),\n                                                        placeholder: \"Enter name in English\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 35\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Name (Arabic) *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"nameAr\",\n                                                        value: formData.nameAr,\n                                                        onChange: handleChange,\n                                                        className: \"form-input \".concat(errors.nameAr ? \"border-red-500\" : \"\"),\n                                                        placeholder: \"أدخل الاسم بالعربية\",\n                                                        dir: \"rtl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.nameAr && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.nameAr\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: [\n                                                            t(\"products.unitPrice\"),\n                                                            \" *\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        name: \"unitPrice\",\n                                                        value: formData.unitPrice,\n                                                        onChange: handleChange,\n                                                        className: \"form-input \".concat(errors.unitPrice ? \"border-red-500\" : \"\"),\n                                                        placeholder: \"0.00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.unitPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.unitPrice\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 40\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: [\n                                                            t(\"products.costPrice\"),\n                                                            \" *\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        name: \"costPrice\",\n                                                        value: formData.costPrice,\n                                                        onChange: handleChange,\n                                                        className: \"form-input \".concat(errors.costPrice ? \"border-red-500\" : \"\"),\n                                                        placeholder: \"0.00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.costPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"form-error\",\n                                                        children: errors.costPrice\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 40\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: t(\"products.unit\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        name: \"unit\",\n                                                        value: formData.unit,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"piece\",\n                                                                children: \"Piece\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"kg\",\n                                                                children: \"Kilogram\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"liter\",\n                                                                children: \"Liter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"meter\",\n                                                                children: \"Meter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"box\",\n                                                                children: \"Box\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: t(\"products.minStock\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        name: \"minStock\",\n                                                        value: formData.minStock,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: t(\"products.barcode\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"barcode\",\n                                                        value: formData.barcode,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        placeholder: \"Enter barcode\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Description (English)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        name: \"description\",\n                                                        value: formData.description,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        rows: \"3\",\n                                                        placeholder: \"Enter description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Description (Arabic)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        name: \"descriptionAr\",\n                                                        value: formData.descriptionAr,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        rows: \"3\",\n                                                        placeholder: \"أدخل الوصف\",\n                                                        dir: \"rtl\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                name: \"hasInventory\",\n                                                                checked: formData.hasInventory,\n                                                                onChange: handleChange,\n                                                                className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\",\n                                                                disabled: formData.productType === \"SERVICE\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-sm text-gray-700\",\n                                                                children: \"Has Inventory (يحتاج مخزون)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: \"Services automatically have no inventory\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this),\n                                            formData.hasInventory && formData.productType !== \"SERVICE\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"form-label\",\n                                                        children: \"Current Stock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        name: \"currentStock\",\n                                                        value: formData.currentStock,\n                                                        onChange: handleChange,\n                                                        className: \"form-input\",\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this),\n                                            formData.productType === \"CUSTOMIZABLE\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"md:col-span-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label\",\n                                                                children: \"Assembly Instructions (English)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                name: \"assemblyInstructions\",\n                                                                value: formData.assemblyInstructions,\n                                                                onChange: handleChange,\n                                                                className: \"form-input\",\n                                                                rows: \"3\",\n                                                                placeholder: \"Enter assembly instructions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"md:col-span-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label\",\n                                                                children: \"Assembly Instructions (Arabic)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                name: \"assemblyInstructionsAr\",\n                                                                value: formData.assemblyInstructionsAr,\n                                                                onChange: handleChange,\n                                                                className: \"form-input\",\n                                                                rows: \"3\",\n                                                                placeholder: \"أدخل تعليمات التجميع\",\n                                                                dir: \"rtl\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"md:col-span-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"form-label\",\n                                                                children: \"Compatible Components (Comma-separated codes)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                name: \"compatibleComponents\",\n                                                                value: Array.isArray(formData.compatibleComponents) ? formData.compatibleComponents.join(\", \") : \"\",\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            compatibleComponents: e.target.value.split(\",\").map((s)=>s.trim()).filter((s)=>s)\n                                                                        })),\n                                                                className: \"form-input\",\n                                                                placeholder: \"CPU001, RAM001, SSD001\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 531,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: \"Enter product codes separated by commas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            name: \"isActive\",\n                                                            checked: formData.isActive,\n                                                            onChange: handleChange,\n                                                            className: \"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm text-gray-700\",\n                                                            children: t(\"common.active\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                                lineNumber: 549,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: mutation.isLoading,\n                                        className: \"btn-primary w-full sm:w-auto sm:ml-3\",\n                                        children: mutation.isLoading ? \"Saving...\" : isEdit ? t(\"common.save\") : t(\"common.add\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                        lineNumber: 567,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: onClose,\n                                        className: \"btn-secondary w-full sm:w-auto mt-3 sm:mt-0\",\n                                        children: t(\"common.cancel\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                        lineNumber: 574,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                                lineNumber: 566,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n            lineNumber: 249,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\components\\\\ProductModals.js\",\n        lineNumber: 248,\n        columnNumber: 5\n    }, this);\n}\n_s1(ProductFormModal, \"vl7ZrU9p3V0cYge36bHFNqCq6os=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation\n    ];\n});\n_c1 = ProductFormModal;\nvar _c, _c1;\n$RefreshReg$(_c, \"ViewProductModal\");\n$RefreshReg$(_c1, \"ProductFormModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ProductModals.js\n"));

/***/ }),

/***/ "./pages/products.js":
/*!***************************!*\
  !*** ./pages/products.js ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __N_SSG: function() { return /* binding */ __N_SSG; },\n/* harmony export */   \"default\": function() { return /* binding */ Products; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!@heroicons/react/24/outline */ \"__barrel_optimize__?names=EyeIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,TrashIcon!=!./node_modules/@heroicons/react/24/outline/esm/index.js\");\n/* harmony import */ var _components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/LoadingSpinner */ \"./components/LoadingSpinner.js\");\n/* harmony import */ var _components_ProductModals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/ProductModals */ \"./components/ProductModals.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nvar __N_SSG = true;\nfunction Products() {\n    _s();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    const queryClient = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showViewModal, setShowViewModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProduct, setSelectedProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch products\n    const { data: productsData, isLoading, error, refetch } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        \"products\",\n        currentPage,\n        searchTerm,\n        selectedCategory,\n        selectedStatus\n    ], async ()=>{\n        const params = new URLSearchParams({\n            page: currentPage.toString(),\n            limit: \"10\",\n            search: searchTerm,\n            category: selectedCategory,\n            status: selectedStatus\n        });\n        const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"\".concat(\"http://localhost:3070\", \"/api/products?\").concat(params));\n        return response.data;\n    }, {\n        keepPreviousData: true\n    });\n    const products = (productsData === null || productsData === void 0 ? void 0 : productsData.products) || [];\n    const pagination = (productsData === null || productsData === void 0 ? void 0 : productsData.pagination) || {};\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        setCurrentPage(1);\n        refetch();\n    };\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n    };\n    // Delete product mutation\n    const deleteMutation = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)(async (productId)=>{\n        const response = await axios__WEBPACK_IMPORTED_MODULE_7__[\"default\"][\"delete\"](\"\".concat(\"http://localhost:3070\", \"/api/products/\").concat(productId));\n        return response.data;\n    }, {\n        onSuccess: ()=>{\n            queryClient.invalidateQueries([\n                \"products\"\n            ]);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].success(t(\"products.deleteSuccess\") || \"Product deleted successfully\");\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || \"Failed to delete product\");\n        }\n    });\n    // Handle delete product\n    const handleDelete = async (product)=>{\n        if (window.confirm(t(\"products.confirmDelete\") || \"Are you sure you want to delete \".concat(product.name, \"?\"))) {\n            deleteMutation.mutate(product.id);\n        }\n    };\n    // Handle view product\n    const handleView = (product)=>{\n        setSelectedProduct(product);\n        setShowViewModal(true);\n    };\n    // Handle edit product\n    const handleEdit = (product)=>{\n        setSelectedProduct(product);\n        setShowEditModal(true);\n    };\n    // Handle create product\n    const handleCreate = ()=>{\n        setSelectedProduct(null);\n        setShowCreateModal(true);\n    };\n    if (isLoading && !products.length) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                lineNumber: 107,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"mt-2 text-sm font-medium text-gray-900\",\n                    children: t(\"common.error\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: \"Failed to load products\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>refetch(),\n                    className: \"mt-4 btn-primary\",\n                    children: \"Try Again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: t(\"products.title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-600\",\n                                children: \"Manage your product catalog\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCreate,\n                        className: \"btn-primary\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.PlusIcon, {\n                                className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            t(\"products.addProduct\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-4 rounded-lg shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col sm:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.MagnifyingGlassIcon, {\n                                        className: \"absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t(\"common.search\"),\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"form-input pl-10 rtl:pl-3 rtl:pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedCategory,\n                            onChange: (e)=>setSelectedCategory(e.target.value),\n                            className: \"form-input\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"all\",\n                                    children: \"All Categories\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"electronics\",\n                                    children: \"Electronics\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"computers\",\n                                    children: \"Computers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"mobile\",\n                                    children: \"Mobile Devices\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedStatus,\n                            onChange: (e)=>setSelectedStatus(e.target.value),\n                            className: \"form-input\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"all\",\n                                    children: \"All Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"active\",\n                                    children: t(\"products.active\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"inactive\",\n                                    children: t(\"products.inactive\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary\",\n                            children: t(\"common.search\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"table\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"products.productCode\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"products.productName\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"products.category\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"products.unitPrice\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"products.currentStock\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"products.status\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                children: t(\"common.actions\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: products.map((product)=>{\n                                        var _product_category, _product_category1;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"font-medium\",\n                                                    children: product.code\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: product.nameAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: (_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: (_product_category1 = product.category) === null || _product_category1 === void 0 ? void 0 : _product_category1.nameAr\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: [\n                                                        \"$\",\n                                                        (parseFloat(product.unitPrice) || 0).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(product.currentStock <= product.minStock ? \"bg-red-100 text-red-800\" : product.currentStock <= product.minStock * 2 ? \"bg-yellow-100 text-yellow-800\" : \"bg-green-100 text-green-800\"),\n                                                        children: [\n                                                            product.currentStock,\n                                                            \" \",\n                                                            product.unit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"badge \".concat(product.isActive ? \"badge-success\" : \"badge-secondary\"),\n                                                        children: product.isActive ? t(\"products.active\") : t(\"products.inactive\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleView(product),\n                                                                className: \"p-1 text-gray-400 hover:text-blue-600 transition-colors\",\n                                                                title: t(\"common.view\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.EyeIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleEdit(product),\n                                                                className: \"p-1 text-gray-400 hover:text-green-600 transition-colors\",\n                                                                title: t(\"common.edit\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.PencilIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDelete(product),\n                                                                className: \"p-1 text-gray-400 hover:text-red-600 transition-colors\",\n                                                                title: t(\"common.delete\"),\n                                                                disabled: deleteMutation.isLoading,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.TrashIcon, {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, product.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    pagination.pages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-700\",\n                                    children: [\n                                        \"Showing \",\n                                        (pagination.page - 1) * pagination.limit + 1,\n                                        \" to\",\n                                        \" \",\n                                        Math.min(pagination.page * pagination.limit, pagination.total),\n                                        \" of\",\n                                        \" \",\n                                        pagination.total,\n                                        \" results\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handlePageChange(pagination.page - 1),\n                                            disabled: pagination.page <= 1,\n                                            className: \"btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Previous\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this),\n                                        Array.from({\n                                            length: Math.min(5, pagination.pages)\n                                        }, (_, i)=>{\n                                            const page = i + Math.max(1, pagination.page - 2);\n                                            if (page > pagination.pages) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePageChange(page),\n                                                className: \"btn-sm \".concat(page === pagination.page ? \"btn-primary\" : \"btn-secondary\"),\n                                                children: page\n                                            }, page, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                                lineNumber: 284,\n                                                columnNumber: 21\n                                            }, this);\n                                        }),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handlePageChange(pagination.page + 1),\n                                            disabled: pagination.page >= pagination.pages,\n                                            className: \"btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: \"Next\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                            lineNumber: 266,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, this),\n                    products.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"mt-2 text-sm font-medium text-gray-900\",\n                                children: \"No products found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-500\",\n                                children: \"Get started by creating your first product.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCreate,\n                                    className: \"btn-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__.PlusIcon, {\n                                            className: \"h-5 w-5 mr-2 rtl:mr-0 rtl:ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this),\n                                        t(\"products.addProduct\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductModals__WEBPACK_IMPORTED_MODULE_6__.ViewProductModal, {\n                product: selectedProduct,\n                isOpen: showViewModal,\n                onClose: ()=>setShowViewModal(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductModals__WEBPACK_IMPORTED_MODULE_6__.ProductFormModal, {\n                product: selectedProduct,\n                isOpen: showCreateModal || showEditModal,\n                onClose: ()=>{\n                    setShowCreateModal(false);\n                    setShowEditModal(false);\n                    setSelectedProduct(null);\n                },\n                onSuccess: ()=>{\n                    setShowCreateModal(false);\n                    setShowEditModal(false);\n                    setSelectedProduct(null);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder\\\\pages\\\\products.js\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n_s(Products, \"FI+UvlqzrDOuwYaDp5bu17nRwRM=\", false, function() {\n    return [\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation\n    ];\n});\n_c = Products;\nvar _c;\n$RefreshReg$(_c, \"Products\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/products.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js ***!
  \****************************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction CurrencyDollarIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n    }));\n}\n_c = CurrencyDollarIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CurrencyDollarIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CurrencyDollarIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js ***!
  \*****************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction EyeIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n    }));\n}\n_c = EyeIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(EyeIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"EyeIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lL2VzbS9FeWVJY29uLmpzIiwibWFwcGluZ3MiOiI7O0FBQStCO0FBQy9CLFNBQVNDLFFBQVEsS0FJaEIsRUFBRUMsTUFBTTtRQUpRLEVBQ2ZDLEtBQUssRUFDTEMsT0FBTyxFQUNQLEdBQUdDLE9BQ0osR0FKZ0I7SUFLZixPQUFPLFdBQVcsR0FBRUwsZ0RBQW1CLENBQUMsT0FBT08sT0FBT0MsTUFBTSxDQUFDO1FBQzNEQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLFFBQVE7UUFDUixlQUFlO1FBQ2YsYUFBYTtRQUNiQyxLQUFLWjtRQUNMLG1CQUFtQkU7SUFDckIsR0FBR0MsUUFBUUYsUUFBUSxXQUFXLEdBQUVILGdEQUFtQixDQUFDLFNBQVM7UUFDM0RlLElBQUlYO0lBQ04sR0FBR0QsU0FBUyxNQUFNLFdBQVcsR0FBRUgsZ0RBQW1CLENBQUMsUUFBUTtRQUN6RGdCLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxHQUFHO0lBQ0wsSUFBSSxXQUFXLEdBQUVsQixnREFBbUIsQ0FBQyxRQUFRO1FBQzNDZ0IsZUFBZTtRQUNmQyxnQkFBZ0I7UUFDaEJDLEdBQUc7SUFDTDtBQUNGO0tBMUJTakI7QUEyQlQsTUFBTWtCLGFBQWEsV0FBVyxHQUFHbkIsNkNBQWdCLENBQUNDOztBQUNsRCwrREFBZWtCLFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZS9lc20vRXllSWNvbi5qcz80NjVlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gRXllSWNvbih7XG4gIHRpdGxlLFxuICB0aXRsZUlkLFxuICAuLi5wcm9wc1xufSwgc3ZnUmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCBPYmplY3QuYXNzaWduKHtcbiAgICB4bWxuczogXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiLFxuICAgIGZpbGw6IFwibm9uZVwiLFxuICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgc3Ryb2tlV2lkdGg6IDEuNSxcbiAgICBzdHJva2U6IFwiY3VycmVudENvbG9yXCIsXG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIixcbiAgICBcImRhdGEtc2xvdFwiOiBcImljb25cIixcbiAgICByZWY6IHN2Z1JlZixcbiAgICBcImFyaWEtbGFiZWxsZWRieVwiOiB0aXRsZUlkXG4gIH0sIHByb3BzKSwgdGl0bGUgPyAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInRpdGxlXCIsIHtcbiAgICBpZDogdGl0bGVJZFxuICB9LCB0aXRsZSkgOiBudWxsLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInBhdGhcIiwge1xuICAgIHN0cm9rZUxpbmVjYXA6IFwicm91bmRcIixcbiAgICBzdHJva2VMaW5lam9pbjogXCJyb3VuZFwiLFxuICAgIGQ6IFwiTTIuMDM2IDEyLjMyMmExLjAxMiAxLjAxMiAwIDAgMSAwLS42MzlDMy40MjMgNy41MSA3LjM2IDQuNSAxMiA0LjVjNC42MzggMCA4LjU3MyAzLjAwNyA5Ljk2MyA3LjE3OC4wNy4yMDcuMDcuNDMxIDAgLjYzOUMyMC41NzcgMTYuNDkgMTYuNjQgMTkuNSAxMiAxOS41Yy00LjYzOCAwLTguNTczLTMuMDA3LTkuOTYzLTcuMTc4WlwiXG4gIH0pLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInBhdGhcIiwge1xuICAgIHN0cm9rZUxpbmVjYXA6IFwicm91bmRcIixcbiAgICBzdHJva2VMaW5lam9pbjogXCJyb3VuZFwiLFxuICAgIGQ6IFwiTTE1IDEyYTMgMyAwIDEgMS02IDAgMyAzIDAgMCAxIDYgMFpcIlxuICB9KSk7XG59XG5jb25zdCBGb3J3YXJkUmVmID0gLyojX19QVVJFX18qLyBSZWFjdC5mb3J3YXJkUmVmKEV5ZUljb24pO1xuZXhwb3J0IGRlZmF1bHQgRm9yd2FyZFJlZjsiXSwibmFtZXMiOlsiUmVhY3QiLCJFeWVJY29uIiwic3ZnUmVmIiwidGl0bGUiLCJ0aXRsZUlkIiwicHJvcHMiLCJjcmVhdGVFbGVtZW50IiwiT2JqZWN0IiwiYXNzaWduIiwieG1sbnMiLCJmaWxsIiwidmlld0JveCIsInN0cm9rZVdpZHRoIiwic3Ryb2tlIiwicmVmIiwiaWQiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJkIiwiRm9yd2FyZFJlZiIsImZvcndhcmRSZWYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js ***!
  \*****************************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction MagnifyingGlassIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n    }));\n}\n_c = MagnifyingGlassIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(MagnifyingGlassIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"MagnifyingGlassIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js":
/*!********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js ***!
  \********************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction PencilIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125\"\n    }));\n}\n_c = PencilIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(PencilIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"PencilIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js":
/*!******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js ***!
  \******************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction PlusIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 4.5v15m7.5-7.5h-15\"\n    }));\n}\n_c = PlusIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(PlusIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"PlusIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/TagIcon.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/TagIcon.js ***!
  \*****************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction TagIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M6 6h.008v.008H6V6Z\"\n    }));\n}\n_c = TagIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(TagIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"TagIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/TagIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js ***!
  \*******************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction TrashIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n    }));\n}\n_c = TrashIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(TrashIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"TrashIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\n"));

/***/ }),

/***/ "./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js ***!
  \*******************************************************************/
/***/ (function(__webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction XMarkIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M6 18 18 6M6 6l12 12\"\n    }));\n}\n_c = XMarkIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(XMarkIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"XMarkIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5CNew%20folder%5Cpages%5Cproducts.js&page=%2Fproducts!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);