import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { useMutation, useQueryClient, useQuery } from 'react-query';
import axios from 'axios';
import toast from 'react-hot-toast';
import {
  XMarkIcon,
  ShoppingCartIcon,
  UserIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  CpuChipIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';
import CustomizableProductBuilder from './CustomizableProductBuilder';

// View Sales Order Modal
export function ViewSalesOrderModal({ order, isOpen, onClose }) {
  const { t } = useTranslation('common');

  if (!isOpen || !order) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Sales Order Details
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-6">
              {/* Order Header */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <DocumentTextIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Order Number</p>
                    <p className="text-sm text-gray-500">{order.orderNumber}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <CalendarIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Order Date</p>
                    <p className="text-sm text-gray-500">
                      {new Date(order.orderDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <UserIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Customer</p>
                    <p className="text-sm text-gray-500">{order.customer?.name}</p>
                    <p className="text-xs text-gray-400">{order.customer?.nameAr}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="w-5 h-5 flex items-center justify-center">
                    <span className="text-xs font-medium text-gray-500">STATUS</span>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Status</p>
                    <span className={`badge ${
                      order.status === 'DRAFT' ? 'badge-secondary' :
                      order.status === 'PENDING' ? 'badge-warning' :
                      order.status === 'CONFIRMED' ? 'badge-info' :
                      order.status === 'DELIVERED' ? 'badge-success' : 'badge-danger'
                    }`}>
                      {order.status}
                    </span>
                  </div>
                </div>
              </div>

              {/* Order Items */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Order Items</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Product</th>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Qty</th>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Price</th>
                        <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {order.items?.map((item, index) => (
                        <tr key={index}>
                          <td className="px-3 py-2 text-sm text-gray-900">
                            {item.product?.name}
                            <div className="text-xs text-gray-500">{item.product?.nameAr}</div>
                          </td>
                          <td className="px-3 py-2 text-sm text-gray-900">{item.quantity}</td>
                          <td className="px-3 py-2 text-sm text-gray-900">${(parseFloat(item.unitPrice) || 0).toFixed(2)}</td>
                          <td className="px-3 py-2 text-sm text-gray-900">${(parseFloat(item.total) || 0).toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Order Summary */}
              <div className="border-t pt-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-900">Subtotal:</span>
                  <span className="text-sm text-gray-900">${(parseFloat(order.subtotal) || 0).toFixed(2)}</span>
                </div>
                {order.discount > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-900">Discount:</span>
                    <span className="text-sm text-gray-900">-${(parseFloat(order.discount) || 0).toFixed(2)}</span>
                  </div>
                )}
                {order.taxAmount > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-900">Tax:</span>
                    <span className="text-sm text-gray-900">${(parseFloat(order.taxAmount) || 0).toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between items-center border-t pt-2 mt-2">
                  <span className="text-lg font-bold text-gray-900">Total:</span>
                  <span className="text-lg font-bold text-gray-900">${(parseFloat(order.total) || 0).toFixed(2)}</span>
                </div>
              </div>

              {/* Notes */}
              {order.notes && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Notes</h4>
                  <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">{order.notes}</p>
                </div>
              )}
            </div>
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              onClick={onClose}
              className="btn-secondary w-full sm:w-auto sm:ml-3"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Create Sales Order Modal
export function CreateSalesOrderModal({ isOpen, onClose, onSuccess }) {
  const { t } = useTranslation('common');
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState({
    customerId: '',
    branchId: '',
    dueDate: '',
    items: [],
    notes: '',
  });

  const [errors, setErrors] = useState({});
  const [showCustomBuilder, setShowCustomBuilder] = useState(false);

  // Fetch customers
  const { data: customersData } = useQuery('customers', async () => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/customers`);
    return response.data;
  });

  // Fetch branches
  const { data: branchesData } = useQuery('branches', async () => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/branches`);
    return response.data;
  });

  // Fetch products
  const { data: productsData } = useQuery('products', async () => {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/products`);
    return response.data;
  });

  const customers = customersData?.customers || [];
  const branches = branchesData?.branches || [];
  const products = productsData?.products || [];

  const mutation = useMutation(
    async (data) => {
      const response = await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/api/sales`, data);
      return response.data;
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['sales']);
        toast.success('Sales order created successfully');
        onSuccess?.();
        onClose();
        resetForm();
      },
      onError: (error) => {
        const errorMessage = error.response?.data?.error || 'Failed to create sales order';
        toast.error(errorMessage);
        if (error.response?.data?.errors) {
          setErrors(error.response.data.errors);
        }
      }
    }
  );

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrors({});

    // Basic validation
    const newErrors = {};
    if (!formData.customerId) newErrors.customerId = 'Customer is required';
    if (!formData.branchId) newErrors.branchId = 'Branch is required';
    if (!formData.items.length) newErrors.items = 'At least one item is required';

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    mutation.mutate(formData);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { productId: '', quantity: 1, unitPrice: 0 }]
    }));
  };

  const removeItem = (index) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  const updateItem = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const resetForm = () => {
    setFormData({
      customerId: '',
      branchId: '',
      dueDate: '',
      items: [],
      notes: '',
    });
    setErrors({});
  };

  // Handle custom product configuration
  const handleCustomProduct = (configuration) => {
    const customItem = {
      productId: 'CUSTOM_' + Date.now(),
      productName: 'Custom Built PC',
      productNameAr: 'جهاز كمبيوتر مخصص',
      quantity: 1,
      unitPrice: configuration.totalPrice,
      isCustom: true,
      configuration: configuration
    };

    setFormData(prev => ({
      ...prev,
      items: [...prev.items, customItem]
    }));

    setShowCustomBuilder(false);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Create Sales Order
                </h3>
                <button
                  type="button"
                  onClick={() => {
                    resetForm();
                    onClose();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-6">
                {/* Order Header */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="form-label">Customer *</label>
                    <select
                      name="customerId"
                      value={formData.customerId}
                      onChange={handleChange}
                      className={`form-input ${errors.customerId ? 'border-red-500' : ''}`}
                    >
                      <option value="">Select Customer</option>
                      {customers.filter(c => c.type === 'CUSTOMER' || c.type === 'BOTH').map((customer) => (
                        <option key={customer.id} value={customer.id}>
                          {customer.name}
                        </option>
                      ))}
                    </select>
                    {errors.customerId && <p className="form-error">{errors.customerId}</p>}
                  </div>

                  <div>
                    <label className="form-label">Branch *</label>
                    <select
                      name="branchId"
                      value={formData.branchId}
                      onChange={handleChange}
                      className={`form-input ${errors.branchId ? 'border-red-500' : ''}`}
                    >
                      <option value="">Select Branch</option>
                      {branches.map((branch) => (
                        <option key={branch.id} value={branch.id}>
                          {branch.name}
                        </option>
                      ))}
                    </select>
                    {errors.branchId && <p className="form-error">{errors.branchId}</p>}
                  </div>

                  <div>
                    <label className="form-label">Due Date</label>
                    <input
                      type="date"
                      name="dueDate"
                      value={formData.dueDate}
                      onChange={handleChange}
                      className="form-input"
                    />
                  </div>
                </div>

                {/* Order Items */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-900">Order Items</h4>
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={() => setShowCustomBuilder(true)}
                        className="btn-secondary btn-sm flex items-center"
                      >
                        <CpuChipIcon className="h-4 w-4 mr-1" />
                        Custom PC
                      </button>
                      <button
                        type="button"
                        onClick={addItem}
                        className="btn-secondary btn-sm flex items-center"
                      >
                        <PlusIcon className="h-4 w-4 mr-1" />
                        Add Item
                      </button>
                    </div>
                  </div>

                  {formData.items.map((item, index) => (
                    <div key={index} className={`grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 p-4 border rounded ${item.isCustom ? 'border-primary-300 bg-primary-50' : ''}`}>
                      {item.isCustom ? (
                        <div className="md:col-span-4">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center">
                              <CpuChipIcon className="h-5 w-5 text-primary-600 mr-2" />
                              <span className="font-medium text-primary-900">{item.productName}</span>
                              <span className="text-sm text-primary-600 mr-2">({item.productNameAr})</span>
                            </div>
                            <span className="text-lg font-bold text-primary-900">${item.unitPrice.toFixed(2)}</span>
                          </div>
                          <div className="text-sm text-gray-600">
                            <p>Custom PC Configuration:</p>
                            <ul className="list-disc list-inside mt-1 space-y-1">
                              {Object.entries(item.configuration.components).map(([category, component]) => {
                                if (!component) return null;
                                return (
                                  <li key={category}>
                                    <strong>{category.toUpperCase()}:</strong> {component.name} - ${component.unitPrice}
                                  </li>
                                );
                              })}
                              <li><strong>Assembly Fee:</strong> ${item.configuration.assemblyFee}</li>
                              <li><strong>Service Fee:</strong> ${item.configuration.serviceFee}</li>
                            </ul>
                          </div>
                          <div className="flex items-end mt-4">
                            <button
                              type="button"
                              onClick={() => removeItem(index)}
                              className="btn-danger btn-sm w-full"
                            >
                              Remove Custom PC
                            </button>
                          </div>
                        </div>
                      ) : (
                        <>
                          <div>
                            <label className="form-label">Product</label>
                            <select
                              value={item.productId}
                              onChange={(e) => {
                                const product = products.find(p => p.id === e.target.value);
                                updateItem(index, 'productId', e.target.value);
                                if (product) {
                                  updateItem(index, 'unitPrice', product.unitPrice);
                                }
                              }}
                              className="form-input"
                            >
                              <option value="">Select Product</option>
                              {products.map((product) => (
                                <option key={product.id} value={product.id}>
                                  {product.name}
                                </option>
                              ))}
                            </select>
                          </div>

                          <div>
                            <label className="form-label">Quantity</label>
                            <input
                              type="number"
                              min="1"
                              value={item.quantity}
                              onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value))}
                              className="form-input"
                            />
                          </div>

                          <div>
                            <label className="form-label">Unit Price</label>
                            <input
                              type="number"
                              step="0.01"
                              value={item.unitPrice}
                              onChange={(e) => updateItem(index, 'unitPrice', parseFloat(e.target.value))}
                              className="form-input"
                            />
                          </div>

                          <div className="flex items-end">
                            <button
                              type="button"
                              onClick={() => removeItem(index)}
                              className="btn-danger btn-sm w-full"
                            >
                              Remove
                            </button>
                          </div>
                        </>
                      )}
                    </div>
                  ))}

                  {errors.items && <p className="form-error">{errors.items}</p>}
                </div>

                {/* Notes */}
                <div>
                  <label className="form-label">Notes</label>
                  <textarea
                    name="notes"
                    value={formData.notes}
                    onChange={handleChange}
                    className="form-input"
                    rows="3"
                    placeholder="Enter any additional notes"
                  />
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={mutation.isLoading}
                className="btn-primary w-full sm:w-auto sm:ml-3"
              >
                {mutation.isLoading ? 'Creating...' : 'Create Order'}
              </button>
              <button
                type="button"
                onClick={() => {
                  resetForm();
                  onClose();
                }}
                className="btn-secondary w-full sm:w-auto mt-3 sm:mt-0"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Custom Product Builder Modal */}
      <CustomizableProductBuilder
        isOpen={showCustomBuilder}
        onClose={() => setShowCustomBuilder(false)}
        onSave={handleCustomProduct}
        mode="sales"
      />
    </div>
  );
}
